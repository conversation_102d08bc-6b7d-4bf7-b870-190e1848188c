
package com.dc.summer.ext.postgresql.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.PostgreConstants;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceInfo;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.utils.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * PostgreDataSourceInfo
 */
public class PostgreDataSourceInfo extends JDBCDataSourceInfo {

    private final PostgreDataSource dataSource;

    public PostgreDataSourceInfo(PostgreDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super(metaData);
        this.dataSource = dataSource;
    }

    @Override
    public String getDatabaseProductVersion() {
        String serverVersion = dataSource.getServerVersion();
        return CommonUtils.isEmpty(serverVersion) ? super.getDatabaseProductVersion() : super.getDatabaseProductVersion() + "\n" + serverVersion;
    }

    @Override
    public boolean supportsMultipleResults() {
        return true;
    }

    @Override
    public boolean needsTableMetaForColumnResolution() {
        return dataSource.getServerType().supportsEntityMetadataInResults();
    }

    @Override
    public boolean supportsResultSetLimit() {
        // ??? Disable maxRows for data transfer - it turns cursors off ?
        return dataSource.getServerType().supportsResultSetLimits();
    }

    @Override
    public boolean supportsTransactions() {
        return dataSource.getServerType().supportsTransactions();
    }

    @Override
    protected boolean isIgnoreReadOnlyFlag() {
        return true;
    }

    @Override
    public boolean supportsDDLAutoCommit() {
        return false;
    }

    @Override
    public boolean isExecuteErrorTransactionInterrupted() {
        return true;
    }

    @Override
    public boolean isQueryStatementLockTable() {
        return true;
    }

    @Override
    public String getPrimaryKeySql(String schemaName, String tableName) {
        return "select d.attname as \"key_column\" from pg_catalog.pg_namespace a \n" +
                " left join pg_catalog.pg_class b on a.oid=b.relnamespace \n" +
                " left join pg_catalog.pg_constraint c on b.oid=c.conrelid \n" +
                " left join pg_catalog.pg_attribute d on d.attrelid = c.conrelid and d.attnum=any(c.conkey) \n" +
                " where a.nspname='" + schemaName + "' and b.relname='" + tableName + "' and contype='p'";
    }

    @Override
    public List<String> getPrimaryKeyColumns(List<Map<String, Object>> list) {

        List<String> columns = new ArrayList<>();

        for (Map<String, Object> map : list) {
            if (map.get("key_column") != null) {
                columns.add(map.get("key_column").toString());
            } else if (map.get("KEY_COLUMN") != null) {
                columns.add(map.get("KEY_COLUMN").toString());
            }
        }

        return columns;
    }

/*    @Override
    public String getSchemaOid(String schemaName) {
        return String.format("select oid from pg_catalog.pg_namespace where nspname = '%s'", schemaName);
    }*/

    @Override
    public String getTableRealNameSql(String schemaName, String tableName) {
        return String.format("SELECT b.relname as \"tablename\"\n" +
                "    from pg_catalog.pg_class b \n" +
                "    join pg_catalog.pg_namespace a \n" +
                "    on b.relnamespace = a.oid \n" +
                "    where b.relkind in ('r','t','f','p') and a.nspname = '%s' and upper(b.relname) = '%s'", schemaName, tableName.toUpperCase(Locale.ROOT));
    }

    @Override
    public String getTableRealName(List<Map<String, Object>> list) {

        String realName = "";

        for (Map<String, Object> map : list) {
            if (map.get("tablename") != null) {
                realName = map.get("tablename").toString();
                break;
            } else if (map.get("TABLENAME") != null) {
                realName = map.get("TABLENAME").toString();
                break;
            }
        }

        return realName;
    }

    @Override
    public String getTableColumnSql(String schemaName, String tableName) {
        boolean queryComment = isQueryComment();
        String query = "SELECT\n" +
                "'" + tableName + "' AS \"table_name\",\n" +
                "  attname as \"column_name\",\n" +
                "  attnum as \"column_no\",\n" +
                "'" + schemaName + "' AS \"schema_name\",\n" +
                "  CURRENT_database() as \"catalog_name\",\n" +
                "  format_type(a.atttypid, a.atttypmod) as \"data_type\",\n" +
                "  format_type(a.atttypid, a.atttypmod) as \"displaycolumns\",\n" +
                "  '' as \"column_type\",\n" +
                "  '' as \"data_length\",\n" +
                "  '' as \"precision\",\n" +
                "  '' as \"numeric_scale\",\n";
        if (queryComment) {
            query += "  description as \"comments\",\n";
        }
        query += "  0 as \"is_increment\",\n" +
                "  (\n" +
                "    case\n" +
                "      when (\n" +
                "        select\n" +
                "          contype\n" +
                "        from\n" +
                "          pg_catalog.pg_constraint c\n" +
                "        where\n" +
                "          a.attrelid = c.conrelid\n" +
                "          and a.attnum = any (c.conkey)\n" +
                "        limit\n" +
                "          1\n" +
                "      ) = 'p' then 1\n" +
                "      else 0\n" +
                "    end\n" +
                "  ) as \"is_primary_key\"\n" +
                "from\n" +
                "  pg_catalog.pg_attribute a\n";
        if (queryComment) {
            query += "  left join pg_catalog.pg_description b on a.attrelid = b.objoid\n" +
                    "  and a.attnum = b.objsubid\n";
        }
        query += "where\n" +
                "  a.attisdropped = false\n" +
                "  and attrelid = (\n" +
                "      select e.oid from pg_catalog.pg_class e\n" +
                "        join pg_catalog.pg_namespace d on d.oid = e.relnamespace\n" +
                "        where d.nspname = '" + schemaName + "' and relname = '" + tableName + "' and e.relkind in ('r', 't', 'f', 'p')\n" +
                "  )";
        return query;
    }

    /**
     * 获取标识符引号字符
     */
    protected String getIdentifierQuote() {
        return "\"";
    }

    // 提取：对单个标识符加引号
    protected String quoteIdentifier(String name) {
        return getIdentifierQuote() + name + getIdentifierQuote();
    }

    protected String quotePossiblyDotted(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        String[] parts = name.split("\\.", 2);
        if (parts.length == 1) {
            return quoteIdentifier(parts[0]);
        }
        return quoteIdentifier(parts[0]) + "." + quoteIdentifier(parts[1]);
    }

    protected String buildQualifiedTableName(String schemaName, String tableName) {
        String formatSchema = quotePossiblyDotted(schemaName);
        String formatTable = quotePossiblyDotted(tableName);
        if (StringUtils.isBlank(formatSchema)) {
            return formatTable;
        }
        return formatSchema + "." + formatTable;
    }

    protected String joinQuotedColumns(List<String> columnNames) {
        return columnNames.stream().map(this::quoteIdentifier).collect(Collectors.joining(","));
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content) {
        if (list == null) {
            return "";
        }
        // 列名
        List<String> fields = list.stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
        String columns = joinQuotedColumns(fields);
        // 值
        List<String> data = new ArrayList<>();
        for (SqlFieldData item : list) {
            if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("CHAR", "NCHAR", "VARCHAR", "VARCHAR2", "NVARCHAR", "NVARCHAR2", "TSQUERY", "TSVECTOR", "CHAR", "CHARACTER", "CHARACTER VARYING").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    String varcharData = item.getFieldValue().toString().contains("'") ? item.getFieldValue().toString().replace("'", "''") : item.getFieldValue().toString();
                    data.add(String.format("'%s'", varcharData));
                } else {
                    data.add(String.format("'%s'", item.getFieldValue()));
                }
            } else {
                data.add(String.format("%s", "NULL"));
            }
        }
        String values = StringUtils.join(data, ",");
        // 表名
        String formattedTableName = buildQualifiedTableName(schemaName, tableName);
        return String.format("INSERT INTO %s (%s) VALUES (%s)", formattedTableName, columns, values);
    }

    @Override
    public String generateInsertSqlBatch(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        // 列名
        List<String> firstRowFields = list.get(0).stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
        String fields = joinQuotedColumns(firstRowFields);
        // 值
        String values = list.stream().map(sqlFieldDataList -> {
            List<String> data = new ArrayList<>();
            for (SqlFieldData item : sqlFieldDataList) {
                if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                    if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("CHAR", "NCHAR", "VARCHAR", "VARCHAR2", "NVARCHAR", "NVARCHAR2", "TSQUERY", "TSVECTOR", "CHAR", "CHARACTER", "CHARACTER VARYING", "INTERVAL").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                        String varcharData = item.getFieldValue().toString().contains("'") ? item.getFieldValue().toString().replace("'", "''") : item.getFieldValue().toString();
                        data.add(String.format("'%s'", varcharData));
                    } else {
                        data.add(String.format("'%s'", item.getFieldValue()));
                    }
                } else {
                    data.add(String.format("%s", "NULL"));
                }
            }
            return String.format("(%s)", StringUtils.join(data, ","));
        }).collect(Collectors.joining(","));
        // 表名
        String formattedTableName = buildQualifiedTableName(schemaName, tableName);
        return String.format("INSERT INTO %s (%s) VALUES %s", formattedTableName, fields, values);
    }

    @Override
    public String generateTruncateSql(String schemaName, String tableName) {
        String formattedTableName = buildQualifiedTableName(schemaName, tableName);
        return String.format("TRUNCATE TABLE %s", formattedTableName);
    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {
        ArrayList<Map<String, Object>> returnList = new ArrayList<>();

        List<Map<String, Object>> list = DBExecUtils.executeQuery(monitor, context, "get catalog list",
                "select db.datname as owner, pg_encoding_to_char(ENCODING) as charset \nfrom pg_database db \nwhere datistemplate = FALSE;");

        for (Map<String, Object> map : list) {
            Map<String, Object> returnMap = new LinkedHashMap<>();

            if (map.get("owner") != null) {
                returnMap.put("username", map.get("owner"));
            } else if (map.get("OWNER") != null) {
                returnMap.put("username", map.get("OWNER"));
            } else {
                returnMap.put("username", "");
            }

            if (map.get("charset") != null) {
                returnMap.put("charset", map.get("charset"));
            } else if (map.get("CHARSET") != null) {
                returnMap.put("charset", map.get("CHARSET"));
            } else {
                returnMap.put("charset", "");
            }

            returnMap.put("is_sys", 0);
            returnMap.put("def_dbo_name", "public");

            String dbName = returnMap.get("username").toString();

            List<Map<String, Object>> listSchema = DBExecUtils.executeQuery(monitor, dbNameContextFunc.apply(dbName), "get schema and count",
                    "select nspname as \"username\", count(1) as \"count\"\n" +
                            "from pg_catalog.pg_namespace a\n" +
                            "left join pg_catalog.pg_tables b \n" +
                            "on a.nspname=b.schemaname \n" +
                            "and lower(b.schemaname) not in ('pg_toast','pg_temp_1','pg_toast_temp_1','pg_catalog','information_schema') \n" +
                            "and lower(b.schemaname) not like 'pg_%temp_%'\n" +
                            "where lower(nspname) not in ('pg_toast','pg_temp_1','pg_toast_temp_1','pg_catalog','information_schema') \n" +
                            "and lower(nspname) not like 'pg_%temp_%'\n" +
                            "group by nspname");

            List<Map<String, Object>> innerSchemaInfos = new ArrayList<>();
            long count = 0L;
            for (Map<String, Object> schemaSingle : listSchema) {
                Map<String, Object> schemaInfo = new LinkedHashMap<>();
                schemaInfo.put("username", schemaSingle.get("username"));
                schemaInfo.put("charset", returnMap.get("charset"));
                schemaInfo.put("is_sys", returnMap.get("is_sys"));
                schemaInfo.put("count", Long.parseLong(schemaSingle.get("count").toString()));
                count += Long.parseLong(schemaSingle.get("count").toString());
                schemaInfo.put("def_dbo_name", schemaSingle.get("username"));
                schemaInfo.put("catalog_name", dbName);
                innerSchemaInfos.add(schemaInfo);
            }
            returnMap.put("innerSchemaInfos", innerSchemaInfos);
            returnMap.put("count", count);

            returnList.add(returnMap);
        }

        return returnList;
    }

    @Override
    public String getFunctionOrProcedureOid(String objectName, String schemaName, List<String> args) {
        return "SELECT \n" +
                "    p.oid,\n" +
                "    p.pronargs,\n" +
                "    p.proargtypes,\n" +
                "    t.oid as toid,\n" +
                "    t.typname\n" +
                "FROM pg_catalog.pg_proc p\n" +
                "LEFT OUTER JOIN pg_catalog.pg_namespace d ON d.oid=p.pronamespace\n" +
                "LEFT OUTER JOIN pg_catalog.pg_type t ON t.oid = any(p.proargtypes)\n" +
                "WHERE lower(p.proname)=lower('" + objectName + "') and lower(d.nspname)=lower('" + schemaName + "') and pronargs=" + args.size();
    }

    @Override
    public boolean isIntervalNeedQuotes() {
        return false;
    }

    @Override
    public boolean isSystemSchema(String name) {
        return PostgreConstants.CATALOG_SCHEMA_NAME.equals(name) ||
                PostgreConstants.INFO_SCHEMA_NAME.equalsIgnoreCase(name) ||
                name.startsWith(PostgreConstants.SYSTEM_SCHEMA_PREFIX);
    }

    @Override
    public boolean supportsCatalog() {
        return true;
    }
}
