package org.apache.calcite.sql.dialect;

import org.apache.calcite.sql.SqlDialect;

public class GaussDbSqlDialect extends SqlDialect {

    public GaussDbSqlDialect(Context context) {
        super(context);
    }
    public static final SqlDialect DEFAULT =
            new GaussDbSqlDialect(EMPTY_CONTEXT
                    .withDatabaseProduct(DatabaseProduct.POSTGRESQL)
                    .withIdentifierQuoteString("`")); // 改成反引号

    @Override
    public StringBuilder quoteIdentifier(StringBuilder buf, String name) {
        buf.append("`")
                .append(name.replace("`", "``"))
                .append("`");
        return buf;
    }
}
