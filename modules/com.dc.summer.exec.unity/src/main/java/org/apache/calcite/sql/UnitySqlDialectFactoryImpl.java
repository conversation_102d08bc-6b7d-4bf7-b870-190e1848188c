package org.apache.calcite.sql;

import org.apache.calcite.sql.dialect.GaussDbSqlDialect;

import java.sql.*;

public class UnitySqlDialectFactoryImpl extends SqlDialectFactoryImpl{

    public static final UnitySqlDialectFactoryImpl INSTANCE = new UnitySqlDialectFactoryImpl();

    @Override
    public SqlDialect create(DatabaseMetaData databaseMetaData) {
        try {
            String productName = databaseMetaData.getDatabaseProductName();
            if (productName != null && productName.toLowerCase().contains("gaussdb")) {
                if (checkMysqlModel(databaseMetaData.getConnection())) {
                    return GaussDbSqlDialect.DEFAULT;
                }
            }
        } catch (SQLException e) {
        }
        return super.create(databaseMetaData);
    }

    private boolean checkMysqlModel(Connection conn) {
        try (Statement st = conn.createStatement()) {
            try (ResultSet rs = st.executeQuery("SHOW sql_compatibility")) {
                if (rs.next()) {
                    String v = rs.getString(1);
                    return "M".equalsIgnoreCase(v);
                }
            } catch (SQLException ignore) {
            }
        } catch (SQLException e) {
        }
        return false;
    }
}
