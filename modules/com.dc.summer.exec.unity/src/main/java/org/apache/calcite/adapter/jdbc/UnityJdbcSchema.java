package org.apache.calcite.adapter.jdbc;

import org.apache.calcite.schema.SchemaPlus;
import org.apache.calcite.sql.SqlDialect;
import org.apache.calcite.sql.UnitySqlDialectFactoryImpl;
import org.checkerframework.checker.nullness.qual.Nullable;

import javax.sql.DataSource;

public class UnityJdbcSchema extends JdbcSchema{
    /**
     * Creates a JDBC schema.
     *
     * @param dataSource Data source
     * @param dialect    SQL dialect
     * @param convention Calling convention
     * @param catalog    Catalog name, or null
     * @param schema     Schema name pattern
     */
    public UnityJdbcSchema(DataSource dataSource, SqlDialect dialect, JdbcConvention convention, @Nullable String catalog, @Nullable String schema) {
        super(dataSource, dialect, convention, catalog, schema);
    }

    public static JdbcSchema create(
            SchemaPlus parentSchema,
            String name,
            DataSource dataSource,
            @Nullable String catalog,
            @Nullable String schema) {
        return create(parentSchema, name, dataSource,
                UnitySqlDialectFactoryImpl.INSTANCE, catalog, schema);
    }

}
