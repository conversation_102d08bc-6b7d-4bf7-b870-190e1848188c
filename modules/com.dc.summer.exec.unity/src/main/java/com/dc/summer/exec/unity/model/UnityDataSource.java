package com.dc.summer.exec.unity.model;

import com.dc.summer.DBException;
import com.dc.summer.exec.unity.model.data.NestedDataSource;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCExecutionContextDefaults;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.impl.jdbc.JDBCConnectionConfigurer;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.impl.jdbc.JDBCRemoteInstance;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSDataType;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.rdb.DBSCatalog;
import com.dc.summer.model.struct.rdb.DBSSchema;
import com.dc.utils.ConstantUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.adapter.jdbc.JdbcSchema;
import org.apache.calcite.adapter.jdbc.UnityJdbcSchema;
import org.apache.calcite.config.CalciteConnectionConfig;
import org.apache.calcite.jdbc.CalciteConnection;
import org.apache.calcite.schema.SchemaPlus;
import org.apache.calcite.schema.Table;
import org.apache.calcite.schema.impl.AbstractSchema;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
public class UnityDataSource extends JDBCDataSource implements JDBCConnectionConfigurer {

    public UnityDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        super(monitor, container, new UnitySQLDialect());
    }

    @Override
    public Collection<? extends DBSDataType> getLocalDataTypes() {
        return List.of();
    }

    @Override
    public DBSDataType getLocalDataType(String typeName) {
        return null;
    }

    @Override
    public Collection<? extends DBSObject> getChildren(DBRProgressMonitor monitor) throws DBException {
        return List.of();
    }

    @Override
    public DBSObject getChild(DBRProgressMonitor monitor, String childName) throws DBException {
        return null;
    }

    @Override
    public Class<? extends DBSObject> getPrimaryChildType(DBRProgressMonitor monitor) throws DBException {
        return UnityDataSource.class;
    }

    @Override
    public void cacheStructure(DBRProgressMonitor monitor, int scope) throws DBException {

    }

    protected JDBCExecutionContext createExecutionContext(JDBCRemoteInstance instance, String type) {
        return new UnityExecutionContext(instance, type);
    }

    @Override
    public void beforeConnection(DBRProgressMonitor monitor, DBPConnectionConfiguration connectionInfo, Properties connectProps) throws DBCException {

    }

    /**
     * @see SqlParser.Config
     */
    @Override
    protected Map<String, String> getInternalConnectionProperties(DBRProgressMonitor monitor, DBPDriver driver, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration connectionInfo) throws DBCException {
        Map<String, String> connectionsProps = new HashMap<>();
        connectionsProps.put("conformance", UnityUtils.SQL_CONFORMANCE_ENUM.name());
        return connectionsProps;
    }

    @Override
    public void afterConnection(DBRProgressMonitor monitor, DBPConnectionConfiguration connectionInfo, Properties connectProps, Connection connection, Throwable error) throws DBException {

        try {
            CalciteConnection calciteConnection = connection.unwrap(CalciteConnection.class);

            SchemaPlus rootSchema = calciteConnection.getRootSchema();
            SchemaPlus virtualRoot = rootSchema.add(ConstantUtils.DC_VIRTUAL_ROOT, new AbstractSchema());

            UnityUtils.transformNestedConfig(connectionInfo, nestedConnectionConfiguration -> {

                final String concatName = nestedConnectionConfiguration.getConcatName();

                DBCExecutionContextDefaults<?, ?> contextDefaults = nestedConnectionConfiguration.getExecutionContext().getContextDefaults();
                DBSCatalog defaultCatalog = contextDefaults.getDefaultCatalog();
                DBSSchema defaultSchema = contextDefaults.getDefaultSchema();

                AtomicReference<SchemaPlus> schemaReference = new AtomicReference<>();

                // 添加虚拟节点
                DBExecUtils.tryExecuteRecover(nestedConnectionConfiguration.getExecutionContext(),
                        nestedConnectionConfiguration.getExecutionContext().getDataSource(),
                        param -> {
                            try {
                                SchemaPlus subSchema = virtualRoot.getSubSchema(concatName);
                                if (subSchema == null) {
                                    JdbcSchema jdbcSchema = UnityJdbcSchema.create(
                                            virtualRoot,
                                            concatName,
                                            new NestedDataSource(nestedConnectionConfiguration),
                                            defaultCatalog != null ? defaultCatalog.getName() : null,
                                            defaultSchema != null ? defaultSchema.getName() : null);
                                    subSchema = virtualRoot.add(concatName, jdbcSchema);
                                }
                                schemaReference.set(subSchema);
                            } catch (Exception e) {
                                throw new InvocationTargetException(e);
                            }
                        });

                // 添加真实节点
                SchemaPlus actualSchema = rootSchema;

                actualSchema = getSchemaPlus(nestedConnectionConfiguration.getDbLinkName(), actualSchema);
                actualSchema = getSchemaPlus(nestedConnectionConfiguration.getCatalogName(), actualSchema);
                actualSchema = getSchemaPlus(nestedConnectionConfiguration.getSchemaName(), actualSchema);

                final String tableName = nestedConnectionConfiguration.getTableName();
                if (StringUtils.isNotBlank(tableName) && actualSchema.getTable(tableName) == null) {
                    AtomicReference<Table> tableReference = new AtomicReference<>();
                    DBExecUtils.tryExecuteRecover(nestedConnectionConfiguration.getExecutionContext(),
                            nestedConnectionConfiguration.getExecutionContext().getDataSource(),
                            param -> {
                                try {
                                    tableReference.set(schemaReference.get().getTable(tableName));
                                } catch (Exception e) {
                                    throw new InvocationTargetException(e);
                                }
                            });
                    if (tableReference.get() != null) {
                        actualSchema.add(tableName, tableReference.get());
                    }
                }

            });

        } catch (Exception e) {
            throw new DBException(e, getDataSource());
        }
    }

    private static SchemaPlus getSchemaPlus(String schemaName, SchemaPlus actualSchema) {
        if (StringUtils.isNotBlank(schemaName)) {
            SchemaPlus subSchema = actualSchema.getSubSchema(schemaName);
            if (subSchema == null) {
                actualSchema = actualSchema.add(schemaName, new AbstractSchema());
            } else {
                actualSchema = subSchema;
            }
        }
        return actualSchema;
    }

    @Override
    public void killConnection(DBRProgressMonitor monitor, JDBCExecutionContext executionContext) {
        UnityUtils.transformNestedConfig(executionContext.getConfiguration(), nestedConnectionConfiguration -> {
            DBCExecutionContext isolatedContext = nestedConnectionConfiguration.getExecutionContext();
            DBPDataSource dataSource = isolatedContext.getDataSource();
            if (dataSource instanceof JDBCDataSource) {
                try {
                    ((JDBCDataSource) dataSource).killConnection(monitor, (JDBCExecutionContext) isolatedContext);
                } catch (Exception e) {
                    log.warn("CalciteDataSource KillConnection Error: {}", e.getMessage());
                }
            }
        });
    }
}
