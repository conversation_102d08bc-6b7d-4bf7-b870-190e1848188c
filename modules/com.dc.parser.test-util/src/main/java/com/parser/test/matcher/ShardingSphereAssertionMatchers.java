package com.parser.test.matcher;

import com.cedarsoftware.util.DeepEquals;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.hamcrest.BaseMatcher;
import org.hamcrest.Description;
import org.hamcrest.Matcher;

/**
 * ShardingSphere assertion matchers.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ShardingSphereAssertionMatchers {

    /**
     * Deep equal.
     *
     * @param operand operand
     * @param <T>     type of operand
     * @return matcher
     */
    public static <T> Matcher<T> deepEqual(final T operand) {
        return new DeepEqualMatcher<>(operand);
    }

    @RequiredArgsConstructor
    private static class DeepEqualMatcher<T> extends BaseMatcher<T> {

        private final T expectedValue;

        @Override
        public boolean matches(final Object arg) {
            return DeepEquals.deepEquals(arg, expectedValue);
        }

        @Override
        public void describeTo(final Description description) {
            description.appendValue(expectedValue);
        }
    }
}
