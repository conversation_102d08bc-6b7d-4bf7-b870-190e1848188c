package com.parser.test.loader;

import java.util.Collection;
import java.util.List;

/**
 * Test parameter load template.
 */
public interface TestParameterLoadTemplate {

    /**
     * Load test parameters.
     *
     * @param sqlCaseFileName    SQL case file name
     * @param sqlCaseFileContent SQL case file content
     * @param resultFileContent  result file content
     * @param databaseType       database type
     * @param reportType         report type
     * @return loaded test parameters
     */
    Collection<ExternalSQLTestParameter> load(String sqlCaseFileName, List<String> sqlCaseFileContent, List<String> resultFileContent, String databaseType, String reportType);
}
