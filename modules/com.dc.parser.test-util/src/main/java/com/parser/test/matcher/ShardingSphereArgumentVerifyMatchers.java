package com.parser.test.matcher;

import com.cedarsoftware.util.DeepEquals;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.mockito.ArgumentMatcher;
import org.mockito.internal.progress.ThreadSafeMockingProgress;

/**
 * ShardingSphere argument verify matchers.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ShardingSphereArgumentVerifyMatchers {

    /**
     * Deep equals.
     *
     * @param obj to be verified object
     * @param <T> type of to be verified object
     * @return null
     */
    public static <T> T deepEq(final T obj) {
        reportMatcher(new DeepEqualsMatcher(obj));
        return obj;
    }

    private static void reportMatcher(final ArgumentMatcher<?> matcher) {
        ThreadSafeMockingProgress.mockingProgress().getArgumentMatcherStorage().reportMatcher(matcher);
    }

    @RequiredArgsConstructor
    private static class DeepEqualsMatcher implements ArgumentMatcher<Object> {

        private final Object wanted;

        @Override
        public boolean matches(final Object actual) {
            return DeepEquals.deepEquals(wanted, actual);
        }

        @Override
        public String toString() {
            return "deepEq(" + wanted + ")";
        }
    }
}
