package com.parser.test.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.stream.Collectors;

/**
 * Configuration file utility class.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ConfigurationFileUtils {

    /**
     * Read file content.
     *
     * @param fileName file name
     * @return file content
     */
    @SneakyThrows({IOException.class, URISyntaxException.class})
    public static String readFile(final String fileName) {
        return String.join(System.lineSeparator(), Files.readAllLines(Paths.get(ClassLoader.getSystemResource(fileName).toURI())));
    }

    /**
     * Read file and ignore comments.
     *
     * @param fileName file name
     * @return file content without comments
     */
    @SneakyThrows({IOException.class, URISyntaxException.class})
    public static String readFileAndIgnoreComments(final String fileName) {
        return Files.readAllLines(Paths.get(ClassLoader.getSystemResource(fileName).toURI()))
                .stream().filter(each -> !"".equals(each) && !each.startsWith("#")).map(each -> each + System.lineSeparator()).collect(Collectors.joining());
    }
}
