package com.parser.test.loader;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Collection;

/**
 * SQL line comment.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SQLLineComment {

    private static final Collection<String> COMMENT_PREFIXES = Arrays.asList("#", "/", "--", ":", "\\");

    /**
     * Judge whether SQL line is comment.
     *
     * @param line SQL line
     * @return SQL line is comment or not
     */
    public static boolean isComment(final String line) {
        return COMMENT_PREFIXES.stream().anyMatch(line::startsWith);
    }

}
