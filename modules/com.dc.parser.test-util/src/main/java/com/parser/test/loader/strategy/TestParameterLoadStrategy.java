package com.parser.test.loader.strategy;

import com.parser.test.loader.summary.FileSummary;

import java.net.URI;
import java.util.Collection;

/**
 * Test parameter load strategy.
 */
public interface TestParameterLoadStrategy {

    /**
     * Load SQL case file summaries.
     *
     * @param uri URL to be loaded
     * @return loaded SQL file summaries
     */
    Collection<FileSummary> loadSQLCaseFileSummaries(URI uri);
}
