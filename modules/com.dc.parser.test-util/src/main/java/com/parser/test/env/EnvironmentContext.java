package com.parser.test.env;

import lombok.SneakyThrows;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * environment context.
 */
public final class EnvironmentContext {

    private static final EnvironmentContext INSTANCE = new EnvironmentContext();

    private final Properties props;

    private EnvironmentContext() {
        props = loadProperties();
    }

    /**
     * Get GitHub environment instance.
     *
     * @return got instance
     */
    public static EnvironmentContext getInstance() {
        return INSTANCE;
    }

    /**
     * Get value by key.
     *
     * @param key key
     * @return value
     */
    public String getValue(final String key) {
        return props.getProperty(key);
    }

    @SneakyThrows(IOException.class)
    private Properties loadProperties() {
        Properties result = new Properties();
        try (InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("env/env.properties")) {
            result.load(inputStream);
        }
        for (String each : System.getProperties().stringPropertyNames()) {
            result.setProperty(each, System.getProperty(each));
        }
        return result;
    }
}
