package com.parser.test.loader.strategy.impl;

import com.google.common.base.Strings;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.parser.test.env.EnvironmentContext;
import com.parser.test.loader.strategy.TestParameterLoadStrategy;
import com.parser.test.loader.summary.FileSummary;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.URLConnection;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Test parameter load strategy with GitHub.
 */
@Slf4j
public final class GitHubTestParameterLoadStrategy implements TestParameterLoadStrategy {

    private static final String TOKEN_KEY = "it.github.token";

    @Override
    public Collection<FileSummary> loadSQLCaseFileSummaries(final URI uri) {
        if (uri.toString().isEmpty()) {
            return Collections.emptyList();
        }
        String content = loadContent(getGitHubApiUri(uri));
        if (content.isEmpty()) {
            return Collections.emptyList();
        }
        DocumentContext documentContext = JsonPath.parse(content);
        if (documentContext.read("$") instanceof Collection) {
            return getFileSummariesByArray(documentContext);
        } else {
            return getFileSummaries(documentContext);
        }
    }

    private Collection<FileSummary> getFileSummaries(final DocumentContext documentContext) {
        Collection<FileSummary> result = new LinkedList<>();
        String fileName = documentContext.read("$.name");
        String folderType = documentContext.read("$.type");
        String downloadURL = documentContext.read("$.download_url");
        String htmlURL = documentContext.read("$.html_url");
        if ("file".equals(folderType)) {
            result.add(new FileSummary(fileName, downloadURL));
        } else if ("dir".equals(folderType)) {
            result.addAll(loadSQLCaseFileSummaries(URI.create(htmlURL)));
        }
        return result;
    }

    private Collection<FileSummary> getFileSummariesByArray(final DocumentContext documentContext) {
        Collection<FileSummary> result = new LinkedList<>();
        List<String> fileNames = documentContext.read("$..name");
        List<String> folderTypes = documentContext.read("$..type");
        List<String> downloadURLs = documentContext.read("$..download_url");
        List<String> htmlURLs = documentContext.read("$..html_url");
        int length = documentContext.read("$.length()");
        for (int i = 0; i < length; i++) {
            String fileName = fileNames.get(i);
            String folderType = folderTypes.get(i);
            String downloadURL = downloadURLs.get(i);
            String htmlURL = htmlURLs.get(i);
            if ("file".equals(folderType)) {
                result.add(new FileSummary(fileName, downloadURL));
            } else if ("dir".equals(folderType)) {
                result.addAll(loadSQLCaseFileSummaries(URI.create(htmlURL)));
            }
        }
        return result;
    }

    private URI getGitHubApiUri(final URI sqlCaseURI) {
        String[] patches = sqlCaseURI.toString().split("/", 8);
        String casesOwner = patches[3];
        String casesRepo = patches[4];
        String casesDirectory = patches[7];
        return URI.create(String.join("/", "https://api.github.com/repos", casesOwner, casesRepo, "contents", casesDirectory));
    }

    private String loadContent(final URI casesURI) {
        try {
            URLConnection urlConnection = casesURI.toURL().openConnection();
            String githubToken = EnvironmentContext.getInstance().getValue(TOKEN_KEY);
            if (!Strings.isNullOrEmpty(githubToken)) {
                urlConnection.setRequestProperty("Authorization", "Bearer " + githubToken);
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(urlConnection.getInputStream()))) {
                return reader.lines().collect(Collectors.joining(System.lineSeparator()));
            }
        } catch (final IOException ex) {
            log.warn("Load failed, reason is: ", ex);
            return "";
        }
    }
}
