package com.parser.test.loader;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.io.Files;
import com.parser.test.env.EnvironmentContext;
import com.parser.test.loader.strategy.TestParameterLoadStrategy;
import com.parser.test.loader.summary.FileSummary;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.URLConnection;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Test parameter loader.
 */
@RequiredArgsConstructor
@Slf4j
public final class TestParameterLoader {

    private static final String TOKEN_KEY = "it.github.token";

    private final TestParameterLoadStrategy loadStrategy;

    private final TestParameterLoadTemplate loadTemplate;

    /**
     * Load test parameters.
     *
     * @param sqlCaseURI   SQL case URI
     * @param resultURI    result URI
     * @param databaseType database type
     * @param reportType   report type
     * @return loaded test parameters
     */
    @SneakyThrows
    public Stream<ExternalSQLTestParameter> load(final URI sqlCaseURI, final URI resultURI, final String databaseType, final String reportType) {
        return load(sqlCaseURI, resultURI, databaseType, reportType, null);
    }

    /**
     * Load test parameters.
     *
     * @param sqlCaseURI   SQL case URI
     * @param resultURI    result URI
     * @param databaseType database type
     * @param reportType   report type
     * @param caseRegex    case regex
     * @return loaded test parameters
     */
    @SneakyThrows
    public Stream<ExternalSQLTestParameter> load(final URI sqlCaseURI, final URI resultURI, final String databaseType, final String reportType, final String caseRegex) {
        Collection<FileSummary> sqlCaseFileSummaries = loadStrategy.loadSQLCaseFileSummaries(sqlCaseURI);
        Collection<FileSummary> resultFileSummaries = loadStrategy.loadSQLCaseFileSummaries(resultURI);
        Map<String, FileSummary> resultFileSummaryMap =
                resultFileSummaries.stream().collect(Collectors.toMap(fileSummary -> Files.getNameWithoutExtension(fileSummary.getFileName()), Function.identity()));
        return sqlCaseFileSummaries.stream().filter(each -> StringUtils.isEmpty(caseRegex) || each.getFileName().matches(caseRegex)).flatMap(each -> {
            List<String> sqlCaseFileContent = loadContent(URI.create(each.getAccessURI()));
            String fileName = Files.getNameWithoutExtension(each.getFileName());
            Optional<FileSummary> resultFileSummary = Optional.ofNullable(resultFileSummaryMap.get(fileName));
            List<String> resultFileContent = resultFileSummary.map(summary -> loadContent(URI.create(summary.getAccessURI()))).orElse(Collections.emptyList());
            return loadTemplate.load(fileName, sqlCaseFileContent, resultFileContent, databaseType, reportType).stream();
        });
    }

    private List<String> loadContent(final URI uri) {
        try {
            URLConnection urlConnection = uri.toURL().openConnection();
            String githubToken = EnvironmentContext.getInstance().getValue(TOKEN_KEY);
            if (!Strings.isNullOrEmpty(githubToken)) {
                urlConnection.setRequestProperty("Authorization", "Bearer " + githubToken);
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(urlConnection.getInputStream()))) {
                return reader.lines().collect(Collectors.toList());
            }
        } catch (final IOException ex) {
            log.warn("Load failed, reason is: ", ex);
            return Lists.newArrayList();
        }
    }
}
