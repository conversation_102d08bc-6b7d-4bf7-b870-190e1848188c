package com.parser.test.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;

import java.util.Properties;

/**
 * Properties builder.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class PropertiesBuilder {

    /**
     * Build properties.
     *
     * @param props to be built properties
     * @return built properties
     */
    public static Properties build(final Property... props) {
        Properties result = new Properties();
        for (Property each : props) {
            if (each.value instanceof String) {
                result.setProperty(each.key, each.value.toString());
            } else {
                result.put(each.key, each.value);
            }

        }
        return result;
    }

    /**
     * Property.
     */
    @RequiredArgsConstructor
    public static class Property {

        private final String key;

        private final Object value;
    }
}
