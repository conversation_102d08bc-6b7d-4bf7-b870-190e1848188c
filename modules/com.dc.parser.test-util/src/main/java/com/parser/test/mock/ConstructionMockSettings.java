package com.parser.test.mock;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * Construction mock settings.
 */
@ExtendWith(MockitoExtension.class)
@Inherited
@Retention(RetentionPolicy.RUNTIME)
public @interface ConstructionMockSettings {

    /**
     * Mock classes.
     *
     * @return mock classes
     */
    Class<?>[] value();
}
