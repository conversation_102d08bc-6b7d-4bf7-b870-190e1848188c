package com.dc.summer.exec.handler;

import com.dc.summer.DBException;
import com.dc.summer.ModelPreferences;
import com.dc.summer.exec.config.DataSourceConfig;
import com.dc.summer.exec.config.HandlerConfig;
import com.dc.summer.exec.config.SessionConfig;
import com.dc.summer.exec.model.ConnectionManager;
import com.dc.summer.exec.model.DataSourceManager;
import com.dc.summer.exec.model.counter.HandlerCounter;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.exec.model.exception.MaximumContextSizeException;
import com.dc.summer.exec.model.observer.ContextSubject;
import com.dc.summer.exec.model.proxy.impl.SessionProxy;
import com.dc.summer.exec.model.proxy.impl.TimeoutSessionProxy;
import com.dc.summer.exec.model.type.RecordSignType;
import com.dc.summer.exec.model.type.RecordType;
import com.dc.summer.exec.monitor.AbstractStatMonitor;
import com.dc.summer.exec.monitor.data.StatParam;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.exec.DBCTransactionManager;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.summer.registry.DataSourceDescriptor;
import com.dc.summer.registry.DataSourceProviderDescriptor;
import com.dc.summer.registry.DataSourceProviderRegistry;
import com.dc.summer.registry.driver.DriverDescriptor;
import com.dc.type.AuthSourceType;
import com.dc.type.DatabaseType;
import com.dc.utils.verification.VerificationUtils;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DurationFormatUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;
@Slf4j
public class DataSourceConnectionHandler extends DataSourceDescriptor implements DataSourceManager, ConnectionManager {

    private static final Map<String, Entry> ENTRY_MAP = new ConcurrentHashMap<>();

    /**
     * 误操作恢复存储事务id
     */
    private static final Map<String, Integer> transactionIndexes = new ConcurrentHashMap<>();

    private static final LoggingProgressMonitor MONITOR = new LoggingProgressMonitor();

    private static DataSourceConnectionHandler fake;

    private final Map<String, Element> elementMap = new ConcurrentHashMap<>();

    /**
     * 时间槽 映射 TOKEN
     */
    private final Map<Long, Set<String>> slotTokensMap = new ConcurrentHashMap<>();

    private final AtomicLong count = new AtomicLong(0);

    private static final boolean INITIALIZE = true;

    @Getter
    private DatabaseType databaseType;

    private final boolean activateRecord;

    private final boolean forceClose;

    @Getter
    private final String connectionId;

    @Setter
    @Getter
    private String connectionDesc;

    @Setter
    @Getter
    private String instanceName;

    @Setter
    @Getter
    private Integer environment;

    @Setter
    @Getter
    private long refreshTime;

    public static synchronized DataSourceConnectionHandler getFake() {
        if (fake == null) {
            fake = new DataSourceConnectionHandler("fake", DriverDescriptor.NULL_DRIVER, null, true, true, null);
        }
        return fake;
    }

    @Data
    private static class Entry {
        private DataSourceConnectionHandler handler;
        private final ReadWriteLock lock = new ReentrantReadWriteLock();
        private volatile Long time = System.currentTimeMillis();

        public void refreshTime() {
            time = System.currentTimeMillis();
        }
    }

    @Data
    private static class Element {

        private String token;
        private DBCExecutionContext context;
        private DBCExecutionPurpose purpose;
        private final long create = System.currentTimeMillis();
        private volatile long time;

        public Element(String token, DBCExecutionContext context, DBCExecutionPurpose purpose) {
            this.token = token;
            this.context = context;
            this.purpose = purpose;
        }

        public void refreshTime() {
            // 刷新时间
            time = System.currentTimeMillis();
            String survived = DurationFormatUtils.formatDuration(time - create, "d '天' H '小时' m '分钟' s '秒'");
            log.debug("指定 token ({}) 刷新正常，purpose ({}) 已存活 {}。", token, purpose, survived);
        }

    }

    public static int getTransactionIndex(String token) {
        return transactionIndexes.computeIfAbsent(token, k -> 0);
    }

    public static void plusTransactionIndex(String token) {
        transactionIndexes.put(token, getTransactionIndex(token) + 1);
    }

    private static void logThreadLocal(String containerId, boolean activateRecord) {
        HandlerCounter.setContainerId(containerId);
        RecordHandler handle = RecordHandler.handle(RecordType.DATASOURCE, activateRecord);
        StatParam param = new StatParam();
        param.setContainerId(containerId);
        handle.appendRow(param, RecordSignType.CONTAINER_HANDLE);
    }

    public static DataSourceConnectionHandler handle(ConnectionConfiguration connectionConfiguration) {
        if (HandlerConfig.isForceUseSessionConfig()) {
            SessionConfig sessionConfig = SessionConfig.getInstance();
            connectionConfiguration.setMaximumContextSize(sessionConfig.getMaximumContextSize());
            connectionConfiguration.setKeepAliveTime(sessionConfig.getKeepAliveTime());
            connectionConfiguration.setQueryTimeout(sessionConfig.getQueryTimeout());
            connectionConfiguration.setHeartBeatCount(sessionConfig.getHeartBeatCount());
            connectionConfiguration.setNetworkTimeout(sessionConfig.getNetworkTimeout());
        }
        return handle(connectionConfiguration, HandlerConfig.isActivateRecord(), HandlerConfig.isForceClose());
    }

    @SneakyThrows(DBException.class)
    private static DataSourceConnectionHandler handle(ConnectionConfiguration connectionConfiguration, boolean activateRecord, boolean forceClose) {

        if (connectionConfiguration == null) {
            return null;
        }

        VerificationUtils.byAnnotation(connectionConfiguration);

        long ctm = System.currentTimeMillis();

        StatParam statParam = new StatParam();
        RecordHandler recordhandler = RecordHandler.handle(RecordType.DATASOURCE, activateRecord);

        String id = connectionConfiguration.getUserName() + "@" + connectionConfiguration.getConnectionId() + "/" + connectionConfiguration.getDatabaseName();
        String name = String.format("[%s] %s - %s:%s -> %s", connectionConfiguration.getDatabaseType().name(), connectionConfiguration.getDriverId(), connectionConfiguration.getHostName(), connectionConfiguration.getHostPort(), id);

        DataSourceConnectionHandler handler;

        Entry entry = ENTRY_MAP.computeIfAbsent(id, k -> new Entry());

        Lock readLock = entry.getLock().readLock();
        readLock.lock();
        try {
            handler = entry.getHandler();
        } finally {
            readLock.unlock();
        }

        boolean create = false;
        // 新建数据源
        if (handler == null) {
            Lock writeLock = entry.getLock().writeLock();
            writeLock.lock();
            try {
                if (entry.getHandler() == null) {
                    create = true;

                    ContextSubject.trigger(contextObserver -> contextObserver.fillingConfigurationPassword(connectionConfiguration));

                    DataSourceProviderRegistry providerRegistry = DataSourceProviderRegistry.getInstance();
                    DataSourceProviderDescriptor providerDescriptor = providerRegistry.getDataSourceProvider(connectionConfiguration.getDatabaseType().getDataSourceId());
                    DriverDescriptor driver = providerDescriptor.getDriver(connectionConfiguration.getDriverId());
                    handler = new DataSourceConnectionHandler(id, driver, connectionConfiguration, activateRecord, forceClose, connectionConfiguration.getConnectionId());
                    statParam.setContainerId(id);
                    statParam.setConnectionId(connectionConfiguration.getConnectionId());
                    handler.setName(name);
                    handler.setConnectionDesc(connectionConfiguration.getConnectionDesc());
                    handler.setInstanceName(connectionConfiguration.getInstanceName());
                    handler.setEnvironment(connectionConfiguration.getEnvironment());
                    handler.openDataSource(MONITOR, INITIALIZE);
                    if (INITIALIZE) {
                        recordhandler.appendRow(statParam, RecordSignType.CONTEXT_ADD);
                    }
                    entry.setHandler(handler);
                } else {
                    handler = entry.getHandler();
                }
            } catch (DBException e) {
                ENTRY_MAP.remove(id, entry);
                log.debug("Create Handler Error, remove entry {}", id);
                recordhandler.appendRow(statParam, RecordSignType.CONTEXT_ERROR);
                throw e;
            } finally {
                log.info("Visit Handler (Create) -> {}, spend [{}] ms.", name, System.currentTimeMillis() - ctm);
                writeLock.unlock();
            }
        }

        boolean refresh = false;
        // 更新数据源
        if (!connectionConfiguration.equals(handler.getConnectionConfiguration())) {
            Lock writeLock = entry.getLock().writeLock();
            writeLock.lock();
            try {
                if (!handler.getConnectionConfiguration().equals(connectionConfiguration)) {
                    refresh = true;

                    // 所有来源基础信息变更；本系统密码变更。
                    connectionConfiguration.setRefreshPool(true);

                    handler.setConnectionInfo(connectionConfiguration);
                    // clearContext
                    for (String token : handler.elementMap.keySet()) {
                        DBCExecutionContext context = handler.elementMap.get(token).getContext();
                        ContextSubject.trigger(contextObserver -> contextObserver.printLogCloseLifeCycle(context, token, "实例变更", null));
                        handler.closeExecutionContext(token);
                    }
                    statParam.setContainerId(handler.getId());
                    statParam.setConnectionId(connectionConfiguration.getConnectionId());
                    handler.setConnectionDesc(connectionConfiguration.getConnectionDesc());
                    handler.setInstanceName(connectionConfiguration.getInstanceName());
                    handler.setEnvironment(connectionConfiguration.getEnvironment());
                    handler.setName(name);
                    handler.openDataSource(MONITOR, INITIALIZE);
                    if (INITIALIZE) {
                        recordhandler.appendRow(statParam, RecordSignType.CONTEXT_ADD);
                    }
                    entry.setHandler(handler);
                } else {
                    handler = entry.getHandler();
                }
            } catch (DBException e) {
                ENTRY_MAP.remove(id, entry);
                log.debug("Refresh Handler Error, remove entry {}", id);
                recordhandler.appendRow(statParam, RecordSignType.CONTEXT_ERROR);
                throw e;
            } finally {
                log.info("Visit Handler (Refresh) -> {}, spend [{}] ms.", name, System.currentTimeMillis() - ctm);
                writeLock.unlock();
            }
        }
        // 刷新实例名称
        refreshInstanceName(handler,connectionConfiguration);

        // 获取数据源
        if (!create && !refresh) {
            try {
                // refillConfigurationPassword
                if (connectionConfiguration.getAuthSourceType() != AuthSourceType.SYSTEM) {
                    DataSourceConnectionHandler dataSourceContainer = entry.getHandler();

                    if (dataSourceContainer.getActualConnectionConfiguration() != null) {
                        connectionConfiguration.setUserPassword(dataSourceContainer.getActualConnectionConfiguration().getUserPassword());

                    } else if (dataSourceContainer.getConnectionConfiguration() != null) {
                        connectionConfiguration.setUserPassword(dataSourceContainer.getConnectionConfiguration().getUserPassword());
                    }
                }
            } finally {
                log.info("Visit Handler (Get) -> {}, spend [{}] ms.", name, System.currentTimeMillis() - ctm);
            }
        }

        handler.count.incrementAndGet();
        logThreadLocal(id, activateRecord);
        return handler;
    }

    private static void refreshInstanceName(DataSourceConnectionHandler handler, ConnectionConfiguration connectionConfiguration) {
        // 刷新实例名称
        if (StringUtils.isNotBlank(connectionConfiguration.getInstanceName()) && !connectionConfiguration.getInstanceName().equals(handler.getInstanceName())){
            handler.setInstanceName(connectionConfiguration.getInstanceName());
            if (handler.getConnectionConfiguration() != null){
                handler.getConnectionConfiguration().setInstanceName(connectionConfiguration.getInstanceName());
            }
            if (handler.getActualConnectionConfiguration() != null){
                handler.getActualConnectionConfiguration().setInstanceName(connectionConfiguration.getInstanceName());
            }
        }
    }

    private DataSourceConnectionHandler(String id, DBPDriver driver, ConnectionConfiguration connectionConfiguration, boolean activateRecord, boolean forceClose, String connectionId) {
        super(id, driver, connectionConfiguration);
        if (connectionConfiguration != null) {
            this.databaseType = connectionConfiguration.getDatabaseType();
        }
        this.activateRecord = activateRecord;
        this.forceClose = forceClose;
        this.connectionId = connectionId;
    }

    @Override
    public <T> T getAdapter(Class<T> adapter) {
        if (adapter == DataSourceManager.class || adapter == ConnectionManager.class) {
            return adapter.cast(this);
        }
        return super.getAdapter(adapter);
    }

    private void addContext(String token, Integer purpose, Boolean autoCommit, DBPConnectionConfiguration configuration) throws DBException {

        Integer maximumContextSize = configuration.getMaximumContextSize();
        if (maximumContextSize != null && maximumContextSize > 0) {
            int size = elementMap.size();
            log.info("指定 datasource ({}) 当前连接数 [{}]，最大连接数 [{}]。", getName(), size, maximumContextSize);
            if (size >= maximumContextSize) {
                throw new MaximumContextSizeException();
            }
        }

        StatParam statParam = new StatParam();
        statParam.setContainerId(getId());
        statParam.setConnectionId(getConnectionId());
        RecordHandler handle = RecordHandler.handle(RecordType.DATASOURCE, activateRecord);
        try {
            DBCExecutionPurpose executionPurpose = DBCExecutionPurpose.getById(purpose);
            DBCExecutionContext context = this.getDataSource().getDefaultInstance()
                    .openIsolatedContext(MONITOR, makePurpose(executionPurpose), null, autoCommit, configuration);
            handle.appendRow(statParam, RecordSignType.CONTEXT_ADD);
            context.setSessionProxy(activateRecord ? SessionProxy.class : TimeoutSessionProxy.class);
            elementMap.putIfAbsent(token, new Element(token, context, executionPurpose));
        } catch (DBException e) {
            handle.appendRow(statParam, RecordSignType.CONTEXT_ERROR);
            throw e;
        }
    }

    private String makePurpose(DBCExecutionPurpose executionPurpose) {
        String title = executionPurpose.getTitle();
        if (executionPurpose.isUser()) {
            return title + " " + com.dc.utils.StringUtils.removeParentheses(HandlerCounter.getOperator());
        }
        return title;
    }

    /**
     * @see DBCExecutionPurpose
     */
    @Override
    public DBCExecutionContext getExecutionContext(String token, Integer purpose, Boolean autoCommit, Boolean autoConnect,Boolean shortConnect, String prefs, String charset, DBPConnectionConfiguration configuration) throws DBException {
        HandlerCounter.setContainerId(getId());
        HandlerCounter.setToken(token);
        HandlerCounter.setExecuteId(UUID.randomUUID().toString());
        boolean add = false;

        if (!elementMap.containsKey(token)) {
            String userPassword = configuration.getUserPassword();
            try {
                addContext(token, purpose, autoCommit, configuration);
            } catch (Exception e) {

                ContextSubject.trigger(contextObserver -> contextObserver.fillingConfigurationPassword(configuration));

                String refreshedUserPassword = configuration.getUserPassword();
                if (refreshedUserPassword.equals(userPassword)) {
                    throw e;
                } else {
                    // 其他来源密码变更
                    getActualConnectionConfiguration().setRefreshPool(true);
                    getConnectionConfiguration().setRefreshPool(true);
                    getActualConnectionConfiguration().setUserPassword(refreshedUserPassword);
                    getConnectionConfiguration().setUserPassword(refreshedUserPassword);
                    addContext(token, purpose, autoCommit, configuration);
                }
            }
            add = true;
        }
        this.refreshTime(token);
        DBCExecutionContext context = elementMap.get(token).getContext();
        if (Boolean.TRUE.equals(shortConnect)){
            tryReconnect(token,context);
        }
        context.getPreferenceStore().setValue(ModelPreferences.EXECUTE_RECOVER_CONNECTION_LOST_ENABLED, Boolean.TRUE.equals(autoConnect));
        context.setCharset(charset);

        DBExecUtils.tryExecuteRecover(context, this.getDataSource(), param -> {
            try {
                if (context instanceof DBCTransactionManager) {
                    DBCTransactionManager transactionManager = (DBCTransactionManager) context;
                    if (autoCommit != null && transactionManager.isAutoCommit() != autoCommit) {
                        transactionManager.setAutoCommit(MONITOR, autoCommit);
                    }
                }
                context.execPrefs(MONITOR, prefs);
            } catch (Exception e) {
                throw new InvocationTargetException(e);
            }
        });

        StatParam statParam = new StatParam();
        statParam.setToken(token);
        statParam.setContainerId(getId());
        statParam.setConnectionId(getConnectionId());
        statParam.setOperator(HandlerCounter.getOperator());
        statParam.setUserId(HandlerCounter.getUserId());
        statParam.setConnectionPattern(HandlerCounter.getConnectionPattern());
        RecordHandler.handle(RecordType.SESSION, activateRecord).appendRow(statParam, add ? RecordSignType.CONTEXT_ADD : RecordSignType.CONTEXT_GET);
        RecordHandler.handle(RecordType.DATASOURCE, activateRecord).appendRow(statParam, RecordSignType.CONTEXT_GET);
        if (activateRecord && add) {
            AbstractStatMonitor.getMonitorMap().get("stat.session").run(new Date());
        }
        return context;
    }

    private void tryReconnect(String token, DBCExecutionContext context) {
        if (context.isInterrupted()){
            synchronized (context){
                if (!context.isInterrupted()){
                    log.debug("[shortConnect] token:{} 已被其他线程重连，直接返回", token);
                    return;
                }
                try {
                    log.debug("[shortConnect] 开始重连");
                    context.invalidateContext(MONITOR,false,context.getConfiguration(),true);
                    ContextSubject.trigger(contextObserver -> contextObserver.printLogOpenLifeCycle(context, token, context.getConfiguration() != null ? context.getConfiguration().getUserName() : ""));
                } catch (DBException e) {
                    log.error("[shortConnect]token:{} 重连失败", token, e);
                    throw new RuntimeException(e);
                }
            }
        }
    }

    @Override
    public void refreshTime(String token) {
        Element element = elementMap.get(token);
        if (element != null) {
            element.refreshTime();
        }
        Entry entry = ENTRY_MAP.get(getId());
        if (entry != null) {
            entry.refreshTime();
        }
    }

    @Override
    public void closeExecutionContext(String token) {
        try {
            ContextSubject.trigger(contextObserver -> contextObserver.closeSpecifiedContext(token));
        } finally {
            close(token);
            StatParam statParam = new StatParam();
            statParam.setToken(token);
            statParam.setContainerId(getId());
            RecordHandler handle = RecordHandler.handle(RecordType.DATASOURCE, activateRecord);
            handle.appendRow(statParam, RecordSignType.CONTEXT_CLOSE);
            handle.appendRow(statParam, RecordSignType.CONTEXT_OVER);
            RecordHandler.handle(RecordType.SESSION, activateRecord).appendRow(statParam, RecordSignType.CONTEXT_CLOSE);
        }
    }

    private void close(String token) {
        Element element = elementMap.remove(token);
        if (element != null) {
            DBCExecutionContext executionContext = element.getContext();

            if (forceClose) {
                try {
                    if (getDataSource() instanceof JDBCDataSource) {
                        ((JDBCDataSource) getDataSource()).killConnection(MONITOR, (JDBCExecutionContext) executionContext);
                    }
                } catch (Exception e) {
                    log.warn("JdbcDataSource KillConnection Error: {}", e.getMessage());
                }
            }
            try {
                executionContext.close();
            } catch (Exception e) {
                log.error("ExecutionContext Close Error: {}", e.getMessage());
            }
        }
    }

public void closeShortConnection(String token) {
    // 此处不移除Context
    Element element = elementMap.get(token);
    if (element != null) {
        DBCExecutionContext executionContext = element.getContext();
        executionContext.close();
        ContextSubject.trigger(contextObserver -> contextObserver.printLogCloseLifeCycle(executionContext, token, "短连接", null));
    }
}

    @Override
    public void closeExpiredContext() {
        log.debug("Closing expired context: {}", getId());
        for (Map.Entry<String, Element> entry : elementMap.entrySet()) {

            String token = entry.getKey();

            if (StatementHandler.isRunning(token)) {
                continue;
            }

            Element element = entry.getValue();
            DBCExecutionPurpose purpose = element.getPurpose();
            long currentTimeMillis = System.currentTimeMillis();
            long time = element.getTime();

            if (time > 0) {
                DBCExecutionContext context = element.getContext();
                Long keepAliveTime = context.getConfiguration().getKeepAliveTime();
                if (purpose != DBCExecutionPurpose.USER_SCRIPT && keepAliveTime != null && keepAliveTime > 0) {
                    keepAliveTime *= 1000;
                    long currentTime = currentTimeMillis - time;
                    if (currentTime > keepAliveTime) {
                        log.info("指定 token ({}) 存活超时，purpose ({}) 已超时 {} ms。", token, purpose, currentTime - keepAliveTime);
                        ContextSubject.trigger(contextObserver -> contextObserver.printLogCloseLifeCycle(context, token, "超时关闭", null));
                        this.closeExecutionContext(token);
                        continue;
                    }
                }

                Long heartBeatTime = context.getConfiguration().getHeartBeatTime();
                Integer heartBeatCount = context.getConfiguration().getHeartBeatCount();
                if (purpose == DBCExecutionPurpose.USER && heartBeatCount != null && heartBeatCount > 0 && heartBeatTime != null && heartBeatTime > 0) {
                    heartBeatTime *= 1000;
                    heartBeatTime *= heartBeatCount;
                    long currentTime = currentTimeMillis - time;
                    if (currentTime > heartBeatTime) {
                        log.info("指定 token ({}) 心跳超时，purpose ({}) 已超时 {} ms。", token, purpose, currentTime - heartBeatTime);
                        ContextSubject.trigger(contextObserver -> contextObserver.printLogCloseLifeCycle(context, token, "心跳停止", null));
                        this.closeExecutionContext(token);
                        continue;
                    }
                }
            }

            String survived = DurationFormatUtils.formatDuration(currentTimeMillis - element.getCreate(), "d '天' H '小时' m '分钟' s '秒'");
            log.debug("指定 token ({}) 检测正常，purpose ({}) 已存活 {}。", token, purpose, survived);
        }
    }

    @Override
    public Collection<DBCExecutionContext> getAllContexts() {
        return elementMap.values().stream().map(Element::getContext).collect(Collectors.toSet());
    }

    @Override
    public Collection<? extends ConnectionManager> getAllConnectionManagers() {
        return ENTRY_MAP.values().stream().map(Entry::getHandler).collect(Collectors.toList());
    }

    @Override
    public void refreshDataSource(String connectionId, String userName, String serverName, long refreshTime) {
        ENTRY_MAP.keySet().stream().filter(id -> id.contains(connectionId)).forEach(id -> {
            Entry entry = ENTRY_MAP.get(id);
            if (entry != null) {
                Lock writeLock = entry.getLock().writeLock();
                writeLock.lock();
                try {
                    DataSourceConnectionHandler handler = entry.getHandler();
                    // 如果存在用户名、服务名，说明只刷新指定的容器，其他容器，默认需要关闭。
                    if ((StringUtils.isNotBlank(userName) && !id.contains(userName + "@" + connectionId)) ||
                            (StringUtils.isNotBlank(serverName) && !id.contains(connectionId + "/" + serverName))) {
                        handler.disconnect(MONITOR);
                        ENTRY_MAP.remove(id);
                        log.debug("Disconnect DataSource : {}", id);
                    } else if (refreshTime == 0 || refreshTime > handler.getRefreshTime()) {
                        handler.refreshObject(MONITOR);
                        handler.setRefreshTime(System.currentTimeMillis());
                    }
                } catch (Exception e) {
                    log.error(String.format("[%s] 容器刷新失败！", id), e);
                } finally {
                    writeLock.unlock();
                }
            }
        });
        // 移除保存时候，临时生成的数据源。
        closeDataSource("@0/", true);
    }

    @Override
    public boolean closeDataSource(String connectionId, boolean forceClose) {
        AtomicBoolean isSuccess = new AtomicBoolean(true);
        ENTRY_MAP.keySet().stream().filter(id -> id.contains(connectionId)).forEach(id -> {
            Entry entry = ENTRY_MAP.get(id);
            if (forceClose || entry.getHandler().elementMap.isEmpty()) {
                boolean closeFail = !closeDataSource(entry, "会话丢失");
                if (closeFail) {
                    isSuccess.set(false);
                }
            }

        });
        return isSuccess.get();
    }

    private static boolean closeDataSource(Entry entry, String scene) {
        if (entry != null) {
            Lock writeLock = entry.getLock().writeLock();
            writeLock.lock();
            String id = entry.getHandler().getId();
            try {
                if (entry.getHandler() != null) {
                    DataSourceConnectionHandler handler = entry.getHandler();
                    handler.elementMap.keySet().forEach(token -> {
                        ContextSubject.trigger(contextObserver -> contextObserver.printLogCloseLifeCycle(handler.elementMap.get(token).getContext(), token, scene, null));
                        handler.closeExecutionContext(token);
                    });
                    handler.disconnect(MONITOR);
                }
                ENTRY_MAP.remove(id);
                log.debug("Close datasource ({})", id);
            } catch (Exception e) {
                log.error(String.format("[%s] 容器关闭失败！", id), e);
                return false;
            } finally {
                writeLock.unlock();
            }
        }
        return true;
    }

    @Override
    public void closeExpiredDataSource() {
        log.debug("Closing expired datasource");
        Long keepAliveTime = DataSourceConfig.getInstance().getKeepAliveTime();
        if (keepAliveTime == null || keepAliveTime < 0) {
            return;
        } else {
            keepAliveTime *= 1000;
        }
        long currentTimeMillis = System.currentTimeMillis();
        Long finalKeepAliveTime = keepAliveTime;
        ENTRY_MAP.forEach((id, entry) -> {
            if (entry.getTime() != null && currentTimeMillis - entry.getTime() > finalKeepAliveTime &&
                    (entry.getHandler() == null || entry.getHandler().elementMap.isEmpty())) {
                closeDataSource(entry, "超时关闭");
                log.debug("存活 dataSource 超时, dataSource 关闭: {}", id);
            }
        });
    }

    public Long getCount() {
        return this.count.get();
    }

    public LoggingProgressMonitor getMonitor() {
        return MONITOR;
    }

    private static long getSlot(long time) {
        return time % 60 + 1;
    }

    @Override
    public boolean hasContext(String token) {
        return elementMap.containsKey(token);
    }

}
