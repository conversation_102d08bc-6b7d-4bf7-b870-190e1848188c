package com.dc.summer.exec.model;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.DBCExecutionContext;

import java.util.Collection;

public interface ConnectionManager extends DBPDataSourceContainer {

    DBCExecutionContext getExecutionContext(String token, Integer purpose, Boolean autoCommit,
                                            Boolean autoConnect,
                                            Boolean shortConnect,
                                            String prefs, String charset,
                                            DBPConnectionConfiguration configuration) throws DBException;

    void refreshTime(String token);

    void closeExecutionContext(String token);

    void closeExpiredContext();

    Collection<DBCExecutionContext> getAllContexts();

    boolean hasContext(String token);

}
