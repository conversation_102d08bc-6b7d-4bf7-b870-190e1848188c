package com.dc.summer.parser.utils.model;

import com.dc.stmt.CustomSqlStatement;
import com.dc.sqlparser.DCustomSqlStatement;

public class SqlParseResult {
    private DCustomSqlStatement tCustomSqlStatement; // GSqlParser的解析树
    private Object ast; // clickhouse的解析树
    private CustomSqlStatement customSqlStatement; // mongo/redis的解析树
    private String errorMessage; // 解析插件(GSqlParser)的报错
    private String realSql; // 将oracle的中文符号替换为英文符号方便解析,原始sql保存起来发给执行器
    private boolean isSubmitJob; // adb-mysql3的submit job INSERT INTO test SELECT * FROM test_external_table;

    public DCustomSqlStatement gettCustomSqlStatement() {
        return tCustomSqlStatement;
    }

    public void settCustomSqlStatement(DCustomSqlStatement tCustomSqlStatement) {
        this.tCustomSqlStatement = tCustomSqlStatement;
    }

    public Object getAst() {
        return ast;
    }

    public void setAst(Object ast) {
        this.ast = ast;
    }

    public CustomSqlStatement getCustomSqlStatement() {
        return customSqlStatement;
    }

    public void setCustomSqlStatement(CustomSqlStatement customSqlStatement) {
        this.customSqlStatement = customSqlStatement;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getRealSql() {
        return realSql;
    }

    public void setRealSql(String realSql) {
        this.realSql = realSql;
    }

    public boolean isSubmitJob() {
        return isSubmitJob;
    }

    public void setSubmitJob(boolean submitJob) {
        isSubmitJob = submitJob;
    }
}
