package com.dc.summer.parser.utils;

import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.nodes.*;
import com.dc.sqlparser.stmt.*;
import com.dc.sqlparser.types.*;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

public class GetStatisticsFunctionUtil {

    private final Set<String> curLinkedStaFuncNames = new HashSet<>(); //当前已关联的（之前版本暴力解决专用）
//    private final Set<String> fineGrainedCurLinkedStaFuncNames = new HashSet<>(); //细粒度的。（有列限定的）。当前已关联的。
    private final Map<String, Set<String>> fineGrainedMap = new HashMap<>(); //细


    public void fillStaFuncMap(Map<String, Set<String>> staFuncMap, DCustomSqlStatement sqlStmt, Set<String> outerExtraStaFunctions){

        //列 - 结果列 的 函数统计
        //TODO 需要非空判断。 已做，尚未优化
        if (sqlStmt.getResultColumnList() != null) {
            sqlStmt.getResultColumnList().forEach(resultColumn -> {
                if (resultColumn.getExpr().getExpressionType() == EExpressionType.function_t) {
                    String funcName = resultColumn.getExpr().getFunctionCall().getFunctionName().toString();
                    //是否可以精细的搞，不用后续暴力的搞了。
                    boolean isSpecifyColumn = isSpecifyColumnAndFill(resultColumn.getExpr().getFunctionCall(), fineGrainedMap, outerExtraStaFunctions, staFuncMap);
                    if (isStaFunc(funcName) && !isSpecifyColumn) {
                        curLinkedStaFuncNames.add(funcName);
                    }
                } else if (resultColumn.getExpr().getExpressionType() == EExpressionType.subquery_t) {
                    fillStaFuncMap(staFuncMap, resultColumn.getExpr().getSubQuery(), outerExtraStaFunctions);
                }
            });
        }

        // order by and group by
        if (sqlStmt instanceof TSelectSqlStatement) {
            TSelectSqlStatement selectSqlStmt = (TSelectSqlStatement) sqlStmt;
            if (selectSqlStmt.getOrderbyClause() != null) {
                boolean isSpecifyForOrderAndFill = isSpecifyForOrderAndFill(selectSqlStmt.getOrderbyClause(), fineGrainedMap, outerExtraStaFunctions, staFuncMap);
                if (!isSpecifyForOrderAndFill) {
                    curLinkedStaFuncNames.add("ORDER");
                }
            }
            if (selectSqlStmt.getGroupByClause() != null) {
                boolean isSpecifyForGroupAndFill = isSpecifyForGroupAndFill(selectSqlStmt.getGroupByClause(), fineGrainedMap, outerExtraStaFunctions, staFuncMap);
                if (!isSpecifyForGroupAndFill) {
                    curLinkedStaFuncNames.add("GROUP");
                }
                //having子句中使用统计函数的情况
                TExpression havingClause = selectSqlStmt.getGroupByClause().getHavingClause();
                if (havingClause != null) {
                    handleWhereAndHavingClause(havingClause, staFuncMap, outerExtraStaFunctions);
                }
            }
            if (selectSqlStmt.getSetOperatorType() == ESetOperatorType.union) {
                fillStaFuncMap(staFuncMap, selectSqlStmt.getLeftStmt(), outerExtraStaFunctions);
                fillStaFuncMap(staFuncMap, selectSqlStmt.getRightStmt(), outerExtraStaFunctions);
                return;
            }
            if (selectSqlStmt.getSelectDistinct() != null && selectSqlStmt.getResultColumnList() != null) {
                boolean isSpecifyForDistinctAndFill = isSpecifyForDistinctAndFill(selectSqlStmt.getResultColumnList(), fineGrainedMap, outerExtraStaFunctions, staFuncMap);
                if (!isSpecifyForDistinctAndFill) {
                    curLinkedStaFuncNames.add("DISTINCT");
                }
            }
        }else if (sqlStmt instanceof TInsertSqlStatement){
            TInsertSqlStatement insertSqlStmt = (TInsertSqlStatement) sqlStmt;
            if (insertSqlStmt.getSubQuery() != null || insertSqlStmt.getInsertSource() == EInsertSource.subquery){
                sqlStmt = insertSqlStmt.getSubQuery();
                fillStaFuncMap(staFuncMap, sqlStmt, outerExtraStaFunctions);
                return;
            }
        }else if (sqlStmt instanceof TUpdateSqlStatement){
            TUpdateSqlStatement updateSqlStmt = (TUpdateSqlStatement) sqlStmt;
            if (updateSqlStmt.getOrderByClause() != null){
                boolean isSpecifyForOrderAndFill = isSpecifyForOrderAndFill(updateSqlStmt.getOrderByClause(), fineGrainedMap, outerExtraStaFunctions, staFuncMap);
                if (!isSpecifyForOrderAndFill) {
                    curLinkedStaFuncNames.add("ORDER");
                }
            }
        }else if (sqlStmt instanceof TDeleteSqlStatement){
            TDeleteSqlStatement deleteSqlStmt = (TDeleteSqlStatement) sqlStmt;
            if (deleteSqlStmt.getOrderByClause() != null){
                boolean isSpecifyForOrderAndFill = isSpecifyForOrderAndFill(deleteSqlStmt.getOrderByClause(), fineGrainedMap, outerExtraStaFunctions, staFuncMap);
                if (!isSpecifyForOrderAndFill) {
                    curLinkedStaFuncNames.add("ORDER");
                }
            }
        }else if (sqlStmt instanceof TCreateTableSqlStatement){
            TCreateTableSqlStatement createTableSqlStmt = (TCreateTableSqlStatement) sqlStmt;
            if (createTableSqlStmt.getTableSourceType() == TCreateTableSqlStatement.a.subquery || createTableSqlStmt.getSubQuery() != null){
                sqlStmt = createTableSqlStmt.getSubQuery();
                fillStaFuncMap(staFuncMap, sqlStmt, outerExtraStaFunctions);
                return;
            }
        }else if (sqlStmt instanceof TCreateViewSqlStatement) {
            TCreateViewSqlStatement createViewSqlStatement = (TCreateViewSqlStatement) sqlStmt;
            if (createViewSqlStatement.getSubquery() != null) {
                fillStaFuncMap(staFuncMap, createViewSqlStatement.getSubquery(), outerExtraStaFunctions);
            }
        }

        //where子句
        if (sqlStmt.getWhereClause() != null) {
            TExpression condition = sqlStmt.getWhereClause().getCondition();

            handleWhereAndHavingClause(condition, staFuncMap, outerExtraStaFunctions);
        }


        TTableList tables = sqlStmt.getTables();
        for (TTable table : tables) {

            switch (table.getTableType()){
                case objectname:
                    curLinkedStaFuncNames.forEach(staFuncName -> staFuncMap.computeIfAbsent(staFuncName, k -> new HashSet<>()).add(table.getTableName().toString()));
                    break;
                case subquery:
                    fillStaFuncMap(staFuncMap, table.getSubquery(), outerExtraStaFunctions);
                    break;
                case function:
                    curLinkedStaFuncNames.forEach(staFuncName -> staFuncMap.computeIfAbsent(staFuncName, k -> new HashSet<>()).add(""));
                    System.out.println("tableSource为function，可能在数据库中此对象为函数，由于无法统计函数对象，因此构造空名表");
                    break;
                case unknown:
                    curLinkedStaFuncNames.forEach(staFuncName -> staFuncMap.computeIfAbsent(staFuncName, k -> new HashSet<>()).add(table.getTableName() == null ? "" : table.getTableName().toString()));
                    break;
                default:
                    System.out.println("tableSource暂未支持");
                    break;
            }
        }
        //最终将 精细的过程 和 粗糙的过程 进行合并
        fineGrainedMap.forEach((key, value) -> {
            staFuncMap.computeIfAbsent(key, k -> new HashSet<>()).addAll(value);
        });

    }

    private boolean isSpecifyColumnAndFill(TFunctionCall functionCall, Map<String, Set<String>> fineGrainedMap, Set<String> outerExtraStaFunctions, Map<String, Set<String>> staFuncMap) {
        if (functionCall == null || functionCall.getArgs() == null) return false;
        if (!isStaFunc(functionCall.getFunctionName().toString())) return false;
        boolean hasOuter = !CollectionUtils.isEmpty(outerExtraStaFunctions);
        boolean ret = false;
        for (TExpression arg : functionCall.getArgs()) {
            if (arg.getExpressionType() == EExpressionType.simple_object_name_t) {
                TTable sourceTable = arg.getObjectOperand().getSourceTable();
                if (sourceTable != null && sourceTable.getTableType() == ETableSource.objectname) {
                    fineGrainedMap.computeIfAbsent(functionCall.getFunctionName().toString(), s -> new HashSet<>()).add(sourceTable.getTableName().toString());
                    if (hasOuter) outerExtraStaFunctions.forEach(f -> fineGrainedMap.computeIfAbsent(f, s -> new HashSet<>()).add(sourceTable.getTableName().toString()));
                    ret = true;
                } else if (sourceTable != null && sourceTable.getTableType() == ETableSource.subquery) {
                    HashSet<String> newOuterExtraStaFunctions = new HashSet<>(outerExtraStaFunctions);
                    newOuterExtraStaFunctions.add(functionCall.getFunctionName().toString());
                    fillStaFuncMap(staFuncMap, sourceTable.getSubquery(), newOuterExtraStaFunctions);
                } else if (sourceTable != null && sourceTable.getTableType() == ETableSource.function) {
                    fineGrainedMap.computeIfAbsent(functionCall.getFunctionName().toString(), s -> new HashSet<>()).add("");
                }
                ArrayList<TTable> sourceTableList = arg.getObjectOperand().getSourceTableList();
                if (!CollectionUtils.isEmpty(sourceTableList)) {
                    for (TTable table : sourceTableList) {
                        if (table.getTableType() == ETableSource.objectname) {
                            fineGrainedMap.computeIfAbsent(functionCall.getFunctionName().toString(), s -> new HashSet<>()).add(table.getTableName().toString());
                            if (hasOuter) outerExtraStaFunctions.forEach(f -> fineGrainedMap.computeIfAbsent(f, s -> new HashSet<>()).add(table.getTableName().toString()));
                        } else if (table.getTableType() == ETableSource.subquery) {
                            HashSet<String> newOuterExtraStaFunctions = new HashSet<>(outerExtraStaFunctions);
                            newOuterExtraStaFunctions.add(functionCall.getFunctionName().toString());
                            fillStaFuncMap(staFuncMap, table.getSubquery(), newOuterExtraStaFunctions);
                        }
                    }
                    ret = true;
                }
                TTableList candidateTables = arg.getObjectOperand().getCandidateTables();
                if (candidateTables != null && candidateTables.size() > 0) {
                    for (TTable candidateTable : candidateTables) {
                        if (candidateTable.getTableType() == ETableSource.objectname) {
                            fineGrainedMap.computeIfAbsent(functionCall.getFunctionName().toString(), s -> new HashSet<>()).add(candidateTable.getTableName().toString());
                            if (hasOuter) outerExtraStaFunctions.forEach(f -> fineGrainedMap.computeIfAbsent(f, s -> new HashSet<>()).add(candidateTable.getTableName().toString()));
                        } else if (candidateTable.getTableType() == ETableSource.subquery) {
                            HashSet<String> newOuterExtraStaFunctions = new HashSet<>(outerExtraStaFunctions);
                            newOuterExtraStaFunctions.add(functionCall.getFunctionName().toString());
                            fillStaFuncMap(staFuncMap, candidateTable.getSubquery(), newOuterExtraStaFunctions);
                        }
                    }
                    ret = true;
                }
            } else if (arg.getExpressionType() == EExpressionType.subquery_t) {
                HashSet<String> newOuterExtraStaFunctions = new HashSet<>(outerExtraStaFunctions);
                newOuterExtraStaFunctions.add(functionCall.getFunctionName().toString());
                fillStaFuncMap(staFuncMap, arg.getSubQuery(), newOuterExtraStaFunctions);
            }
        }
        return ret;
    }

    private static boolean isStaFunc(String funcName){
        return CommonUtil.getDCSupportFunctions().contains(funcName.toUpperCase(Locale.ROOT));
    }

    //针对ORDER BY
    private boolean isSpecifyForOrderAndFill(TOrderBy orderBy, Map<String, Set<String>> fineGrainedMap, Set<String> outerExtraStaFunctions, Map<String, Set<String>> staFuncMap) {
        boolean hasOuter = !CollectionUtils.isEmpty(outerExtraStaFunctions);
        boolean ret = false;
        for (TOrderByItem orderByItem : orderBy.getItems()) {
            if (orderByItem.getSortKey() == null) continue;
            TExpression sortKey = orderByItem.getSortKey();
            if (sortKey.getExpressionType() == EExpressionType.simple_object_name_t) {
                TTable sourceTable = sortKey.getObjectOperand().getSourceTable();
                if (sourceTable != null && sourceTable.getTableType() == ETableSource.objectname) {
                    fineGrainedMap.computeIfAbsent("ORDER", k -> new HashSet<>()).add(sourceTable.getTableName().toString());
                    if (hasOuter) outerExtraStaFunctions.forEach(f -> fineGrainedMap.computeIfAbsent(f, k -> new HashSet<>()).add(sourceTable.getTableName().toString()));
                    ret = true;
                } else if (sourceTable != null && sourceTable.getTableType() == ETableSource.subquery) {
                    HashSet<String> newOuterExtraStaFunctions = new HashSet<>(outerExtraStaFunctions);
                    newOuterExtraStaFunctions.add("ORDER");
                    fillStaFuncMap(staFuncMap, sourceTable.getSubquery(), newOuterExtraStaFunctions);
                }
            }
            ArrayList<TTable> sourceTableList = sortKey.getObjectOperand().getSourceTableList();
            if (!CollectionUtils.isEmpty(sourceTableList)) {
                for (TTable table : sourceTableList) {
                    if (table.getTableType() == ETableSource.objectname) {
                        fineGrainedMap.computeIfAbsent("ORDER", s -> new HashSet<>()).add(table.getTableName().toString());
                        if (hasOuter) outerExtraStaFunctions.forEach(f -> fineGrainedMap.computeIfAbsent(f, s -> new HashSet<>()).add(table.getTableName().toString()));
                    } else if (table.getTableType() == ETableSource.subquery) {
                        HashSet<String> newOuterExtraStaFunctions = new HashSet<>(outerExtraStaFunctions);
                        newOuterExtraStaFunctions.add("ORDER");
                        fillStaFuncMap(staFuncMap, table.getSubquery(), newOuterExtraStaFunctions);
                    }
                }
                ret = true;
            }
            TTableList candidateTables = sortKey.getObjectOperand().getCandidateTables();
            if (candidateTables != null && candidateTables.size() > 0) {
                for (TTable candidateTable : candidateTables) {
                    if (candidateTable.getTableType() == ETableSource.objectname) {
                        fineGrainedMap.computeIfAbsent("ORDER", s -> new HashSet<>()).add(candidateTable.getTableName().toString());
                        if (hasOuter) outerExtraStaFunctions.forEach(f -> fineGrainedMap.computeIfAbsent(f, s -> new HashSet<>()).add(candidateTable.getTableName().toString()));
                    } else if (candidateTable.getTableType() == ETableSource.subquery) {
                        HashSet<String> newOuterExtraStaFunctions = new HashSet<>(outerExtraStaFunctions);
                        newOuterExtraStaFunctions.add("ORDER");
                        fillStaFuncMap(staFuncMap, candidateTable.getSubquery(), newOuterExtraStaFunctions);
                    }
                }
                ret = true;
            }
        }

        return ret;
    }

    //针对GROUP BY
    private boolean isSpecifyForGroupAndFill(TGroupBy groupBy, Map<String, Set<String>> fineGrainedMap, Set<String> outerExtraStaFunctions, Map<String, Set<String>> staFuncMap) {
        boolean hasOuter = !CollectionUtils.isEmpty(outerExtraStaFunctions);
        boolean ret = false;
        for (TGroupByItem groupByItem : groupBy.getItems()) {
            if (groupByItem.getExpr() == null) continue;
            TExpression expr = groupByItem.getExpr();
            if (expr.getExpressionType() == EExpressionType.simple_object_name_t) {
                TTable sourceTable = expr.getObjectOperand().getSourceTable();
                if (sourceTable != null && sourceTable.getTableType() == ETableSource.objectname) {
                    fineGrainedMap.computeIfAbsent("GROUP", k -> new HashSet<>()).add(sourceTable.getTableName().toString());
                    if (hasOuter) outerExtraStaFunctions.forEach(f -> fineGrainedMap.computeIfAbsent(f, k -> new HashSet<>()).add(sourceTable.getTableName().toString()));
                    ret = true;
                } else if (sourceTable != null && sourceTable.getTableType() == ETableSource.subquery) {
                    HashSet<String> newOuterExtraStaFunctions = new HashSet<>(outerExtraStaFunctions);
                    newOuterExtraStaFunctions.add("GROUP");
                    fillStaFuncMap(staFuncMap, sourceTable.getSubquery(), newOuterExtraStaFunctions);
                }
            }
            ArrayList<TTable> sourceTableList = expr.getObjectOperand().getSourceTableList();
            if (!CollectionUtils.isEmpty(sourceTableList)) {
                for (TTable table : sourceTableList) {
                    if (table.getTableType() == ETableSource.objectname) {
                        fineGrainedMap.computeIfAbsent("GROUP", s -> new HashSet<>()).add(table.getTableName().toString());
                        if (hasOuter) outerExtraStaFunctions.forEach(f -> fineGrainedMap.computeIfAbsent(f, s -> new HashSet<>()).add(table.getTableName().toString()));
                    } else if (table.getTableType() == ETableSource.subquery) {
                        HashSet<String> newOuterExtraStaFunctions = new HashSet<>(outerExtraStaFunctions);
                        newOuterExtraStaFunctions.add("GROUP");
                        fillStaFuncMap(staFuncMap, table.getSubquery(), newOuterExtraStaFunctions);
                    }
                }
                ret = true;
            }
            TTableList candidateTables = expr.getObjectOperand().getCandidateTables();
            if (candidateTables != null && candidateTables.size() > 0) {
                for (TTable candidateTable : candidateTables) {
                    if (candidateTable.getTableType() == ETableSource.objectname) {
                        fineGrainedMap.computeIfAbsent("GROUP", s -> new HashSet<>()).add(candidateTable.getTableName().toString());
                        if (hasOuter) outerExtraStaFunctions.forEach(f -> fineGrainedMap.computeIfAbsent(f, s -> new HashSet<>()).add(candidateTable.getTableName().toString()));
                    } else if (candidateTable.getTableType() == ETableSource.subquery) {
                        HashSet<String> newOuterExtraStaFunctions = new HashSet<>(outerExtraStaFunctions);
                        newOuterExtraStaFunctions.add("GROUP");
                        fillStaFuncMap(staFuncMap, candidateTable.getSubquery(), newOuterExtraStaFunctions);
                    }
                }
                ret = true;
            }
        }

        return ret;
    }

    //针对DISTINCT
    private boolean isSpecifyForDistinctAndFill(TResultColumnList resultColumnList, Map<String, Set<String>> fineGrainedMap, Set<String> outerExtraStaFunctions, Map<String, Set<String>> staFuncMap) {
        if (resultColumnList == null || resultColumnList.size() == 0) return false;
        boolean hasOuter = !CollectionUtils.isEmpty(outerExtraStaFunctions);
        boolean ret = false;

        for (TResultColumn resultColumn : resultColumnList) {
            TExpression expr = resultColumn.getExpr();
            if (expr.getExpressionType() == EExpressionType.simple_object_name_t) {
                TTable sourceTable = expr.getObjectOperand().getSourceTable();
                if (sourceTable != null && sourceTable.getTableType() == ETableSource.objectname) {
                    fineGrainedMap.computeIfAbsent("DISTINCT", k -> new HashSet<>()).add(sourceTable.getTableName().toString());
                    if (hasOuter) outerExtraStaFunctions.forEach(f -> fineGrainedMap.computeIfAbsent(f, k -> new HashSet<>()).add(sourceTable.getTableName().toString()));
                    ret = true;
                } else if (sourceTable != null && sourceTable.getTableType() == ETableSource.subquery) {
                    HashSet<String> newOuterExtraStaFunctions = new HashSet<>(outerExtraStaFunctions);
                    newOuterExtraStaFunctions.add("DISTINCT");
                    fillStaFuncMap(staFuncMap, sourceTable.getSubquery(), newOuterExtraStaFunctions);
                }
                ArrayList<TTable> sourceTableList = expr.getObjectOperand().getSourceTableList();
                if (!CollectionUtils.isEmpty(sourceTableList)) {
                    for (TTable table : sourceTableList) {
                        if (table.getTableType() == ETableSource.objectname) {
                            fineGrainedMap.computeIfAbsent("DISTINCT", s -> new HashSet<>()).add(table.getTableName().toString());
                            if (hasOuter) outerExtraStaFunctions.forEach(f -> fineGrainedMap.computeIfAbsent(f, s -> new HashSet<>()).add(table.getTableName().toString()));
                        } else if (table.getTableType() == ETableSource.subquery) {
                            HashSet<String> newOuterExtraStaFunctions = new HashSet<>(outerExtraStaFunctions);
                            newOuterExtraStaFunctions.add("DISTINCT");
                            fillStaFuncMap(staFuncMap, table.getSubquery(), newOuterExtraStaFunctions);
                        }
                    }
                    ret = true;
                }
                TTableList candidateTables = expr.getObjectOperand().getCandidateTables();
                if (candidateTables != null && candidateTables.size() > 0 && !CollectionUtils.isEmpty(sourceTableList)) { //如果list是空，那么不考虑候选的
                    for (TTable candidateTable : candidateTables) {
                        if (candidateTable.getTableType() == ETableSource.objectname) {
                            fineGrainedMap.computeIfAbsent("DISTINCT", s -> new HashSet<>()).add(candidateTable.getTableName().toString());
                            if (hasOuter) outerExtraStaFunctions.forEach(f -> fineGrainedMap.computeIfAbsent(f, s -> new HashSet<>()).add(candidateTable.getTableName().toString()));
                        } else if (candidateTable.getTableType() == ETableSource.subquery) {
                            HashSet<String> newOuterExtraStaFunctions = new HashSet<>(outerExtraStaFunctions);
                            newOuterExtraStaFunctions.add("DISTINCT");
                            fillStaFuncMap(staFuncMap, candidateTable.getSubquery(), newOuterExtraStaFunctions);
                        }
                    }
                    ret = true;
                }
            }
        }

        return ret;
    }

    //处理where子句和having子句，都是统一类型，属性condition都是TExpression类型
    private void handleWhereAndHavingClause(TExpression condition, Map<String, Set<String>> staFuncMap, Set<String> outerExtraStaFunctions) {
        if (condition != null && condition.getExpressionType() != null) {
            switch (condition.getExpressionType()) {
                case simple_comparison_t: {
                    List.of(condition.getLeftOperand(), condition.getRightOperand()).forEach(operand -> {
                        switch (operand.getExpressionType()) {
                            case function_t: isSpecifyColumnAndFill(operand.getFunctionCall(), fineGrainedMap, outerExtraStaFunctions, staFuncMap);break;
                            case subquery_t: fillStaFuncMap(staFuncMap, operand.getSubQuery(), outerExtraStaFunctions);
                        }
                    });
                }
                case logical_and_t:
                case logical_or_t: {
                    if (condition.getFlattedAndOrExprs() == null) break;
                    for (Object flattedAndOrExpr : condition.getFlattedAndOrExprs()) {
                        if (flattedAndOrExpr instanceof TExpression) {
                            TExpression expression = (TExpression) flattedAndOrExpr;
                            if (expression.getExpressionType() == EExpressionType.simple_comparison_t) {
                                List.of(expression.getLeftOperand(), expression.getRightOperand()).forEach(operand -> {
                                    switch (operand.getExpressionType()) {
                                        case function_t: isSpecifyColumnAndFill(operand.getFunctionCall(), fineGrainedMap, outerExtraStaFunctions, staFuncMap);break;
                                        case subquery_t: fillStaFuncMap(staFuncMap, operand.getSubQuery(), outerExtraStaFunctions);break;
                                    }
                                });
                            }
                        }
                    }
                }

            }
        }
    }
}
