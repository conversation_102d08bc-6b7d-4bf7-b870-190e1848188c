package com.dc.summer.parser.utils;

import com.dc.stmt.CustomSqlStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.type.RegularType;
import com.dc.summer.parser.sql.utils.RegularUtil;
import com.dc.summer.parser.utils.model.SqlParseResult;
import com.dc.summer.parser.utils.model.SqlParseStrengthen;
import com.dc.type.DatabaseType;
import com.dc.sqlparser.types.ESqlStatementType;
import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.DPSqlParser;
import com.dc.sqlparser.DStatementList;
import com.dc.sqlparser.nodes.TForUpdate;
import com.dc.sqlparser.stmt.TCreateTriggerStmt;
import com.dc.sqlparser.stmt.TSelectSqlStatement;
import com.dc.sqlparser.stmt.mssql.TMssqlCreateFunction;
import com.dc.sqlparser.stmt.mssql.TMssqlCreateProcedure;
import com.dc.sqlparser.stmt.mssql.TMssqlIfElse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SqlSplitUtil {

    private static final Logger logger = LoggerFactory.getLogger(SqlSplitUtil.class);

    public static DStatementList split(String sql, Integer dbType) {

        try {
            DPSqlParser parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(dbType));
            sql = SqlPreparedUtil.sqlReplace(sql, dbType, false);
            parser.setSqltext(sql);

            int ret;
            try {
                ret = parser.parse();
            } catch (Exception e) {
                ret = 1; // 解析插件本身可能会报异常,如空指针异常(kingBase:create table tableName1 as table tableName2;)
            }

            if (ret != 0) {
                sql = transformSql(sql, dbType);

                if (DatabaseType.getPGSqlIntegerValueList().contains(dbType) || DatabaseType.getIdentCode(DatabaseType.needCheckAgainByOracle()).contains(dbType)) {
                    // 再用oracle解析一遍, 如 pg: 1.insert all语句; 2.创建可编程对象语句会被分割
                    // g_base_s: ROLLBACK 语句 会报错
                    // db2: SELECT CAST(name AS varchar) FROM TEST2."NAME_TEST";
                    parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.ORACLE.getValue()));
                    parser.setSqltext(sql);
                    ret = parser.parse();
                    if (ret != 0) {
                        printLog(parser.getErrormessage(), sql);
                    }
                } else if (DatabaseType.getIdentCode(DatabaseType.needCheckAgainByMysql()).contains(dbType)) {
                    // 再用mysql解析一遍, 如 DM: create/drop schema schemaName 语句
                    // HIVE: insert into 语句
                    // ob-oracle: insert into tableName values (1,10,1),(2,20,1),(3,30,1)
                    parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.MYSQL.getValue()));
                    parser.setSqltext(sql);
                    ret = parser.parse();
                    if (ret != 0) {
                        if (DatabaseType.getIdentCode(DatabaseType.adaptToMultipleGrammars()).contains(dbType)) {
                            // 神通数据库支持oracle、mysql、postGreSql、sqlserver的语法
                            // 达梦数据库支持oracle、mysql、sqlserver的语法
                            parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.SQL_SERVER.getValue()));
                            parser.setSqltext(sql);
                            ret = parser.parse();
                            if (ret != 0) {
                                parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.PG_SQL.getValue()));
                                parser.setSqltext(sql);
                                ret = parser.parse();
                                if (ret != 0) {
                                    printLog(parser.getErrormessage(), sql);
                                }
                            }
                        } else {
                            printLog(parser.getErrormessage(), sql);
                        }
                    }
                } else if (DatabaseType.getIdentCode(DatabaseType.needCheckAgainByPostGreSql()).contains(dbType)) {
                    // 再用pgsql解析一遍, 如以下sql是pgsql语法
                    // select * from (SELECT name,age FROM table1) as a(name1);
                    // SELECT * FROM table1 except SELECT * FROM table2;
                    parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.PG_SQL.getValue()));
                    parser.setSqltext(sql);
                    ret = parser.parse();
                    if (ret != 0) {
                        printLog(parser.getErrormessage(), sql);
                    }
                } else {
                    printLog(parser.getErrormessage(), sql);
                }
            }

            return parser.sqlstatements;
        } catch (Exception e) {
            printLog(e.getMessage(), sql);
            throw new RuntimeException(e.getMessage());
        }
    }

    public static List<SqlParseResult> getSqlParserResultMessage(DStatementList sqlStatements) {
        List<SqlParseResult> sqlParserResultMessageList = new ArrayList<>();
        for (int k = 0; k < sqlStatements.size(); k++) {
            SqlParseResult sqlParserResultMessage = new SqlParseResult();
            sqlParserResultMessage.settCustomSqlStatement(sqlStatements.get(k));
            sqlParserResultMessageList.add(sqlParserResultMessage);
        }
        return sqlParserResultMessageList;
    }

    public static List<SqlParseStrengthen> mergeErrorSplitSql(List<SqlParseResult> sqlStatements, Integer dbType) {
        List<SqlParseStrengthen> list = new ArrayList<>();

        String outSql = "";
        boolean isFind = false;
        String realOperation = "";
        boolean hasNextNew = false;
        for (int k = 0; k < sqlStatements.size(); k++) {
            DCustomSqlStatement next = sqlStatements.get(k).gettCustomSqlStatement();

            if (next == null) {
                CustomSqlStatement customSqlStatement = sqlStatements.get(k).getCustomSqlStatement();
                SqlParseStrengthen sqlParserStrengthenModel = buildSqlParserStrengthenModel(customSqlStatement, sqlStatements.get(k).getRealSql(), dbType);
                list.add(sqlParserStrengthenModel);
                continue;
            } else if (DatabaseType.H_BASE.getValue().equals(dbType)) {
                SqlParseStrengthen sqlParserStrengthenModel = buildSqlParserStrengthenModel(next);
                list.add(sqlParserStrengthenModel);
                break;
            }

            Object ast = sqlStatements.get(k).getAst();
            String token = GetOperationUtil.getOperation(next);
            if (SqlConstant.KEY_GO.equalsIgnoreCase(token)) {
                continue;
            }

            // fix bug: create Global Temporary table  xxx on commit preserve rows  as select ... 会被拆分
            if (ESqlStatementType.sstcreatetable.equals(next.sqlstatementtype)) {
                if ((k + 1) < sqlStatements.size() && null != sqlStatements.get(k + 1).gettCustomSqlStatement().getStartToken() &&
                        SqlConstant.KEY_COMMIT.equalsIgnoreCase(sqlStatements.get(k + 1).gettCustomSqlStatement().getStartToken().toString())) {
                    String pattern = "(COMMIT)\\s+(DELETE|PRESERVE)\\s+(ROWS)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(sqlStatements.get(k + 1).gettCustomSqlStatement().toString());
                    if (m.find()) {
                        String sql1 = next.toString();
                        String sql2 = sqlStatements.get(k + 1).gettCustomSqlStatement().toString();
                        outSql = sql1 + " " + sql2;
                        isFind = true;
                        realOperation = SqlConstant.KEY_CREATE;
                        continue;
                    }
                }
            }

            // fix bug: alter view xxx as select ...(会被分割成两句sql)
            if (ESqlStatementType.sstmssqlalterview.equals(next.sqlstatementtype)) {
                if ((k + 1) < sqlStatements.size() && null != sqlStatements.get(k + 1).gettCustomSqlStatement().getStartToken() &&
                        SqlConstant.KEY_SELECT.equalsIgnoreCase(sqlStatements.get(k + 1).gettCustomSqlStatement().getStartToken().toString())) {
                    String pattern = "(?i)\\s+as\\s*$";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(sqlStatements.get(k).gettCustomSqlStatement().toString());
                    if (m.find()) {
                        String sql1 = next.toString();
                        String sql2 = sqlStatements.get(k + 1).gettCustomSqlStatement().toString();
                        outSql = sql1 + " " + sql2;
                        isFind = true;
                        realOperation = SqlConstant.KEY_ALTER;
                        continue;
                    }
                }
            }

            // fix bug: gBase8s/informix的 create/drop database  xxx 语句会被拆分
            if (CommonUtil.useColonSplit(dbType) && Arrays.asList(SqlConstant.KEY_CREATE,
                    SqlConstant.KEY_DROP, SqlConstant.KEY_RENAME, SqlConstant.KEY_USE).contains(next.toString().trim().toUpperCase(Locale.ROOT))) {
                if ((k + 1) < sqlStatements.size() && null != sqlStatements.get(k + 1).gettCustomSqlStatement().getStartToken() &&
                        SqlConstant.KEY_DATABASE.equalsIgnoreCase(sqlStatements.get(k + 1).gettCustomSqlStatement().getStartToken().toString())) {
                    String pattern = "(CREATE|DROP|RENAME|USE)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(sqlStatements.get(k).gettCustomSqlStatement().toString());
                    if (m.find()) {
                        String sql1 = next.toString();
                        String sql2 = sqlStatements.get(k + 1).gettCustomSqlStatement().toString();
                        outSql = sql1 + " " + sql2;
                        isFind = true;
                        realOperation = m.group(1);
                        continue;
                    }
                }
            }

            // fix bug: create or replace procedure...begin...for...end FOR; end
            if (ESqlStatementType.sstdb2createprocedure.equals(next.sqlstatementtype)) {
                if ((k + 1) < sqlStatements.size() && null != sqlStatements.get(k + 1).gettCustomSqlStatement().getStartToken() &&
                        SqlConstant.KEY_END.equalsIgnoreCase(sqlStatements.get(k + 1).gettCustomSqlStatement().getStartToken().toString())) {
                    String sql1 = next.toString();
                    String sql2 = sqlStatements.get(k + 1).gettCustomSqlStatement().toString().replaceAll(";", "");
                    if (SqlConstant.KEY_END.equalsIgnoreCase(sql2.trim())) {
                        outSql = sql1 + " " + sqlStatements.get(k + 1).gettCustomSqlStatement().toString();
                        isFind = true;
                        realOperation = SqlConstant.KEY_CREATE;
                        continue;
                    }
                }
            }

            // fix bug: sqlserver的 CREATE OR ALTER FUNCTION/PROCEDURE 语句会被拆分
            if (DatabaseType.SQL_SERVER.getValue().equals(dbType)
                    && "CREATEOR".equalsIgnoreCase(next.toString().replaceAll(" ", ""))) {
                if ((k + 1) < sqlStatements.size()
                        && (sqlStatements.get(k + 1).gettCustomSqlStatement() instanceof TMssqlCreateFunction
                        || sqlStatements.get(k + 1).gettCustomSqlStatement() instanceof TMssqlCreateProcedure
                        || sqlStatements.get(k + 1).gettCustomSqlStatement() instanceof TCreateTriggerStmt)) {
                    String sql1 = next.toString();
                    String sql2 = sqlStatements.get(k + 1).gettCustomSqlStatement().toString();
                    outSql = sql1 + " " + sql2;
                    isFind = true;
                    realOperation = SqlConstant.KEY_CREATE;
                    continue;
                }
            }

            // fix bug: sqlserver的 DROP VIEW IF EXISTS 语句会被拆分
            if (DatabaseType.SQL_SERVER.getValue().equals(dbType)
                    && Arrays.asList("DROPVIEW", "DROPTABLE", "DROPTRIGGER", "DROPSEQUENCE", "DROPFUNCTION", "DROPPROCEDURE", "DROPPROC", "DROPSYNONYM")
                    .contains(next.toString().replaceAll(" ", "").toUpperCase(Locale.ROOT))) {
                if ((k + 1) < sqlStatements.size()
                        && sqlStatements.get(k + 1).gettCustomSqlStatement() instanceof TMssqlIfElse) {
                    String sql1 = next.toString();
                    String sql2 = sqlStatements.get(k + 1).gettCustomSqlStatement().toString();
                    outSql = sql1 + " " + sql2;
                    isFind = true;
                    realOperation = SqlConstant.KEY_DROP;
                    hasNextNew = true;
                    continue;
                }
            }

            SqlParseStrengthen sqlParserStrengthenModel = new SqlParseStrengthen();

            String sql;
            if (sqlStatements.get(k).getRealSql() != null) {
                sql = sqlStatements.get(k).getRealSql();
            } else {
                sql = next.toString();
                if (DatabaseType.SQL_SERVER.getValue().equals(dbType)) {
                    sql = sql.replaceAll("(?i)\\s+GO\\s*$", "");
                }
            }

            if (isFind) {
                sql = outSql;
                isFind = false;
                token = realOperation;
                sqlStatements.get(k).setErrorMessage(null);
                if (hasNextNew) {
                    hasNextNew = false;
                    DCustomSqlStatement nextNew = CommonUtil.getStatement(sql, DatabaseType.SQL_SERVER.getValue());
                    if (nextNew != null) {
                        next = nextNew;
                    }
                }
            }
            sqlParserStrengthenModel.setSql(sql);
            sqlParserStrengthenModel.settCustomSqlStatement(next);
            sqlParserStrengthenModel.setAst(ast);
            sqlParserStrengthenModel.setOperation(token.toUpperCase());

            boolean isClause = false;
            if (SqlConstant.KEY_SELECT.equalsIgnoreCase(token) && next instanceof TSelectSqlStatement) {
                TSelectSqlStatement select = (TSelectSqlStatement) next;
                TForUpdate forUpdateClause = select.getForUpdateClause();
                if (null != forUpdateClause) {
                    isClause = true;
                }
            }
            sqlParserStrengthenModel.setUpdateClause(isClause);

            sqlParserStrengthenModel.setErrorMessage(sqlStatements.get(k).getErrorMessage());
            if (DatabaseType.CLICKHOUSE.getValue().equals(dbType)) {
                sqlParserStrengthenModel.setErrorMessage(null);
            }


            if (Arrays.asList(SqlConstant.KEY_WITH, SqlConstant.KEY_TABLE).contains(sqlParserStrengthenModel.getOperation())) {
                sqlParserStrengthenModel.setOperation(SqlConstant.KEY_SELECT);
            } else if (Arrays.asList(SqlConstant.KEY_SP_RENAME, SqlConstant.KEY_RENAME, SqlConstant.KEY_ENABLE, SqlConstant.KEY_DISABLE).contains(sqlParserStrengthenModel.getOperation())) {
                sqlParserStrengthenModel.setOperation(SqlConstant.KEY_ALTER);
            }

            sqlParserStrengthenModel.setSubmitJob(sqlStatements.get(k).isSubmitJob());

            list.add(sqlParserStrengthenModel);
        }

        return list;
    }

    public static SqlParseStrengthen buildSqlParserStrengthenModel(CustomSqlStatement customSqlStatement, String sql, Integer dbType) {
        SqlParseStrengthen sqlParserStrengthenModel = new SqlParseStrengthen();

        sqlParserStrengthenModel.setSql(sql);
        sqlParserStrengthenModel.setOperation(SqlOperationUtil.transformOperation(customSqlStatement, dbType));
        sqlParserStrengthenModel.setCustomSqlStatement(customSqlStatement);

        if (DatabaseType.ELASTIC_SEARCH.getValue().equals(dbType) && StringUtils.isBlank(sqlParserStrengthenModel.getOperation()) && customSqlStatement != null) {
            customSqlStatement.setSupport(false);
        }

        boolean isSupport = customSqlStatement.isSupport();
        if (!isSupport) {
            sqlParserStrengthenModel.setErrorMessage("不支持解析的 SQL。");
        }

        return sqlParserStrengthenModel;
    }

    public static SqlParseStrengthen buildSqlParserStrengthenModel(DCustomSqlStatement next) {
        SqlParseStrengthen sqlParserStrengthenModel = new SqlParseStrengthen();

        String token = GetOperationUtil.getOperation(next);
        sqlParserStrengthenModel.setSql(next.toString());
        sqlParserStrengthenModel.settCustomSqlStatement(next);
        sqlParserStrengthenModel.setOperation(token.toUpperCase());

        return sqlParserStrengthenModel;
    }

    public static void printLog(String errorMessage, String sql) {
        logger.info("SqlSplitUtil::split : SQL split failed.");
        logger.info("SqlSplitUtil::split : error message is: " + errorMessage);
        logger.info("SqlSplitUtil::split : error sql is: " + sql);
    }

    public static String transformSql(String sql, Integer dbType) {
        if (DatabaseType.INCEPTOR.getValue().equals(dbType)) {
            return RegularUtil.transformSql(sql, RegularType.PARTITION.getValue());
        }
        return sql;
    }

}
