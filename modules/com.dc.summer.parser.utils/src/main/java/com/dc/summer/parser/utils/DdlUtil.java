package com.dc.summer.parser.utils;

import com.dc.node.ObjectName;
import com.dc.parser.clickhouse.ast.*;
import com.dc.parser.clickhouse.ast.expr.AssignmentExpr;
import com.dc.parser.clickhouse.ast.expr.ColumnExpr;
import com.dc.parser.clickhouse.ast.expr.SubqueryColumnExpr;
import com.dc.parser.clickhouse.util.getCKTablesUtil;
import com.dc.parser.clickhouse.visitor.BaseSqlBuilder;
import com.dc.sqlparser.DPSqlParser;
import com.dc.sqlparser.stmt.oracle.*;
import com.dc.sqlparser.types.EDbObjectType;
import com.dc.stmt.CustomSqlStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.utils.model.SqlDdlResult;
import com.dc.summer.parser.utils.model.SqlParseModel;
import com.dc.type.DatabaseType;
import com.dc.sqlparser.types.ETableEffectType;
import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.nodes.TObjectName;
import com.dc.sqlparser.nodes.TTable;
import com.dc.sqlparser.nodes.TTableList;
import com.dc.sqlparser.stmt.*;
import com.dc.sqlparser.stmt.db2.TDb2CreateFunction;
import com.dc.sqlparser.stmt.db2.TDb2CreateProcedure;
import com.dc.sqlparser.stmt.hive.THiveDropDatabase;
import com.dc.sqlparser.stmt.informix.TInformixCreateFunction;
import com.dc.sqlparser.stmt.informix.TInformixCreateProcedure;
import com.dc.sqlparser.stmt.mssql.TMssqlCreateFunction;
import com.dc.sqlparser.stmt.mssql.TMssqlCreateProcedure;
import com.dc.sqlparser.stmt.mysql.TMySQLCreateFunction;
import com.dc.sqlparser.stmt.mysql.TMySQLCreateProcedure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DdlUtil {

    private static final Logger logger = LoggerFactory.getLogger(DdlUtil.class);

    public static SqlDdlResult getObject(SqlParseModel sqlParserModel, Integer dbType) {
        SqlDdlResult sqlDdlResultModel = new SqlDdlResult();

        Object ast = sqlParserModel.getAst();
        DCustomSqlStatement tCustomSqlStatement = ast != null ? null : sqlParserModel.gettCustomSqlStatement();
        CustomSqlStatement customSqlStatement = sqlParserModel.getCustomSqlStatement();

        GetTablesUtil getTables = new GetTablesUtil();
        GetColumnsUtil getColumns = new GetColumnsUtil();
        getColumns.setDbType(dbType);
        getCKTablesUtil getCKTablesUtil = new getCKTablesUtil();

        String objectType = "";
        String objectName = "";
        String newObjectName = "";
        String asTableName = "";
        String likeTableName = "";
        String ckOperation = "";
        String ckWhere = "";

        boolean isChangeSchema = false;
        boolean isCreateAS = false;

        Set<String> tables = new HashSet<>();
        Set<String> functions = new HashSet<>();
        Set<String> ckTables = new HashSet<>();
        Set<String> ckFunctions = new HashSet<>();
        Set<String> dropObjects = new HashSet<>();
        Set<String> truncateTables = new HashSet<>();

        try {

            if (tCustomSqlStatement instanceof TCommentOnSqlStmt) {
                TCommentOnSqlStmt tCommentOnSqlStmt = (TCommentOnSqlStmt) tCustomSqlStatement;
                if (tCommentOnSqlStmt.getDbObjectType() != null) {
                    objectType = tCommentOnSqlStmt.getDbObjectType().toString();
                }
                if (tCommentOnSqlStmt.getObjectName() != null) {
                    objectName = tCommentOnSqlStmt.getObjectName().toString();
                }
            } else if (tCustomSqlStatement instanceof TTruncateStatement) {
                TTruncateStatement tTruncateStatement = (TTruncateStatement) tCustomSqlStatement;
                if (tTruncateStatement.getTableName() != null) {
                    objectType = tTruncateStatement.getTableName().getDbObjectType().toString();
                    objectName = tTruncateStatement.getTableName().toString();
                } else if (tTruncateStatement.getTables() != null) {
                    TTableList truncateTableList = tTruncateStatement.getTables();
                    for (int i = 0; i < truncateTableList.size(); i++) {
                        TTable table = truncateTableList.getTable(i);
                        if (i == 0) {
                            objectType = "TABLE";
                            objectName = table.getFullName();
                        } else {
                            truncateTables.add(table.getFullName());
                        }
                    }
                }
            } else if (tCustomSqlStatement instanceof TCreateTablespaceStmt) {
                TCreateTablespaceStmt tCreateTablespaceStmt = (TCreateTablespaceStmt) tCustomSqlStatement;
                if (tCreateTablespaceStmt.getTablespaceName() != null) {
                    objectType = tCreateTablespaceStmt.getTablespaceName().getDbObjectType().toString();
                    objectName = tCreateTablespaceStmt.getTablespaceName().toString();
                }
            } else if (tCustomSqlStatement instanceof TCreateDatabaseSqlStatement) {
                TCreateDatabaseSqlStatement tCreateDatabaseSqlStatement = (TCreateDatabaseSqlStatement) tCustomSqlStatement;
                if (tCreateDatabaseSqlStatement.getDatabaseName() != null) {
                    objectType = tCreateDatabaseSqlStatement.getDatabaseName().getDbObjectType().toString();
                    objectName = tCreateDatabaseSqlStatement.getDatabaseName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDropDatabaseStmt) {
                TDropDatabaseStmt tDropDatabaseStmt = (TDropDatabaseStmt) tCustomSqlStatement;
                if (tDropDatabaseStmt.getDatabaseName() != null) {
                    objectType = tDropDatabaseStmt.getDatabaseName().getDbObjectType().toString();
                    objectName = tDropDatabaseStmt.getDatabaseName().toString();
                }
            } else if (tCustomSqlStatement instanceof TAlterDatabaseStmt) {
                TAlterDatabaseStmt tAlterDatabaseStmt = (TAlterDatabaseStmt) tCustomSqlStatement;
                if (tAlterDatabaseStmt.getDatabaseName() != null) {
                    objectType = "database";
                    objectName = tAlterDatabaseStmt.getDatabaseName().toString();
                }
            } else if (tCustomSqlStatement instanceof TCreateDatabaseLinkStmt) {
                TCreateDatabaseLinkStmt tCreateDatabaseLinkStmt = (TCreateDatabaseLinkStmt) tCustomSqlStatement;
                if (tCreateDatabaseLinkStmt.getDatabaseLinkName() != null) {
                    objectType = tCreateDatabaseLinkStmt.getDatabaseLinkName().getDbObjectType().toString();
                    objectName = tCreateDatabaseLinkStmt.getDatabaseLinkName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDropDatabaseLinkStmt) {
                TDropDatabaseLinkStmt tDropDatabaseLinkStmt = (TDropDatabaseLinkStmt) tCustomSqlStatement;
                if (tDropDatabaseLinkStmt.getDatabaseLinkName() != null) {
                    objectType = tDropDatabaseLinkStmt.getDatabaseLinkName().getDbObjectType().toString();
                    objectName = tDropDatabaseLinkStmt.getDatabaseLinkName().toString();
                }
            } else if (tCustomSqlStatement instanceof TCreateUserStmt) {
                TCreateUserStmt tCreateUserStmt = (TCreateUserStmt) tCustomSqlStatement;
                if (tCreateUserStmt.getUserName() != null) {
                    objectType = tCreateUserStmt.getUserName().getDbObjectType().toString();
                    objectName = tCreateUserStmt.getUserName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDropUserStmt) {
                TDropUserStmt tDropUserStmt = (TDropUserStmt) tCustomSqlStatement;
                if (tDropUserStmt.getUserName() != null) {
                    objectType = tDropUserStmt.getUserName().getDbObjectType().toString();
                    objectName = tDropUserStmt.getUserName().toString();
                }
            } else if (tCustomSqlStatement instanceof TAlterUserStmt) {
                TAlterUserStmt tAlterUserStmt = (TAlterUserStmt) tCustomSqlStatement;
                if (tAlterUserStmt.getUserName() != null) {
                    objectType = tAlterUserStmt.getUserName().getDbObjectType().toString();
                    objectName = tAlterUserStmt.getUserName().toString();
                }
            } else if (tCustomSqlStatement instanceof TCreateSchemaSqlStatement) {
                TCreateSchemaSqlStatement statement = (TCreateSchemaSqlStatement) tCustomSqlStatement;
                if (statement.getSchemaName() != null) {
                    objectType = statement.getSchemaName().getDbObjectType().toString();
                    objectName = statement.getSchemaName().toString();
                }
                if ("unknown".equalsIgnoreCase(objectType)) {
                    objectType = "schema";
                }
            } else if (tCustomSqlStatement instanceof TDropSchemaSqlStatement) {
                TDropSchemaSqlStatement statement = (TDropSchemaSqlStatement) tCustomSqlStatement;
                if (statement.getSchemaName() != null) {
                    objectType = statement.getSchemaName().getDbObjectType().toString();
                    objectName = statement.getSchemaName().toString();
                }
                if ("unknown".equalsIgnoreCase(objectType)) {
                    objectType = "schema";
                }
            } else if (tCustomSqlStatement instanceof TAlterSchemaStmt) {
                TAlterSchemaStmt statement = (TAlterSchemaStmt) tCustomSqlStatement;
                if (statement.getSchemaName() != null) {
                    objectType = statement.getSchemaName().getDbObjectType().toString();
                    objectName = statement.getSchemaName().toString();
                }
                if ("unknown".equalsIgnoreCase(objectType)) {
                    objectType = "schema";
                }
            } else if (tCustomSqlStatement instanceof TCreateRoleStmt) {
                TCreateRoleStmt statement = (TCreateRoleStmt) tCustomSqlStatement;
                if (statement.getRoleName() != null) {
                    objectType = statement.getRoleName().getDbObjectType().toString();
                    objectName = statement.getRoleName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDropRoleStmt) {
                TDropRoleStmt statement = (TDropRoleStmt) tCustomSqlStatement;
                if (statement.getRoleName() != null) {
                    objectType = statement.getRoleName().getDbObjectType().toString();
                    objectName = statement.getRoleName().toString();
                }
            } else if (tCustomSqlStatement instanceof TAlterRoleStmt) {
                TAlterRoleStmt statement = (TAlterRoleStmt) tCustomSqlStatement;
                if (statement.getRoleName() != null) {
                    objectType = statement.getRoleName().getDbObjectType().toString();
                    objectName = statement.getRoleName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDropProcedureStmt) {
                TDropProcedureStmt tDropProcedureStmt = (TDropProcedureStmt) tCustomSqlStatement;
                if (tDropProcedureStmt.getProcedureName() != null) {
                    objectType = tDropProcedureStmt.getProcedureName().getDbObjectType().toString();
                    objectName = tDropProcedureStmt.getProcedureName().toString();
                }
            } else if (tCustomSqlStatement instanceof TCreateProcedureStmt) {
                TCreateProcedureStmt tCreateProcedureStmt = (TCreateProcedureStmt) tCustomSqlStatement;
                if (tCreateProcedureStmt.getProcedureName() != null) {
                    objectType = tCreateProcedureStmt.getProcedureName().getDbObjectType().toString();
                    objectName = tCreateProcedureStmt.getProcedureName().toString();
                }
            } else if (tCustomSqlStatement instanceof TPlsqlCreateProcedure) {
                TPlsqlCreateProcedure tPlsqlCreateProcedure = (TPlsqlCreateProcedure) tCustomSqlStatement;
                if (tPlsqlCreateProcedure.getProcedureName() != null) {
                    objectType = tPlsqlCreateProcedure.getProcedureName().getDbObjectType().toString();
                    objectName = tPlsqlCreateProcedure.getProcedureName().toString();
                }
            } else if (tCustomSqlStatement instanceof TPlsqlCreateTypeBody) {
                TPlsqlCreateTypeBody tPlsqlCreateTypeBody = (TPlsqlCreateTypeBody) tCustomSqlStatement;
                TObjectName typeName = tPlsqlCreateTypeBody.getTypeName();
                objectName = Optional.ofNullable(typeName).map(TObjectName::toString).orElse("");
                objectType = Optional.ofNullable(typeName).map(TObjectName::getDbObjectType).map(EDbObjectType::toString).orElse("");
            } else if (tCustomSqlStatement instanceof TMssqlCreateProcedure) {
                TMssqlCreateProcedure tMssqlCreateProcedure = (TMssqlCreateProcedure) tCustomSqlStatement;
                if (tMssqlCreateProcedure.getProcedureName() != null) {
                    objectType = tMssqlCreateProcedure.getProcedureName().getDbObjectType().toString();
                    objectName = tMssqlCreateProcedure.getProcedureName().toString();
                }
            } else if (tCustomSqlStatement instanceof TMySQLCreateProcedure) {
                TMySQLCreateProcedure tMySQLCreateProcedure = (TMySQLCreateProcedure) tCustomSqlStatement;
                if (tMySQLCreateProcedure.getProcedureName() != null) {
                    objectType = tMySQLCreateProcedure.getProcedureName().getDbObjectType().toString();
                    objectName = tMySQLCreateProcedure.getProcedureName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDb2CreateProcedure) {
                TDb2CreateProcedure tDb2CreateProcedure = (TDb2CreateProcedure) tCustomSqlStatement;
                if (tDb2CreateProcedure.getProcedureName() != null) {
                    objectType = tDb2CreateProcedure.getProcedureName().getDbObjectType().toString();
                    objectName = tDb2CreateProcedure.getProcedureName().toString();
                }
            } else if (tCustomSqlStatement instanceof TInformixCreateProcedure) {
                TInformixCreateProcedure procedure = (TInformixCreateProcedure) tCustomSqlStatement;
                if (procedure.getProcedureName() != null) {
                    objectType = procedure.getProcedureName().getDbObjectType().toString();
                    objectName = procedure.getProcedureName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDropTableSqlStatement) {
                TDropTableSqlStatement statement = (TDropTableSqlStatement) tCustomSqlStatement;
                if (statement.getTableName() != null) {
                    objectType = statement.getTableName().getDbObjectType().toString();
                    objectName = statement.getTableName().toString();
                }
                if (statement.getTableNameList() != null) {
                    for (TObjectName tableName : statement.getTableNameList()) {
                        if (tableName != null && !tableName.toString().equalsIgnoreCase(objectName)) {
                            dropObjects.add(tableName.toString());
                        }
                    }
                }
            } else if (tCustomSqlStatement instanceof TCreateTableSqlStatement) {
                TCreateTableSqlStatement statement = (TCreateTableSqlStatement) tCustomSqlStatement;
                try {
                    if (statement.getTableName() != null) {
                        objectType = statement.getTableName().getDbObjectType().toString();
                        objectName = statement.getTableName().toString();
                    }
                    /*if (statement.getLikeTableName() != null) {
                        likeTableName = statement.getLikeTableName().toString();
                    }*/
                    if (statement.getSubQuery() != null) {
                        sqlParserModel.getAction().setCreateTableAsSelect(true);
                        TSelectSqlStatement subQuery = statement.getSubQuery();
                        tables = getTables.analyzeSelectStatement(subQuery);
                        Object[] objects = getColumns.getObject(subQuery);
                        functions = (Set<String>) objects[1];
                    }

                    //处理 create table name1 like name2 的情况。
                    if (statement.getTableSourceType() != null) {
                        TCreateTableSqlStatement.a tableSourceType = statement.getTableSourceType();
                        if (tableSourceType == TCreateTableSqlStatement.a.like && statement.getLikeTableName() != null) {
                            likeTableName = statement.getLikeTableName().toString();
                        }
                    }

                } catch (Exception e) {
                    isCreateAS = true;
                    logger.info("Get Table Name failed. ");
                }
            } else if (tCustomSqlStatement instanceof TAlterTableStatement) {
                TAlterTableStatement statement = (TAlterTableStatement) tCustomSqlStatement;
                if (statement.getTableName() != null) {
                    objectType = statement.getTableName().getDbObjectType().toString();
                    objectName = statement.getTableName().toString();
                }
                if (statement.getTables() != null) {
                    TTableList alterTables = statement.getTables();
                    for (TTable tTable : alterTables) {
                        if (!objectName.equals(tTable.getFullName()) && ETableEffectType.tetAlterTableRename.equals(tTable.getEffectType())) {
                            newObjectName = tTable.getFullName();
                            break;
                        }
                    }
                }
            } else if (tCustomSqlStatement instanceof TRenameStmt) {
                TRenameStmt statement = (TRenameStmt) tCustomSqlStatement;
                if (statement.getOldName() != null) {
                    objectType = statement.getOldName().getDbObjectType().toString();
                    objectName = statement.getOldName().toString();
                }
                if (statement.getNewName() != null) {
                    newObjectName = statement.getNewName().toString();
                }
            } else if (tCustomSqlStatement instanceof TAlterTriggerStmt) {
                TAlterTriggerStmt statement = (TAlterTriggerStmt) tCustomSqlStatement;
                if (statement.getTriggerName() != null) {
                    objectType = statement.getTriggerName().getDbObjectType().toString();
                    objectName = statement.getTriggerName().toString();
                }
            } else if (tCustomSqlStatement instanceof TCreateTriggerStmt) {
                TCreateTriggerStmt statement = (TCreateTriggerStmt) tCustomSqlStatement;
                if (statement.getTriggerName() != null) {
                    objectType = statement.getTriggerName().getDbObjectType().toString();
                    objectName = statement.getTriggerName().toString();
                }
            } else if (tCustomSqlStatement instanceof TPlsqlCreateTrigger) {
                TPlsqlCreateTrigger statement = (TPlsqlCreateTrigger) tCustomSqlStatement;
                if (statement.getTriggerName() != null) {
                    objectType = statement.getTriggerName().getDbObjectType().toString();
                    objectName = statement.getTriggerName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDropViewSqlStatement) {
                TDropViewSqlStatement statement = (TDropViewSqlStatement) tCustomSqlStatement;
                if (statement.getViewName() != null) {
                    objectType = statement.getViewName().getDbObjectType().toString();
                    objectName = statement.getViewName().toString();
                }
                if (statement.getViewNameList() != null) {
                    for (TObjectName viewName : statement.getViewNameList()) {
                        if (viewName != null && !viewName.toString().equalsIgnoreCase(objectName)) {
                            dropObjects.add(viewName.toString());
                        }
                    }
                }
            } else if (tCustomSqlStatement instanceof TCreateViewSqlStatement) {
                TCreateViewSqlStatement statement = (TCreateViewSqlStatement) tCustomSqlStatement;
                try {
                    if (statement.getViewName() != null) {
                        objectType = statement.getViewName().getDbObjectType().toString();
                        objectName = statement.getViewName().toString();
                    }
                    if (statement.getSubquery() != null) {
                        TSelectSqlStatement subQuery = statement.getSubquery();
                        tables = getTables.analyzeSelectStatement(subQuery);
                        Object[] objects = getColumns.getObject(subQuery);
                        functions = (Set<String>) objects[1];
                    }
                } catch (Exception e) {
                    logger.info("Get View Name failed. ");
                }
            } else if (tCustomSqlStatement instanceof TAlterViewStatement) {
                TAlterViewStatement statement = (TAlterViewStatement) tCustomSqlStatement;
                if (statement.getViewName() != null) {
                    objectType = statement.getViewName().getDbObjectType().toString();
                    objectName = statement.getViewName().toString();
                }
            } else if (tCustomSqlStatement instanceof TCreateMaterializedSqlStatement) {
                TCreateMaterializedSqlStatement statement = (TCreateMaterializedSqlStatement) tCustomSqlStatement;
                try {
                    if (statement.getViewName() != null) {
                        objectType = statement.getViewName().getDbObjectType().toString();
                        objectName = statement.getViewName().toString();
                    }
                    if (statement.getSubquery() != null) {
                        TSelectSqlStatement subQuery = statement.getSubquery();
                        tables = getTables.analyzeSelectStatement(subQuery);
                        Object[] objects = getColumns.getObject(subQuery);
                        functions = (Set<String>) objects[1];
                    }
                } catch (Exception e) {
                    logger.info("Get View Name failed. ");
                }
            } else if (tCustomSqlStatement instanceof TDropMaterializedViewStmt) {
                TDropMaterializedViewStmt statement = (TDropMaterializedViewStmt) tCustomSqlStatement;
                if (statement.getViewName() != null) {
                    objectType = statement.getViewName().getDbObjectType().toString();
                    objectName = statement.getViewName().toString();
                }
            } else if (tCustomSqlStatement instanceof TAlterMaterializedViewStmt) {
                TAlterMaterializedViewStmt statement = (TAlterMaterializedViewStmt) tCustomSqlStatement;
                if (statement.getMaterializedViewName() != null) {
                    objectType = statement.getMaterializedViewName().getDbObjectType().toString();
                    objectName = statement.getMaterializedViewName().toString();
                }
            } else if (tCustomSqlStatement instanceof TCreateMaterializedViewLogSqlStatement) { // 只有tableName，没有viewName
                TCreateMaterializedViewLogSqlStatement statement = (TCreateMaterializedViewLogSqlStatement) tCustomSqlStatement;
                if (statement.getTableName() != null) {
                    objectType = "view";
                    objectName = statement.getTableName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDropMaterializedViewLogStmt) { // 只有tableName，没有viewName
                TDropMaterializedViewLogStmt statement = (TDropMaterializedViewLogStmt) tCustomSqlStatement;
                if (statement.getTableName() != null) {
                    objectType = statement.getTableName().getDbObjectType().toString();
                    objectName = statement.getTableName().toString();
                }
            } else if (tCustomSqlStatement instanceof TAlterFunctionStmt) {
                TAlterFunctionStmt statement = (TAlterFunctionStmt) tCustomSqlStatement;
                if (statement.getFunctionName() != null) {
                    objectType = statement.getFunctionName().getDbObjectType().toString();
                    objectName = statement.getFunctionName().toString();
                }
            } else if (tCustomSqlStatement instanceof TCreateFunctionStmt) {
                TCreateFunctionStmt statement = (TCreateFunctionStmt) tCustomSqlStatement;
                if (statement.getFunctionName() != null) {
                    objectType = statement.getFunctionName().getDbObjectType().toString();
                    objectName = statement.getFunctionName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDropFunctionStmt) {
                TDropFunctionStmt statement = (TDropFunctionStmt) tCustomSqlStatement;
                if (statement.getFunctionName() != null) {
                    objectType = statement.getFunctionName().getDbObjectType().toString();
                    objectName = statement.getFunctionName().toString();
                }
            } else if (tCustomSqlStatement instanceof TMssqlCreateFunction) {
                TMssqlCreateFunction statement = (TMssqlCreateFunction) tCustomSqlStatement;
                if (statement.getFunctionName() != null) {
                    objectType = statement.getFunctionName().getDbObjectType().toString();
                    objectName = statement.getFunctionName().toString();
                }
            } else if (tCustomSqlStatement instanceof TPlsqlCreateFunction) {
                TPlsqlCreateFunction statement = (TPlsqlCreateFunction) tCustomSqlStatement;
                if (statement.getFunctionName() != null) {
                    objectType = statement.getFunctionName().getDbObjectType().toString();
                    objectName = statement.getFunctionName().toString();
                }
            } else if (tCustomSqlStatement instanceof TMySQLCreateFunction) {
                TMySQLCreateFunction statement = (TMySQLCreateFunction) tCustomSqlStatement;
                if (statement.getFunctionName() != null) {
                    objectType = statement.getFunctionName().getDbObjectType().toString();
                    objectName = statement.getFunctionName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDb2CreateFunction) {
                TDb2CreateFunction statement = (TDb2CreateFunction) tCustomSqlStatement;
                if (statement.getFunctionName() != null) {
                    objectType = statement.getFunctionName().getDbObjectType().toString();
                    objectName = statement.getFunctionName().toString();
                }
            } else if (tCustomSqlStatement instanceof TInformixCreateFunction) {
                TInformixCreateFunction statement = (TInformixCreateFunction) tCustomSqlStatement;
                if (statement.getProcedureName() != null) {
                    objectType = statement.getProcedureName().getDbObjectType().toString();
                    objectName = statement.getProcedureName().toString();
                }
            } else if (tCustomSqlStatement instanceof TCreateSequenceStmt) {
                TCreateSequenceStmt statement = (TCreateSequenceStmt) tCustomSqlStatement;
                if (statement.getSequenceName() != null) {
                    objectType = statement.getSequenceName().getDbObjectType().toString();
                    objectName = statement.getSequenceName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDropSequenceStmt) {
                TDropSequenceStmt statement = (TDropSequenceStmt) tCustomSqlStatement;
                if (statement.getSequenceName() != null) {
                    objectType = statement.getSequenceName().getDbObjectType().toString();
                    objectName = statement.getSequenceName().toString();
                }
            } else if (tCustomSqlStatement instanceof TCreateIndexSqlStatement) {
                TCreateIndexSqlStatement statement = (TCreateIndexSqlStatement) tCustomSqlStatement;
                if (statement.getIndexName() != null) {
                    objectType = statement.getIndexName().getDbObjectType().toString();
                    objectName = statement.getIndexName().toString();
                }
            } else if (tCustomSqlStatement instanceof TAlterIndexStmt) {
                TAlterIndexStmt statement = (TAlterIndexStmt) tCustomSqlStatement;
                if (statement.getIndexName() != null) {
                    objectType = statement.getIndexName().getDbObjectType().toString();
                    objectName = statement.getIndexName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDropIndexSqlStatement) {
                TDropIndexSqlStatement statement = (TDropIndexSqlStatement) tCustomSqlStatement;
                if (statement.getIndexName() != null) {
                    objectType = statement.getIndexName().getDbObjectType().toString();
                    objectName = statement.getIndexName().toString();
                }
            } else if (tCustomSqlStatement instanceof TAlterSynonymStmt) {
                TAlterSynonymStmt statement = (TAlterSynonymStmt) tCustomSqlStatement;
                if (statement.getSynonymName() != null) {
                    objectType = statement.getSynonymName().getDbObjectType().toString();
                    objectName = statement.getSynonymName().toString();
                }
            } else if (tCustomSqlStatement instanceof TCreateSynonymStmt) {
                TCreateSynonymStmt statement = (TCreateSynonymStmt) tCustomSqlStatement;
                if (statement.getSynonymName() != null) {
                    objectType = statement.getSynonymName().getDbObjectType().toString();
                    objectName = statement.getSynonymName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDropSynonymStmt) {
                TDropSynonymStmt statement = (TDropSynonymStmt) tCustomSqlStatement;
                if (statement.getSynonymName() != null) {
                    objectType = statement.getSynonymName().getDbObjectType().toString();
                    objectName = statement.getSynonymName().toString();
                }
            } else if (tCustomSqlStatement instanceof TPlsqlCreatePackage) {
                TPlsqlCreatePackage statement = (TPlsqlCreatePackage) tCustomSqlStatement;
                if (statement.getPackageName() != null) {
                    objectType = statement.getPackageName().getDbObjectType().toString();
                    objectName = statement.getPackageName().toString();
                }
            } else if (ast instanceof CreateTableQuery) {
                CreateTableQuery createTableQuery = (CreateTableQuery) ast;
                if (createTableQuery.getIdentifier() != null) {
                    objectName = createTableQuery.getIdentifier().getQualifiedName();
                    objectType = "TABLE";
                }
                if (createTableQuery.getSchema() != null && createTableQuery.getSchema().getIdentifier() != null) {
                    asTableName = createTableQuery.getSchema().getIdentifier().getQualifiedName();
                } else if (createTableQuery.getQuery() != null) {
                    SelectUnionQuery selectUnionQuery = createTableQuery.getQuery();
                    List<SelectStatement> statements = selectUnionQuery.getStatements();
                    for (SelectStatement selectStatement : statements) {
                        getCKTablesUtil.analyzeSelectStatement(selectStatement);
                        tables.addAll(getCKTablesUtil.getReturnTables());
                        functions.addAll(getCKTablesUtil.getFunctions());
                    }
                }
            } else if (ast instanceof CreateMaterializedViewQuery) {
                CreateMaterializedViewQuery createMaterializedViewQuery = (CreateMaterializedViewQuery) ast;
                if (createMaterializedViewQuery.getIdentifier() != null) {
                    objectName = createMaterializedViewQuery.getIdentifier().getQualifiedName();
//                    objectType = "VIEW";
                }
                if (createMaterializedViewQuery.getSchema() != null && createMaterializedViewQuery.getSchema().getIdentifier() != null) {
                    asTableName = createMaterializedViewQuery.getSchema().getIdentifier().getQualifiedName();
                } else if (createMaterializedViewQuery.getQuery() != null) {
                    SelectUnionQuery selectUnionQuery = createMaterializedViewQuery.getQuery();
                    List<SelectStatement> statements = selectUnionQuery.getStatements();
                    for (SelectStatement selectStatement : statements) {
                        getCKTablesUtil.analyzeSelectStatement(selectStatement);
                        tables.addAll(getCKTablesUtil.getReturnTables());
                        functions.addAll(getCKTablesUtil.getFunctions());
                    }
                }
            } else if (ast instanceof AlterTableQuery) {
                AlterTableQuery stmt = (AlterTableQuery) ast;
                if (stmt.getIdentifier() != null) {
                    objectName = stmt.getIdentifier().getQualifiedName();
                    objectType = "TABLE";
                }
                if (stmt.getClauses() != null) {
                    List<AlterTableClause> clauses = stmt.getClauses();
                    BaseSqlBuilder baseSqlBuilder = new BaseSqlBuilder();
                    for (AlterTableClause alterTableClause : clauses) {
                        if (alterTableClause instanceof AttachAlterTableClause) {
                            AttachAlterTableClause attachAlterTableClause = (AttachAlterTableClause) alterTableClause;
                            TableIdentifier from = attachAlterTableClause.getFrom();
                            if (from != null) {
                                asTableName = from.getQualifiedName();
                                break;
                            }
                        } else if (alterTableClause instanceof ReplaceAlterTableClause) {
                            ReplaceAlterTableClause replaceAlterTableClause = (ReplaceAlterTableClause) alterTableClause;
                            TableIdentifier from = replaceAlterTableClause.getFrom();
                            if (from != null) {
                                asTableName = from.getQualifiedName();
                                break;
                            }
                        } else if (alterTableClause instanceof DeleteAlterTableClause) {
                            DeleteAlterTableClause deleteAlterTableClause = (DeleteAlterTableClause) alterTableClause;
                            String deleteWhere = baseSqlBuilder.visitFunctionColumnExpr(deleteAlterTableClause.getExpr());
                            if (deleteWhere != null && !deleteWhere.isEmpty()) {
                                deleteWhere = "where " + deleteWhere;
                                ckWhere = deleteWhere;
                            }
                            ckOperation = "DELETE";
                            break;
                        } else if (alterTableClause instanceof UpdateAlterTableClause) {
                            UpdateAlterTableClause updateAlterTableClause = (UpdateAlterTableClause) alterTableClause;
                            Map<String, Set<String>> clickHouseQueryObject = getClickHouseQueryObject(updateAlterTableClause);
                            if (clickHouseQueryObject.get("tables") != null) {
                                ckTables = clickHouseQueryObject.get("tables");
                            }
                            if (clickHouseQueryObject.get("functions") != null) {
                                ckFunctions = clickHouseQueryObject.get("functions");
                            }

                            String updateWhere = baseSqlBuilder.visitWhereClause(updateAlterTableClause.getWhere());
                            if (updateWhere != null && !updateWhere.isEmpty()) {
                                ckWhere = updateWhere;
                            }
                            ckOperation = "UPDATE";
                            break;
                        }
                    }
                }
            } else if (tCustomSqlStatement instanceof THiveDropDatabase) {
                THiveDropDatabase statement = (THiveDropDatabase) tCustomSqlStatement;
                if (statement.getDbName() != null) {
                    objectType = "database";
                    objectName = statement.getDbName().toString();
                }
            } else if (customSqlStatement != null) {
                objectType = CommonUtil.getObjectType(customSqlStatement, dbType);
                objectName = customSqlStatement.getObjectName() != null ?
                        customSqlStatement.getObjectName().getName() != null ? customSqlStatement.getObjectName().getName() : ""
                        : "";

                if (customSqlStatement.getObjectNameList() != null) {
                    for (ObjectName objectNameTemp : customSqlStatement.getObjectNameList()) {
                        if (!objectName.equals(objectNameTemp.getName())) {
                            dropObjects.add(objectNameTemp.getName());
                        }
                    }
                }
            }


            // 类型为unknown的走正则解析
            if ("unknown".equalsIgnoreCase(objectType)) {
                objectType = "";
            }

            if (objectType.isEmpty() || objectName.isEmpty()) {
                boolean isMatched = false;

                if (isCreateAS) {
                    String pattern = "(CREATE\\s+TABLE)\\s+([0-9a-zA-Z_\"'\\[\\]`.]+)\\s+(AS\\s+TABLE)\\s+([0-9a-zA-Z_\"'\\[\\]`.]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(sqlParserModel.getSql());
                    if (m.find()) {
                        objectType = "TABLE";
                        objectName = m.group(2);
                        sqlParserModel.setErrorMessage(null);
                        asTableName = m.group(4);
                        isMatched = true;
                    }
                }

                if (!isMatched) {
                    String pattern = "(ALTER)\\s+(PACKAGE)\\s+([0-9a-zA-Z_\"'\\[\\]`.:]+)\\s+(COMPILE\\s+BODY)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = "PACKAGE BODY";
                        objectName = m.group(3);
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }

                if (!isMatched) {
                    String pattern = "^\\s*(CREATE|ALTER|TRUNCATE|DROP|COMMENT|DETACH|ATTACH)\\s+" +
                            "(OR\\s+REPLACE\\s+|ON\\s+|GLOBAL\\s+|VIRTUAL\\s+|DEFINER\\s*=\\s*`\\s*[0-9a-zA-Z_]+\\s*`\\s*@\\s*`\\s*[0-9a-zA-Z%.]+\\s*`\\s*)?" +
                            "(TEMPORARY\\s+|PUBLIC\\s+|EXTERNAL\\s+|HUGE\\s+|CLUSTER\\s+|NOT\\s+PARTIAL\\s+|TRANSACTIONAL\\s+|TEMP\\s+)?" +
                            "(UNIQUE\\s+|BITMAP\\s+|SPATIAL\\s+|CONTEXT\\s+|ARRAY\\s+|FLEX\\s+|DIMENSION\\s+|CLUSTERED\\s+|SPECIFIC\\s+|NORMAL\\s+|MEMORY\\s+)?" +
                            "(TABLESPACE|DATABASE\\s+LINK|DATABASE|SCHEMA|USER|PROCEDURE|PROC|TABLE|TRIGGER|MATERIALIZED\\s+VIEW|VIEW|FUNCTION|SEQUENCE|INDEX|SYNONYM|ALIAS|COLUMN|EVENT|PACKAGE\\s+BODY|PACKAGE|ROLE|TYPE|CLASS|DOMAIN|CONTEXT|DIRECTORY|DICTIONARY|CONNECTOR|TABLEGROUP|STATISTICS|SAVEPOINT|KSTORE)\\s+" +
                            "(IF\\s+)?(NOT\\s+)?(EXISTS\\s+)?" + "(" + Lexer.STRICT_COMPOSE_NAME + ")" + "(\\s+|$|;)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    String sql = Pattern.compile("--.*?$", Pattern.MULTILINE).matcher(sqlParserModel.getSql()).replaceAll("");
                    Matcher m = p.matcher(sql);
                    if (m.find()) {
                        objectType = m.group(5);
                        if ("proc".equalsIgnoreCase(objectType)) {
                            objectType = "PROCEDURE";
                        } else if ("KSTORE".equalsIgnoreCase(objectType)) {
                            objectType = "TABLE";
                        }
                        objectName = m.group(9);
                        if (objectName.strip().equals(".")) {
                            objectName = "";
                        } else {
                            sqlParserModel.setErrorMessage(null);
                        }

                    }
                }

                //doris : create table xxx like xxx with rollup [(...)]
                if (!isMatched) {
                    String regex = "^\\s*CREATE\\s+TABLE\\s+(.*?)\\s+LIKE\\s+(.*?)\\s+(WITH\\s+ROLLUP.*?)";
                    Pattern p = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(sqlParserModel.getSql());
                    if (m.find()) {
                        String group2 = m.group(2);
                        if (group2 != null) {
                            likeTableName = group2;
                        }
                    }
                }

                //doris : create table table_name [(column_name)] as select...
                if (!isMatched && DatabaseType.DORIS.getValue().equals(dbType)) {
                    String regex = "(?si)^\\s*CREATE\\s+TABLE\\s+(?:([0-9a-zA-Z_]+)|\\`([0-9a-zA-Z_]+)\\`).*?AS\\s+(SELECT.*)";
                    Pattern p = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(sqlParserModel.getSql());
                    if (m.find()) {
                        String select = m.group(3);
                        if (select != null) {
                            DPSqlParser parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.MYSQL.getValue()));
                            parser.setSqltext(select);
                            int ret = parser.parse();
                            if (ret == 0) {
                                DCustomSqlStatement selectStatement = parser.getSqlstatements().get(0);
                                if (selectStatement instanceof TSelectSqlStatement) {
                                    tables.addAll(getTables.analyzeSelectStatement((TSelectSqlStatement) selectStatement));
                                    Object[] objects = getColumns.getObject(selectStatement);
                                    functions.addAll((Set<String>) objects[1]);
                                }
                            }
                        }

                    }
                }

                if (!isMatched) {
                    String pattern = "(ALTER)\\s+(SESSION)\\s+(SET)\\s+(CURRENT_SCHEMA)\\s*(=)\\s*([0-9a-zA-Z_\"'\\[\\]`.:]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = "SCHEMA";
                        objectName = m.group(6);
                        isMatched = true;
                        isChangeSchema = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }

                if (!isMatched) {
                    String pattern = "(RENAME)\\s+(TABLE|DATABASE|USER)\\s+([0-9a-zA-Z_\"'\\[\\]`.:]+)\\s+(TO)\\s+([0-9a-zA-Z_\"'\\[\\]`.:]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(sqlParserModel.getSql());
                    if (m.find()) {
                        objectType = m.group(2);
                        objectName = m.group(3);
                        sqlParserModel.setErrorMessage(null);
                    }
                }

                if (!isMatched) {
                    String pattern = "(SP_RENAME)\\s+([0-9a-zA-Z_\"'\\[\\]`.:]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(sqlParserModel.getSql());
                    if (m.find()) {
                        objectType = "TABLE";
                        objectName = m.group(2);
                        sqlParserModel.setErrorMessage(null);
                    }
                }

                if (!isMatched) {
                    String pattern = "(OPTIMIZE|CHECK)\\s+(TABLE)\\s+([0-9a-zA-Z_\"'\\[\\]`.:]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(sqlParserModel.getSql());
                    if (m.find()) {
                        objectType = m.group(2);
                        objectName = m.group(3);
                        sqlParserModel.setErrorMessage(null);
                    }
                }

                if (!isMatched) {
                    String pattern = "(CREATE)\\s+(COLUMN)\\s+(TABLE)\\s+([0-9a-zA-Z_\"'\\[\\]`.:]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(sqlParserModel.getSql());
                    if (m.find()) {
                        objectType = m.group(3);
                        objectName = m.group(4);
                        sqlParserModel.setErrorMessage(null);
                    }
                }

                if (!isMatched) {
                    String pattern = "(DISABLE|ENABLE)\\s+(TRIGGER)\\s+([0-9a-zA-Z_\"'\\[\\]`.:]+)\\s+(ON)\\s+(DATABASE|ALL\\s+SERVER|[0-9a-zA-Z_\"'\\[\\]`.:]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(sqlParserModel.getSql());
                    if (m.find() && m.group(5) != null) {
                        if ("DATABASE".equalsIgnoreCase(m.group(5))) {
                            objectType = "DATABASE_TRIGGER";
                            objectName = m.group(3);
                        } else if ("ALLSERVER".equalsIgnoreCase(m.group(5).replaceAll(" ", ""))) {
                            objectType = "SERVER_TRIGGER";
                            objectName = m.group(3);
                        } else {
                            objectType = "TABLE";
                            objectName = m.group(5);
                        }
                        sqlParserModel.setErrorMessage(null);
                    }
                }
            }

        } catch (Exception e) {
            logger.info("DDL parse failed. ");
        }

        if (objectType != null && "MATERIALIZEDVIEW".equalsIgnoreCase(objectType.replaceAll(" ", ""))) {
            objectType = "VIEW";
        }

        if (DatabaseType.MYSQL.getValue().equals(dbType) && "VIEW".equalsIgnoreCase(objectType) && SqlConstant.KEY_DROP.equalsIgnoreCase(sqlParserModel.getOperation())) {
            DCustomSqlStatement statement = CommonUtil.getStatement(sqlParserModel.getSql(), DatabaseType.SQL_SERVER.getValue());
            if (statement instanceof TDropViewSqlStatement) {
                TDropViewSqlStatement dropViewSqlStatement = (TDropViewSqlStatement) statement;
                if (dropViewSqlStatement.getViewName() != null) {
                    objectName = dropViewSqlStatement.getViewName().toString();
                }
                if (dropViewSqlStatement.getViewNameList() != null) {
                    for (TObjectName viewName : dropViewSqlStatement.getViewNameList()) {
                        if (viewName != null && !viewName.toString().equalsIgnoreCase(objectName)) {
                            dropObjects.add(viewName.toString());
                        }
                    }
                }
            }
        }

        if (DatabaseType.getIdentCode(DatabaseType.getPGSqlDatabaseTypeList()).contains(dbType) && SqlConstant.KEY_DROP.equals(sqlParserModel.getOperation()) && SqlConstant.KEY_TRIGGER.equalsIgnoreCase(objectType)) {
            String pattern = "(DROP)\\s+(TRIGGER)\\s+(IF\\s+)?(NOT\\s+)?(EXISTS\\s+)?([0-9a-zA-Z_\"'\\[\\]`.:]+)\\s+(ON)\\s+([0-9a-zA-Z_\"'\\[\\]`.:]+)";
            Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(sqlParserModel.getSql());
            if (m.find()) {
                if (m.group(8) != null) {
                    String[] split = m.group(8).split("\\.");
                    String pgSchemaName = "";
                    if (split.length == 2) {
                        pgSchemaName = split[0];
                    } else if (split.length == 3) {
                        pgSchemaName = split[1];
                    }
                    if (!pgSchemaName.isEmpty()) {
                        sqlDdlResultModel.setPgSchemaName(CommonUtil.replace(pgSchemaName));
                    }
                }
            }
        }

        sqlDdlResultModel.setObjectType(objectType);
        sqlDdlResultModel.setObjectName(objectName);
        sqlDdlResultModel.setNewObjectName(newObjectName);
        sqlDdlResultModel.setAsTableName(asTableName);
        sqlDdlResultModel.setLikeTableName(likeTableName);
        sqlDdlResultModel.setAsSelectTables(tables);
        sqlDdlResultModel.setAsSelectFunctions(functions);
        sqlDdlResultModel.setChangeSchema(isChangeSchema);
        sqlDdlResultModel.setCkOperation(ckOperation);
        sqlDdlResultModel.setCkWhere(ckWhere);
        sqlDdlResultModel.setCkUpdateSelectTables(ckTables);
        sqlDdlResultModel.setCkUpdateSelectFunctions(ckFunctions);
        sqlDdlResultModel.setDropObjects(dropObjects);
        sqlDdlResultModel.setTruncateTables(truncateTables);

        return sqlDdlResultModel;
    }

    public static Map<String, Set<String>> getClickHouseQueryObject(UpdateAlterTableClause updateAlterTableClause) {
        Map<String, Set<String>> map = new HashMap<>();

        List<AssignmentExpr> list = updateAlterTableClause.getList();

        if (list != null) {

            getCKTablesUtil getCKTablesUtil = new getCKTablesUtil();
            Set<String> tables = new HashSet<>();
            Set<String> functions = new HashSet<>();

            for (AssignmentExpr expr : list) {
                ColumnExpr exprTemp = expr.getExpr();
                if (exprTemp instanceof SubqueryColumnExpr) {
                    SubqueryColumnExpr subqueryColumnExpr = (SubqueryColumnExpr) exprTemp;
                    SelectUnionQuery query = subqueryColumnExpr.getQuery();
                    if (query != null) {
                        List<SelectStatement> statements = query.getStatements();
                        for (SelectStatement selectStatement : statements) {
                            getCKTablesUtil.analyzeSelectStatement(selectStatement);
                            tables.addAll(getCKTablesUtil.getReturnTables());
                            functions.addAll(getCKTablesUtil.getFunctions());
                        }
                    }
                }
            }

            if (tables.size() > 0) {
                map.put("tables", tables);
            }

            if (functions.size() > 0) {
                map.put("functions", functions);
            }

        }

        return map;
    }
}
