package com.dc.summer.parser.utils.model;

import lombok.Data;

import java.util.HashSet;
import java.util.Set;

@Data
public class SqlDdlResult {

    private String objectType;
    private String objectName;
    private String newObjectName;
    private String asTableName;
    private String likeTableName;
    private String ckOperation;
    private String ckWhere;
    private String pgSchemaName;

    private boolean isChangeSchema;
    private boolean isCreateAS;

    private Set<String> asSelectTables;
    private Set<String> asSelectFunctions;
    private Set<String> ckUpdateSelectTables;
    private Set<String> ckUpdateSelectFunctions;
    private Set<String> dropObjects;
    private Set<String> truncateTables;

    public SqlDdlResult() {
        this.objectType = "";
        this.objectName = "";
        this.newObjectName = "";
        this.asTableName = "";
        this.likeTableName = "";
        this.ckOperation = "";
        this.ckWhere = "";
        this.pgSchemaName = "";
        this.isChangeSchema = false;
        this.isCreateAS = false;
        this.asSelectTables = new HashSet<>();
        this.asSelectFunctions = new HashSet<>();
        this.ckUpdateSelectTables = new HashSet<>();
        this.ckUpdateSelectFunctions = new HashSet<>();
        this.dropObjects = new HashSet<>();
        this.truncateTables = new HashSet<>();
    }
}
