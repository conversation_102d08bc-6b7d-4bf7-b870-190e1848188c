package com.dc.summer.parser.utils;

import com.dc.sqlparser.stmt.*;
import com.dc.stmt.CustomSqlStatement;
import com.dc.stmt.UseSqlStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.utils.model.SqlCallFunctionResult;
import com.dc.summer.parser.utils.model.SqlParseModel;
import com.dc.summer.parser.utils.model.SqlParseObject;
import com.dc.type.DatabaseType;
import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.nodes.TExpression;
import com.dc.sqlparser.nodes.TExpressionList;
import com.dc.sqlparser.stmt.db2.TDb2CallStmt;
import com.dc.sqlparser.stmt.hive.THiveDescribe;
import com.dc.sqlparser.stmt.hive.THiveShow;
import com.dc.sqlparser.stmt.hive.THiveSwitchDatabase;
import com.dc.sqlparser.stmt.mssql.TMssqlExecute;
import com.dc.sqlparser.stmt.mssql.TMssqlExecuteAs;
import com.dc.sqlparser.stmt.mssql.TMssqlSaveTran;
import com.dc.sqlparser.stmt.mysql.TMySQLCallStmt;
import com.dc.sqlparser.stmt.mysql.TShowIndexStmt;
import com.dc.sqlparser.stmt.oracle.TOracleExecuteProcedure;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CallFunctionUtil {

    private static final Logger logger = LoggerFactory.getLogger(CallFunctionUtil.class);

    public static SqlCallFunctionResult getObject(SqlParseModel sqlParserModel, Integer dbType) {
        SqlCallFunctionResult sqlCallFunctionResultModel = new SqlCallFunctionResult();

        DCustomSqlStatement tCustomSqlStatement = sqlParserModel.gettCustomSqlStatement();

        String objectType = "";
        String objectName = "";
        String operation = "";
        boolean isChangeSchema = false;
        boolean hasWhereClause = false;
        List<String> funcArgs = new ArrayList<>();

        try {
            if (tCustomSqlStatement instanceof TMySQLCallStmt) {
                TMySQLCallStmt call = (TMySQLCallStmt) tCustomSqlStatement;
                if (call.getProcedureName() != null) {
                    objectType = call.getProcedureName().getDbObjectType().toString();
                    if ("unknown".equalsIgnoreCase(objectType)) {
                        objectType = "PROCEDURE";
                    }
                    objectName = call.getProcedureName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDb2CallStmt) {
                TDb2CallStmt call = (TDb2CallStmt) tCustomSqlStatement;
                if (call.getProcedureName() != null) {
                    objectType = call.getProcedureName().getDbObjectType().toString();
                    if ("unknown".equalsIgnoreCase(objectType)) {
                        objectType = "PROCEDURE";
                    }
                    objectName = call.getProcedureName().toString();
                }
            } else if (tCustomSqlStatement instanceof TCallStatement) {
                TCallStatement call = (TCallStatement) tCustomSqlStatement;
                if (call.getRoutineName() != null) {
                    if (call.getRoutineName().getDbObjectType() != null) {
                        objectType = "PROCEDURE";
                    }

                    objectName = call.getRoutineName().toString();
                    TExpressionList args = call.getArgs();
                    if (args != null) {
                        for (TExpression expression : args) {
                            if (expression.getConstantOperand() != null) {
                                String s = expression.getConstantOperand().toString();
                                funcArgs.add(s);
                            }
                        }
                    }
                }
            } else if (tCustomSqlStatement instanceof TExecutePreparedStatement) {
                TExecutePreparedStatement execute = (TExecutePreparedStatement) tCustomSqlStatement;
                if (execute.getStatementName() != null) {
                    objectType = execute.getStatementName().getDbObjectType().toString();
                    objectName = execute.getStatementName().toString();
                }
            } else if (tCustomSqlStatement instanceof TOracleExecuteProcedure) {
                TOracleExecuteProcedure execute = (TOracleExecuteProcedure) tCustomSqlStatement;
                if (execute.getProcedureName() != null) {
                    objectType = execute.getProcedureName().getDbObjectType().toString();
                    if ("unknown".equalsIgnoreCase(objectType)) {
                        objectType = "PROCEDURE";
                    }
                    objectName = execute.getProcedureName().toString();
                }
            } else if (tCustomSqlStatement instanceof TMssqlExecute) {
                TMssqlExecute execute = (TMssqlExecute) tCustomSqlStatement;
                if (execute.getModuleName() != null) {
                    objectType = execute.getModuleName().getDbObjectType().toString();
                    objectName = execute.getModuleName().toString();
                }
            } else if (tCustomSqlStatement instanceof TMssqlExecuteAs) {
                TMssqlExecuteAs execute = (TMssqlExecuteAs) tCustomSqlStatement;
                if (execute.getLoginName() != null) {
                    objectType = execute.getLoginName().getDbObjectType().toString();
                    objectName = execute.getLoginName().toString();
                }
            } else if (tCustomSqlStatement instanceof TShowIndexStmt) {
                TShowIndexStmt show = (TShowIndexStmt) tCustomSqlStatement;
                if (show.getTableName() != null) {
                    objectType = show.getTableName().getDbObjectType().toString();
                    objectName = show.getTableName().toString();
                }
            } else if (tCustomSqlStatement instanceof TDescribeStmt) {
                TDescribeStmt desc = (TDescribeStmt) tCustomSqlStatement;
                if (desc.getDbObjectName() != null) {
                    objectName = desc.getDbObjectName().toString();
                    objectType = desc.getDbObjectName().getDbObjectType().toString();
                }
                if (desc.getDbObjectType() != null) {
                    objectType = desc.getDbObjectType().toString();
                }
                String describeFormattedTableName = getDescribeFormattedTableName(tCustomSqlStatement.toString(), dbType);
                if (StringUtils.isNotBlank(describeFormattedTableName)) {
                    objectName = describeFormattedTableName;
                }
            } else if (tCustomSqlStatement instanceof THiveDescribe) {
                THiveDescribe desc = (THiveDescribe) tCustomSqlStatement;
                if (desc.getDbName() != null) {
                    objectName = desc.getDbName().toString();
                    objectType = "database";
                } else if (desc.getTablePartition() != null && desc.getTablePartition().getDescTabType() != null) {
                    objectName = desc.getTablePartition().getDescTabType().toString();
                    objectType = "table";
                }
            } else if (tCustomSqlStatement instanceof THiveShow) {
                THiveShow show = (THiveShow) tCustomSqlStatement;
                if (show.getTableName() != null) {
                    objectName = show.getTableName().toString();
                    objectType = "table";
                } else if (show.getShowIdentifier() != null) {
                    objectName = show.getShowIdentifier().toString();
                    objectType = "table";
                }

            } else if (tCustomSqlStatement instanceof TMssqlSaveTran) {
                TMssqlSaveTran mssqlSaveTran = (TMssqlSaveTran) tCustomSqlStatement;
                if (mssqlSaveTran.getTransactionName() != null) {
                    objectName = mssqlSaveTran.getTransactionName().toString();
                }
                if (mssqlSaveTran.getTrans_or_work() != null) {
                    objectType = mssqlSaveTran.getTrans_or_work().toString();
                }
            } else if (tCustomSqlStatement instanceof TVacuumStmt) {
                TVacuumStmt tVacuumStmt = (TVacuumStmt) tCustomSqlStatement;
                if (tVacuumStmt.getTableName() != null) {
                    objectName = tVacuumStmt.getTableName().toString();
                    objectType = "table";
                }
            }

            if (objectType.isEmpty() || objectName.isEmpty()) {
                boolean isMatched = false;
                if (!isMatched) {
                    String pattern =
                            "(?i)"
                            + "(?<=\\b(?:CALL|EXEC))\\s+"
                            + "([A-Za-z_'\"][A-Za-z0-9_'\"]*"
                            +   "(?:\\.[A-Za-z_'\"][A-Za-z0-9_'\"]*)*\\.?"
                            + ")"
                            + "(?=\\s*\\()";

                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString().replaceAll("(?i)\\s+GO\\s*$", ""));
                    if (m.find()) {
                        objectName = m.group(0).stripLeading();
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
                if (!isMatched) {
                    // 达梦 函数和存储过程
                    String pattern =
                            "(?i)"
                                    + "(CALL|EXEC)\\s+"
                                    + "([A-Za-z_'\"][A-Za-z0-9_'\"]*"
                                    +   "(?:\\.[A-Za-z_'\"][A-Za-z0-9_'\"]*)*"
                                    + ")"
                                    + "\\s*$";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString().replaceAll("(?i)\\s+GO\\s*$", ""));
                    if (m.find()) {
                        objectName = m.group(2);
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
                if (!isMatched) {
                    String pattern = "(SHOW)\\s+(ALL\\s+)?(CREATE)\\s+(TABLESPACE|DATABASE|SCHEMA|USER|PROCEDURE|PROC|TABLE|TRIGGER|VIEW|FUNCTION|SEQUENCE|INDEX|SYNONYM|COLUMN|EVENT|PACKAGE\\s+BODY|PACKAGE)\\s+([0-9a-zA-Z_\"'\\[\\]`.]+)\\s*(FROM)?\\s*([0-9a-zA-Z_\"'\\[\\]`.$]+)?";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString().replaceAll("(?i)\\s+GO\\s*$", ""));
                    if (m.find()) {
                        objectType = m.group(4);
                        if (m.group(7) != null) {
                            objectName = m.group(7) + "." + m.group(5);
                        } else {
                            objectName = m.group(5);
                        }
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
                if (!isMatched) {
                    String pattern = "(SHOW)\\s+(CREATE)\\s+([0-9a-zA-Z_\"'\\[\\]`.]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString().replaceAll("(?i)\\s+GO\\s*$", ""));
                    if (m.find()) {
                        objectType = "TABLE";
                        objectName = m.group(3);
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
                if (!isMatched) {
                    String pattern = "(SHOW)\\s+(FULL\\s+)?(TABLES|COLUMNS|INDEX|TRIGGERS|TABLE\\s+STATUS|FIELDS)\\s+(FROM|IN)\\s+([0-9a-zA-Z_\"'\\[\\]`.$]+)\\s*(FROM|WHERE)?\\s*([0-9a-zA-Z_\"'\\[\\]`.$]+)?";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = m.group(3);
                        if (m.group(7) != null && "FROM".equalsIgnoreCase(m.group(6))) {
                            objectName = m.group(7) + "." + m.group(5);
                        } else {
                            if ("tables".equalsIgnoreCase(objectType)) {
                                objectName = m.group(5) + ".tables";
                            } else if ("TRIGGERS".equalsIgnoreCase(objectType)) {
                                objectName = m.group(5) + ".TRIGGERS";
                            } else if ("TABLESTATUS".equalsIgnoreCase(objectType.replaceAll(" ", ""))) {
                                objectName = m.group(5) + ".table status";
                            } else {
                                objectName = m.group(5);
                            }

                            if ("WHERE".equalsIgnoreCase(m.group(6))) {
                                hasWhereClause = true;
                            }
                        }
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
                if (!isMatched) {
                    String pattern = "(?<=(SHOW|DESC|DESCRIBE))\\s+(DATABASE\\s+)?([0-9a-zA-Z_\"'\\[\\]`.$]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString().replaceAll("(?i)\\s+GO\\s*$", ""));
                    if (m.find()) {
                        objectName = m.group(3);
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                        if (m.group(2) != null && DatabaseType.HETU.getValue().equals(dbType)) {
                            sqlParserModel.getAction().setDescDatabase(true);
                        }
                    }
                }
                if (!isMatched) {
                    String pattern = "(VALUES)\\s+(current)\\s+(date|time)\\s*(-|\\+)\\s*([0-9]+)\\s+(day|days|month|months|year|years)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = "FUNCTION";
                        objectName = m.group(2) + " " + m.group(3) + " " + m.group(4) + " " + m.group(5) + " " + m.group(6);
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                        sqlParserModel.getAction().setSelectValue(true);
                        sqlParserModel.setOperation(SqlConstant.KEY_SELECT);
                    }
                }
                if (!isMatched) {
                    String pattern = "(VALUES)\\s+([0-9a-zA-Z_.]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = "FUNCTION";
                        objectName = m.group(2);
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
                if (!isMatched) {
                    String pattern = "(EXECUTE)\\s+(FUNCTION|PROCEDURE)\\s+([0-9a-zA-Z_.:]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = m.group(2);
                        objectName = m.group(3);
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                        if ("FUNCTION".equalsIgnoreCase(objectType)) {
                            sqlParserModel.getAction().setSelectFunction(true);
                        }
                    }
                }
                // pgsql系切换schema
                if (!isMatched) {
                    String pattern = "(?si)^\\s*(SET)\\s+(SESSION\\s+|LOCAL\\s+)?(SEARCH_PATH)\\s*(TO|=)\\s*([0-9a-zA-Z_\"'\\[\\]`.:]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = "SCHEMA";
                        objectName = m.group(5);
                        isMatched = true;
                        isChangeSchema = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
                // 达梦切换数据库
                if (!isMatched) {
                    String pattern = "(SET)\\s+(CURRENT\\s+)?(SCHEMA)\\s+([0-9a-zA-Z_\"'\\[\\]`.:]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = "SCHEMA";
                        objectName = m.group(4);
                        isMatched = true;
                        isChangeSchema = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
                // db2切换schema
                if (!isMatched) {
                    String pattern = "(SET)\\s+(CURRENT\\s+)?(SCHEMA)\\s*(=)\\s*([0-9a-zA-Z_\"'\\[\\]`.:]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = "SCHEMA";
                        objectName = m.group(5);
                        isMatched = true;
                        isChangeSchema = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
                // db2切换path
                if (!isMatched) {
                    String pattern = "(SET)\\s+(CURRENT\\s+)?(PATH)\\s*(=)\\s*([0-9a-zA-Z_\"'\\[\\]`.:]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = "PATH";
                        objectName = m.group(5);
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
                if (!isMatched) {
                    String pattern = "^(?i)\\s*(SET)\\s+([0-9a-zA-Z_@]+)\\s*(=)\\s*([0-9a-zA-Z_`.]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = "FUNCTION";
                        objectName = m.group(4);
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                        if ("AUTOCOMMIT".equalsIgnoreCase(m.group(2)) && "0".equals(m.group(4))) {
                            sqlParserModel.getAction().setTransactionStatus(1);
                            sqlParserModel.getAction().setForceBackup(false);
                        } else if ("AUTOCOMMIT".equalsIgnoreCase(m.group(2)) && "1".equals(m.group(4))) {
                            sqlParserModel.getAction().setForceBackup(true);
                        }
                    }
                }
                if (!isMatched) {
                    String pattern = "(LOCK)\\s+(TABLE)\\s+([0-9a-zA-Z_\"'\\[\\]`.:]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = "TABLE";
                        objectName = m.group(3);
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
                if (!isMatched) {
                    String pattern = "(BEGIN)\\s+(DISTRIBUTED\\s+)?(TRAN|TRANSACTION)\\s+([0-9a-zA-Z_\"'\\[\\]`.]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = "TRANSACTION";
                        objectName = m.group(4);
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                        List<String> authSqlList = new ArrayList<>();
                        authSqlList.add("BEGINTRAN" + objectName.toUpperCase(Locale.ROOT));
                        authSqlList.add("BEGINTRANSACTION" + objectName.toUpperCase(Locale.ROOT));
                        authSqlList.add("BEGINDISTRIBUTEDTRAN" + objectName.toUpperCase(Locale.ROOT));
                        authSqlList.add("BEGINDISTRIBUTEDTRANSACTION" + objectName.toUpperCase(Locale.ROOT));
                        if (authSqlList.contains(tCustomSqlStatement.toString()
                                .replaceAll("\\s|;", "").toUpperCase(Locale.ROOT))) {
                            sqlParserModel.setPassAuth(true);
                        }
                    }
                }
                if (!isMatched) {
                    String pattern = "(CANCEL)\\s+(JOB)\\s+([0-9a-zA-Z_\"'\\[\\]`.:]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = "JOB";
                        objectName = m.group(3);
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
                if (!isMatched) {
                    String pattern = "(COMPUTE)\\s+(STATS)\\s+([0-9a-zA-Z_\"'\\[\\]`.:]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = "TABLE";
                        objectName = m.group(3);
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
                if (!isMatched) {
                    String pattern = "^(MASTER.)\\s*(MASTER.)\\s*([0-9a-zA-Z_\"'\\[\\]`.:]+)$";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        objectType = SqlConstant.KEY_MSSQL_WORK;
                        objectName = "master.master." + m.group(3);
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
                if (!isMatched && DatabaseType.DORIS.getValue().equals(dbType)) {
                    String pattern = "(?i)^\\s*SET\\s+PASSWORD(\\s+FOR\\s+[^\\s]+)?\\s*=\\s*(?:PASSWORD\\s*\\([^)\\s]+\\) | [^\\s]+)";
                    Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(tCustomSqlStatement.toString());
                    if (m.find()) {
                        isMatched = true;
                        sqlParserModel.setErrorMessage(null);
                    }
                }
            }

            SqlParseObject sqlParserObject = CommonUtil.parserObject(tCustomSqlStatement, dbType);
            if (sqlParserObject != null) {
                objectType = sqlParserObject.getType();
                objectName = sqlParserObject.getName();
                operation = sqlParserObject.getOperation();
            }
        } catch (Exception e) {
            logger.error("getObject failed.");
        }

        sqlCallFunctionResultModel.setObjectType(objectType);
        sqlCallFunctionResultModel.setObjectName(objectName);
        sqlCallFunctionResultModel.setOperation(operation);
        sqlCallFunctionResultModel.setFuncArgs(funcArgs);
        sqlCallFunctionResultModel.setChangeSchema(isChangeSchema);
        sqlCallFunctionResultModel.setHasWhereClause(hasWhereClause);

        return sqlCallFunctionResultModel;
    }

    public static SqlCallFunctionResult getUseObject(SqlParseModel sqlParserModel) {
        SqlCallFunctionResult sqlCallFunctionResultModel = new SqlCallFunctionResult();

        DCustomSqlStatement tCustomSqlStatement = sqlParserModel.gettCustomSqlStatement();
        CustomSqlStatement customSqlStatement = sqlParserModel.getCustomSqlStatement();

        String objectType = "";
        String objectName = "";

        try {
            if (tCustomSqlStatement instanceof TUseDatabase) {
                TUseDatabase useDatabase = (TUseDatabase) tCustomSqlStatement;
                if (useDatabase.getDatabaseName() != null) {
                    objectType = useDatabase.getDatabaseName().getDbObjectType().toString();
                    if ("unknown".equalsIgnoreCase(objectType)) {
                        objectType = "DATABASE";
                    }
                    objectName = useDatabase.getDatabaseName().toString();
                }
            } else if (tCustomSqlStatement instanceof THiveSwitchDatabase) {
                THiveSwitchDatabase useDatabase = (THiveSwitchDatabase) tCustomSqlStatement;
                if (useDatabase.getDbName() != null) {
                    objectType = "DATABASE";
                    objectName = useDatabase.getDbName().toString();
                }
            } else if (customSqlStatement instanceof UseSqlStatement) {
                objectType = "DATABASE";
                objectName = customSqlStatement.getObjectName() != null ?
                        customSqlStatement.getObjectName().getName() != null ? customSqlStatement.getObjectName().getName() : ""
                        : "";
            }

            if (objectType.isEmpty() || objectName.isEmpty()) {

                String pattern = "(?<=(USE|DATABASE))\\s+([0-9a-zA-Z_\"'\\[\\]`.]+)";
                Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(tCustomSqlStatement.toString().replaceAll("(?i)\\s+GO\\s*$", ""));
                if (m.find()) {
                    objectType = "DATABASE";
                    objectName = m.group(2);
                    sqlParserModel.setErrorMessage(null);
                }

            }
        } catch (Exception e) {
            logger.error("getUseObject failed.");
        }

        sqlCallFunctionResultModel.setObjectType(objectType);
        sqlCallFunctionResultModel.setObjectName(objectName);

        return sqlCallFunctionResultModel;
    }

    public static SqlCallFunctionResult getJobObject(SqlParseModel sqlParserModel) {
        SqlCallFunctionResult sqlCallFunctionResultModel = new SqlCallFunctionResult();

        DCustomSqlStatement tCustomSqlStatement = sqlParserModel.gettCustomSqlStatement();

        String objectType = "";
        String objectName = "";

        try {
            String pattern = "(declare)\\s+([0-9a-zA-Z_]+)\\s+(number;)\\s+(begin)\\s+(sys.dbms_job.change\\()([0-9]+)";
            Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(tCustomSqlStatement.toString());
            if (m.find()) {
                objectType = "JOB";
                objectName = m.group(6);
                sqlParserModel.setErrorMessage(null);
            }
        } catch (Exception e) {
            logger.error("getJobObject failed.");
        }

        sqlCallFunctionResultModel.setObjectType(objectType);
        sqlCallFunctionResultModel.setObjectName(objectName);

        return sqlCallFunctionResultModel;
    }

    public static String getDescribeFormattedTableName(String sql, Integer dbType) {
        if (!Arrays.asList(DatabaseType.HIVE.getValue(), DatabaseType.SPARK.getValue()).contains(dbType)) {
            return "";
        }
        try {
            String pattern = "(desc|describe)\\s+(formatted)\\s+([0-9a-zA-Z_\"'\\[\\]`.:]+)";
            Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(sql);
            if (m.find()) {
                return m.group(3);
            }
        } catch (Exception e) {
            logger.error("get describe formatted tableName error!", e);
        }
        return "";
    }

}
