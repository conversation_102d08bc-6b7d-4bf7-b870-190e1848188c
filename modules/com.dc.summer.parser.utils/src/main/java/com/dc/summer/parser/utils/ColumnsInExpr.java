package com.dc.summer.parser.utils;

import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.utils.model.ActionAliasEntry;
import com.dc.summer.parser.sql.model.ActionColumnEntry;
import com.dc.summer.parser.sql.type.ClauseType;
import com.dc.sqlparser.types.EExpressionType;
import com.dc.sqlparser.types.EFunctionType;
import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.nodes.*;

import java.util.ArrayList;
import java.util.List;

public class ColumnsInExpr implements IExpressionVisitor {
    private List<ActionColumnEntry> columns;
    private TExpression expr;
    private GetColumnsUtil impact;
    private int level;
    private DCustomSqlStatement stmt;
    private boolean collectExpr;
    private ClauseType clauseType;
    private ActionAliasEntry parentAlias;

    public ColumnsInExpr(GetColumnsUtil impact, TExpression expr,
                         List<ActionColumnEntry> columns, DCustomSqlStatement stmt, int level,
                         boolean collectExpr, ClauseType clauseType, ActionAliasEntry parentAlias) {
        this.stmt = stmt;
        this.impact = impact;
        this.expr = expr;
        this.columns = columns;
        this.level = level;
        this.collectExpr = collectExpr;
        this.clauseType = clauseType;
        this.parentAlias = parentAlias;
    }

    private void addColumnToList(TParseTreeNodeList list) {
        if (list != null) {
            for (int i = 0; i < list.size(); i++) {
                List<TExpression> exprList = new ArrayList<>();
                Object element = list.getElement(i);

                if (element instanceof TGroupByItem) {
                    exprList.add(((TGroupByItem) element).getExpr());
                    impact.returnFunctions.add(SqlConstant.KEY_GROUP);
                }
                if (element instanceof TOrderByItem) {
                    exprList.add(((TOrderByItem) element).getSortKey());
                    impact.returnFunctions.add(SqlConstant.KEY_ORDER);
                } else if (element instanceof TExpression) {
                    exprList.add((TExpression) element);
                } else if (element instanceof TWhenClauseItem) {
                    exprList.add(((TWhenClauseItem) element)
                            .getComparison_expr());
                    exprList.add(((TWhenClauseItem) element)
                            .getReturn_expr());
                }

                for (TExpression expr : exprList) {
                    if (expr != null) {
                        expr.inOrderTraverse(this);
                    }
                }
            }
        }
    }

    @Override
    public boolean exprVisit(TParseTreeNode pNode, boolean isLeafNode) {
        TExpression lcexpr = (TExpression) pNode;
        if (lcexpr.getExpressionType() == EExpressionType.simple_object_name_t) {
            columns.add(impact.attrToColumn(lcexpr, stmt, expr,
                    collectExpr, clauseType, parentAlias));
        } else if (lcexpr.getExpressionType() == EExpressionType.between_t) {
            columns.add(impact.attrToColumn(lcexpr.getBetweenOperand(),
                    stmt, expr, collectExpr, clauseType, parentAlias));
        } else if (lcexpr.getExpressionType() == EExpressionType.function_t) {
            TFunctionCall func = lcexpr.getFunctionCall();
            if (func.getFunctionName() != null) {
                impact.returnFunctions.add(func.getFunctionName().toString());
            }
            if (func.getFunctionType() == EFunctionType.trim_t) {
                TTrimArgument args = func.getTrimArgument();
                TExpression expr = args.getStringExpression();
                if (expr != null) {
                    expr.inOrderTraverse(this);
                }
                expr = args.getTrimCharacter();
                if (expr != null) {
                    expr.inOrderTraverse(this);
                }
            } else if (func.getFunctionType() == EFunctionType.cast_t) {
                TExpression expr = func.getExpr1();
                if (expr != null) {
                    expr.inOrderTraverse(this);
                }
            } else if (func.getFunctionType() == EFunctionType.convert_t) {
                TExpression expr = func.getExpr1();
                if (expr != null) {
                    expr.inOrderTraverse(this);
                }
                expr = func.getExpr2();
                if (expr != null) {
                    expr.inOrderTraverse(this);
                }
                expr = func.getParameter();
                if (expr != null) {
                    expr.inOrderTraverse(this);
                }
            } else if (func.getFunctionType() == EFunctionType.contains_t
                    || func.getFunctionType() == EFunctionType.freetext_t) {
                TExpression expr = func.getExpr1();
                if (expr != null) {
                    expr.inOrderTraverse(this);
                }
                if (func.getInExpr() != null && func.getInExpr().getExprList() != null) {
                    for (int k = 0; k < func.getInExpr().getExprList().size(); k++) {
                        expr = func.getInExpr().getExprList().getExpression(k);
                        expr.inOrderTraverse(this);
                    }
                    if (expr != null) {
                        expr.inOrderTraverse(this);
                    }
                }
                expr = func.getInExpr() != null ? func.getInExpr().getFunc_expr() : null;
                if (expr != null) {
                    expr.inOrderTraverse(this);
                }
            } else if (func.getFunctionType() == EFunctionType.extractxml_t) {
                TExpression expr = func.getXMLType_Instance();
                if (expr != null) {
                    expr.inOrderTraverse(this);
                }
                expr = func.getXPath_String();
                if (expr != null) {
                    expr.inOrderTraverse(this);
                }
                expr = func.getNamespace_String();
                if (expr != null) {
                    expr.inOrderTraverse(this);
                }
            } else if (func.getFunctionType() == EFunctionType.rank_t) {
                TOrderByItemList orderByList = func.getOrderByList();
                for (int k = 0; k < orderByList.size(); k++) {
                    TExpression expr = orderByList.getOrderByItem(k)
                            .getSortKey();
                    if (expr != null) {
                        expr.inOrderTraverse(this);
                    }
                }
            } else if (func.getArgs() != null) {
                for (int k = 0; k < func.getArgs().size(); k++) {
                    TExpression expr = func.getArgs().getExpression(k);
                    if (expr != null) {
                        expr.inOrderTraverse(this);
                    }
                }
            }
            if (func.getWindowDef() != null) {
                if (func.getWindowDef().getPartitionClause() != null) {
                    TParseTreeNodeList<TExpression> list = func.getWindowDef().getPartitionClause().getExpressionList();
                    addColumnToList(list);
                }
                if (func.getWindowDef().getOrderBy() != null) {
                    TParseTreeNodeList<TOrderByItem> list = func.getWindowDef().getOrderBy()
                            .getItems();
                    addColumnToList(list);
                }
            }
        } else if (lcexpr.getExpressionType() == EExpressionType.subquery_t) {
            impact.impactSqlFromStatement(lcexpr.getSubQuery(), level + 1);
        } else if (lcexpr.getExpressionType() == EExpressionType.case_t) {
            TCaseExpression expr = lcexpr.getCaseExpression();
            TExpression conditionExpr = expr.getInput_expr();
            if (conditionExpr != null) {
                conditionExpr.inOrderTraverse(this);
            }
            TExpression defaultExpr = expr.getElse_expr();
            if (defaultExpr != null) {
                defaultExpr.inOrderTraverse(this);
            }
            TWhenClauseItemList list = expr.getWhenClauseItemList();
            addColumnToList(list);
        }
        return true;
    }

    public void searchColumn() {
        this.expr.inOrderTraverse(this);
    }
}
