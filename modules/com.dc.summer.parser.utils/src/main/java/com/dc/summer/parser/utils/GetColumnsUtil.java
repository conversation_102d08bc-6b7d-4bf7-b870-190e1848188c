package com.dc.summer.parser.utils;

import com.dc.sqlparser.types.EDbType;
import com.dc.sqlparser.types.EExpressionType;
import com.dc.sqlparser.types.ETableSource;
import com.dc.summer.parser.sql.constants.GrantConstant;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.utils.model.ActionAliasEntry;
import com.dc.summer.parser.sql.model.ActionColumnEntry;
import com.dc.summer.parser.utils.model.ActionResultEntry;
import com.dc.summer.parser.sql.model.ActionTableEntry;
import com.dc.summer.parser.sql.type.ClauseType;
import com.dc.summer.parser.sql.type.GrantType;
import com.dc.summer.parser.sql.model.ColumnData;
import com.dc.type.DatabaseType;
import com.dc.sqlparser.*;
import com.dc.sqlparser.nodes.*;
import com.dc.sqlparser.nodes.mysql.TGroupConcatParam;
import com.dc.sqlparser.stmt.TInsertSqlStatement;
import com.dc.sqlparser.stmt.TSelectSqlStatement;
import com.dc.sqlparser.stmt.TUpdateSqlStatement;
import com.dc.utils.CommonUtils;
import org.apache.commons.lang3.StringUtils;

import java.awt.*;
import java.util.List;
import java.util.*;

public class GetColumnsUtil {

    private List<ActionAliasEntry> aliases = new ArrayList<>(); // store the relations of alias to column
    private Map<String, List<ActionResultEntry>> dependMap = new LinkedHashMap<>(); // store the dependency relations
    private Map<String, TCTE> cteMap = new LinkedHashMap<>();
    private Map<String, LinkedHashMap<DCustomSqlStatement, Boolean>> accessMap = new LinkedHashMap<>();
    private Map<DCustomSqlStatement, ClauseType> currentClauseMap = new LinkedHashMap<>();
    private String currentSource = null;
    private DCustomSqlStatement subquery = null;

    private Integer dbType;
    private Set<String> returnColumns = new LinkedHashSet<>();
    public Set<String> returnFunctions = new LinkedHashSet<>();
    public Map<String, Set<String>> statisticsFuncMap = new HashMap<>();
    private Map<String, String> aliasMap = new HashMap<>();
    private Map<String, List<String>> aliasMapBk = new HashMap<>();
    private List<ColumnData> columnDataList = new ArrayList<>();
    private Map<Integer, Boolean> columnExists = new HashMap<>();

    private Set<String> sqlSet = new HashSet<>();

    private List<String> FUNCTIONS = Arrays.asList("COUNT", "SUM", "ROW_NUMBER", "ADDDATE", "SUBDATE", "TIME_FORMAT", "DATE_SUB", "DATE_ADD", "DATE_FORMAT", "SUBSTRING", "EXTRACT", "GROUP_CONCAT");

    private List<String> SUB_FUNCTIONS = Arrays.asList("ADDDATE", "SUBDATE", "TIME_FORMAT", "DATE_SUB", "DATE_ADD", "DATE_FORMAT", "SUBSTRING", "EXTRACT");

    public GetColumnsUtil() {
    }

    public void setDbType(Integer dbType) {
        this.dbType = dbType;
    }

    public Set<String> getFunctions(DCustomSqlStatement tCustomSqlStatement) {
        analyzeSelectStatement(tCustomSqlStatement);
        return returnFunctions;
    }

    public Set<String> getNeedFunctions(DCustomSqlStatement tCustomSqlStatement) {
        Set<String> functions = new LinkedHashSet<>();
        Set<String> oldFunctions = this.getFunctions(tCustomSqlStatement);
        if (oldFunctions.size() > 0) {
            Set<String> allFunctions = new LinkedHashSet<>();
            for (GrantType gt : GrantConstant.FunTarget) {
                allFunctions.add(gt.getValue());
            }
            Set<String> newFunctions = new LinkedHashSet<>(oldFunctions);
            for (String function : newFunctions) {
                if (allFunctions.contains(function.toUpperCase())) {
                    functions.add(function);
                }
            }
        }
        return functions;
    }

    public Object[] getObject(DCustomSqlStatement tCustomSqlStatement) {
        analyzeSelectStatement(tCustomSqlStatement);
        Object[] objects = new Object[5];
        objects[0] = returnColumns;
        objects[1] = returnFunctions;
        objects[2] = aliasMap;
        objects[3] = aliasMapBk;
        objects[4] = columnDataList;

        return objects;
    }

    public void analyzeSelectStatement(DCustomSqlStatement select) {
        if (select != null) {
            dependMap.clear();
            aliases.clear();
            currentSource = null;
            cteMap.clear();
            currentClauseMap.clear();
            accessMap.clear();
            returnColumns.clear();
            returnFunctions.clear();
            statisticsFuncMap.clear();
            aliasMap.clear();
            aliasMapBk.clear();
            columnDataList.clear();
            sqlSet.clear();
            columnExists.clear();

            initCTEMap(select);

            impactSqlFromStatement(select);

            aliases.stream()
                    .filter(CommonUtils.distinctByColumn(
                            actionAliasEntry -> actionAliasEntry.alias,
                            actionAliasEntry -> actionAliasEntry.column,
                            actionAliasEntry -> actionAliasEntry.columnExpr))
                    .forEach(alias -> {
                        List<String> collections = new ArrayList<>();

                        if (dependMap.containsKey(alias.alias)) {

                            List<ActionResultEntry> results = dependMap.get(alias.alias);

                            for (ActionResultEntry result : results) {
                                if (result.columnObject == null
                                        || result.columnObject.columnName == null
                                        || result.clause != ClauseType.select
                                        || result.targetTable.getFullName() == null) {
                                    continue;
                                }

                                String column;

                                if ("*".equals(result.targetColumn)) {
                                    if (result.targetTable.getFullName() == null) {
                                        continue;
                                    }

                                    // tabName.column
                                    String[] columnNames = alias.alias.split("\\.");
                                    String columnName = columnNames[columnNames.length - 1];
                                    // count(*)
                                    if (columnName.indexOf('(') > -1) {
                                        columnName = columnName.substring(columnName.indexOf('(') + 1, columnName.indexOf(')'));
                                        //函数没有参数的情况，跳过
                                        if (columnName.isEmpty()) {
                                            continue;
                                        }
                                    }
                                    // name、b.*
                                    column = removeQuote(result.targetTable.getFullName() + "." + columnName);
                                    // gBase8s/informix的情况
                                    if (CommonUtil.useColonSplit(dbType)) {
                                        column = removeQuote(result.targetTable.getFullName() + ":" + columnName);
                                    }
                                } else {
                                    column = removeQuote(result.targetTable.getFullName() + "." + result.targetColumn);
                                    // gBase8s/informix的情况
                                    if (CommonUtil.useColonSplit(dbType)) {
                                        column = removeQuote(result.targetTable.getFullName() + ":" + result.targetColumn);
                                    }
                                }

                                column = CommonUtil.replace(column);

                                if (!collections.contains(column)) {
                                    collections.add(column);

                                    String splitRegex = CommonUtil.useColonSplit(dbType) ? ":|\\." : "\\.";
                                    String[] split = column.split(splitRegex);

                                    int frameIdx = split.length - 3;
                                    int tabIdx = split.length - 2;
                                    int colIdx = split.length - 1;

                                    if (CommonUtil.useColonSplit(dbType)) {
                                        frameIdx = -1; // gBase8s/informix的情况
                                    }

                                    if (frameIdx >= 0) {
                                        column = split[frameIdx] + "." + split[tabIdx] + "." + split[colIdx];
                                    } else {
                                        column = split[tabIdx] + "." + split[colIdx];
                                    }

                                    if (aliasMap.get(column.toUpperCase()) != null) {
                                        if (aliasMapBk.get(column.toUpperCase()) != null) {
                                            aliasMapBk.get(column.toUpperCase()).add(CommonUtil.replace(alias.alias));
                                        } else {
                                            List<String> list = new ArrayList<>();
                                            list.add(CommonUtil.replace(alias.alias));
                                            aliasMapBk.put(column.toUpperCase(), list);
                                        }
                                    } else {
                                        aliasMap.put(column.toUpperCase(), CommonUtil.replace(alias.alias));
                                    }
                                }

                                // ------之后代码是为平安现场的脱敏准备-------
                                if ("*".equals(alias.alias) && !"*".equals(result.targetColumn)) {
                                    // SELECT * FROM (SELECT id as id1, name as name1 FROM "CY3");
                                    continue;
                                }

                                String aliasName = alias.alias;
                                if (alias.alias != null) {
                                    String[] aliasNames = alias.alias.split("\\.");
                                    String aliasNameTemp = aliasNames[aliasNames.length - 1];
                                    if ("*".equals(aliasNameTemp) && "*".equals(result.targetColumn)) {
                                        // SELECT * FROM (SELECT cy3.*,cy2.* FROM "CY3",cy2);
                                        aliasName = aliasNameTemp;
                                    }
                                }

                                ColumnData columnData = new ColumnData();
                                columnData.setColumnName(CommonUtil.replace(result.targetColumn));
                                columnData.setColumnAlias(CommonUtil.replace(aliasName));
                                if (alias.columnExpr != null && Arrays.asList(EExpressionType.function_t, EExpressionType.concatenate_t).contains(alias.columnExpr.getExpressionType())) {
                                    columnData.setWithinFunc(true);
                                }
                                if (result.targetTable.getFullName() != null) {
                                    String[] targetTable = result.targetTable.getFullName().split("\\.");
                                    columnData.setTableName(CommonUtil.getTransformationTableName(targetTable[targetTable.length - 1], dbType));
                                    columnData.setTable(true);
                                    if (targetTable.length > 1) {
                                        columnData.setSchemaName(CommonUtil.getTransformationTableName(targetTable[targetTable.length - 2], dbType));
                                    }
                                    if (targetTable.length > 2) {
                                        columnData.setCatalogName(CommonUtil.getTransformationTableName(targetTable[targetTable.length - 3], dbType));
                                    }
                                }

                                if (!columnDataList.contains(columnData)) {
                                    columnDataList.add(columnData);
                                }
                            }
                        }

                        List<String> list = new ArrayList<>(collections);
                        returnColumns.addAll(list);
                    });
        }
    }

    private void initCTEMap(DCustomSqlStatement select) {
        if (select.getStatements() != null && select.getStatements().size() > 0) {
            for (int i = 0; i < select.getStatements().size(); i++) {
                initCTEMap(select.getStatements().get(i));
            }
        }
        if (select.getCteList() != null && select.getCteList().size() > 0) {
            for (int i = 0; i < select.getCteList().size(); i++) {
                TCTE expression = select.getCteList().getCTE(i);
                cteMap.put(removeQuote(expression.getTableName().toString()), expression);
            }
        }
    }

    private void impactSqlFromStatement(DCustomSqlStatement select) {


        if (select == null) {
            return;
        }

        String sql = select.toString();

        if (!sqlSet.add(sql)) {
            return;
        }

        if (select instanceof TSelectSqlStatement) {
            TSelectSqlStatement stmt = (TSelectSqlStatement) select;

            ArrayDeque<TSelectSqlStatement> deque = new ArrayDeque<>();
            deque.push(stmt);

            while (!deque.isEmpty()) {
                TSelectSqlStatement current = deque.pop();

                if (current.getOrderbyClause() != null) {
                    returnFunctions.add(SqlConstant.KEY_ORDER);
                    Set<String> tables = new HashSet<>();
                    current.getTables().forEach(t -> tables.add(t.getFullName()));
                    CommonUtil.getFunctions(returnFunctions).forEach(func -> statisticsFuncMap.put(func, tables));
                }
                // 增加DISTINCT支持
                if (current.getSelectDistinct() != null) {
                    returnFunctions.add(SqlConstant.KEY_DISTINCT);
                    Set<String> tables = new HashSet<>();
                    current.getTables().forEach(t -> tables.add(t.getFullName()));
                    for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                        if (statisticsFuncMap.containsKey(staFunc)) statisticsFuncMap.get(staFunc).addAll(tables);
                        else statisticsFuncMap.put(staFunc, tables);
                    }
                }

                if (current.getWhereClause() != null) {
                    if (current.getWhereClause().getCondition() != null) {
                        TExpression expression = current.getWhereClause().getCondition();
                        if (expression.getExpressionType() == EExpressionType.simple_comparison_t) {
                            TExpression rightOperand = expression.getRightOperand();
                            if (rightOperand != null && rightOperand.getExpressionType() == EExpressionType.function_t) {
                                returnFunctions.add(rightOperand.getFunctionCall().getFunctionName().toString());
                            }
                        }
                    }
                }

                if (current.getTables() != null) {
                    current.getTables().forEach(t -> {
                        if (t.getTableType() == ETableSource.function && t.getFuncCall() != null) {
                            returnFunctions.add(t.getFuncCall().getFunctionName().toString());
                        }
                    });
                }

                // 检查是否是叶子节点
                if (current.getLeftStmt() == null && current.getRightStmt() == null) {

                    if (current.getStatements() != null) {
                        for (int i = 0; i < current.getStatements().size(); i++) {
                            impactSqlFromStatement(current.getStatements().get(i));
                        }
                    }

                    if (current.getResultColumnList() != null) {
                        for (int i = 0; i < current.getResultColumnList().size(); i++) {
                            linkFieldToTables(null, current.getResultColumnList().getResultColumn(i), current, 0);
                        }
                    }

                }

                // 先压入右节点，再压入左节点，这样在遍历时会先处理左子树
                if (current.getRightStmt() != null) {
                    deque.push(current.getRightStmt());
                }
                if (current.getLeftStmt() != null) {
                    deque.push(current.getLeftStmt());
                }
            }
        } else if (select instanceof TInsertSqlStatement && ((TInsertSqlStatement) select).getSubQuery() != null) {
            impactSqlFromStatement(((TInsertSqlStatement) select).getSubQuery());
        } else {
            if (select.getStatements() != null) {
                for (int i = 0; i < select.getStatements().size(); i++) {
                    impactSqlFromStatement(select.getStatements().get(i));
                }
            }
            if (select.getResultColumnList() != null) {
                for (int i = 0; i < select.getResultColumnList().size(); i++) {
                    linkFieldToTables(null, select.getResultColumnList().getResultColumn(i), select, 0);
                }
            }
        }
    }

    private boolean linkFieldToTables(ActionAliasEntry parentAlias, TResultColumn field,
                                      DCustomSqlStatement select, int level) {

        int selectCode = select.toString().hashCode();
        int fieldCode = field.toString().hashCode();
        int code = Objects.hash(selectCode, fieldCode);
        if (columnExists.containsKey(code)) {
            return columnExists.get(code);
        }

        if (level == 0) {
            accessMap.clear();
        }
        boolean ret = false;
        // all items in select list was represented by a TLzField Objects
        switch (field.getExpr().getExpressionType()) {
            case simple_object_name_t:
                ActionColumnEntry column = attrToColumn(field.getExpr(), select, ClauseType.select);
                boolean isPseudocolumn = select.dbvendor == EDbType.dbvoracle && this.isPseudocolumn(column.columnName);
                if (level == 0 || parentAlias != null) {
                    ActionAliasEntry alias = null;
                    if (parentAlias != null) {
                        alias = parentAlias;
                    } else {
                        alias = new ActionAliasEntry();
                        alias.column = removeQuote(field.toString());
                        alias.columnExpr = field.getExpr();
                        alias.alias = removeQuote(field.toString());
                        alias.location = new Point(
                                (int) field.getStartToken().lineNo,
                                (int) field.getStartToken().columnNo);
                        if (field.getAliasClause() != null) {
                            alias.alias = removeQuote(field.getAliasClause()
                                    .toString());
                            alias.column = removeQuote(field.toString());
                            alias.columnExpr = field.getExpr();
                            DSourceToken startToken = field.getAliasClause()
                                    .getAliasName().getStartToken();
                            alias.location = new Point((int) startToken.lineNo,
                                    (int) startToken.columnNo);
                        }
                        aliases.add(alias);
                    }
                    currentSource = alias.alias;
                    if (!dependMap.containsKey(currentSource)) {
                        dependMap.put(currentSource, new ArrayList<>());
                    }

                }
                if (isPseudocolumn) {
                    break;
                }
                ret = findColumnInTables(column, select, level + 1, null, null);
                findColumnsFromClauses(select, level + 2);
                break;
            case subquery_t:
                ActionAliasEntry alias1 = new ActionAliasEntry();
                alias1.column = removeQuote(field.toString());
                alias1.columnExpr = field.getExpr();
                alias1.alias = removeQuote(field.toString());
                alias1.location = new Point((int) field.getStartToken().lineNo,
                        (int) field.getStartToken().columnNo);
                if (field.getAliasClause() != null) {
                    alias1.alias = removeQuote(field.getAliasClause().toString());
                    DSourceToken startToken = field.getAliasClause().getAliasName().getStartToken();
                    alias1.column = removeQuote(field.toString());
                    alias1.columnExpr = field.getExpr();
                    alias1.location = new Point((int) startToken.lineNo,
                            (int) startToken.columnNo);
                }

                if (level == 0) {
                    aliases.add(alias1);
                }
                TSelectSqlStatement stmt = field.getExpr().getSubQuery();
                List<TSelectSqlStatement> stmtList = new ArrayList<>();
                getSelectSqlStatements(stmt, stmtList);
                for (TSelectSqlStatement tSelectSqlStatement : stmtList) {
                    linkFieldToTables(alias1, tSelectSqlStatement.getResultColumnList()
                            .getResultColumn(0), tSelectSqlStatement, Math.max(level + 1, 0));
                }
                break;
            default:
                ActionAliasEntry alias = parentAlias;
                if (level == 0) {
                    alias = new ActionAliasEntry();

                    if (select instanceof TUpdateSqlStatement) {
                        TExpression expression = field.getExpr().getLeftOperand();
                        alias.column = removeQuote(expression.toString());
                        alias.columnExpr = expression;
                        alias.alias = alias.column;
                        alias.location = new Point(
                                (int) expression.getStartToken().lineNo,
                                (int) expression.getStartToken().columnNo);
                    } else {
                        alias.column = removeQuote(field.toString());
                        alias.columnExpr = field.getExpr();
                        alias.alias = alias.column;
                        alias.location = new Point(
                                (int) field.getStartToken().lineNo,
                                (int) field.getStartToken().columnNo);

                    }
                    if (parentAlias == null) {
                        if (field.getAliasClause() != null) {
                            alias.alias = removeQuote(field.getAliasClause().toString());
                            alias.column = removeQuote(field.toString());
                            alias.columnExpr = field.getExpr();
                            DSourceToken startToken = field.getAliasClause().getAliasName().getStartToken();
                            alias.location = new Point((int) startToken.lineNo,
                                    (int) startToken.columnNo);
                        }
                        aliases.add(alias);

                        currentSource = alias.alias;
                        if (!dependMap.containsKey(currentSource)) {
                            dependMap.put(currentSource, new ArrayList<ActionResultEntry>());
                        }
                    }
                }

                List<ActionColumnEntry> columns = exprToColumn(field.getExpr(), select,
                        level, true, ClauseType.select, alias);
                if (select instanceof TUpdateSqlStatement) {
                    while (columns.size() > 1) {
                        columns.remove(columns.size() - 1);
                    }
                }

                for (ActionColumnEntry column1 : columns) {
                    if (column1 == null) {
                        continue;
                    }

                    if (!(select instanceof TUpdateSqlStatement)) {
                        findColumnInTables(column1, select, level + 1, null, null);
                    }
                    findColumnsFromClauses(select, level + 2);
                }

                if (field.getExpr().getExpressionType() == EExpressionType.function_t) {
                    TFunctionCall func = field.getExpr().getFunctionCall();
                    returnFunctions.add(func.getFunctionName().toString());
                    select.getTargetTable();
                    Set<String> tables = new HashSet<>();
                    select.getTables().forEach(t -> tables.add(t.getFullName()));
                    String funcName = func.getFunctionName().toString();
                    if (CommonUtil.getDCSupportFunctions().contains(funcName.toUpperCase(Locale.ROOT))) {
                        if (statisticsFuncMap.containsKey(funcName)) statisticsFuncMap.get(funcName).addAll(tables);
                        else statisticsFuncMap.put(funcName, tables);
                    }

                    if (FUNCTIONS.contains(func.getFunctionName().toString().toUpperCase(Locale.ROOT))) {
                        // check column in function arguments
                        int argCount = 0;
                        if (func.getArgs() != null) {
                            for (int k = 0; k < func.getArgs().size(); k++) {
                                TExpression expr = func.getArgs().getExpression(k);
                                if (expr.toString().trim().equals("*")) {
                                    continue;
                                }
                                List<ActionColumnEntry> columns1 = exprToColumn(expr, select,
                                        level + 1, ClauseType.select, parentAlias);
                                for (ActionColumnEntry column1 : columns1) {
                                    findColumnInTables(column1, select, level + 1,
                                            null, null);
                                    findColumnsFromClauses(select, level + 2);
                                }
                                argCount++;
                            }
                        }

                        if (argCount == 0 && SUB_FUNCTIONS.contains(func.getFunctionName().toString().toUpperCase(Locale.ROOT))) {
                            // adddate(t1,interval 5 day)、subdate(t1,2)、time_FORMAT(t2,'%h-%m-%s %r')
                            TExpression expr1 = func.getExpr1();
                            if (expr1 != null) {
                                List<ActionColumnEntry> columns1 = exprToColumn(expr1, select,
                                        level + 1, ClauseType.select, parentAlias);
                                for (ActionColumnEntry column1 : columns1) {
                                    findColumnInTables(column1, select, level + 1,
                                            null, null);
                                    findColumnsFromClauses(select, level + 2);
                                }
                                argCount++;
                            }
                        }

                        if (argCount == 0 && "GROUP_CONCAT".equals(func.getFunctionName().toString().toUpperCase(Locale.ROOT))) {
                            TGroupConcatParam groupConcatParam = func.getGroupConcatParam();
                            if (groupConcatParam != null && groupConcatParam.getExprList() != null) {
                                TExpressionList exprList = groupConcatParam.getExprList();
                                for (TExpression expr1 : exprList) {
                                    if (expr1 != null) {
                                        List<ActionColumnEntry> columns1 = exprToColumn(expr1, select,
                                                level + 1, ClauseType.select, parentAlias);
                                        for (ActionColumnEntry column1 : columns1) {
                                            findColumnInTables(column1, select, level + 1,
                                                    null, null);
                                            findColumnsFromClauses(select, level + 2);
                                        }
                                        argCount++;
                                    }
                                }
                            }
                        }

                        if (argCount == 0 && !"ROW_NUMBER".equalsIgnoreCase(func.getFunctionName().toString())) {

                            Point point = new Point(
                                    (int) func.getEndToken().lineNo,
                                    (int) func.getEndToken().columnNo);
                            if (func.getArgs() != null && func.getArgs().size() > 0) {
                                for (int k = 0; k < func.getArgs().size(); k++) {
                                    TExpression expr = func.getArgs().getExpression(k);
                                    if ("*".equals(expr.toString().trim())) {
                                        point = new Point(
                                                (int) expr.getStartToken().lineNo,
                                                (int) expr.getStartToken().columnNo);
                                        break;
                                    }
                                }
                            }
                            if (dependMap.containsKey(currentSource)) {

                                if (currentClauseMap.containsKey(select)) {
                                    dependMap.get(currentSource).add(
                                            new ActionResultEntry(select.tables.getTable(0), "*",
                                                    (ClauseType) currentClauseMap.get(select), point));
                                } else if (select instanceof TSelectSqlStatement) {
                                    dependMap.get(currentSource).add(
                                            new ActionResultEntry(select.tables.getTable(0), "*",
                                                    ClauseType.select, point));
                                } else {
                                    dependMap.get(currentSource).add(
                                            new ActionResultEntry(select.tables.getTable(0), "*",
                                                    ClauseType.undefine, point));
                                }
                            }
                        }

                        findColumnsFromClauses(select, level + 2);
                    }
                } else if (field.getExpr().getExpressionType() == EExpressionType.list_t) {
                    TExpressionList exprList = field.getExpr().getExprList();
                    for (TExpression expr : exprList) {
                        if ("*".equals(expr.toString().trim())) {
                            continue;
                        }
                        List<ActionColumnEntry> columns1 = exprToColumn(expr, select,
                                level + 1, ClauseType.select, parentAlias);
                        for (ActionColumnEntry column1 : columns1) {
                            findColumnInTables(column1, select, level + 1,
                                    null, null);
                            findColumnsFromClauses(select, level + 2);
                        }
                    }
                    /*findColumnsFromClauses(select, level + 2);*/

                }
                break;
        }

        columnExists.put(code, ret);
        return ret;
    }

    private void getSelectSqlStatements(TSelectSqlStatement select, List<TSelectSqlStatement> stmtList) {
        if (select.getLeftStmt() != null || select.getRightStmt() != null) {
            if (select.getLeftStmt() != null) {
                getSelectSqlStatements(select.getLeftStmt(), stmtList);
            }
            if (select.getRightStmt() != null) {
                getSelectSqlStatements(select.getRightStmt(), stmtList);
            }
        } else {
            stmtList.add(select);
        }
    }

    public ActionColumnEntry attrToColumn(TExpression lcexpr, DCustomSqlStatement stmt,
                                          TExpression expr, boolean collectExpr, ClauseType clause,
                                          ActionAliasEntry parentAlias) {
        ActionColumnEntry column = attrToColumn(lcexpr, stmt, clause);
        if (collectExpr) {
            column.expression = expr.toString().replace("\r\n", "\n").replaceAll("\n+", " ");
            if (column.expression.trim().length() > 0) {
                Stack<TParseTreeNode> tokens = expr.getStartToken().getNodesStartFromThisToken();
                if (tokens != null) {
                    for (int i = 0; i < tokens.size(); i++) {
                        TParseTreeNode node = tokens.get(i);
                        if (node instanceof TResultColumn) {
                            TResultColumn field = (TResultColumn) node;
                            if (field.getAliasClause() != null) {
                                column.alias = field.getAliasClause().toString();
                            }
                        }
                    }
                }
            }
        }
        return column;
    }


    private ActionColumnEntry attrToColumn(TExpression attr, DCustomSqlStatement stmt, ClauseType clauseType) {

        ActionColumnEntry column = new ActionColumnEntry();
        column.clauseType = clauseType;
        if (attr.getObjectOperand() == null) {
            return column;
        }
        column.columnName = removeQuote(attr.getObjectOperand().getEndToken().toString());
        column.location = new Point(
                (int) attr.getObjectOperand().getEndToken().lineNo,
                (int) attr.getEndToken().columnNo);

        Stack<TParseTreeNode> tokens = attr.getObjectOperand().getStartToken().getNodesStartFromThisToken();
        if (tokens != null) {
            for (int i = 0; i < tokens.size(); i++) {
                TParseTreeNode node = tokens.get(i);
                if (node instanceof TResultColumn) {
                    TResultColumn field = (TResultColumn) node;
                    if (field.getAliasClause() != null) {
                        column.alias = field.getAliasClause().toString();
                    }
                }
            }
        }

        if (attr.toString().indexOf(".") > 0) {
            column.columnPrex = removeQuote(attr.toString().substring(0, attr.toString().lastIndexOf(".")));

            String tableName = removeQuote(column.columnPrex);
            if (tableName.indexOf(".") > 0) {
                tableName = removeQuote(tableName.substring(tableName.lastIndexOf(".") + 1));
            }
            if (tableName != null) {
                if (!column.tableNames.contains(tableName)) {
                    column.tableNames.add(tableName);
                    if (!column.tableFullNames.contains(tableName)) {
                        column.tableFullNames.add(tableName);
                    }
                }
            }
        } else {
            TTableList tables = stmt.tables;
            for (int i = 0; i < tables.size(); i++) {
                TTable lztable = tables.getTable(i);
                ActionTableEntry table = TLzTaleToTable(lztable);
                if (table.tableName != null) {
                    if (!column.tableNames.contains(table.tableName)) {
                        column.tableNames.add(table.tableName);
                        if (!column.tableFullNames.contains(lztable.getFullName())) {
                            column.tableFullNames.add(lztable.getFullName());
                        }
                    }
                }
            }
        }

        column.orignColumn = column.columnName;

        return column;
    }


    private DCustomSqlStatement containClause(Map<DCustomSqlStatement, ClauseType> currentClauseMap, DCustomSqlStatement select) {
        if (currentClauseMap.containsKey(select)) {
            return select;
        } else if (select.getParentStmt() != null) {
            return containClause(currentClauseMap, select.getParentStmt());
        } else {
            return null;
        }
    }

    private List<ActionColumnEntry> exprToColumn(TExpression expr, DCustomSqlStatement stmt, int level, ClauseType clauseType) {
        List<ActionColumnEntry> columns = new ArrayList<>();

        ColumnsInExpr c = new ColumnsInExpr(this, expr, columns, stmt, level,
                false, clauseType, null);
        c.searchColumn();

        return columns;
    }

    private List<ActionColumnEntry> exprToColumn(TExpression expr, DCustomSqlStatement stmt, int level, ClauseType clauseType,
                                                 ActionAliasEntry parentAlias) {
        List<ActionColumnEntry> columns = new ArrayList<>();

        ColumnsInExpr c = new ColumnsInExpr(this, expr, columns, stmt, level,
                false, clauseType, parentAlias);
        c.searchColumn();

        return columns;
    }

    private List<ActionColumnEntry> exprToColumn(TExpression expr, DCustomSqlStatement stmt, int level, boolean collectExpr,
                                                 ClauseType clauseType, ActionAliasEntry parentAlias) {
        List<ActionColumnEntry> columns = new ArrayList<>();

        ColumnsInExpr c = new ColumnsInExpr(this, expr, columns, stmt, level,
                collectExpr, clauseType, parentAlias);
        c.searchColumn();

        return columns;
    }

    private String getTableAliasName(TTable lzTable) {
        return removeQuote(lzTable.getAliasClause().getAliasName().toString());
    }

    private String getTableName(TTable lzTable) {
        return removeQuote(lzTable.getName());
    }

    private boolean findColumnInSubQuery(TObjectNameList columnList, TSelectSqlStatement select,
                                         String columnName, int level, Point originLocation) {

        int selectCode = select.toString().hashCode();
        int columnCode = columnName.hashCode();
        int code = Objects.hash(selectCode, columnCode);

        if (columnExists.containsKey(code)) {
            return columnExists.get(code);
        }

        boolean ret = false;
        if (accessMap.get(columnName) != null && accessMap.get(columnName).containsKey(select)) {
            return accessMap.get(columnName).get(select);
        } else {
            if (!accessMap.containsKey(columnName)) {
                accessMap.put(columnName, new LinkedHashMap<DCustomSqlStatement, Boolean>());
            }
            Map<DCustomSqlStatement, Boolean> stmts = accessMap.get(columnName);
            stmts.put(select, false);
        }
        if (select.getLeftStmt() != null || select.getRightStmt() != null) {
            boolean left = false;
            boolean right = false;
            if (select.getLeftStmt() != null) {
                left = findColumnInSubQuery(columnList, select.getLeftStmt(), columnName, level, originLocation);
            }
            if (select.getRightStmt() != null) {
                right = findColumnInSubQuery(columnList, select.getRightStmt(), columnName, level, originLocation);
            }

            ret = left && right;
        } else if (select.getResultColumnList() != null) {
            // check column name in select list of subQuery
            TResultColumn columnField = null;
            if (!"*".equals(columnName)) {
                for (int i = 0; i < select.getResultColumnList().size(); i++) {
                    TResultColumn field = select.getResultColumnList().getResultColumn(i);
                    if (field.getAliasClause() != null) {
                        if (field.getAliasClause().toString().equalsIgnoreCase(columnName)) {
                            columnField = field;
                            break;
                        }
                    } else {
                        if (field.getExpr().getExpressionType() == EExpressionType.simple_object_name_t) {
                            ActionColumnEntry column = attrToColumn(field.getExpr(), select, ClauseType.select);
                            if (columnName != null && columnName.equalsIgnoreCase(column.columnName)) {
                                columnField = field;
                                break;
                            }
                        }
                    }
                }
            }
            for (int i = 0; i < select.getResultColumnList().size(); i++) {
                TResultColumn field = select.getResultColumnList().getResultColumn(i);
                if (columnField != null && !field.equals(columnField)) {
                    continue;
                }
                if (columnList != null) {
                    ActionAliasEntry alias = new ActionAliasEntry();
                    alias.column = removeQuote(field.toString());
                    alias.columnExpr = field.getExpr();
                    alias.alias = removeQuote(columnList.getObjectName(i).toString());
                    alias.location = new Point(
                            (int) field.getStartToken().lineNo,
                            (int) field.getStartToken().columnNo);
                    aliases.add(alias);
                    linkFieldToTables(alias, select.getResultColumnList().getResultColumn(i), select, level + 1);
                } else if (field.getAliasClause() != null) {
                    ret = "*".equals(columnName)
                            || field.getAliasClause().toString().equalsIgnoreCase(columnName);
                    if (ret) {
                        // check where this column come from
                        linkFieldToTables(null, field, select, level);
                    }
                } else {
                    if (field.getExpr().getExpressionType() == EExpressionType.simple_object_name_t) {
                        ActionColumnEntry column = attrToColumn(field.getExpr(), select, ClauseType.select);
                        ret = "*".equals(columnName)
                                || (columnName != null && columnName.equalsIgnoreCase(column.columnName));
                        if (ret || "*".equals(column.columnName)) {
                            findColumnInTables(column, select, level, !ret ? columnName : null, originLocation);
                            findColumnsFromClauses(select, level + 1);
                        }
                    }
                }

                if (ret && !"*".equals(columnName)) {
                    break;
                }
            }
        }

        Map<DCustomSqlStatement, Boolean> stmts = accessMap.get(columnName);
        if (stmts != null) {
            stmts.put(select, ret);
        }

        columnExists.put(code, ret);
        return ret;
    }

    private boolean findColumnInTables(ActionColumnEntry column, String tableName,
                                       DCustomSqlStatement select, int level) {
        return findColumnInTables(column, tableName, select, level, ClauseType.undefine);
    }

    private boolean findColumnInTables(ActionColumnEntry column, String tableName,
                                       DCustomSqlStatement select, int level, ClauseType clause) {

        int selectCode = select.toString().hashCode();
        int columnCode = column.toString().hashCode();
        int code = Objects.hash(selectCode, columnCode);
        if (columnExists.containsKey(code)) {
            return columnExists.get(code);
        }

        boolean ret = false;
        TTableList tables = select.tables;

        if (tables.size() == 1) {
            TTable lzTable = tables.getTable(0);
            if ((lzTable.getTableType() == ETableSource.objectname)
                    && (tableName == null
                    || (lzTable.getAliasClause() == null
                    && getTableName(lzTable).equalsIgnoreCase(tableName))
                    || (lzTable.getAliasClause() != null
                    && lzTable.getAliasClause().toString().equalsIgnoreCase(tableName)))) {
                ret = true;

                if (cteMap.containsKey(getTableName(lzTable))) {
                    TCTE tcte = cteMap.get(getTableName(lzTable));
                    ret = findColumnInSubQuery(tcte.getColumnList(), tcte.getSubquery(),
                            column.columnName, level, column.location);
                } else {
                    if (currentSource != null && dependMap.containsKey(currentSource)) {
                        DCustomSqlStatement stmt = containClause(currentClauseMap, select);
                        if (stmt != null) {
                            dependMap.get(currentSource)
                                    .add(new ActionResultEntry(lzTable, column,
                                            column.columnName, currentClauseMap.get(stmt), column.location));
                        } else if (select instanceof TSelectSqlStatement) {
                            if (ClauseType.undefine.equals(clause)) {
                                dependMap.get(currentSource).add(
                                        new ActionResultEntry(lzTable, column,
                                                column.columnName,
                                                ClauseType.select,
                                                column.location));
                            } else {
                                dependMap.get(currentSource).add(
                                        new ActionResultEntry(lzTable, column,
                                                column.columnName, clause,
                                                column.location));
                            }
                        } else {
                            dependMap.get(currentSource).add(
                                    new ActionResultEntry(lzTable, column,
                                            column.columnName,
                                            ClauseType.undefine,
                                            column.location));
                        }
                    }
                }
            } else if (select.getParentStmt() instanceof TSelectSqlStatement) {
                subquery = select;
                ret = findColumnInTables(column, tableName, select.getParentStmt(), level, clause);
                subquery = null;
            }
        }

        if (ret) {
            return ret;
        }

        for (int x = 0; x < tables.size(); x++) {
            TTable lzTable = tables.getTable(x);
            switch (lzTable.getTableType()) {
                case objectname:
                    ActionTableEntry table = TLzTaleToTable(lzTable);
                    String alias = table.tableAlias;
                    if (alias != null) {
                        alias = alias.trim();
                    }
                    boolean tableNameEquals = false;
                    if (dbType != null && DatabaseType.get3FDatabaseIntegerValueList().contains(dbType) && tableName != null && table.tableName != null) {
                        String[] objectNameFirst = tableName.split("\\.");
                        String[] objectNameSecond = table.tableName.split("\\.");

                        if (objectNameFirst.length == 1 && objectNameFirst[objectNameFirst.length - 1].equalsIgnoreCase(objectNameSecond[objectNameSecond.length - 1])) {
                            tableNameEquals = true;
                        }
                    }
                    if ((tableName != null)
                            && ((tableName.equalsIgnoreCase(alias) || tableName.equalsIgnoreCase(table.tableName) || tableNameEquals))) {
                        if (cteMap.containsKey(getTableName(lzTable))) {
                            TCTE tcte = cteMap.get(getTableName(lzTable));
                            ret = findColumnInSubQuery(
                                    tcte.getColumnList(),
                                    tcte.getSubquery(),
                                    column.columnName, level, column.location);
                        } else {
                            if (dependMap.containsKey(currentSource)) {
                                String columnName = column.orignColumn;
                                if ("*".equals(columnName)) {
                                    columnName = column.columnName;
                                }
                                if (currentClauseMap.containsKey(select)) {
                                    dependMap.get(currentSource).add(
                                            new ActionResultEntry(lzTable, column,
                                                    columnName,
                                                    currentClauseMap.get(select),
                                                    column.location));
                                } else if (select instanceof TSelectSqlStatement) {
                                    if (ClauseType.undefine.equals(clause)) {
                                        dependMap.get(currentSource).add(
                                                new ActionResultEntry(lzTable, column,
                                                        column.columnName,
                                                        ClauseType.select,
                                                        column.location));
                                    } else {
                                        dependMap.get(currentSource).add(
                                                new ActionResultEntry(lzTable, column,
                                                        column.columnName, clause,
                                                        column.location));
                                    }
                                } else {
                                    dependMap.get(currentSource).add(
                                            new ActionResultEntry(lzTable, column,
                                                    columnName,
                                                    ClauseType.undefine,
                                                    column.location));
                                }
                            }
                            ret = true;
                        }
                    }
                    break;
                case subquery:
                    if (column.tableNames.isEmpty()) {
                        ret = findColumnInSubQuery(null,
                                lzTable.getSubquery(),
                                column.columnName,
                                level,
                                column.location);
                    } else {
                        for (int i = 0; i < column.tableNames.size(); i++) {
                            String name = column.tableNames.get(i);
                            TSelectSqlStatement selectStat = lzTable.getSubquery();

                            if (selectStat == subquery) {
                                continue;
                            }
                            if (name == null) {
                                ret = findColumnInSubQuery(null,
                                        selectStat,
                                        column.columnName,
                                        level,
                                        column.location);
                                break;
                            }

                            if (lzTable.getAliasClause() != null
                                    && getTableAliasName(lzTable).equalsIgnoreCase(name)) {
                                ret = findColumnInSubQuery(null,
                                        selectStat,
                                        column.columnName,
                                        level,
                                        column.location);
                                break;
                            }

                            boolean flag = false;
                            for (int j = 0; j < selectStat.tables.size(); j++) {
                                if (selectStat.tables.getTable(j).getAliasClause() != null) {
                                    if (getTableAliasName(
                                            selectStat.tables.getTable(j)).equalsIgnoreCase(name)) {
                                        ret = findColumnInSubQuery(null,
                                                selectStat,
                                                column.columnName,
                                                level,
                                                column.location);
                                        flag = true;
                                        break;
                                    }
                                } else {
                                    if (selectStat.tables.getTable(j).getTableName()
                                            .toString().equalsIgnoreCase(name)) {
                                        ret = findColumnInSubQuery(null,
                                                selectStat,
                                                column.columnName,
                                                level,
                                                column.location);
                                        flag = true;
                                        break;
                                    }
                                }
                            }
                            if (flag) {
                                break;
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
            if (ret) {
                break;
            }
        }

        if (!ret && select.getParentStmt() instanceof TSelectSqlStatement) {
            subquery = select;
            ret = findColumnInTables(column, tableName, select.getParentStmt(), level, clause);
            subquery = null;
        }

        columnExists.put(code, ret);
        return ret;
    }

    private boolean findColumnInTables(ActionColumnEntry column,
                                       DCustomSqlStatement select, int level, String columnName,
                                       Point originLocation) {

        int selectCode = select.toString().hashCode();
        int columnCode = column.toString().hashCode();
        int code = Objects.hash(selectCode, columnCode);
        if (columnExists.containsKey(code)) {
            return columnExists.get(code);
        }

        boolean ret = false;
        if (column.tableNames.isEmpty()) {
            ret = findColumnInTables(column,
                    null, select, level);
        } else {
            for (String tableName : column.tableNames) {
                if (columnName != null) {
                    column.columnName = columnName;
                }
                ret |= findColumnInTables(column,
                        tableName,
                        select,
                        level);
            }
        }

        columnExists.put(code, ret);
        return ret;
    }

    private void findColumnsFromClauses(DCustomSqlStatement select, int level) {

        currentClauseMap.put(select, ClauseType.undefine);
        Map<TExpression, ClauseType> clauseTable = new LinkedHashMap<>();
        if (select instanceof TSelectSqlStatement) {

            TSelectSqlStatement statement = (TSelectSqlStatement) select;

            if (statement.getOrderbyClause() != null) {
                TOrderBy sortList = statement.getOrderbyClause();
                returnFunctions.add(SqlConstant.KEY_ORDER);
                Set<String> tables = new HashSet<>();
                statement.getTables().forEach(t -> tables.add(t.getFullName()));
                for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                    if (statisticsFuncMap.containsKey(staFunc)) statisticsFuncMap.get(staFunc).addAll(tables);
                    else statisticsFuncMap.put(staFunc, tables);
                }
                for (int i = 0; i < sortList.getItems().size(); i++) {
                    TOrderByItem orderBy = sortList.getItems().getOrderByItem(i);
                    TExpression expr = orderBy.getSortKey();
                    clauseTable.put(expr, ClauseType.orderby);
                }
            }

            if (statement.getWhereClause() != null) {
                clauseTable.put(statement.getWhereClause().getCondition(), ClauseType.where);
            }
            if (statement.getHierarchicalClause() != null
                    && statement.getHierarchicalClause().getConnectByList() != null) {
                for (int i = 0; i < statement.getHierarchicalClause()
                        .getConnectByList().size(); i++) {
                    clauseTable.put(statement.getHierarchicalClause()
                            .getConnectByList().getElement(i).getCondition(), ClauseType.connectby);
                }
            }
            if (statement.getHierarchicalClause() != null
                    && statement.getHierarchicalClause().getStartWithClause() != null) {
                clauseTable.put(statement.getHierarchicalClause()
                        .getStartWithClause(), ClauseType.startwith);
            }
            if (statement.joins != null) {
                for (int i = 0; i < statement.joins.size(); i++) {
                    TJoin join = statement.joins.getJoin(i);
                    if (join.getJoinItems() != null) {
                        for (int j = 0; j < join.getJoinItems().size(); j++) {
                            TJoinItem joinItem = join.getJoinItems().getJoinItem(j);
                            TExpression expr = joinItem.getOnCondition();
                            if (expr != null) {
                                clauseTable.put(expr, ClauseType.join);
                            }
                        }
                    }
                }
            }
        } else if (select instanceof TUpdateSqlStatement) {
            TUpdateSqlStatement statement = (TUpdateSqlStatement) select;
            if (statement.getOrderByClause() != null) {
                returnFunctions.add(SqlConstant.KEY_ORDER);
                Set<String> tables = new HashSet<>();
                statement.getTables().forEach(t -> tables.add(t.getFullName()));
                for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                    if (statisticsFuncMap.containsKey(staFunc)) statisticsFuncMap.get(staFunc).addAll(tables);
                    else statisticsFuncMap.put(staFunc, tables);
                }
                TOrderByItemList sortList = statement.getOrderByClause().getItems();
                for (int i = 0; i < sortList.size(); i++) {
                    TOrderByItem orderBy = sortList.getOrderByItem(i);
                    TExpression expr = orderBy.getSortKey();
                    clauseTable.put(expr, ClauseType.orderby);
                }
            }
            if (statement.getWhereClause() != null) {
                clauseTable.put(statement.getWhereClause().getCondition(), ClauseType.where);
            }

            if (statement.joins != null) {
                for (int i = 0; i < statement.joins.size(); i++) {
                    TJoin join = statement.joins.getJoin(i);
                    if (join.getJoinItems() != null) {
                        for (int j = 0; j < join.getJoinItems().size(); j++) {
                            TJoinItem joinItem = join.getJoinItems().getJoinItem(j);
                            TExpression expr = joinItem.getOnCondition();
                            if (expr != null) {
                                clauseTable.put(expr, ClauseType.join);
                            }
                        }
                    }
                }
            }
        }

        for (TExpression expr : clauseTable.keySet()) {
            currentClauseMap.put(select, clauseTable.get(expr));

            List<ActionColumnEntry> columns = exprToColumn(expr, select, level,
                    clauseTable.get(expr));
            for (ActionColumnEntry column1 : columns) {
                for (String tableName : column1.tableNames) {
                    findColumnInTables(column1, tableName, select, level + 2, column1.clauseType);
                }

            }
        }
        currentClauseMap.remove(select);

        // check order by clause
        findColumnsFromGroupBy(select, level);
    }

    private void findColumnsFromGroupBy(DCustomSqlStatement select, int level) {
        if (select instanceof TSelectSqlStatement
                && ((TSelectSqlStatement) select).getGroupByClause() != null) {
            returnFunctions.add(SqlConstant.KEY_GROUP);
            Set<String> tables = new HashSet<>();
            TSelectSqlStatement statement = ((TSelectSqlStatement) select);
            statement.getTables().forEach(t -> tables.add(t.getFullName()));
            for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                if (statisticsFuncMap.containsKey(staFunc)) statisticsFuncMap.get(staFunc).addAll(tables);
                else statisticsFuncMap.put(staFunc, tables);
            }
            if (((TSelectSqlStatement) select).getGroupByClause().getHavingClause() != null) {
                TExpression having = ((TSelectSqlStatement) select).getGroupByClause().getHavingClause();
                exprToColumn(having, select, level, ClauseType.groupby);
            }
            for (int j = 0; j < ((TSelectSqlStatement) select).getGroupByClause().getItems().size(); j++) {
                TGroupByItem i = ((TSelectSqlStatement) select).getGroupByClause().getItems().getGroupByItem(j);

                List<ActionColumnEntry> columns1;
                try {
                    if (i.getExpr() == null) {
                        return;
                    }
                    int index = Integer.parseInt(i.getExpr().toString());
                    columns1 = exprToColumn(select.getResultColumnList()
                                    .getResultColumn(index - 1).getExpr(), select,
                            level, ClauseType.groupby);
                } catch (NumberFormatException e) {
                    columns1 = exprToColumn(i.getExpr(), select, level, ClauseType.groupby);
                }

                if (columns1.size() > 0) {
                    ActionColumnEntry column1 = columns1.get(0);
                    for (String tableName : column1.tableNames) {
                        findColumnInTables(column1, tableName, select, level + 1, ClauseType.groupby);
                    }
                }
            }
        }
    }

    public void impactSqlFromStatement(DCustomSqlStatement select,
                                       int baseLevel) {
        if (select instanceof TSelectSqlStatement) {
            TSelectSqlStatement stmt = (TSelectSqlStatement) select;
            if (stmt.getLeftStmt() != null || stmt.getRightStmt() != null) {
                if (stmt.getLeftStmt() != null) {
                    impactSqlFromStatement(stmt.getLeftStmt(), baseLevel);
                }
                if (stmt.getRightStmt() != null) {
                    impactSqlFromStatement(stmt.getRightStmt(), baseLevel);
                }
            } else {
                for (int i = 0; i < select.getResultColumnList().size(); i++) {
                    linkFieldToTables(null, select.getResultColumnList().getResultColumn(i), select, baseLevel);
                }
            }
        } else if (select instanceof TInsertSqlStatement
                && ((TInsertSqlStatement) select).getSubQuery() != null) {
            impactSqlFromStatement(((TInsertSqlStatement) select).getSubQuery(), baseLevel);
        } else {
            if (select.getResultColumnList() != null) {
                for (int i = 0; i < select.getResultColumnList().size(); i++) {
                    linkFieldToTables(null, select.getResultColumnList().getResultColumn(i), select, baseLevel);
                }
            }
        }
    }

    private ActionTableEntry TLzTaleToTable(TTable lzTable) {
        ActionTableEntry table = new ActionTableEntry();
        if (lzTable.getSubquery() == null && lzTable.getTableName() != null) {
            table.tableName = removeQuote(getTableName(lzTable));
            if (lzTable.getTableName().toString().indexOf(".") > 0) {
                table.prefixName = removeQuote(lzTable.getTableName()
                        .toString().substring(0, lzTable.getTableName().toString().lastIndexOf('.')));
            }
        }

        if (StringUtils.isNotBlank(table.prefixName) && dbType != null && DatabaseType.get3FDatabaseIntegerValueList().contains(dbType)) {
            table.tableName = table.prefixName + "." + table.tableName;
        }

        if (lzTable.getAliasClause() != null) {
            table.tableAlias = removeQuote(lzTable.getAliasClause().toString());
        }
        return table;
    }

    private boolean isPseudocolumn(String column) {
        if (column == null) {
            return false;
        }
        if ("rownum".equalsIgnoreCase(column.trim())) {
            return true;
        } else if ("rowid".equalsIgnoreCase(column.trim())) {
            return true;
        } else if ("nextval".equalsIgnoreCase(column.trim())) {
            return true;
        } else if ("sysdate".equalsIgnoreCase(column.trim())) {
            return true;
        }
        return false;
    }

    private String removeQuote(String string) {
        if (string == null) {
            return string;
        }

        if (string.indexOf('.') != -1 && string.length() < 128) {
            List<String> splits = parseNames(string);
            StringBuilder buffer = new StringBuilder();
            for (int i = 0; i < splits.size(); i++) {
                buffer.append(splits.get(i));
                if (i < splits.size() - 1) {
                    buffer.append(".");
                }
            }
            string = buffer.toString();
        } else {
            if (string.startsWith("\"") && string.endsWith("\"")) {
                return string.substring(1, string.length() - 1);
            }

            if (string.startsWith("[") && string.endsWith("]")) {
                return string.substring(1, string.length() - 1);
            }

            if (string.startsWith("`") && string.endsWith("`")) {
                return string.substring(1, string.length() - 1);
            }
        }
        return string;
    }

    public static List<String> parseNames(String nameString) {
        String name = nameString.trim();
        List<String> names = new ArrayList<>();
        String[] splits = nameString.split("\\.");
        if ((name.startsWith("\"") && name.endsWith("\""))
                || (name.startsWith("[") && name.endsWith("]"))
                || (name.startsWith("`") && name.endsWith("`"))) {
            for (int i = 0; i < splits.length; i++) {
                String split = splits[i].trim();
                if (split.startsWith("[") && !split.endsWith("]")) {
                    StringBuilder buffer = new StringBuilder();
                    buffer.append(splits[i]);
                    while (!(split = splits[++i].trim()).endsWith("]")) {
                        buffer.append(".");
                        buffer.append(splits[i]);
                    }

                    buffer.append(".");
                    buffer.append(splits[i]);

                    names.add(buffer.toString());
                    continue;
                }
                if (split.startsWith("\"") && !split.endsWith("\"")) {
                    StringBuilder buffer = new StringBuilder();
                    buffer.append(splits[i]);
                    while (!(split = splits[++i].trim()).endsWith("\"")) {
                        buffer.append(".");
                        buffer.append(splits[i]);
                    }

                    buffer.append(".");
                    buffer.append(splits[i]);

                    names.add(buffer.toString());
                    continue;
                }
                if (split.startsWith("`") && !split.endsWith("`")) {
                    StringBuilder buffer = new StringBuilder();
                    buffer.append(splits[i]);
                    while (!(split = splits[++i].trim()).endsWith("`")) {
                        buffer.append(".");
                        buffer.append(splits[i]);
                    }

                    buffer.append(".");
                    buffer.append(splits[i]);

                    names.add(buffer.toString());
                    continue;
                }
                names.add(splits[i]);
            }
        } else {
            names.addAll(Arrays.asList(splits));
        }
        return names;
    }

}
