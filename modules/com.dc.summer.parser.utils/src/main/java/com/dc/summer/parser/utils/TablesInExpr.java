package com.dc.summer.parser.utils;

import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.sqlparser.types.EExpressionType;
import com.dc.sqlparser.types.EFunctionType;
import com.dc.sqlparser.nodes.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class TablesInExpr implements IExpressionVisitor {


    private Set<String> tables;
    private TExpression expr;
    private GetTablesUtil impact;
    private GetColumnsUtil getFunctions;
    private Set<String> functions;

    public TablesInExpr(GetTablesUtil impact, TExpression expr,
                        Set<String> tables, GetColumnsUtil getFunctions, Set<String> functions) {
        this.impact = impact;
        this.expr = expr;
        this.tables = tables;
        this.getFunctions = getFunctions;
        this.functions = functions;
    }


    private void addColumnToList(TParseTreeNodeList list) {
        if (list != null) {
            for (int i = 0; i < list.size(); i++) {
                List<TExpression> exprList = new ArrayList<>();
                Object element = list.getElement(i);

                if (element instanceof TGroupByItem) {
                    functions.add(SqlConstant.KEY_GROUP);
                    exprList.add(((TGroupByItem) element).getExpr());
                }
                if (element instanceof TOrderByItem) {
                    functions.add(SqlConstant.KEY_ORDER);
                    exprList.add(((TOrderByItem) element).getSortKey());
                } else if (element instanceof TExpression) {
                    exprList.add((TExpression) element);
                } else if (element instanceof TWhenClauseItem) {
                    exprList.add(((TWhenClauseItem) element).getComparison_expr());
                    exprList.add(((TWhenClauseItem) element).getReturn_expr());
                }

                for (TExpression expr : exprList) {
                    expr.inOrderTraverse(this);
                }
            }
        }
    }

    @Override
    public boolean exprVisit(TParseTreeNode pNode, boolean isLeafNode) {
        TExpression lcexpr = (TExpression) pNode;
        if (lcexpr.getExpressionType() == EExpressionType.simple_object_name_t) {

        } else if (lcexpr.getExpressionType() == EExpressionType.between_t) {

        } else if (lcexpr.getExpressionType() == EExpressionType.function_t) {
            TFunctionCall func = lcexpr.getFunctionCall();
            if (func.getFunctionName() != null) {
                functions.add(func.getFunctionName().toString());
            }
            if (func.getFunctionType() == EFunctionType.trim_t) {
                TTrimArgument args = func.getTrimArgument();
                TExpression expr = args.getStringExpression();
                if (expr != null && !"*".equals(expr.toString().trim())) {
                    expr.inOrderTraverse(this);
                }
                expr = args.getTrimCharacter();
                if (expr != null && !"*".equals(expr.toString().trim())) {
                    expr.inOrderTraverse(this);
                }
            } else if (func.getFunctionType() == EFunctionType.cast_t) {
                TExpression expr = func.getExpr1();
                if ((expr != null && !"*".equals(expr.toString().trim())) ||
                        (expr != null && func.getFunctionType() == EFunctionType.extract_t)) {
                    expr.inOrderTraverse(this);
                }
            } else if (func.getFunctionType() == EFunctionType.convert_t) {
                TExpression expr = func.getExpr1();
                if (expr != null && !"*".equals(expr.toString().trim())) {
                    expr.inOrderTraverse(this);
                }
                expr = func.getExpr2();
                if (expr != null && !"*".equals(expr.toString().trim())) {
                    expr.inOrderTraverse(this);
                }
            } else if (func.getFunctionType() == EFunctionType.contains_t
                    || func.getFunctionType() == EFunctionType.freetext_t) {
                TExpression expr = func.getExpr1();
                if (expr != null && !"*".equals(expr.toString().trim())) {
                    expr.inOrderTraverse(this);
                }
                if (func.getInExpr() != null && func.getInExpr().getExprList() != null) {
                    for (int k = 0; k < func.getInExpr().getExprList().size(); k++) {
                        expr = func.getInExpr().getExprList().getExpression(k);
                        if ("*".equals(expr.toString().trim())) {
                            continue;
                        }
                        expr.inOrderTraverse(this);
                    }
                    if (expr != null
                            && !"*".equals(expr.toString().trim())) {
                        expr.inOrderTraverse(this);
                    }
                }
                expr = func.getInExpr() != null ? func.getInExpr().getFunc_expr() : null;
                if (expr != null && !"*".equals(expr.toString().trim())) {
                    expr.inOrderTraverse(this);
                }
            } else if (func.getFunctionType() == EFunctionType.extractxml_t) {
                TExpression expr = func.getXMLType_Instance();
                if (expr != null && !"*".equals(expr.toString().trim())) {
                    expr.inOrderTraverse(this);
                }
                expr = func.getXPath_String();
                if (expr != null && !"*".equals(expr.toString().trim())) {
                    expr.inOrderTraverse(this);
                }
                expr = func.getNamespace_String();
                if (expr != null && !"*".equals(expr.toString().trim())) {
                    expr.inOrderTraverse(this);
                }
            }

            if (func.getFunctionType() == EFunctionType.rank_t) {
                TOrderByItemList orderByList = func.getOrderByList();
                for (int k = 0; k < orderByList.size(); k++) {
                    TExpression expr = orderByList.getOrderByItem(k)
                            .getSortKey();
                    if ("*".equals(expr.toString().trim())) {
                        continue;
                    }
                    expr.inOrderTraverse(this);
                }
            } else if (func.getArgs() != null) {
                for (int k = 0; k < func.getArgs().size(); k++) {
                    TExpression expr = func.getArgs().getExpression(k);
                    if ("*".equals(expr.toString().trim())) {
                        continue;
                    }
                    expr.inOrderTraverse(this);
                }
            }

        } else if (lcexpr.getExpressionType() == EExpressionType.subquery_t) {
            tables.addAll(impact.analyzeSelectStatement(lcexpr.getSubQuery()));
            functions.addAll(getFunctions.getFunctions(lcexpr.getSubQuery()));
        } else if (lcexpr.getExpressionType() == EExpressionType.case_t) {
            TCaseExpression expr = lcexpr.getCaseExpression();
            TExpression conditionExpr = expr.getInput_expr();
            if (conditionExpr != null) {
                conditionExpr.inOrderTraverse(this);
            }
            TExpression defaultExpr = expr.getElse_expr();
            if (defaultExpr != null) {
                defaultExpr.inOrderTraverse(this);
            }
            TWhenClauseItemList list = expr.getWhenClauseItemList();
            addColumnToList(list);
        } else if (lcexpr.getExpressionType() == EExpressionType.exists_t) {
            tables.addAll(impact.analyzeSelectStatement(lcexpr.getSubQuery()));
            functions.addAll(getFunctions.getFunctions(lcexpr.getSubQuery()));
        }
        return true;
    }

    public void searchTable() {
        this.expr.inOrderTraverse(this);
    }
}
