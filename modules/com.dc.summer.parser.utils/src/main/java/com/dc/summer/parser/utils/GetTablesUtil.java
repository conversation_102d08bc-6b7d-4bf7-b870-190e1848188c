package com.dc.summer.parser.utils;

import com.dc.sqlparser.types.EExpressionType;
import com.dc.sqlparser.nodes.*;
import com.dc.sqlparser.stmt.*;
import com.dc.sqlparser.types.ETableSource;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.utils.model.SqlParseModel;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nonnull;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


public class GetTablesUtil {

    public GetColumnsUtil getFunctions = new GetColumnsUtil();
    public Set<String> returnFunctions = new LinkedHashSet<>();
    public Set<String> returnTablesInWhereClause = new LinkedHashSet<>();
    public Set<String> cteTable = new LinkedHashSet<>();
    public Map<String, Set<String>> sqlTableMap = new HashMap<>();

    public Map<String, Set<String>> statisticsFuncMap = new HashMap<>(); //funcName --> tableNames

    public SqlParseModel sqlParseModel;

    public Set<String> valuesSelectTables = new HashSet<>();

    public Set<String> getValuesSelectTables() {
        return valuesSelectTables;
    }

    public void setValuesSelectTables(Set<String> valuesSelectTables) {
        this.valuesSelectTables = valuesSelectTables;
    }

    public Set<String> getFunctions() {
        return returnFunctions;
    }

    public Set<String> getReturnTablesInWhereClause() {
        return returnTablesInWhereClause;
    }

    public Map<String, Set<String>> getStatisticsFuncMap(){
        return statisticsFuncMap;
    }

    public SqlParseModel getSqlParseModel() {
        return this.sqlParseModel;
    }

    public void setSqlParseModel(SqlParseModel sqlParseModel) {
        this.sqlParseModel = sqlParseModel;
    }

    public Set<String> analyzeSelectStatement(TSelectSqlStatement stmt) {

        Set<String> tables = new LinkedHashSet<>();

        if (stmt == null) {
            return tables;
        }

        String sql = stmt.toString();

        if (sqlTableMap.containsKey(sql)) {
            return sqlTableMap.get(sql);
        }

        ArrayDeque<TSelectSqlStatement> deque = new ArrayDeque<>();
        deque.push(stmt);

        while (!deque.isEmpty()) {
            TSelectSqlStatement current = deque.pop();

            // 检查是否是叶子节点
            if (current.getLeftStmt() == null && current.getRightStmt() == null) {
                if (current.getStatements() != null && current.getStatements().size() > 0) {
                    for (int i = 0; i < current.getStatements().size(); i++) {
                        if (current.getStatements().get(i) instanceof TSelectSqlStatement) {
                            tables.addAll(analyzeSelectStatement((TSelectSqlStatement) current.getStatements().get(i)));
                        }
                        if (current.getStatements().get(i) instanceof TMergeSqlStatement) {
                            sqlParseModel.setMergeResult(CrudUtil.produceMergeWhenClauseOperationMap((TMergeSqlStatement) current.getStatements().get(i),
                                    Optional.ofNullable(sqlParseModel).map(SqlParseModel::getMergeResult).orElse(null)));

                            Set<String> tablesSelect = analyzeMergeStatement((TMergeSqlStatement) current.getStatements().get(i));
                            sqlParseModel.getMergeResult().getTableMap().computeIfAbsent(SqlConstant.KEY_SELECT, k -> new HashSet<>()).addAll(tablesSelect);
                        }
                    }
                }
                if (current.getCteList() != null && current.getCteList().size() > 0) {
                    for (int i = 0; i < current.getCteList().size(); i++) {
                        TCTE expression = current.getCteList().getCTE(i);
                        if (expression != null && expression.getSubquery() != null) {
                            tables.addAll(analyzeSelectStatement(expression.getSubquery()));
                        }
                    }
                }

                if (current.getResultColumnList() != null) {

                    for (int i = 0; i < current.getResultColumnList().size(); i++) {
                        TResultColumn field = current.getResultColumnList()
                                .getResultColumn(i);
                        if (field.getExpr().getExpressionType() == EExpressionType.subquery_t) {
                            tables.addAll(analyzeSelectStatement(field.getExpr()
                                    .getSubQuery()));
                        }
                    }

                }

                if (current.getWhereClause() != null
                        && current.getWhereClause().getCondition() != null) {
                    new TablesInExpr(this,
                            current.getWhereClause().getCondition(),
                            tables, getFunctions, returnFunctions).searchTable();
                }

                if (current.joins != null) {
                    for (int i = 0; i < current.joins.size(); i++) {
                        TJoin join = current.joins.getJoin(i);
                        if (join.getTable() != null && join.getTable().isBaseTable()) {
                            tables.add(join.getTable().getFullName());
                        } else if (join.getTable() != null && join.getTable().getSubquery() != null) {
                            tables.addAll(analyzeSelectStatement(join.getTable()
                                    .getSubquery()));
                        }
                        TJoinItemList items = join.getJoinItems();

                        extractTablesInJoin(tables, items);

                        //join解析到了fromTableList的情况
                        Optional.ofNullable(join.getTable()).map(TTable::getFromTableList).ifPresent(fromTableList -> {
                            for (TFromTable fromTable : fromTableList) {
                                if (fromTable.getFromtableType() == ETableSource.subquery && fromTable.getSubquerynode() != null) {
                                    extractByTSelectSqlNode(tables, fromTable.getSubquerynode());
                                }
                            }
                        });
                    }
                }

                if (current.getTargetTable() != null) {
                    if (current.getTargetTable().isBaseTable()) {
                        tables.add(current.getTargetTable().getFullName());
                    } else if (current.getTargetTable().getSubquery() != null) {
                        tables.addAll(analyzeSelectStatement(current.getTargetTable()
                                .getSubquery()));
                    }
                }

                if (current.tables != null) {
                    for (int i = 0; i < current.tables.size(); i++) {
                        TTable table = current.tables.getTable(i);
                        if (table.isBaseTable()) {
                            tables.add(table.getFullName());
                        } else if (table.getSubquery() != null) {
                            tables.addAll(analyzeSelectStatement(table.getSubquery()));
                        } else if (table.getCTE() != null) {
                            TCTE expression = table.getCTE();
                            if (expression != null && expression.getSubquery() != null) {

                                if (expression.getTableName() != null && cteTable.contains(expression.getTableName().toString())) {
                                    continue;
                                } else if (expression.getTableName() != null) {
                                    cteTable.add(expression.getTableName().toString());
                                }

                                tables.addAll(analyzeSelectStatementSub(expression.getSubquery()));
                                returnFunctions.addAll(getFunctions.getFunctions(expression.getSubquery()));
                                for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                                    statisticsFuncMap.put(staFunc, tables);
                                }
                            }
                        }
                    }

                }
            }

            // 先压入右节点，再压入左节点，这样在遍历时会先处理左子树
            if (current.getRightStmt() != null) {
                deque.push(current.getRightStmt());
            }
            if (current.getLeftStmt() != null) {
                deque.push(current.getLeftStmt());
            }

        }

        sqlTableMap.put(sql, tables);

        return tables;
    }

    private void extractTablesInJoin(Set<String> target, TJoinItemList joinItems) {
        if (joinItems == null || joinItems.size() == 0) {
            return;
        }
        for (TJoinItem joinItem : joinItems) {

            Optional.ofNullable(joinItem.getTable()).ifPresent(table -> {
                extractTableByTTable(target, table);
            });

            Optional.ofNullable(joinItem.getJoin()).ifPresent(join -> {
                if (join.getTable() != null) {
                    extractTableByTTable(target, join.getTable());
                }
                if (join.getJoinItems() != null && join.getJoinItems().size() > 0) {
                    extractTablesInJoin(target, join.getJoinItems());
                }
            });

            Optional.ofNullable(joinItem.getOnCondition()).ifPresent(onCondition -> {
                new TablesInExpr(this, onCondition, target, getFunctions, returnFunctions).searchTable();
            });

        }
    }

    private void extractTableByTTable(Set<String> target, @Nonnull TTable table) {
        if (table.getTableType() == ETableSource.objectname && !table.getFullName().startsWith("@")) {
            target.add(table.getFullName());
        } else if (table.getTableType() == ETableSource.subquery && table.getSubquery() != null) {
            target.addAll(analyzeSelectStatement(table.getSubquery()));
        }
    }

    private void extractByTFromTableList(Set<String> tableTarget, TFromTableList fromTableList) {
        for (TFromTable fromTable : fromTableList) {
            if (fromTable.getFromtableType() == ETableSource.subquery && fromTable.getSubquerynode() != null) {
                extractByTSelectSqlNode(tableTarget, fromTable.getSubquerynode());
            } else if (fromTable.getFromtableType() == ETableSource.join && fromTable.getJoinExpr() != null) {
                TJoinExpr joinExpr = fromTable.getJoinExpr();
                TFromTable leftOperand = joinExpr.getLeftOperand();
                TFromTable rightOperand = joinExpr.getRightOperand();
                if (leftOperand.getFromtableType() == ETableSource.objectname) {
                    tableTarget.add(leftOperand.getTableObjectName().toString());
                }
                if (rightOperand.getFromtableType() == ETableSource.objectname) {
                    tableTarget.add(rightOperand.getTableObjectName().toString());
                }
            }
        }
    }

    private void extractByTSelectSqlNode(Set<String> tableTarget, @Nonnull TSelectSqlNode selectSqlNode) {
        //selectSqlNode -- resultColumnList
        Optional.ofNullable(selectSqlNode.getResultColumnList()).ifPresent(resultColumnList -> {
            for (TResultColumn resultColumn : resultColumnList) {
                if (resultColumn.getExpr() != null) {
                    TExpression expr = resultColumn.getExpr();
                    switch (expr.getExpressionType()) {
                        case function_t:
                            extractResultColumn(tableTarget, returnFunctions, resultColumn);
                            break;
                        case subquery_t:
                            tableTarget.addAll(analyzeSelectStatement(expr.getSubQuery()));
                            break;
                    }
                }
            }
        });
        //selectSqlNode -- fromTableList
        Optional.ofNullable(selectSqlNode.getFromTableList()).ifPresent(fromTableList -> {
            extractByTFromTableList(tableTarget, fromTableList);
        });
        //selectSqlNode -- whereCondition
        Optional.ofNullable(selectSqlNode.getWhereCondition()).ifPresent(whereClause -> {
            new TablesInExpr(this, whereClause.getCondition(), tableTarget, getFunctions, returnFunctions).searchTable();
        });
    }

    private void extractResultColumn(Set<String> tableTarget, Set<String> functionTarget, TResultColumn resultColumn) {
        if (resultColumn.getExpr() != null && resultColumn.getExpr().getExpressionType() == EExpressionType.function_t) {
            TExpression expr = resultColumn.getExpr();
            Optional.of(expr.getFunctionCall()).map(TFunctionCall::getFunctionName).map(TObjectName::toString).ifPresent(functionTarget::add);
            if (expr.getFunctionCall() != null) {
                TFunctionCall functionCall = expr.getFunctionCall();
                extractTFunctionCallExcludeName(tableTarget, functionTarget, functionCall);
            }
        }
    }

    private void extractTFunctionCallExcludeName(Set<String> tableTarget, Set<String> functionTarget, TFunctionCall functionCall) {
        if (functionCall.getFunctionType() != null) {
            List<TExpression> allExpressions = Stream.of(functionCall.getExpr1(), functionCall.getExpr2(), functionCall.getExpr3()).filter(Objects::nonNull).collect(Collectors.toList());
            if (functionCall.getArgs() != null && functionCall.getArgs().size() > 0) {
                functionCall.getArgs().forEach(allExpressions::add);
            }
            for (TExpression expression : allExpressions) {
                extractTExpression(tableTarget, functionTarget, expression);
            }
        }
    }

    private void extractTExpression(Set<String> tableTarget, Set<String> functionTarget, TExpression expression) {
        switch (expression.getExpressionType()) {
            case function_t:
                TFunctionCall functionCall = expression.getFunctionCall();
                functionTarget.add(functionCall.getFunctionName().toString());
                extractTFunctionCallExcludeName(tableTarget, functionTarget, functionCall);
                break;
        }
    }


    public Set<String> analyzeMergeStatement(TMergeSqlStatement merge) {
        Set<String> tables = new LinkedHashSet<>();

        returnFunctions.clear();
        statisticsFuncMap.clear();

        if (merge.getUsingTable() != null) {
            if (merge.getUsingTable().isBaseTable()) {
                tables.add(merge.getUsingTable().getFullName());
            } else if (merge.getUsingTable().getSubquery() != null) {
                tables.addAll(analyzeSelectStatement(merge.getUsingTable().getSubquery()));
                returnFunctions.addAll(getFunctions.getFunctions(merge.getUsingTable().getSubquery()));
                for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                    statisticsFuncMap.put(staFunc, tables);
                }
            }
            /*if (merge.getCondition() != null) {
                new TablesInExpr(this, merge.getCondition(), tables, getFunctions, returnFunctions).searchTable();
            }
            if (merge.getWhenClauses() != null) {
                for (int i = 0; i < merge.getWhenClauses().size(); i++) {
                    TMergeWhenClause when = merge.getWhenClauses()
                            .getElement(i);
                    if (when.getCondition() != null) {
                        new TablesInExpr(this, when.getCondition(), tables, getFunctions, returnFunctions).searchTable();
                    }
                    if (when.getInsertClause() != null
                            && when.getInsertClause().getValuelist() != null) {
                        for (int j = 0; j < when.getInsertClause()
                                .getValuelist()
                                .size(); j++) {
                            TResultColumn field = when.getInsertClause()
                                    .getValuelist()
                                    .getResultColumn(j);
                            if (field.getExpr().getExpressionType() == EExpressionType.subquery_t) {
                                tables.addAll(analyzeSelectStatement(field.getExpr()
                                        .getSubQuery()));
                                returnFunctions.addAll(getFunctions.getFunctions(field.getExpr()
                                        .getSubQuery()));
                                for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                                    if (statisticsFuncMap.containsKey(staFunc)) statisticsFuncMap.get(staFunc).addAll(tables);
                                    else statisticsFuncMap.put(staFunc, tables);
                                }
                            }
                        }
                    }
                    if (when.getUpdateClause() != null) {
                        if (when.getUpdateClause().getUpdateWhereClause() != null) {
                            new TablesInExpr(this, when.getUpdateClause()
                                    .getUpdateWhereClause(), tables, getFunctions, returnFunctions).searchTable();
                        }
                        if (when.getUpdateClause().getUpdateColumnList() != null) {
                            for (int j = 0; j < when.getUpdateClause()
                                    .getUpdateColumnList()
                                    .size(); j++) {
                                TResultColumn field = when.getUpdateClause()
                                        .getUpdateColumnList()
                                        .getResultColumn(j);
                                if (field.getExpr()
                                        .getRightOperand()
                                        .getExpressionType() == EExpressionType.subquery_t) {
                                    tables.addAll(analyzeSelectStatement(field.getExpr()
                                            .getRightOperand()
                                            .getSubQuery()));
                                    returnFunctions.addAll(getFunctions.getFunctions(field.getExpr()
                                            .getRightOperand()
                                            .getSubQuery()));
                                    for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                                        if (statisticsFuncMap.containsKey(staFunc)) statisticsFuncMap.get(staFunc).addAll(tables);
                                        else statisticsFuncMap.put(staFunc, tables);
                                    }
                                }
                            }
                        }
                    }
                }
            }*/
        }
        return tables;
    }

    public Set<String> analyzeDeleteStatement(TDeleteSqlStatement delete) {
        Set<String> tables = new LinkedHashSet<>();

        returnFunctions.clear();
        statisticsFuncMap.clear();
        if (delete.getTargetTable() != null) {
            tables.add(delete.getTargetTable().getFullName());
        }

        if (delete.getResultColumnList() != null) {
            for (int i = 0; i < delete.getResultColumnList().size(); i++) {
                TResultColumn field = delete.getResultColumnList()
                        .getResultColumn(i);
                if (field.getExpr().getRightOperand() != null
                        && field.getExpr()
                        .getRightOperand()
                        .getExpressionType() == EExpressionType.subquery_t) {
                    tables.addAll(analyzeSelectStatement(field.getExpr()
                            .getRightOperand()
                            .getSubQuery()));
                    returnFunctions.addAll(getFunctions.getFunctions(field.getExpr()
                            .getRightOperand()
                            .getSubQuery()));
                    for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                        statisticsFuncMap.put(staFunc, tables);
                    }
                }
            }
        }

        if (delete.joins != null) {
            for (int i = 0; i < delete.joins.size(); i++) {
                TJoin join = delete.joins.getJoin(i);
                if (join.getTable().isBaseTable()) {
                    tables.add(join.getTable().getFullName());
                }
                TJoinItemList items = join.getJoinItems();
                if (items != null) {
                    for (int j = 0; j < items.size(); j++) {
                        TJoinItem item = items.getJoinItem(j);
                        if (item.getTable().isBaseTable()) {
                            tables.add(item.getTable().getFullName());
                        }
                        if (item.getOnCondition() != null) {
                            new TablesInExpr(this,
                                    item.getOnCondition(),
                                    tables, getFunctions, returnFunctions).searchTable();
                        }
                    }
                }
            }
        }

        // mysql删除多表时，targetTable和joins里都是表别名，需用tables里的表名
        // delete a,q from aaa as a, qqq as q where a.id =1 and q.id =1
        if (delete.tables != null && delete.tables.size() > 0) {
            tables.clear();
            for (int i = 0; i < delete.tables.size(); i++) {
                TTable table = delete.tables.getTable(i);
                if (table.isBaseTable()) {
                    tables.add(table.getFullName());
                } else if (table.getSubquery() != null) {
                    tables.addAll(this.analyzeSelectStatement(table.getSubquery()));
                }
            }
        }

        if (delete.getWhereClause() != null
                && delete.getWhereClause().getCondition() != null) {
            new TablesInExpr(this,
                    delete.getWhereClause().getCondition(),
                    returnTablesInWhereClause, getFunctions, returnFunctions).searchTable();
        }
        return tables;
    }

    public Set<String> analyzeUpdateStatement(TUpdateSqlStatement update) {
        Set<String> tables = new LinkedHashSet<>();

        returnFunctions.clear();
        statisticsFuncMap.clear();
        if (update.getTargetTable() != null) {
            String tableName = update.getTargetTable().getFullName();
            if (update.getTargetTable().isLinkTable() && update.getTargetTable().getLinkTable() != null) {
                tableName = update.getTargetTable().getLinkTable().toString();
            } else if (update.getTargetTable().getSubquery() != null) {
                tables.addAll(this.analyzeSelectStatement(update.getTargetTable().getSubquery()));
            }

            if (StringUtils.isNotBlank(tableName)) {
                tables.add(tableName);
            }

        }

        if (update.getResultColumnList() != null) {
            for (int i = 0; i < update.getResultColumnList().size(); i++) {
                TResultColumn field = update.getResultColumnList().getResultColumn(i);
                if (field.getExpr() != null && field.getExpr().getRightOperand() != null) {
                    TExpression rightOperand = field.getExpr().getRightOperand();
                    if (rightOperand.getExpressionType() == EExpressionType.subquery_t) {
                        returnTablesInWhereClause.addAll(analyzeSelectStatement(rightOperand.getSubQuery()));
                        returnFunctions.addAll(getFunctions.getFunctions(rightOperand.getSubQuery()));
                        //记录统计函数和对应的表(统计函数鉴权所有表，因此添加所有表进去)
                        for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                            statisticsFuncMap.put(staFunc, returnTablesInWhereClause);
                        }
                    } else if (rightOperand.getExpressionType() == EExpressionType.function_t) {
                        if (rightOperand.getFunctionCall() != null && rightOperand.getFunctionCall().getArgs() != null
                                && rightOperand.getFunctionCall().getArgs().size() > 0
                                && rightOperand.getFunctionCall().getArgs().getExpression(0).getSubQuery() != null) {
                            TSelectSqlStatement subQuery = rightOperand.getFunctionCall().getArgs().getExpression(0).getSubQuery();
                            returnTablesInWhereClause.addAll(analyzeSelectStatement(subQuery));
                            returnFunctions.addAll(getFunctions.getFunctions(subQuery));
                            for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                                statisticsFuncMap.put(staFunc, returnTablesInWhereClause);
                            }
                        }
                        if (rightOperand.getFunctionCall() != null) {
                            returnFunctions.add(rightOperand.getFunctionCall().getFunctionName().toString());
                            for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                                statisticsFuncMap.put(staFunc, returnTablesInWhereClause);
                            }
                        }
                    }
                }
            }
        }

        if (update.joins != null) {
            for (int i = 0; i < update.joins.size(); i++) {
                TJoin join = update.joins.getJoin(i);
                if (join.getTable().isBaseTable()) {
                    tables.add(join.getTable().getFullName());
                }
                TJoinItemList items = join.getJoinItems();
                if (items != null) {
                    for (int j = 0; j < items.size(); j++) {
                        TJoinItem item = items.getJoinItem(j);
                        if (item.getTable().isBaseTable()) {
                            tables.add(item.getTable().getFullName());
                        }
                        if (item.getOnCondition() != null) {
                            new TablesInExpr(this,
                                    item.getOnCondition(),
                                    tables, getFunctions, returnFunctions).searchTable();
                        }
                    }
                }
            }
        }

        if (update.getWhereClause() != null
                && update.getWhereClause().getCondition() != null) {
            new TablesInExpr(this,
                    update.getWhereClause().getCondition(),
                    returnTablesInWhereClause, getFunctions, returnFunctions).searchTable();
        }
        return tables;
    }

    public Set<String> analyzeInsertStatement(TInsertSqlStatement insert) {
        Set<String> targets = new LinkedHashSet<>();

        returnFunctions.clear();
        statisticsFuncMap.clear();
        if (insert.getTargetTable() != null) {
            if (insert.getTargetTable().isBaseTable()) {
                targets.add(insert.getTargetTable().getFullName());
            } else if (insert.getTargetTable().getSubquery() != null) {
                targets.addAll(analyzeSelectStatement(insert.getTargetTable()
                        .getSubquery()));
                returnFunctions.addAll(getFunctions.getFunctions(insert.getTargetTable()
                        .getSubquery()));
                for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                    statisticsFuncMap.put(staFunc, targets);
                }
            }
        }
        if (insert.getInsertIntoValues() != null) {
            for (int i = 0; i < insert.getInsertIntoValues().size(); i++) {
                TInsertIntoValue intoValue = insert.getInsertIntoValues()
                        .getElement(i);
                if (intoValue.getTable() != null
                        && intoValue.getTable().isBaseTable()) {
                    targets.add(intoValue.getTable().getFullName());
                }
            }
        }
        if (insert.getInsertConditions() != null) {
            for (int i = 0; i < insert.getInsertConditions().size(); i++) {
                TInsertCondition intoCondition = insert.getInsertConditions()
                        .getElement(i);
                if (intoCondition.getInsertIntoValues() != null) {
                    for (int j = 0; j < intoCondition.getInsertIntoValues()
                            .size(); j++) {
                        TInsertIntoValue intoValue = intoCondition.getInsertIntoValues()
                                .getElement(j);
                        if (intoValue.getTable() != null
                                && intoValue.getTable().isBaseTable()) {
                            targets.add(intoValue.getTable().getFullName());
                        }
                    }
                }
            }
        }

        Set<String> sources = new LinkedHashSet<>();

        if (insert.getValues() != null) {
            for (int i = 0; i < insert.getValues().size(); i++) {
                TMultiTarget multiTarget = insert.getValues()
                        .getMultiTarget(i);
                if (multiTarget.getSubQuery() != null) {
                    sources.addAll(analyzeSelectStatement(multiTarget.getSubQuery()));
                    returnFunctions.addAll(getFunctions.getFunctions(multiTarget.getSubQuery()));
                    for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                        if (statisticsFuncMap.containsKey(staFunc)) statisticsFuncMap.get(staFunc).addAll(sources);
                        else statisticsFuncMap.put(staFunc, sources);
                    }
                }

                for (int j = 0; j < multiTarget.getColumnList().size(); j++) {
                    TResultColumn field = multiTarget.getColumnList()
                            .getResultColumn(j);
                    if (field.getExpr().getExpressionType() == EExpressionType.subquery_t) {
                        valuesSelectTables.addAll(analyzeSelectStatement(field.getExpr()
                                .getSubQuery()));
                        returnFunctions.addAll(getFunctions.getFunctions(field.getExpr()
                                .getSubQuery()));
                        for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                            if (statisticsFuncMap.containsKey(staFunc)) statisticsFuncMap.get(staFunc).addAll(sources);
                            else statisticsFuncMap.put(staFunc, sources);
                        }
                    } else if (field.getExpr().getExpressionType() == EExpressionType.function_t) {
                        returnFunctions.add(field.getExpr().getFunctionCall().getFunctionName().toString());
                        for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                            if (statisticsFuncMap.containsKey(staFunc)) statisticsFuncMap.get(staFunc).addAll(sources);
                            else statisticsFuncMap.put(staFunc, sources);
                        }
                    }

                }
            }
        }

        if (insert.getSubQuery() != null) {
            sources.addAll(analyzeSelectStatement(insert.getSubQuery()));
            returnFunctions.addAll(getFunctions.getFunctions(insert.getSubQuery()));
            for (String staFunc : CommonUtil.getFunctions(returnFunctions)) {
                if (statisticsFuncMap.containsKey(staFunc)) statisticsFuncMap.get(staFunc).addAll(sources);
                else statisticsFuncMap.put(staFunc, sources);
            }
        }

        targets.addAll(sources);

        return targets;
    }

    public Set<String> getInsertSelectTables(TInsertSqlStatement insert) {
        Set<String> targets = new LinkedHashSet<>();

        if (insert.getSubQuery() != null) {
            targets.addAll(analyzeSelectStatement(insert.getSubQuery()));
        }

        return targets;
    }

    public Set<String> getInsertIntoValuesTables(TInsertSqlStatement insert) {
        Set<String> targets = new LinkedHashSet<>();

        if (insert.getInsertIntoValues() != null) {
            for (int i = 0; i < insert.getInsertIntoValues().size(); i++) {
                TInsertIntoValue intoValue = insert.getInsertIntoValues()
                        .getElement(i);
                if (intoValue.getTable() != null
                        && intoValue.getTable().isBaseTable()) {
                    targets.add(intoValue.getTable().getFullName());
                }
            }
        }

        if (insert.getElseIntoValues() != null) {
            for (int i = 0; i < insert.getElseIntoValues().size(); i++) {
                TInsertIntoValue intoValue = insert.getElseIntoValues()
                        .getElement(i);
                if (intoValue.getTable() != null
                        && intoValue.getTable().isBaseTable()) {
                    targets.add(intoValue.getTable().getFullName());
                }
            }
        }

        return targets;
    }

    public Set<String> analyzeSelectStatementSub(TSelectSqlStatement stmt) {
        Set<String> tables = new LinkedHashSet<>();

        if (stmt == null) {
            return tables;
        }

        if (stmt.getLeftStmt() != null || stmt.getRightStmt() != null) {
            if (stmt.getLeftStmt() != null) {
                tables.addAll(analyzeSelectStatementSub(stmt.getLeftStmt()));
            }
            if (stmt.getRightStmt() != null) {
                tables.addAll(analyzeSelectStatementSub(stmt.getRightStmt()));
            }
        } else {
            if (stmt.getStatements() != null && stmt.getStatements().size() > 0) {
                for (int i = 0; i < stmt.getStatements().size(); i++) {
                    if (stmt.getStatements().get(i) instanceof TSelectSqlStatement) {
                        tables.addAll(analyzeSelectStatementSub((TSelectSqlStatement) stmt.getStatements().get(i)));
                    }
                }
            }
            if (stmt.getCteList() != null && stmt.getCteList().size() > 0) {
                for (int i = 0; i < stmt.getCteList().size(); i++) {
                    TCTE expression = stmt.getCteList().getCTE(i);
                    if (expression != null && expression.getSubquery() != null) {
                        tables.addAll(analyzeSelectStatementSub(expression.getSubquery()));
                    }
                }
            }

            if (stmt.getResultColumnList() != null) {

                for (int i = 0; i < stmt.getResultColumnList().size(); i++) {
                    TResultColumn field = stmt.getResultColumnList()
                            .getResultColumn(i);
                    if (field.getExpr().getExpressionType() == EExpressionType.subquery_t) {
                        tables.addAll(analyzeSelectStatementSub(field.getExpr()
                                .getSubQuery()));
                    }
                }

            }

            if (stmt.getWhereClause() != null
                    && stmt.getWhereClause().getCondition() != null) {
                new TablesInExpr(this,
                        stmt.getWhereClause().getCondition(),
                        tables, getFunctions, returnFunctions).searchTable();
            }

            if (stmt.joins != null) {
                for (int i = 0; i < stmt.joins.size(); i++) {
                    TJoin join = stmt.joins.getJoin(i);
                    if (join.getTable() != null && join.getTable().isBaseTable()) {
                        tables.add(join.getTable().getFullName());
                    } else if (join.getTable() != null && join.getTable().getSubquery() != null) {
                        tables.addAll(analyzeSelectStatementSub(join.getTable()
                                .getSubquery()));
                    }
                    TJoinItemList items = join.getJoinItems();
                    if (items != null) {
                        for (int j = 0; j < items.size(); j++) {
                            TJoinItem item = items.getJoinItem(j);
                            if (item.getTable().isBaseTable()) {
                                tables.add(item.getTable().getFullName());
                            } else if (item.getTable().getSubquery() != null) {
                                tables.addAll(analyzeSelectStatementSub(item.getTable()
                                        .getSubquery()));
                            }
                            if (item.getOnCondition() != null) {
                                new TablesInExpr(this,
                                        item.getOnCondition(),
                                        tables, getFunctions, returnFunctions).searchTable();
                            }
                        }
                    }
                }
            }

            if (stmt.getTargetTable() != null) {
                if (stmt.getTargetTable().isBaseTable()) {
                    tables.add(stmt.getTargetTable().getFullName());
                } else if (stmt.getTargetTable().getSubquery() != null) {
                    tables.addAll(analyzeSelectStatementSub(stmt.getTargetTable()
                            .getSubquery()));
                }
            }

            if (stmt.tables != null) {
                for (int i = 0; i < stmt.tables.size(); i++) {
                    TTable table = stmt.tables.getTable(i);
                    if (table.isBaseTable()) {
                        tables.add(table.getFullName());
                    } else if (table.getSubquery() != null) {
                        tables.addAll(analyzeSelectStatementSub(table.getSubquery()));
                    }
                }

            }
        }

        return tables;
    }

}
