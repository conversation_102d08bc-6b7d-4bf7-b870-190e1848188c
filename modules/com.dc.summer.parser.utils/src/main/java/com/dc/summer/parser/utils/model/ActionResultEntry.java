package com.dc.summer.parser.utils.model;

import com.dc.summer.parser.sql.model.ActionColumnEntry;
import com.dc.summer.parser.sql.type.ClauseType;
import com.dc.sqlparser.nodes.TTable;

import java.awt.*;
import java.util.List;

public class ActionResultEntry {
    public ClauseType clause;
    public String targetColumn;
    public TTable targetTable;
    public Point location;
    public ActionColumnEntry columnObject;

    public ActionResultEntry(TTable table, String column,
                             ClauseType clause, Point location) {
        this.targetTable = table;
        this.targetColumn = column;
        this.clause = clause;
        this.location = location;
        columnObject = new ActionColumnEntry();
        columnObject.columnName = "*";
        updateColumnTableFullName(table, columnObject);
    }

    public ActionResultEntry(TTable table, ActionColumnEntry columnObject, String column,
                             ClauseType clause, Point location) {
        this.targetTable = table;
        this.targetColumn = column;
        this.clause = clause;
        this.location = location;
        this.columnObject = columnObject;
        updateColumnTableFullName(table, this.columnObject);
    }

    private void updateColumnTableFullName(TTable table, ActionColumnEntry column) {
        List<String> fullNames = column.tableFullNames;
        if (fullNames != null) {
            for (int i = 0; i < fullNames.size(); i++) {
                String tableName = table.getName();
                String fullName = fullNames.get(i);
                if (tableName != null) {
                    fullName = fullName == null ? "" : fullName.trim();
                    if (!tableName.equalsIgnoreCase(fullName)) {
                        if (!fullNames.contains(table.getFullName())) {
                            fullNames.remove(i);
                            fullNames.add(i, table.getFullName());
                        }
                    }
                }
            }
        }
    }
}
