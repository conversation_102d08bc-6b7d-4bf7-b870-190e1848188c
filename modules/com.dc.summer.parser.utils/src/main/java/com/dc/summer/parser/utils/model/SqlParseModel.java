package com.dc.summer.parser.utils.model;

import com.dc.stmt.CustomSqlStatement;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.summer.parser.utils.CrudUtil.MergeResult;

import java.util.List;

public class SqlParseModel {

    private DCustomSqlStatement tCustomSqlStatement;
    private Object ast;
    private CustomSqlStatement customSqlStatement;
    private String sql;
    private String operation;
    private String errorMessage;
    private SqlActionModel action;
    private List<SqlAuthModel> sqlAuthModelList;
    private boolean isPassAuth = false;
    private Boolean isUpdateClause = false;  // SELECT * FROM AGE6 for update; 或 select * from aaa lock in share mode;
    private boolean switchDatabase; // 是否是切换schema的sql

    private String dblinkName; // 跨库查询的dblink的名字
    private String catalogName; // pg系列的catalog名称
    private Integer dbType; // 数据库类型
    private int authModelsNum; //原始sqlAuthModelList数量
    private MergeResult mergeResult;

    public DCustomSqlStatement gettCustomSqlStatement() {
        return tCustomSqlStatement;
    }

    public void settCustomSqlStatement(DCustomSqlStatement tCustomSqlStatement) {
        this.tCustomSqlStatement = tCustomSqlStatement;
    }

    public Object getAst() {
        return ast;
    }

    public void setAst(Object ast) {
        this.ast = ast;
    }

    public CustomSqlStatement getCustomSqlStatement() {
        return customSqlStatement;
    }

    public void setCustomSqlStatement(CustomSqlStatement customSqlStatement) {
        this.customSqlStatement = customSqlStatement;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public SqlActionModel getAction() {
        return action;
    }

    public void setAction(SqlActionModel action) {
        this.action = action;
    }

    public List<SqlAuthModel> getSqlAuthModelList() {
        return sqlAuthModelList;
    }

    public void setSqlAuthModelList(List<SqlAuthModel> sqlAuthModelList) {
        this.sqlAuthModelList = sqlAuthModelList;
    }

    public boolean isPassAuth() {
        return isPassAuth;
    }

    public void setPassAuth(boolean passAuth) {
        isPassAuth = passAuth;
    }

    public Boolean getUpdateClause() {
        return isUpdateClause;
    }

    public void setUpdateClause(Boolean updateClause) {
        isUpdateClause = updateClause;
    }

    public boolean isSwitchDatabase() {
        return switchDatabase;
    }

    public void setSwitchDatabase(boolean switchDatabase) {
        this.switchDatabase = switchDatabase;
    }

    public String getDblinkName() {
        return dblinkName;
    }

    public void setDblinkName(String dblinkName) {
        this.dblinkName = dblinkName;
    }

    public String getCatalogName() {
        return catalogName;
    }

    public void setCatalogName(String catalogName) {
        this.catalogName = catalogName;
    }

    public Integer getDbType() {
        return dbType;
    }

    public void setDbType(Integer dbType) {
        this.dbType = dbType;
    }

    public int getAuthModelsNum() {
        return authModelsNum;
    }

    public void setAuthModelsNum(int authModelsNum) {
        this.authModelsNum = authModelsNum;
    }

    public MergeResult getMergeResult() {
        return mergeResult;
    }

    public void setMergeResult(MergeResult mergeResult) {
        this.mergeResult = mergeResult;
    }
}
