package com.dc.summer.parser.utils;

import com.dc.DCSqlParser;
import com.dc.sqlparser.*;
import com.dc.sqlparser.stmt.*;
import com.dc.sqlparser.types.EDbType;
import com.dc.sqlparser.types.ESqlStatementType;
import com.dc.sqlparser.types.ETokenType;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.utils.model.SqlParseResult;
import com.dc.summer.parser.sql.type.RegularType;
import com.dc.summer.parser.sql.utils.RegularUtil;
import com.dc.type.DatabaseType;
import com.dc.stmt.CustomSqlStatement;
import com.dc.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

public class SqlParserUtil {

    private static final Logger logger = LoggerFactory.getLogger(SqlParserUtil.class);

    public static List<SqlParseResult> parser(String sql, Integer dbType) {

        try {
            // mongoDB/redis/es不使用GSqlParser解析
            if (DatabaseType.getIdentCode(DatabaseType.useDcSqlParser()).contains(dbType)) {
                return dcSqlParser(sql, dbType);
            }

            List<SqlParseResult> list = new ArrayList<>();

            DPSqlParser parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(dbType));
            boolean isSubmitJob = SqlPreparedUtil.isSubmitJob(sql, dbType);
            sql = SqlPreparedUtil.sqlReplace(sql, dbType, isSubmitJob);
            parser.setSqltext(sql);

            int ret;
            try {
                ret = parser.parse();
            } catch (Exception e) {
                ret = 1; // 解析插件本身可能会报异常,如空指针异常(kingBase:create table tableName1 as table tableName2;)
            }

            if (ret != 0) {
                if (DatabaseType.getPGSqlIntegerValueList().contains(dbType) || DatabaseType.getIdentCode(DatabaseType.needCheckAgainByOracle()).contains(dbType)) {
                    // 再用oracle解析一遍, 如 pg: 1.insert all语句; 2.创建可编程对象语句会被分割
                    // g_base_s: ROLLBACK 语句 会报错
                    // db2: SELECT CAST(name AS varchar) FROM TEST2."NAME_TEST";
                    // hana: CREATE COLUMN TABLE "BOBO"."STUDENT" ( ... 会导致sql丢失(parser.sqlstatements的size为0)
                    parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.ORACLE.getValue()));
                    parser.setSqltext(sql);
                    ret = parser.parse();
                    if (ret != 0) {
                        parserLog(parser.getErrormessage(), sql);
                    }
                } else if (DatabaseType.getIdentCode(DatabaseType.needCheckAgainByMysql()).contains(dbType)) {
                    // 再用mysql解析一遍, 如 DM: create/drop schema schemaName 语句
                    // HIVE: insert into 语句
                    // ob-oracle: insert into tableName values (1,10,1),(2,20,1),(3,30,1)
                    parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.MYSQL.getValue()));
                    parser.setSqltext(sql);
                    ret = parser.parse();
                    if (ret != 0) {
                        parserLog(parser.getErrormessage(), sql);
                    }
                } else {
                    parserLog(parser.getErrormessage(), sql);
                }
            }

            if (DatabaseType.INCEPTOR.getValue().equals(dbType) && parser.getDbVendor() == EDbType.dbvoracle) {
                DSourceToken startToken = parser.sqlstatements.get(0).getStartToken();
                if (Stream.of(SqlConstant.KEY_BEGIN, SqlConstant.KEY_DECLARE).anyMatch(s -> s.equalsIgnoreCase(startToken.toString()))) {
                    parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.MYSQL.getValue()));
                    parser.setSqltext(sql);
                    ret = parser.parse();
                }
            }

            DStatementList sqlStatements = parser.sqlstatements;

            for (int k = 0; k < sqlStatements.size(); k++) {
                DCustomSqlStatement next = sqlStatements.get(k);

                RepairSql repairSql = tryToRepairSql(sqlStatements, next.toString(), sql, dbType);
                String singleSql = repairSql.sql; //如果正确解析。这里依旧是next.toString()的结果。
                String realSql = singleSql; // 备份真实sql

                singleSql = replaceChineseSymbols(next, singleSql, dbType);

                if (ret != 0) {
                    singleSql = transformSingleSql(singleSql, next, dbType);
                }

                SqlParseResult sqlParserResultMessage = buildParserResult(singleSql, dbType);

                if (sqlParserResultMessage == null) {
                    continue;
                }
                if (!realSql.isEmpty()) {
                    sqlParserResultMessage.setRealSql(realSql);
                }
                sqlParserResultMessage.setSubmitJob(isSubmitJob);

                list.add(sqlParserResultMessage);

                if (repairSql.hasBeenRepaired) {
                    break;
                }
            }

            return list;
        } catch (Exception e) {
            String error = "SqlParserUtil::parser : Sql parse catch exception. Sql is : " + sql;
            logger.error(error);
            throw new RuntimeException(error + "\n" + e.getMessage());
        }
    }

    private static RepairSql tryToRepairSql(DStatementList sqlStatements, String currentSql, String originalSql, Integer dbType) {

        final RepairSql result = new RepairSql(currentSql);
        if (sqlStatements.size() == 1) {
            return result;
        }
        for (DCustomSqlStatement each : sqlStatements) {
            if (CollectionUtils.isNotEmpty(each.getSyntaxErrors()) || each.sqlstatementtype == ESqlStatementType.sstinvalid) {
                result.sql = originalSql;
                result.hasBeenRepaired = true;
                break;
            }
            //repair inceptor block sql that parsed by mysql
            if (DatabaseType.INCEPTOR.getValue().equals(dbType) && sqlStatements.get(0).dbvendor == EDbType.dbvmysql) {
                DCustomSqlStatement firstStatement = sqlStatements.get(0);
                DCustomSqlStatement lastStatement = sqlStatements.get(sqlStatements.size() - 1);
                if (List.of(SqlConstant.KEY_BEGIN, SqlConstant.KEY_DECLARE).contains(firstStatement.getStartToken().toString().toUpperCase(Locale.ROOT)) && lastKeywordTokenIsEnd(lastStatement)) {
                    result.sql = originalSql;
                    result.hasBeenRepaired = true;
                    break;
                }
            }
        }

        return result;
    }

    private static class RepairSql {
        String sql;
        boolean hasBeenRepaired;

        RepairSql(String sql) {
            this.sql = sql;
        }
    }

    public static SqlParseResult buildParserResult(String sql, Integer dbType) {

        try {
            SqlParseResult sqlParserResultMessage = new SqlParseResult();

            DPSqlParser parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(dbType));
            parser.setSqltext(sql);

            int ret;
            try {
                ret = parser.parse();
            } catch (Exception e) {
                ret = 1; // 解析插件本身可能会报异常,如空指针异常(kingBase:create table tableName1 as table tableName2;)
            }

            if (ret != 0) {
                if (DatabaseType.getPGSqlIntegerValueList().contains(dbType)) {
                    // 再用oracle解析一遍, 如 pg: 1.insert all语句; 2.创建可编程对象语句会被分割
                    parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.ORACLE.getValue()));
                    parser.setSqltext(sql);
                    ret = parser.parse();
                    if (ret != 0) {
                        // 再用mysql解析一遍, 如 SELECT (id,name) ... 无法解析
                        parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.MYSQL.getValue()));
                        parser.setSqltext(sql);
                        ret = parser.parse();
                        if (ret != 0) {
                            buildParserResultLog(parser.getErrormessage(), sql);
                            sqlParserResultMessage.setErrorMessage(parser.getErrormessage());
                        }
                    }
                } else if (DatabaseType.getIdentCode(DatabaseType.needCheckAgainByMysql()).contains(dbType)) {
                    // 再用mysql解析一遍, 如 DM: create/drop schema schemaName 语句
                    // HIVE: insert into 语句
                    // ob-oracle: insert into tableName values (1,10,1),(2,20,1),(3,30,1)
                    parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.MYSQL.getValue()));
                    parser.setSqltext(sql);
                    ret = parser.parse();
                    if (ret != 0) {
                        if (DatabaseType.getIdentCode(DatabaseType.adaptToMultipleGrammars()).contains(dbType)) {
                            // 神通数据库支持oracle、mysql、postGreSql、sqlserver的语法
                            // 达梦数据库支持oracle、mysql、sqlserver的语法
                            parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.SQL_SERVER.getValue()));
                            parser.setSqltext(sql);
                            ret = parser.parse();
                            if (ret != 0) {
                                parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.PG_SQL.getValue()));
                                parser.setSqltext(sql);
                                ret = parser.parse();
                                if (ret != 0) {
                                    buildParserResultLog(parser.getErrormessage(), sql);
                                    sqlParserResultMessage.setErrorMessage(parser.getErrormessage());
                                }
                            }
                        } else if (DatabaseType.SPARK.getValue().equals(dbType)) {
                            parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.HIVE.getValue()));
                            parser.setSqltext(sql);
                            ret = parser.parse();
                            if (ret != 0) {
                                buildParserResultLog(parser.getErrormessage(), sql);
                                sqlParserResultMessage.setErrorMessage(parser.getErrormessage());
                            }
                        } else {
                            buildParserResultLog(parser.getErrormessage(), sql);
                            sqlParserResultMessage.setErrorMessage(parser.getErrormessage());
                        }
                    }
                } else if (DatabaseType.getIdentCode(DatabaseType.needCheckAgainByOracle()).contains(dbType)) {
                    // 再用oracle解析一遍, 如 g_base_s: ROLLBACK 语句 会报错
                    // db2: SELECT CAST(name AS varchar) FROM TEST2."NAME_TEST";
                    // hana: select * from _sys_afl.plugin_errors
                    parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.ORACLE.getValue()));
                    parser.setSqltext(sql);
                    ret = parser.parse();
                    if (ret != 0) {
                        if (DatabaseType.G_BASE_8A.getValue().equals(dbType)) {
                            parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.SQL_SERVER.getValue()));
                            parser.setSqltext(sql);
                            ret = parser.parse();
                            if (ret != 0) {
                                buildParserResultLog(parser.getErrormessage(), sql);
                                sqlParserResultMessage.setErrorMessage(parser.getErrormessage());
                            }
                        } else {
                            buildParserResultLog(parser.getErrormessage(), sql);
                            sqlParserResultMessage.setErrorMessage(parser.getErrormessage());
                        }
                    }
                } else if (DatabaseType.CLICKHOUSE.getValue().equals(dbType)) {
                    try {
                        sqlParserResultMessage.setAst(getAst(sql, DatabaseType.CLICKHOUSE.getValue()));
                        sqlParserResultMessage.setErrorMessage(null);
                    } catch (Exception e) {
                        logger.error("Parser clickhouse fail!", e);
                    }
                } else if (DatabaseType.getIdentCode(DatabaseType.needCheckAgainByPostGreSql()).contains(dbType)) {
                    // 再用pgsql解析一遍, 如(adb-mysql3)以下sql是pgsql语法
                    // select * from (SELECT name,age FROM table1) as a(name1);
                    // SELECT * FROM table1 except SELECT * FROM table2;
                    // 河图(presto)的update语句不支持解析
                    parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.PG_SQL.getValue()));
                    parser.setSqltext(sql);
                    ret = parser.parse();
                    if (ret != 0) {
                        buildParserResultLog(parser.getErrormessage(), sql);
                        sqlParserResultMessage.setErrorMessage(parser.getErrormessage());
                    }
                } else {
                    buildParserResultLog(parser.getErrormessage(), sql);
                    sqlParserResultMessage.setErrorMessage(parser.getErrormessage());
                }
            }

            DCustomSqlStatement sqlStatement = parser.sqlstatements.size() > 0 ? parser.sqlstatements.get(0) : null;

            if (sqlStatement == null) {
                return null;
            }

            if (DatabaseType.INCEPTOR.getValue().equals(dbType)) {
                if (!CommonUtil.anonymousBlockOrContains(sqlStatement)
                        && List.of(SqlConstant.KEY_BEGIN, SqlConstant.KEY_DECLARE).contains(sqlStatement.getStartToken().toString().toUpperCase(Locale.ROOT))
                ) {
                    parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.ORACLE.getValue()));
                    parser.setSqltext(sql);
                    int tempRet = parser.parse();
                    DCustomSqlStatement tempStatement = parser.sqlstatements.get(0);
                    if (List.of(SqlConstant.KEY_BEGIN, SqlConstant.KEY_DECLARE).contains(tempStatement.getStartToken().toString().toUpperCase(Locale.ROOT))
                        && lastKeywordTokenIsEnd(tempStatement)) {
                        ret = tempRet;
                        sqlStatement = tempStatement;
                        if (ret == 0) {
                            sqlParserResultMessage.setErrorMessage(null);
                        }
                    }
                } else if ("/".equals(sql.strip())) {
                    parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.ORACLE.getValue()));
                    parser.setSqltext(sql);
                    ret = parser.parse();
                    sqlStatement = parser.sqlstatements.get(0);
                    if (ret == 0) {
                        sqlStatement.getSyntaxErrors().clear();
                        sqlParserResultMessage.setErrorMessage(null);
                    } else {
                        sqlParserResultMessage.setErrorMessage(sqlStatement.getErrormessage());
                    }
                }
            }

            if (DatabaseType.getIdentCode(DatabaseType.getNeedExplainParser()).contains(dbType) && SqlConstant.KEY_EXPLAIN.equalsIgnoreCase(sqlStatement.getStartToken().toString())) {
                // explain 用mysql解析
                parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.MYSQL.getValue()));
                parser.setSqltext(sql);
                ret = parser.parse();
                if (ret == 0) {
                    sqlStatement = parser.sqlstatements.get(0);
                    sqlParserResultMessage.setErrorMessage(null);
                }
            }

            if (DatabaseType.CLICKHOUSE.getValue().equals(dbType) && sqlParserResultMessage.getAst() == null
                    && sqlStatement.getStartToken() != null && sqlStatement.getStartToken().toString() != null
                    && SqlConstant.KEY_SELECT.equals(sqlStatement.getStartToken().toString().toUpperCase(Locale.ROOT))) {
                try {
                    sqlParserResultMessage.setAst(getAst(sql, DatabaseType.CLICKHOUSE.getValue()));
                    sqlParserResultMessage.setErrorMessage(null);
                } catch (Exception e) {
                    logger.error("Parser clickhouse select fail!", e);
                }
            }

            //mysql -- some select sql, need parsed by oracle again
            if (ret != 0 && DatabaseType.MYSQL.getValue().equals(dbType)) {
                //for example : select floor(abs(cast(a.id as float(24)))/7.5) from t1 a;
                if (sqlStatement.sqlstatementtype == ESqlStatementType.sstselect && sqlStatement.getTokenList() != null) {
                    parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.ORACLE.getValue()));
                    parser.setSqltext(sql);
                    ret = parser.parse();
                    if (ret == 0) {
                        sqlStatement = parser.sqlstatements.get(0);
                        sqlParserResultMessage.setErrorMessage(null);
                    }
                }
            }

            //doris
            if (ret != 0 && DatabaseType.DORIS.getValue().equals(dbType)) {
                //doris 的 select ... [GROUP BY [GROUPING SETS | ROLLUP | CUBE] {col_name | expr | position}] 语法和 postgresql 语法类似。
                if (sqlStatement.sqlstatementtype == ESqlStatementType.sstselect && sqlStatement.getTokenList() != null) {
                    parser = new DPSqlParser(DatabaseTypeUtils.getEDbVendor(DatabaseType.PG_SQL.getValue()));
                    parser.setSqltext(sql);
                    ret = parser.parse();
                    if (ret == 0) {
                        sqlStatement = parser.sqlstatements.get(0);
                        sqlParserResultMessage.setErrorMessage(null);
                    }
                }
            }

            //匿名块只需要知道整体是匿名块即可。旧解析器很大概率对匿名块中的内容解析不对。
            if (ret != 0 && DatabaseType.of(dbType) != DatabaseType.NULL) {
                if (CommonUtil.blockInstance(sqlStatement)) {
                    ret = 0;
                    sqlStatement.getSyntaxErrors().clear();
                    sqlParserResultMessage.setErrorMessage(null);
                }
            }

            sqlParserResultMessage.settCustomSqlStatement(sqlStatement);

            return sqlParserResultMessage;
        } catch (Exception e) {
            String error = "SqlParserUtil::split : Sql split catch exception. Sql is : " + sql;
            logger.error(error);
            throw new RuntimeException(error + "\n" + e.getMessage());
        }
    }

    public static List<SqlParseResult> dcSqlParser(String sql, Integer dbType) {

        try {
            List<SqlParseResult> list = new ArrayList<>();

            DCSqlParser parser = new DCSqlParser(DatabaseTypeUtils.getDbVendor(dbType));
            parser.setSqlText(sql);
            parser.parse();

            List<CustomSqlStatement> sqlStatements = parser.customSqlStatements;

            for (CustomSqlStatement statement : sqlStatements) {
                SqlParseResult sqlParserResultMessage = new SqlParseResult();
                sqlParserResultMessage.setCustomSqlStatement(statement);
                sqlParserResultMessage.setRealSql(sql);
                list.add(sqlParserResultMessage);
            }

            return list;
        } catch (Exception e) {
            String error = "SqlParserUtil::dcSqlParser : Sql parse catch exception. Sql is : " + sql;
            logger.error(error);
            throw new RuntimeException(error + "\n" + e.getMessage());
        }
    }

    public static Object getAst(String sql, Integer dbType) {
        try {
            DCSqlParser parser = new DCSqlParser(DatabaseTypeUtils.getDbVendor(dbType));
            parser.setSqlText(sql);
            parser.parse();
            return parser.customSqlStatements.get(0);
        } catch (Exception e) {
            String error = "SqlParserUtil::getAst : Sql parse catch exception. Sql is : " + sql;
            logger.error(error);
            throw new RuntimeException(error + "\n" + e.getMessage());
        }
    }

    public static void parserLog(String errorMessage, String sql) {
        logger.debug("SqlParserUtil::parser : error message is: " + errorMessage);
        logger.debug("SqlParserUtil::parser : error sql is: " + sql);
    }

    public static void buildParserResultLog(String errorMessage, String sql) {
        logger.debug("SqlParserUtil::buildParserResult : error message is: " + errorMessage);
        logger.debug("SqlParserUtil::buildParserResult : error sql is: " + sql);
    }

    public static String replaceChineseSymbols(DCustomSqlStatement dCustomSqlStatement, String singleSql, Integer dbType) {
        try {
            if (DatabaseType.getIdentCode(DatabaseType.getReplaceChineseSymbols()).contains(dbType) && checkCharactersByIteration(singleSql)) {
                // 将oracle等的中文符号替换为英文符号方便解析,原始sql保存起来发给执行器
                if (dCustomSqlStatement instanceof TSelectSqlStatement ||
                        dCustomSqlStatement instanceof TInsertSqlStatement ||
                        dCustomSqlStatement instanceof TUpdateSqlStatement ||
                        dCustomSqlStatement instanceof TDeleteSqlStatement) {
                    String regex = "(\\s*[,(=]?\\s*'\\s*)[\\s\\S]*?(\\s*'\\s*([=,;)]?\\s*|$))";
                    return StringUtils.reverseReplace(singleSql, regex,
                            s -> s.replace("（", "(")
                                    .replace("，", ",")
                                    .replace("）", ")")
                                    .replace(" ", " ")
                                    .replace("\u3000", " "));
                } else {
                    // 其他类型全部走简单替换，计算快
                    return convertChinesePunctuation(singleSql);
                }
            }

        } catch (Exception e) {
            logger.error("replace chinese symbols error!", e);
        }
        return singleSql;
    }

    public static String convertChinesePunctuation(String input) {

        char[] replacementMap = new char[65536];

        // 初始化所有字符默认映射为自己
        for (int i = 0; i < replacementMap.length; i++) {
            replacementMap[i] = (char) i;
        }
        // 设置中文符号到英文符号的映射
        replacementMap['，'] = ',';
        replacementMap['（'] = '(';
        replacementMap['）'] = ')';
        replacementMap[' '] = ' ';
        replacementMap['\u3000'] = ' ';

        StringBuilder sb = new StringBuilder(input.length());
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            sb.append(replacementMap[c]);
        }
        return sb.toString();
    }

    public static boolean checkCharactersByIteration(String str) {
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            switch (c) {
                case '（':
                case '，':
                case '）':
                case ' ':
                case '\u3000':
                    return true;
                default:
            }
        }
        return false;
    }

    public static String transformSingleSql(String singleSql, DCustomSqlStatement next, Integer dbType) {

        if (DatabaseType.DM.getValue().equals(dbType)) {
            if (SqlPreparedUtil.isDMFunction(singleSql)) {
                singleSql = "CALL " + singleSql; // 达梦 函数和存储过程不带call前缀的手动加上
            }
        } else if (next.getStartToken() != null) {
            if (DatabaseType.getPGSqlIntegerValueList().contains(dbType) && SqlConstant.KEY_WITH.equalsIgnoreCase(next.getStartToken().toString())) {
                singleSql = singleSql.replaceAll("(?i)VALUES", "SELECT");
            } else if (DatabaseType.DB2.getValue().equals(dbType) && SqlConstant.KEY_SELECT.equalsIgnoreCase(next.getStartToken().toString())) {
                singleSql = singleSql.replaceAll("(?i)with\\s+rr", " ").replaceAll("(?i)with\\s+rs", " ");
            } else if (DatabaseType.OCEAN_BASE_ORACLE.getValue().equals(dbType) && Arrays.asList(SqlConstant.KEY_DELETE, SqlConstant.KEY_UPDATE,
                    SqlConstant.KEY_INSERT).contains(next.getStartToken().toString().toUpperCase(Locale.ROOT))) {
                singleSql = singleSql.replaceAll("(?i)(RETURNING|RETURN)\\s+([0-9a-zA-Z_]+)\\s*([,\\s0-9a-zA-Z_]+)?", " ");
            } else if (DatabaseType.ADBMYSQL3.getValue().equals(dbType)) {
                if (SqlConstant.KEY_INSERT.equalsIgnoreCase(next.getStartToken().toString())) {
                    singleSql = singleSql.replaceFirst("(?i)OVERWRITE", " ");
                } else if (SqlConstant.KEY_CREATE.equalsIgnoreCase(next.getStartToken().toString())) {
                    singleSql = singleSql.replaceFirst("(?i)DISTRIBUTE\\s+BY\\s+HASH\\s+\\([0-9a-zA-Z_\"'\\[\\]`.:,]+\\)", " ");
                }
                singleSql = singleSql.replaceAll("`", "");
            } else if (DatabaseType.GOLDEN_DB.getValue().equals(dbType) && SqlConstant.KEY_SELECT.equalsIgnoreCase(next.getStartToken().toString())) {
                singleSql = singleSql.replaceAll("(?i)storagedb\\s+[0-9a-zA-Z_\"'\\[\\]`.]+", " ");
            } else if (DatabaseType.INCEPTOR.getValue().equals(dbType) && Arrays.asList(SqlConstant.KEY_DELETE, SqlConstant.KEY_UPDATE,
                    SqlConstant.KEY_INSERT, SqlConstant.KEY_DESC, SqlConstant.KEY_DESCRIBE).contains(next.getStartToken().toString().toUpperCase(Locale.ROOT))) {
                singleSql = RegularUtil.transformSql(singleSql, RegularType.PARTITION.getValue());
            } else if (DatabaseType.INCEPTOR.getValue().equals(dbType) && SqlConstant.KEY_MERGE.equalsIgnoreCase(next.getStartToken().toString())) {
                singleSql = RegularUtil.transformSql(singleSql, RegularType.MERGE_INTO_WHERE.getValue());
            } else if (Arrays.asList(DatabaseType.OSCAR.getValue(), DatabaseType.OSCAR_CLUSTER.getValue()).contains(dbType) && SqlConstant.KEY_SELECT.equalsIgnoreCase(next.getStartToken().toString())) {
                singleSql = RegularUtil.transformSql(singleSql, RegularType.DOUBLE_COLON.getValue());
                singleSql = RegularUtil.transformSql(singleSql, RegularType.OFFSET.getValue());
            } else if (DatabaseType.HETU.getValue().equals(dbType) && SqlConstant.KEY_INSERT.equalsIgnoreCase(next.getStartToken().toString())) {
                singleSql = RegularUtil.transformSql(singleSql, RegularType.STRUCT.getValue());
                singleSql = RegularUtil.transformSql(singleSql, RegularType.OVERWRITE_TO_INTO.getValue());
                singleSql = RegularUtil.transformSql(singleSql, RegularType.PARTITION.getValue());
            } else if (DatabaseType.HETU.getValue().equals(dbType) && SqlConstant.KEY_SELECT.equalsIgnoreCase(next.getStartToken().toString())) {
                singleSql = RegularUtil.transformSql(singleSql, RegularType.FETCH_FIRST_ROW_WITH_TIES.getValue());
            } else if (DatabaseType.HETU.getValue().equals(dbType) && SqlConstant.KEY_TRUNCATE.equalsIgnoreCase(next.getStartToken().toString())) {
                singleSql = RegularUtil.transformSql(singleSql, RegularType.PARTITION.getValue());
            } else if (DatabaseType.HETU.getValue().equals(dbType) && SqlConstant.KEY_SHOW.equalsIgnoreCase(next.getStartToken().toString())) {
                singleSql = RegularUtil.transformSql(singleSql, RegularType.PARTITIONS.getValue());
            } else if (DatabaseType.G_BASE_8S.getValue().equals(dbType) && SqlConstant.KEY_SELECT.equalsIgnoreCase(next.getStartToken().toString())) {
                singleSql = RegularUtil.transformSql(singleSql, RegularType.LIMIT.getValue());
            } else if (DatabaseType.OCEAN_BASE_MYSQL.getValue().equals(dbType) && SqlConstant.KEY_SELECT.equalsIgnoreCase(next.getStartToken().toString())) {
                singleSql = RegularUtil.transformSql(singleSql, RegularType.EXCEPT.getValue());
                singleSql = RegularUtil.transformSql(singleSql, RegularType.MINUS.getValue());
            } else if (DatabaseType.MYSQL.getValue().equals(dbType)) {
                if (SqlConstant.KEY_CREATE.equalsIgnoreCase(next.getStartToken().toString())) {
                    singleSql = RegularUtil.transformSql(singleSql, RegularType.START_TRANSACTION.getValue());
                } else if (next.sqlstatementtype == ESqlStatementType.sstselect) {
                    singleSql = RegularUtil.transformSql(singleSql, RegularType.PARTITION.getValue());
                }
            } else if (List.of(DatabaseType.HIVE.getValue(), DatabaseType.IMPALA.getValue(), DatabaseType.INCEPTOR.getValue()).contains(dbType) &&
                    "!".equals(next.getStartToken().toString())) {
                String regex = "^\\s*!SET\\s+plsqlUseSlash\\s+(\\S+)\\s*;?\\s*$";
                Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(singleSql);
                if (matcher.matches()) {
                    String value = matcher.group(1);
                    singleSql = "set plsql.use.slash = " + value + ";";
                }
            } else if (DatabaseType.IMPALA.getValue().equals(dbType) && next.sqlstatementtype == ESqlStatementType.sstselect) {
                //单行注释内容带空格兼容不友好例如:  --注释 空格注释
                singleSql = RegularUtil.transformSql(singleSql, RegularType.REMOVE_SQL_LINE_COMMENTS.getValue());
            } else if (DatabaseType.TDPG.getValue().equals(dbType) && next.getStartToken().toString().equalsIgnoreCase(SqlConstant.KEY_EXECUTE)) {
                // EXECUTE DIRECT ON (dn001) 'select * from t_direct'; 只保留select语句
                singleSql = RegularUtil.transformSql(singleSql, RegularType.TDPG_EXECUTE_DIRECT_ON_DN.getValue());
            } else if (DatabaseType.DORIS.getValue().equals(dbType) && next.sqlstatementtype != null) {
                switch (next.sqlstatementtype) {
                    case sstcreatetable:
                        singleSql = RegularUtil.transformSql(singleSql, RegularType.DORIS_REMOVE_PROPERTIES_OP.getValue());
                        break;
                    case sstdelete:
                        singleSql = RegularUtil.transformSql(singleSql, RegularType.DORIS_DELETE_REMOVE_PARTITION.getValue());
                        break;
                    case sstselect:
                        singleSql = RegularUtil.transformSql(singleSql, RegularType.DORIS_SELECT_REMOVE_ALLEXCEPT.getValue());
                        break;
                }
            } else if (DatabaseType.DB2.getValue().equals(dbType) && next.sqlstatementtype == ESqlStatementType.sstinsert) {
                singleSql = singleSql.replaceAll("\\s+(?i)CODEUNITS16", "");
            }

        }

        return singleSql;
    }

    public static boolean containsChineseSymbols(String singleSql) {
        return singleSql.contains("（") || singleSql.contains("，") || singleSql.contains("）") || singleSql.contains(" ");
    }

    public static boolean lastKeywordTokenIsEnd(DCustomSqlStatement statement) {
        if (statement == null) {
            return false;
        }

        boolean result = false;
        int times = 0; //单词出现次数
        DSourceToken currToken = statement.getEndToken();
        while (currToken != null && times < 2) {
            if (currToken.tokentype == ETokenType.ttkeyword || currToken.tokentype == ETokenType.ttidentifier) {
                times++;
                if (SqlConstant.KEY_END.equalsIgnoreCase(currToken.toString())) {
                    result = true;
                    break;
                }
            }
            currToken = currToken.getPrevTokenInChain();
        }
        return result;
    }

    public static String getLastKeywordTokenText(DCustomSqlStatement statement) {
        if (statement == null) {
            return null;
        }

        DSourceToken currToken = statement.getEndToken();
        while (currToken != null) {
            if (currToken.tokentype == ETokenType.ttkeyword || currToken.tokentype == ETokenType.ttidentifier) {
                return currToken.toString();
            }
            currToken = currToken.getPrevTokenInChain();
        }
        return null;
    }

}
