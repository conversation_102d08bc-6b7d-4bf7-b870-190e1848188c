package com.dc.summer.parser.utils.model;

import com.dc.stmt.CustomSqlStatement;
import com.dc.sqlparser.DCustomSqlStatement;

public class SqlParseStrengthen {
    private DCustomSqlStatement tCustomSqlStatement;
    private Object ast;
    private CustomSqlStatement customSqlStatement;
    private String errorMessage;
    private String sql;
    private String operation;
    private boolean isUpdateClause;
    private boolean isSubmitJob; // adb-mysql3的submit job INSERT INTO test SELECT * FROM test_external_table;

    public DCustomSqlStatement gettCustomSqlStatement() {
        return tCustomSqlStatement;
    }

    public void settCustomSqlStatement(DCustomSqlStatement tCustomSqlStatement) {
        this.tCustomSqlStatement = tCustomSqlStatement;
    }

    public Object getAst() {
        return ast;
    }

    public void setAst(Object ast) {
        this.ast = ast;
    }

    public CustomSqlStatement getCustomSqlStatement() {
        return customSqlStatement;
    }

    public void setCustomSqlStatement(CustomSqlStatement customSqlStatement) {
        this.customSqlStatement = customSqlStatement;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public boolean isUpdateClause() {
        return isUpdateClause;
    }

    public void setUpdateClause(boolean updateClause) {
        this.isUpdateClause = updateClause;
    }

    public boolean isSubmitJob() {
        return isSubmitJob;
    }

    public void setSubmitJob(boolean submitJob) {
        isSubmitJob = submitJob;
    }
}
