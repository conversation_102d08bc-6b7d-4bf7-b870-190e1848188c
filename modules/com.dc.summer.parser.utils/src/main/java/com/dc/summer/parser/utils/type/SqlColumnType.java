package com.dc.summer.parser.utils.type;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum SqlColumnType {
    // 常见字段类型
    INTEGER("integer", Category.NUMERIC),
    INT("int", Category.NUMERIC),
    SMALLINT("smallint", Category.NUMERIC),
    BIGINT("bigint", Category.NUMERIC),
    DECIMAL("decimal", Category.NUMERIC),
    NUMERIC("numeric", Category.NUMERIC),
    FLOAT("float", Category.NUMERIC),
    REAL("real", Category.NUMERIC),
    DOUBLE("double", Category.NUMERIC),
    MONEY("money", Category.NUMERIC),
    SMALLMONEY("smallmoney", Category.NUMERIC),

    CHAR("char", Category.STRING),
    VARCHAR("varchar", Category.STRING),
    TEXT("text", Category.STRING),
    NCHAR("nchar", Category.STRING),
    NVARCHAR("nvarchar", Category.STRING),
    CLOB("clob", Category.STRING),

    DATE("date", Category.DATETIME),
    DATETIME("datetime", Category.DATETIME),
    TIMESTAMP("timestamp", Category.DATETIME),
    TIME("time", Category.DATETIME),
    YEAR("year", Category.DATETIME),

    BINARY("binary", Category.BLOB),
    VARBINARY("varbinary", Category.BLOB),
    BLOB("blob", Category.BLOB),
    LONGBLOB("longblob", Category.BLOB),

    BOOLEAN("boolean", Category.LOGICAL),
    BIT("bit", Category.LOGICAL);
    private final String typeName;
    private final Category category;

    SqlColumnType(String typeName, Category category) {
        this.typeName = typeName;
        this.category = category;
    }

    public String getTypeName() {
        return typeName;
    }

    public Category getCategory() {
        return category;
    }

    public static List<String> getTypesByCategory(Category category) {
        return Arrays.stream(values())
                .filter(t -> t.getCategory() == category)
                .map(SqlColumnType::getTypeName)
                .collect(Collectors.toList());
    }

    public static List<String> getNumericTypes() {
        return getTypesByCategory(Category.NUMERIC);
    }

    public static List<String> getStringTypes() {
        return getTypesByCategory(Category.STRING);
    }

    public static List<String> getDatetimeTypes() {
        return getTypesByCategory(Category.DATETIME);
    }

    public static List<String> getBlobTypes() {
        return getTypesByCategory(Category.BLOB);
    }

    public static List<String> getLogicalTypes() {
        return getTypesByCategory(Category.LOGICAL);
    }

    public enum Category {
        NUMERIC,   //数值类型
        STRING, //字符串类型
        DATETIME,   //日期时间类型
        BLOB,   //二进制类型
        LOGICAL //逻辑类型
    }

}
