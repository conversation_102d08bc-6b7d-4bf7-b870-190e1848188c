package com.dc.summer.parser.utils.model;

import lombok.Data;

/**
 * 和 数据库类型 相关的一个模型
 * 属性都是围绕dbType
 */
@Data
public class DbTypeRelatedModel {

    private Integer dbType;

    private String delimiter; //分隔符

    private boolean isDualWhite; //dual是否是白名单表

    public DbTypeRelatedModel() {}

    public DbTypeRelatedModel(String delimiter, boolean isDualWhite) {
        this.delimiter = delimiter;
        this.isDualWhite = isDualWhite;
    }
}
