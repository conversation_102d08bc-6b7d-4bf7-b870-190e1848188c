package com.dc.summer.parser.utils.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SqlCallFunctionResult {

    private String objectType;
    private String objectName;
    private String operation;

    private boolean isChangeSchema;
    private boolean hasWhereClause;

    private List<String> funcArgs;

    public SqlCallFunctionResult() {
        this.objectType = "";
        this.objectName = "";
        this.operation = "";
        this.isChangeSchema = false;
        this.hasWhereClause = false;
        this.funcArgs = new ArrayList<>();
    }
}
