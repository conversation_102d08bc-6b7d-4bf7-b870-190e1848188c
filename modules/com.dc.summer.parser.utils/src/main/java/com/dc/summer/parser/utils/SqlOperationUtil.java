package com.dc.summer.parser.utils;

import com.dc.parser.mongodb.model.FindSqlStatement;
import com.dc.summer.parser.sql.constants.OperationAuthConstant;
import com.dc.summer.parser.sql.constants.RedisSqlConstant;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.stmt.*;
import com.dc.type.DatabaseType;
import com.dc.sqlparser.types.EExpressionType;
import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.nodes.TJoin;
import com.dc.sqlparser.nodes.TJoinItem;
import com.dc.sqlparser.nodes.TJoinItemList;
import com.dc.sqlparser.nodes.TResultColumn;
import com.dc.sqlparser.stmt.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class SqlOperationUtil {

    private SqlOperationUtil() {}

    public static String transformOperation(CustomSqlStatement customSqlStatement, Integer dbType) {

        if (customSqlStatement instanceof SelectSqlStatement) {
            return SqlConstant.KEY_SELECT;
        } else if (customSqlStatement instanceof InsertSqlStatement) {
            return SqlConstant.KEY_INSERT;
        } else if (customSqlStatement instanceof UpdateSqlStatement) {
            return SqlConstant.KEY_UPDATE;
        } else if (customSqlStatement instanceof DeleteSqlStatement) {
            return SqlConstant.KEY_DELETE;
        } else if (customSqlStatement instanceof CreateSqlStatement) {
            return SqlConstant.KEY_CREATE;
        } else if (customSqlStatement instanceof AlterSqlStatement) {
            return SqlConstant.KEY_ALTER;
        } else if (customSqlStatement instanceof DropSqlStatement) {
            return SqlConstant.KEY_DROP;
        } else if (customSqlStatement instanceof ShowSqlStatement) {
            return SqlConstant.KEY_SHOW;
        } else if (DatabaseType.REDIS.getValue().equals(dbType) && customSqlStatement != null && customSqlStatement.getOperation() != null) {
            if (Arrays.asList(RedisSqlConstant.CREATE_OPERATION).contains(customSqlStatement.getOperation().toUpperCase(Locale.ROOT))) {
                return SqlConstant.KEY_CREATE;
            } else if (Arrays.asList(RedisSqlConstant.ALTER_OPERATION).contains(customSqlStatement.getOperation().toUpperCase(Locale.ROOT))) {
                return SqlConstant.KEY_ALTER;
            } else if (Arrays.asList(RedisSqlConstant.DROP_OPERATION).contains(customSqlStatement.getOperation().toUpperCase(Locale.ROOT))) {
                return SqlConstant.KEY_DROP;
            } else if (Arrays.asList(RedisSqlConstant.DELETE_OPERATION).contains(customSqlStatement.getOperation().toUpperCase(Locale.ROOT))) {
                return SqlConstant.KEY_DELETE;
            } else if (Arrays.asList(RedisSqlConstant.UPDATE_OPERATION).contains(customSqlStatement.getOperation().toUpperCase(Locale.ROOT))) {
                return SqlConstant.KEY_UPDATE;
            } else if (Arrays.asList(RedisSqlConstant.SELECT_OPERATION).contains(customSqlStatement.getOperation().toUpperCase(Locale.ROOT))) {
                return SqlConstant.KEY_SELECT;
            } else if (RedisSqlConstant.KEY_SELECT.equalsIgnoreCase(customSqlStatement.getOperation())) {
                return RedisSqlConstant.KEY_SELECT_DATABASE;
            }
        }

        return customSqlStatement == null ? "" : customSqlStatement.getOperation() != null ? customSqlStatement.getOperation().toUpperCase(Locale.ROOT) : "";
    }

    public static List<String> getOtherOperations(DCustomSqlStatement tCustomSqlStatement, Integer dbType, CustomSqlStatement customSqlStatement) {
        List<String> otherOperations = new ArrayList<>();

        GetTablesUtil getTables = new GetTablesUtil();
        GetColumnsUtil getColumns = new GetColumnsUtil();
        getColumns.setDbType(dbType);
        Set<String> functions;

        try {
            if (tCustomSqlStatement instanceof TSelectSqlStatement) {
                TSelectSqlStatement tSelectSqlStatement = (TSelectSqlStatement) tCustomSqlStatement;
                if (tSelectSqlStatement.getIntoClause() != null && tSelectSqlStatement.getIntoClause().getExprList() != null) {
                    otherOperations.add(OperationAuthConstant.create);
                    otherOperations.add(OperationAuthConstant.create_table);
                } else if (tSelectSqlStatement.getIntoClause() != null && tSelectSqlStatement.getIntoClause().getIntoName() != null) {
                    otherOperations.add(OperationAuthConstant.create);
                    otherOperations.add(OperationAuthConstant.create_table);
                }
                functions = getColumns.getNeedFunctions(tCustomSqlStatement);
                if (!functions.isEmpty()) {
                    otherOperations.add(OperationAuthConstant.statistics);
                }
            } else if (tCustomSqlStatement instanceof TInsertSqlStatement) {
                TInsertSqlStatement tInsertSqlStatement = (TInsertSqlStatement) tCustomSqlStatement;
                if (tInsertSqlStatement.getSubQuery() != null) {
                    otherOperations.add(OperationAuthConstant.select);
                } else if (tInsertSqlStatement.getOnDuplicateKeyUpdate() != null && tInsertSqlStatement.getOnDuplicateKeyUpdate().size() > 0) {
                    otherOperations.add(OperationAuthConstant.update);
                }
                getTables.analyzeInsertStatement(tInsertSqlStatement);
                functions = getTables.getFunctions();
                if (!functions.isEmpty()) {
                    otherOperations.add(OperationAuthConstant.statistics);
                }
            } else if (tCustomSqlStatement instanceof TUpdateSqlStatement) {
                TUpdateSqlStatement tUpdateSqlStatement = (TUpdateSqlStatement) tCustomSqlStatement;
                getTables.analyzeUpdateStatement(tUpdateSqlStatement);
                functions = getTables.getFunctions();
                if (!functions.isEmpty()) {
                    otherOperations.add(OperationAuthConstant.statistics);
                    otherOperations.add(OperationAuthConstant.select);
                } else {
                    boolean containsSubQuery = containsSubQuery(tCustomSqlStatement);
                    if (containsSubQuery) {
                        otherOperations.add(OperationAuthConstant.select);
                    }
                }
            } else if (tCustomSqlStatement instanceof TDeleteSqlStatement) {
                TDeleteSqlStatement tDeleteSqlStatement = (TDeleteSqlStatement) tCustomSqlStatement;
                getTables.analyzeDeleteStatement(tDeleteSqlStatement);
                functions = getTables.getFunctions();
                if (!functions.isEmpty()) {
                    otherOperations.add(OperationAuthConstant.statistics);
                    otherOperations.add(OperationAuthConstant.select);
                } else {
                    boolean containsSubQuery = containsSubQuery(tCustomSqlStatement);
                    if (containsSubQuery) {
                        otherOperations.add(OperationAuthConstant.select);
                    }
                }
            } else if (tCustomSqlStatement instanceof TMergeSqlStatement) {
                TMergeSqlStatement tMergeSqlStatement = (TMergeSqlStatement) tCustomSqlStatement;
                getTables.analyzeMergeStatement(tMergeSqlStatement);
                functions = getTables.getFunctions();
                if (!functions.isEmpty()) {
                    otherOperations.add(OperationAuthConstant.statistics);
                    otherOperations.add(OperationAuthConstant.select);
                } else {
                    if (tMergeSqlStatement.getUsingTable() != null && tMergeSqlStatement.getUsingTable().getSubquery() != null) {
                        otherOperations.add(OperationAuthConstant.select);
                    }
                }
            } else if (tCustomSqlStatement instanceof TCreateTableSqlStatement) {
                TCreateTableSqlStatement tCreateTableSqlStatement = (TCreateTableSqlStatement) tCustomSqlStatement;
                if (tCreateTableSqlStatement.getSubQuery() != null) {
                    functions = getColumns.getNeedFunctions(tCreateTableSqlStatement.getSubQuery());
                    if (!functions.isEmpty()) {
                        otherOperations.add(OperationAuthConstant.statistics);
                        otherOperations.add(OperationAuthConstant.select);
                    } else {
                        otherOperations.add(OperationAuthConstant.select);
                    }
                }
            } else if (tCustomSqlStatement instanceof TCreateViewSqlStatement) {
                TCreateViewSqlStatement statement = (TCreateViewSqlStatement) tCustomSqlStatement;
                if (statement.getSubquery() != null) {
                    functions = getColumns.getNeedFunctions(statement.getSubquery());
                    if (!functions.isEmpty()) {
                        otherOperations.add(OperationAuthConstant.statistics);
                        otherOperations.add(OperationAuthConstant.select);
                    } else {
                        otherOperations.add(OperationAuthConstant.select);
                    }
                }
            } else if (customSqlStatement instanceof FindSqlStatement && !CommonUtil.getMongoDBAggregateFunction(customSqlStatement).isEmpty()) {
                otherOperations.add(OperationAuthConstant.statistics);
            } else if (customSqlStatement instanceof SelectSqlStatement && !CommonUtil.getElasticSearchAggregateFunction(customSqlStatement).isEmpty()) {
                otherOperations.add(OperationAuthConstant.statistics);
            }

            if (CommonUtil.anonymousBlockOrContains(tCustomSqlStatement)) {
                otherOperations.add(OperationAuthConstant.begin_end);
            }

        } catch (Exception e) {
            log.error("get other operations error!", e);
        }

        return otherOperations;
    }

    public static boolean containsSubQuery(DCustomSqlStatement tCustomSqlStatement) {
        boolean containsSubQuery = false;
        try {
            if (tCustomSqlStatement.getResultColumnList() != null) {
                for (int i = 0; i < tCustomSqlStatement.getResultColumnList().size(); i++) {
                    TResultColumn field = tCustomSqlStatement.getResultColumnList().getResultColumn(i);
                    if (field != null && field.getExpr() != null && field.getExpr().getRightOperand() != null
                            && field.getExpr().getRightOperand().getExpressionType() == EExpressionType.subquery_t) {
                        containsSubQuery = true;
                        break;
                    }
                }
            }
            if (tCustomSqlStatement.joins != null) {
                for (int i = 0; i < tCustomSqlStatement.joins.size(); i++) {
                    TJoin join = tCustomSqlStatement.joins.getJoin(i);
                    if (containsSubQuery) {
                        break;
                    }
                    if (join != null && join.getJoinItems() != null) {
                        TJoinItemList items = join.getJoinItems();
                        for (int j = 0; j < items.size(); j++) {
                            TJoinItem item = items.getJoinItem(j);
                            if (item != null && item.getOnCondition() != null && item.getOnCondition().getSubQuery() != null) {
                                containsSubQuery = true;
                                break;
                            }
                        }
                    }
                }
            }
            if (tCustomSqlStatement.getWhereClause() != null
                    && tCustomSqlStatement.getWhereClause().getCondition() != null) {
                if (tCustomSqlStatement.getWhereClause().getCondition().getSubQuery() != null) {
                    containsSubQuery = true;
                } else if (tCustomSqlStatement.getWhereClause().getCondition().getRightOperand() != null
                        && tCustomSqlStatement.getWhereClause().getCondition().getRightOperand().getSubQuery() != null) {
                    containsSubQuery = true;
                }

            }

        } catch (Exception e) {
            log.error("judge contains subQuery error!", e);
        }

        return containsSubQuery;
    }

}
