package com.dc.summer.parser.utils;

import com.dc.type.DBVendor;
import com.dc.type.DatabaseType;
import com.dc.sqlparser.types.EDbType;

public class DatabaseTypeUtils {

    public static DBVendor getDbVendor(Integer dbType) {
        String name = DatabaseType.of(dbType).getDbVendor();
        for (DBVendor dbVendor : DBVendor.values()) {
            if (dbVendor.name().equals(name)) {
                return dbVendor;
            }
        }
        return null;
    }

    public static EDbType getEDbVendor(Integer dbType) {
        String name = DatabaseType.of(dbType).getEDbVendor();
        for (EDbType eDbVendor : EDbType.values()) {
            if (eDbVendor.name().equals(name)) {
                return eDbVendor;
            }
        }
        return null;
    }

}
