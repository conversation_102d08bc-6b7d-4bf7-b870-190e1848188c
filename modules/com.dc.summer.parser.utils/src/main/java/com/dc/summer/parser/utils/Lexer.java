package com.dc.summer.parser.utils;

public class Lexer {

    public static final String LP_;
    public static final String RP_;
    public static final String DQ_;
    public static final String SQ_;
    public static final String BQ_;
    public static final String PIPE_;
    public static final String STAR_;
    public static final String DOT_;

    public static final String IDENTIFIER;
    public static final String STRING_LIT_NAME;
    public static final String SINGLE_OBJECT_NAME;
    public static final String STRICT_COMPOSE_NAME;

    static {
        LP_ = "(";
        RP_ = ")";
        DQ_ = "\"";
        SQ_ = "'";
        BQ_ = "`";
        PIPE_ = "|";
        STAR_ = "*";
        DOT_ = "\\.";

        IDENTIFIER = "[A-Za-z0-9_@]+";
        STRING_LIT_NAME = LP_ + DQ_ + IDENTIFIER + DQ_ + PIPE_ + SQ_ + IDENTIFIER + SQ_ + PIPE_ + BQ_ + IDENTIFIER + BQ_ + RP_;
        SINGLE_OBJECT_NAME = LP_ + IDENTIFIER + PIPE_ + STRING_LIT_NAME + RP_;
        STRICT_COMPOSE_NAME = SINGLE_OBJECT_NAME + LP_ + DOT_ + SINGLE_OBJECT_NAME + RP_ + STAR_;
    }

    private Lexer() {}
}
