package com.dc.summer.parser.utils.model;

import com.dc.stmt.CustomSqlStatement;
import com.dc.sqlparser.DCustomSqlStatement;
import lombok.Data;

import java.io.Serializable;

@Data
public class SimpleParseResult implements Serializable {

    private String sql;     // SQL CONTENT
    private String operation;    // SELECT,UPDATE ...
    private String errorMessage; // 是否有错误信息
    private DCustomSqlStatement tCustomSqlStatement;
    private CustomSqlStatement customSqlStatement;

    public SimpleParseResult() {
    }

    public SimpleParseResult(String sql, String operation) {
        this.sql = sql;
        this.operation = operation;
    }
}
