package com.dc.summer.parser.utils;

import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.stmt.*;
import com.dc.sqlparser.stmt.db2.TDb2CallStmt;
import com.dc.sqlparser.stmt.db2.TDb2CreateFunction;
import com.dc.sqlparser.stmt.db2.TDb2CreateProcedure;
import com.dc.sqlparser.stmt.hive.*;
import com.dc.sqlparser.stmt.informix.TInformixCreateFunction;
import com.dc.sqlparser.stmt.informix.TInformixCreateProcedure;
import com.dc.sqlparser.stmt.mssql.*;
import com.dc.sqlparser.stmt.mysql.TMySQLCallStmt;
import com.dc.sqlparser.stmt.mysql.TMySQLCreateFunction;
import com.dc.sqlparser.stmt.mysql.TMySQLCreateProcedure;
import com.dc.sqlparser.stmt.oracle.*;

import java.util.stream.Stream;

public class GetOperationUtil {

    public static String getOperation(DCustomSqlStatement tCustomSqlStatement) {
        String operation = tCustomSqlStatement.getStartToken().toString();

        if (tCustomSqlStatement instanceof TSelectSqlStatement) {
            operation = "SELECT";
        } else if (tCustomSqlStatement instanceof TInsertSqlStatement) {
            operation = "INSERT";
        } else if (tCustomSqlStatement instanceof TUpdateSqlStatement) {
            operation = "UPDATE";
        } else if (tCustomSqlStatement instanceof TDeleteSqlStatement) {
            operation = "DELETE";
        } else if (tCustomSqlStatement instanceof TMergeSqlStatement) {
            operation = "MERGE";
        } else if (tCustomSqlStatement instanceof TExplainPlan) {
            operation = "EXPLAIN";
        }
        // DDL
        else if (tCustomSqlStatement instanceof TCommentOnSqlStmt) {
            operation = "COMMENT";
        } else if (tCustomSqlStatement instanceof TTruncateStatement) {
            operation = "TRUNCATE";
        } else if (tCustomSqlStatement instanceof TCreateTablespaceStmt) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TCreateDatabaseSqlStatement) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropDatabaseStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TAlterDatabaseStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateDatabaseLinkStmt) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropDatabaseLinkStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TCreateUserStmt) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropUserStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TAlterUserStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateSchemaSqlStatement) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropSchemaSqlStatement) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TAlterSchemaStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateRoleStmt) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropRoleStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TAlterRoleStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TDropProcedureStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TCreateProcedureStmt) {
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "CREATE";
            }
        } else if (tCustomSqlStatement instanceof TPlsqlCreateProcedure) {
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "CREATE";
            }
        } else if (tCustomSqlStatement instanceof TMssqlCreateProcedure) {
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "CREATE";
            }
        } else if (tCustomSqlStatement instanceof TMySQLCreateProcedure) {
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "CREATE";
            }
        } else if (tCustomSqlStatement instanceof TDb2CreateProcedure) {
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "CREATE";
            }
        } else if (tCustomSqlStatement instanceof TInformixCreateProcedure) {
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "CREATE";
            }
        } else if (tCustomSqlStatement instanceof TDropTableSqlStatement) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TCreateTableSqlStatement) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TAlterTableStatement) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TRenameStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TAlterTriggerStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateTriggerStmt) {
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "CREATE";
            }
        } else if (tCustomSqlStatement instanceof TPlsqlCreateTrigger) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropViewSqlStatement) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TCreateViewSqlStatement) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TAlterViewStatement) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateMaterializedSqlStatement) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropMaterializedViewStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TAlterMaterializedViewStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateMaterializedViewLogSqlStatement) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropMaterializedViewLogStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TAlterFunctionStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateFunctionStmt) {
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "CREATE";
            }
        } else if (tCustomSqlStatement instanceof TDropFunctionStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TMssqlCreateFunction) {
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "CREATE";
            }
        } else if (tCustomSqlStatement instanceof TPlsqlCreateFunction) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TMySQLCreateFunction) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDb2CreateFunction) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TInformixCreateFunction) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TCreateSequenceStmt) {
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "CREATE";
            }
        } else if (tCustomSqlStatement instanceof TDropSequenceStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TCreateIndexSqlStatement) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TAlterIndexStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TDropIndexSqlStatement) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TAlterSynonymStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateSynonymStmt) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropSynonymStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TPlsqlCreatePackage) {
            operation = "CREATE";
        }
        // others
        else if (tCustomSqlStatement instanceof TMySQLCallStmt) {
            operation = "CALL";
        } else if (tCustomSqlStatement instanceof TDb2CallStmt) {
            operation = "CALL";
        } else if (tCustomSqlStatement instanceof TCallStatement) {
            operation = "CALL";
        } else if (tCustomSqlStatement instanceof TExecutePreparedStatement) {
            operation = "EXECUTE";
        } else if (tCustomSqlStatement instanceof TOracleExecuteProcedure) {
            operation = "EXECUTE";
        } else if (tCustomSqlStatement instanceof TMssqlExecute) {
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "EXECUTE";
            }
        } else if (tCustomSqlStatement instanceof TMssqlExecuteAs) {
            operation = "EXECUTE";
        } else if (tCustomSqlStatement instanceof TDescribeStmt) {
            operation = "DESC";
        } else if (tCustomSqlStatement instanceof TUseDatabase) {
            operation = "USE";
        }
        // hive
        else if (tCustomSqlStatement instanceof THiveDropDatabase) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof THiveSwitchDatabase) {
            operation = "USE";
        } else if (tCustomSqlStatement instanceof THiveImportTable) {
            operation = "IMPORT";
        } else if (tCustomSqlStatement instanceof THiveExportTable) {
            operation = "EXPORT";
        } else if (tCustomSqlStatement instanceof THiveCreateFunction) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof THiveDropFunction) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof THiveCreateRole) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof THiveDropRole) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof THiveDescribe) {
            operation = "DESC";
        } else if (tCustomSqlStatement instanceof THiveLoad) {
            operation = "LOAD";
        } else if (tCustomSqlStatement instanceof THiveSet) {
            operation = "SET";
        } else if (tCustomSqlStatement instanceof THiveShow) {
            operation = "SHOW";
        } else if (tCustomSqlStatement instanceof THiveFromQuery) {
            operation = "INSERT";
        }
        // mssql
        else if (tCustomSqlStatement instanceof TMssqlSaveTran) {
            operation = "SAVE";
        }
        //匿名块
        else if (CommonUtil.blockInstance(tCustomSqlStatement)) {
            operation = "BEGIN_END";
        }

        return operation;
    }

    public static String getMssqlIfElseOperation(DCustomSqlStatement tCustomSqlStatement) {
        String operation = "";
        if (tCustomSqlStatement instanceof TDropProcedureStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TSelectSqlStatement) {
            operation = "SELECT";
        } else if (tCustomSqlStatement instanceof TInsertSqlStatement) {
            operation = "INSERT";
        } else if (tCustomSqlStatement instanceof TUpdateSqlStatement) {
            operation = "UPDATE";
        } else if (tCustomSqlStatement instanceof TDeleteSqlStatement) {
            operation = "DELETE";
        } else if (tCustomSqlStatement instanceof TMergeSqlStatement) {
            operation = "MERGE";
        } else if (tCustomSqlStatement instanceof TExplainPlan) {
            operation = "EXPLAIN";
        } else if (tCustomSqlStatement instanceof TCommentOnSqlStmt) {
            operation = "COMMENT";
        } else if (tCustomSqlStatement instanceof TTruncateStatement) {
            operation = "TRUNCATE";
        } else if (tCustomSqlStatement instanceof TCreateTablespaceStmt) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TCreateDatabaseSqlStatement) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropDatabaseStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TAlterDatabaseStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateDatabaseLinkStmt) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropDatabaseLinkStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TCreateUserStmt) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropUserStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TAlterUserStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateSchemaSqlStatement) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropSchemaSqlStatement) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TAlterSchemaStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateRoleStmt) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropRoleStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TAlterRoleStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateProcedureStmt) {
            operation = tCustomSqlStatement.getStartToken().toString();
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "CREATE";
            }
        } else if (tCustomSqlStatement instanceof TMssqlCreateProcedure) {
            operation = tCustomSqlStatement.getStartToken().toString();
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "CREATE";
            }
        } else if (tCustomSqlStatement instanceof TDropTableSqlStatement) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TCreateTableSqlStatement) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TAlterTableStatement) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TRenameStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TAlterTriggerStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateTriggerStmt) {
            operation = tCustomSqlStatement.getStartToken().toString();
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "CREATE";
            }
        } else if (tCustomSqlStatement instanceof TDropViewSqlStatement) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TCreateViewSqlStatement) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TAlterViewStatement) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateMaterializedSqlStatement) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropMaterializedViewStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TAlterMaterializedViewStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateMaterializedViewLogSqlStatement) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropMaterializedViewLogStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TAlterFunctionStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateFunctionStmt) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropFunctionStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TMssqlCreateFunction) {
            operation = tCustomSqlStatement.getStartToken().toString();
            if (!"ALTER".equalsIgnoreCase(operation)) {
                operation = "CREATE";
            }
        } else if (tCustomSqlStatement instanceof TCreateSequenceStmt) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropSequenceStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TCreateIndexSqlStatement) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TAlterIndexStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TDropIndexSqlStatement) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TAlterSynonymStmt) {
            operation = "ALTER";
        } else if (tCustomSqlStatement instanceof TCreateSynonymStmt) {
            operation = "CREATE";
        } else if (tCustomSqlStatement instanceof TDropSynonymStmt) {
            operation = "DROP";
        } else if (tCustomSqlStatement instanceof TCallStatement) {
            operation = "CALL";
        } else if (tCustomSqlStatement instanceof TExecutePreparedStatement) {
            operation = "EXECUTE";
        } else if (tCustomSqlStatement instanceof TMssqlExecute) {
            operation = "EXECUTE";
        } else if (tCustomSqlStatement instanceof TMssqlExecuteAs) {
            operation = "EXECUTE";
        } else if (tCustomSqlStatement instanceof TDescribeStmt) {
            operation = "DESC";
        } else if (tCustomSqlStatement instanceof TUseDatabase) {
            operation = "USE";
        } else if (tCustomSqlStatement instanceof TMssqlBlock) {
            TMssqlBlock tMssqlBlock = (TMssqlBlock) tCustomSqlStatement;
            if (tMssqlBlock.getStartToken() != null && "BEGIN".equalsIgnoreCase(tMssqlBlock.getStartToken().toString())) {
                operation = "BEGIN";
            }
        } else if (tCustomSqlStatement instanceof TMssqlCommit) {
            TMssqlCommit tMssqlCommit = (TMssqlCommit) tCustomSqlStatement;
            if (tMssqlCommit.getStartToken() != null && "COMMIT".equalsIgnoreCase(tMssqlCommit.getStartToken().toString())) {
                operation = "COMMIT";
            }
        } else if (tCustomSqlStatement instanceof TMssqlRollback) {
            TMssqlRollback tMssqlRollback = (TMssqlRollback) tCustomSqlStatement;
            if (tMssqlRollback.getStartToken() != null && "ROLLBACK".equalsIgnoreCase(tMssqlRollback.getStartToken().toString())) {
                operation = "ROLLBACK";
            }
        } else if (tCustomSqlStatement instanceof TBeginTran) {
            TBeginTran beginTran = (TBeginTran) tCustomSqlStatement;
            if (beginTran.getStartToken() != null && "BEGIN".equalsIgnoreCase(beginTran.getStartToken().toString())) {
                operation = "BEGIN";
            }
        }

        return operation;
    }
}
