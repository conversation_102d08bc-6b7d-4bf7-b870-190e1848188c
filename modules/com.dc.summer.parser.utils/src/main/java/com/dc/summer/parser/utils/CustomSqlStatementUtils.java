package com.dc.summer.parser.utils;

import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.stmt.TSelectSqlStatement;

public class CustomSqlStatementUtils {

    /**
     * 用来修复 GSQL Parser 中的 toScript 不准的问题。
     */
    public static String toScript(DCustomSqlStatement statement) {
        String script = statement.toScript();
        if (statement instanceof TSelectSqlStatement) {
            TSelectSqlStatement tSelectSqlStatement = (TSelectSqlStatement) statement;
            if (tSelectSqlStatement.getFetchFirstClause() != null) {
                // fix 无法拼接 fetch next 10 row only 问题
                script += "\n" + tSelectSqlStatement.getFetchFirstClause().toString();
            }
        }
        return script;
    }

}
