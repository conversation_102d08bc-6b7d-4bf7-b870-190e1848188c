package com.dc.summer.parser.sql.model;



import com.dc.summer.parser.sql.type.ClauseType;
import lombok.ToString;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

@ToString
public class ActionColumnEntry {
    public String expression = "";
    public String columnName;
    public String columnPrex;
    public String orignColumn;
    @ToString.Exclude
    public Point location;
    public List<String> tableNames = new ArrayList<>();
    public List<String> tableFullNames = new ArrayList<>();
    @ToString.Exclude
    public ClauseType clauseType;
    public String alias;

    public ActionColumnEntry() {

    }
}
