

package com.dc.summer.data.transfer.stream.model;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.impl.AbstractExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSInstance;

/**
 * Data container transfer producer
 */
public class StreamExecutionContext extends AbstractExecutionContext<StreamDataSource> {

    StreamExecutionContext(@NotNull StreamDataSource dataSource, String purpose) {
        super(dataSource, purpose);
    }

    @Override
    public DBSInstance getOwnerInstance() {
        return getDataSource();
    }

    @Override
    public boolean isConnected() {
        return true;
    }

    @NotNull
    @Override
    public StreamTransferSession openSession(@NotNull DBRProgressMonitor monitor, @NotNull DBCExecutionPurpose purpose, @NotNull String task) {
        return new StreamTransferSession(monitor, this, purpose, task);
    }

    @Override
    public void checkContextAlive(DBRProgressMonitor monitor) throws DBException {

    }

    @NotNull
    @Override
    public InvalidateResult invalidateContext(@NotNull DBRProgressMonitor monitor, boolean closeOnFailure, DBPConnectionConfiguration configuration,boolean shortConnect) throws DBException {
        return InvalidateResult.ALIVE;
    }

    @Override
    public String getProcessId() {
        return null;
    }

    @Override
    public void execPrefs(DBRProgressMonitor monitor, String prefs) throws DBCException {
    }

    @Override
    public DBPConnectionConfiguration getConfiguration() {
        return null;
    }

    @Override
    public void close() {

    }
}
