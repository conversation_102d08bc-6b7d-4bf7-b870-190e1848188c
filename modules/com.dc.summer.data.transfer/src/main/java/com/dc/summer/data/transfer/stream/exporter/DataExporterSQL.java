
package com.dc.summer.data.transfer.stream.exporter;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.data.transfer.exception.TransferInterruptException;
import com.dc.summer.model.*;
import com.dc.summer.model.data.*;
import com.dc.summer.model.data.json.JSONUtils;
import com.dc.summer.model.document.data.DBDataWrapper;
import com.dc.summer.model.document.data.DBListValue;
import com.dc.summer.model.document.data.DBMapValue;
import com.dc.summer.model.document.data.DBNullValue;
import com.dc.summer.model.document.data.format.DBFunctionObject;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.impl.sql.ChangeTableDataStatement;
import com.dc.summer.model.redis.DBPRedisObject;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLConstants;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.sql.SQLQueryContainer;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.sql.parser.SQLIdentifierDetector;
import com.dc.summer.model.struct.DBSDataManipulator;
import com.dc.summer.data.transfer.DTConstants;
import com.dc.summer.data.transfer.DTUtils;
import com.dc.summer.data.transfer.stream.IAppendableDataExporter;
import com.dc.summer.data.transfer.stream.IStreamDataExporterSite;
import com.dc.summer.utils.ContentUtils;
import com.dc.summer.utils.GeneralUtils;
import com.dc.type.DatabaseType;
import com.dc.utils.CommonUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * SQL Exporter
 */
public class DataExporterSQL extends StreamExporterAbstract implements IAppendableDataExporter {

    private static final Log log = Log.getLog(DataExporterSQL.class);

    private static final String PROP_INCLUDE_AUTO_GENERATED = "includeAutoGenerated";
    private static final String PROP_OMIT_SCHEMA = "omitSchema";
    private static final String PROP_ROWS_IN_STATEMENT = "rowsInStatement";
    private static final String PROP_DATA_FORMAT = "nativeFormat";
    private static final char STRING_QUOTE = '\'';
    private static final String STRING_EQUALS = " = ";
    private static final String PROP_LINE_BEFORE_ROWS = "lineBeforeRows";
    private static final String PROP_KEYWORD_CASE = "keywordCase";
    private static final String PROP_IDENTIFIER_CASE = "identifierCase";
    private static final String PROP_UPSERT = "upsertKeyword";
    private static final String PROP_EXPORT_SQL_TYPE = "exportSqlType";
    private static final String PROP_ON_CONFLICT = "insertOnConflict";

    private boolean includeAutoGenerated;
    private String rowDelimiter;
    private boolean omitSchema;
    private int rowsInStatement;
    private boolean useNativeDataFormat = true;
    private boolean lineBeforeRows = true;
    private String tableName;
    private DBDAttributeBinding[] columns;
    private boolean oneLineEntry;

    private boolean isDocument;

    private boolean isRedis;

    private boolean formatDateISO = true;

    private final String KEYWORD_INSERT_INTO = "INSERT INTO";
    private final String KEYWORD_VALUES = "VALUES";
    private final String KEYWORD_INTO = "INTO";
    private final static String KEYWORD_INSERT_ALL = "INSERT ALL";
    private final String KEYWORD_SELECT_FROM_DUAL = "SELECT 1 FROM DUAL";
    private final static String KEYWORD_UPDATE_OR = "UPDATE OR";
    private final static String KEYWORD_UPSERT_INTO = "UPSERT INTO";
    private final static String KEYWORD_REPLACE_INTO = "REPLACE INTO";
    private final static String KEYWORD_DUPLICATE_KEY = "ON DUPLICATE KEY UPDATE";
    private final static String KEYWORD_ON_CONFLICT = "ON CONFLICT";

    private DBPIdentifierCase identifierCase;
    private DBPIdentifierCase columnsAndTableNamesCase;
    private static String onConflictExpression;

    private transient StringBuilder sqlBuffer = new StringBuilder(100);
    private transient long rowCount;
    private SQLDialect dialect;

    enum ExportSqlType {
        INSERT("INSERT"),
        UPDATE("UPDATE");
        private String value;

        ExportSqlType(String v) {
            this.value = v;
        }

        public String value() {
            return value;
        }

        public static ExportSqlType fromValue(String v) {
            for (ExportSqlType s : ExportSqlType.values()) {
                if (s.value.equals(v)) {
                    return s;
                }
            }
            return INSERT;
        }
    }

    enum InsertKeyword {
        INSERT("INSERT"),
        INSERT_ALL(KEYWORD_INSERT_ALL),
        UPDATE(KEYWORD_UPDATE_OR),
        UPSERT(KEYWORD_UPSERT_INTO),
        REPLACE(KEYWORD_REPLACE_INTO),
        ON_DUPLICATE(KEYWORD_DUPLICATE_KEY),
        ON_CONFLICT(KEYWORD_ON_CONFLICT);
        private String value;

        InsertKeyword(String v) {
            this.value = v;
        }

        public String value() {
            return value;
        }

        public static InsertKeyword fromValue(String v) {
            for (InsertKeyword s : InsertKeyword.values()) {
                if (s.value.equals(v)) {
                    return s;
                }
            }
            return INSERT;
        }
    }

    private InsertKeyword insertKeyword;

    private ExportSqlType exportSqlType;

    private boolean isSkipColumn(DBDAttributeBinding attr) {
        return attr.isPseudoAttribute() || (!includeAutoGenerated && attr.isAutoGenerated()) ||
                attr instanceof DBDAttributeBindingCustom;
    }

    @Override
    public void init(IStreamDataExporterSite site) throws DBException {
        super.init(site);

        Map<String, Object> properties = site.getProperties();

        if (properties.containsKey(PROP_INCLUDE_AUTO_GENERATED)) {
//            includeAutoGenerated = CommonUtils.toBoolean(properties.get(PROP_INCLUDE_AUTO_GENERATED));
            includeAutoGenerated = true;
        }
        if (properties.containsKey(PROP_OMIT_SCHEMA)) {
            omitSchema = CommonUtils.toBoolean(properties.get(PROP_OMIT_SCHEMA));
        }
        try {
            rowsInStatement = CommonUtils.toInt(properties.get(PROP_ROWS_IN_STATEMENT));
        } catch (NumberFormatException e) {
            rowsInStatement = 10;
        }
        this.formatDateISO = CommonUtils.getBoolean(site.getProperties().get(DataExporterJSON.PROP_FORMAT_DATE_ISO), true);
        useNativeDataFormat = CommonUtils.toBoolean(properties.get(PROP_DATA_FORMAT));
        lineBeforeRows = CommonUtils.toBoolean(properties.get(PROP_LINE_BEFORE_ROWS));
        rowDelimiter = GeneralUtils.getDefaultLineSeparator();
        dialect = SQLUtils.getDialectFromObject(site.getSource());

        if (properties.containsKey(DBSDataManipulator.OPTION_USE_CURRENT_DIALECT_SETTINGS)) {
            boolean useDBDefaultValueMode = CommonUtils.toBoolean(properties.get(DBSDataManipulator.OPTION_USE_CURRENT_DIALECT_SETTINGS));
            if (useDBDefaultValueMode && getDefaultMultiValueInsertMode() != SQLDialect.MultiValueInsertMode.GROUP_ROWS) {
                rowsInStatement = 1;
            }
        }

        String keywordCase = CommonUtils.toString(properties.get(PROP_KEYWORD_CASE));
        if (keywordCase.equals("lower")) {
            identifierCase = DBPIdentifierCase.LOWER;
        } else {
            identifierCase = DBPIdentifierCase.UPPER;
        }

        String identifierCaseProp = CommonUtils.toString(properties.get(PROP_IDENTIFIER_CASE));
        if (identifierCaseProp.equals("as is")) {
            columnsAndTableNamesCase = DBPIdentifierCase.MIXED;
        } else if (identifierCaseProp.equals("lower")) {
            columnsAndTableNamesCase = DBPIdentifierCase.LOWER;
        } else {
            columnsAndTableNamesCase = DBPIdentifierCase.UPPER;
        }

        insertKeyword = InsertKeyword.fromValue(CommonUtils.toString(properties.get(PROP_UPSERT)));
        exportSqlType = ExportSqlType.fromValue(CommonUtils.toString(properties.get(PROP_EXPORT_SQL_TYPE)));
        onConflictExpression = CommonUtils.toString(properties.get(PROP_ON_CONFLICT));
    }

    @Override
    public void dispose() {
        super.dispose();
    }

    @Override
    public void createWaterMark() {

    }

    @Override
    public void exportHeader(DBCSession session) throws DBException, IOException {
        if (useNativeDataFormat) {
            if (session instanceof DBDFormatSettingsExt) {
                ((DBDFormatSettingsExt) session).setUseNativeDateTimeFormat(true);
            }
        }
        columns = getSite().getAttributes();

        isDocument = columns[0].getDataKind() == DBPDataKind.DOCUMENT;

        isRedis = columns[0].getAttribute() instanceof DBPRedisObject;

        if (!isDocument && columns[0].getMetaAttribute() instanceof DBDAttributeBinding) {
            isDocument = ((DBDAttributeBinding) columns[0].getMetaAttribute()).getParentObject().getDataKind() == DBPDataKind.DOCUMENT;
        }

        DBPNamedObject source = getSite().getSource();
        if (!isDocument) {
            if (source instanceof SQLQueryContainer) {
                tableName = DTUtils.getTableNameFromQueryContainer(session.getDataSource(), (SQLQueryContainer) source, false);
            } else {
                tableName = DTUtils.getTableName(session.getDataSource(), source, omitSchema, false);
            }
        } else {
            String id = session.getDataSource().getContainer().getDriver().getProviderDescriptor().getId();
            DatabaseType[] of = DatabaseType.of(id);
            String sql = DTUtils.getSql(source, omitSchema);
            tableName = DTUtils.getDocumentTableName(of[0].getValue(), sql);
        }

        boolean isContainSpace = tableName.contains(" ") && !session.getDataSource().getInfo().isRedis();

        if (CommonUtils.isEmpty(tableName) || isContainSpace) {
            tableName = DTConstants.DEFAULT_TABLE_NAME;
        }

        rowCount = 0;
    }

    private String transformTableNameCase(DBPDataSource dataSource, String tableIdentifier) {
        if (!columnsAndTableNamesCase.equals(DBPIdentifierCase.MIXED)) {
            SQLIdentifierDetector identifierDetector = new SQLIdentifierDetector(SQLUtils.getDialectFromDataSource(dataSource));
            String[] mayBeQualifiedNameParts = Arrays.stream(identifierDetector.splitIdentifier(tableIdentifier))
                    .map(name -> transformIdentifierCase(dataSource, name))
                    .toArray(String[]::new);
            return DBUtils.getFullyQualifiedName(dataSource, mayBeQualifiedNameParts);
        } else {
            return tableIdentifier;
        }
    }

    private String transformIdentifierCase(DBPDataSource dataSource, String identifier) {
        return DBUtils.isQuotedIdentifier(dataSource, identifier) ? identifier : columnsAndTableNamesCase.transform(identifier);
    }

    @Override
    public void exportRow(DBCSession session, DBCResultSet resultSet, Object[] row) throws DBException, IOException, TransferInterruptException {
        //nosql类型数据库：mongodb
        if (isDocument) {
            mongoExport(session, row);
        }
        // redis
        else if (isRedis) {
            redisExport(row);
        }
        //sql类型数据库
        else {
            sqlExport(session, row);
        }
    }

    private void sqlExport(DBCSession session, Object[] row) {
        PrintWriter out = getWriter();
        oneLineEntry = rowsInStatement == 1;
        int columnsSize = columns.length;
        boolean firstRow = false;

        if (exportSqlType == ExportSqlType.UPDATE) {
            if (sqlBuffer.length() == 0) {
                DBPDataSourceInfo info = session.getDataSource().getInfo();
                String entityName = transformTableNameCase(session.getDataSource(), tableName);
                if (info instanceof ChangeTableDataStatement) {
                    ChangeTableDataStatement dataStatement = (ChangeTableDataStatement) info;
                    sqlBuffer.append(dataStatement.generateTableUpdateBegin(entityName));
                    String updateSet = dataStatement.generateTableUpdateSet();
                    if (CommonUtils.isNotEmpty(updateSet)) {
                        sqlBuffer.append(" ").append(updateSet);
                    }
                } else {
                    sqlBuffer.append("UPDATE ").append(entityName);
                    sqlBuffer.append(" ").append("SET ");
                }
            }
            out.write(sqlBuffer.toString());
            // name = value
            boolean hasColumn = false;
            boolean hasPrimaryKey = false;

            for (int i = 0; i < columns.length; i++) {
                DBDAttributeBinding column = columns[i];
                if (isSkipColumn(column)) {
                    continue;
                }
                if (column.isPrimaryKey()) {
                    hasPrimaryKey = true;
                    continue;
                }
                if (column.isRowKey()){
                    continue;
                }
                if (hasColumn) {
                    out.append(',');
                }
                hasColumn = true;
                String columnName = transformIdentifierCase(session.getDataSource(), DBUtils.getQuotedIdentifier(column));
                out.write(columnName);
                out.write(STRING_EQUALS);
                Object value = row[i];
                writeValue(session, value, out, column);
            }
            // where
            out.write(" where ");

            hasColumn = false;
            for (int i = 0; i < columns.length; i++) {
                DBDAttributeBinding column = columns[i];
                if (isSkipColumn(column)) {
                    continue;
                }
                // 跳过非主键
                if (hasPrimaryKey && !column.isPrimaryKey()) {
                    continue;
                }
                if (hasColumn) {
                    out.append(" and ");
                }
                String columnName = transformIdentifierCase(session.getDataSource(), DBUtils.getQuotedIdentifier(column));
                hasColumn = true;
                out.write(columnName);
                out.write(STRING_EQUALS);
                Object value = row[i];
                writeValue(session, value, out, column);
            }
            out.write(';');
            out.write(rowDelimiter);
        } else {
            if (insertKeyword == InsertKeyword.INSERT_ALL) {
                sqlBuffer.append(identifierCase.transform(KEYWORD_INSERT_ALL));
            }
            if (oneLineEntry || insertKeyword == InsertKeyword.INSERT_ALL || rowCount % rowsInStatement == 0) {
                sqlBuffer.setLength(0);
                if (rowCount > 0) {
                    //if (insertMode == SQLDialect.MultiValueInsertMode.PLAIN) {
                    //    sqlBuffer.append(");");
                    if (!oneLineEntry && insertKeyword != InsertKeyword.INSERT_ALL) {
                        if (!CommonUtils.isEmpty(onConflictExpression)) {
                            addOnConflictExpression(out);
                        }
                        sqlBuffer.append(";");
                    } else if (insertKeyword == InsertKeyword.INSERT_ALL && rowCount % rowsInStatement == 0) {
                        sqlBuffer.append(rowDelimiter).append(identifierCase.transform(KEYWORD_SELECT_FROM_DUAL)).append(";");
                    }
                    if (lineBeforeRows) {
                        sqlBuffer.append(rowDelimiter);
                    }
                }
                switch (insertKeyword) {
                    case UPDATE:
                        sqlBuffer.append(identifierCase.transform(KEYWORD_UPDATE_OR)).append(" ").append(identifierCase.transform(KEYWORD_INSERT_INTO));
                        break;
                    case UPSERT:
                        sqlBuffer.append(identifierCase.transform(KEYWORD_UPSERT_INTO));
                        break;
                    case REPLACE:
                        sqlBuffer.append(identifierCase.transform(KEYWORD_REPLACE_INTO));
                        break;
                    default:
                        if (insertKeyword == InsertKeyword.INSERT_ALL) {
                            if (rowCount % rowsInStatement == 0) {
                                sqlBuffer.append(identifierCase.transform(KEYWORD_INSERT_ALL)).append("\n");
                            }
                            sqlBuffer.append("\t").append(identifierCase.transform(KEYWORD_INTO));
                        } else {
                            sqlBuffer.append(identifierCase.transform(KEYWORD_INSERT_INTO));
                        }
                }
                sqlBuffer.append(" ").append(transformTableNameCase(session.getDataSource(), tableName)).append(" (");
                boolean hasColumn = false;
                for (DBDAttributeBinding column : columns) {
                    if (isSkipColumn(column)) {
                        continue;
                    }
                    if (hasColumn) {
                        sqlBuffer.append(',');
                    }
                    hasColumn = true;
                    sqlBuffer.append(transformIdentifierCase(session.getDataSource(), DBUtils.getQuotedIdentifier(column)));
                }
                sqlBuffer.append(") ");
                sqlBuffer.append(identifierCase.transform(KEYWORD_VALUES));
                if (oneLineEntry || insertKeyword == InsertKeyword.INSERT_ALL) {
                    sqlBuffer.append(" (");
                }
                if (rowsInStatement > 1 && lineBeforeRows && insertKeyword != InsertKeyword.INSERT_ALL) {
                    sqlBuffer.append(rowDelimiter);
                }
                out.write(sqlBuffer.toString());
                firstRow = true;
            }
            if (!oneLineEntry && !firstRow) {
                out.write(",");
                if (lineBeforeRows) {
                    out.write(rowDelimiter);
                }
            }
            if (!oneLineEntry && insertKeyword != InsertKeyword.INSERT_ALL) {
                if (lineBeforeRows) {
                    out.write("\t");
                }
                out.write(" (");
            }
            rowCount++;
            boolean hasValue = false;
            for (int i = 0; i < columnsSize; i++) {
                DBDAttributeBinding column = columns[i];
                if (isSkipColumn(column)) {
                    continue;
                }

                if (hasValue) {
                    out.write(',');
                }
                hasValue = true;
                Object value = row[i];
                writeValue(session, value, out, column);
            }
            //if (insertMode != SQLDialect.MultiValueInsertMode.PLAIN) {
            out.write(")");
            //}
            if (!CommonUtils.isEmpty(onConflictExpression) && oneLineEntry) {
                addOnConflictExpression(out);
            }
            if (oneLineEntry) {
                out.write(";");
            }
        }
    }

    private void writeValue(DBCSession session, Object value, PrintWriter out, DBDAttributeBinding column) {
        if (DBUtils.isNullValue(value)) {
            // just skip it
            out.write(SQLConstants.NULL_VALUE);
        } else if (value instanceof DBDContent) {
            DBDContent content = (DBDContent) value;
            try {
                if (column.getValueHandler() instanceof DBDContentValueHandler) {
                    ((DBDContentValueHandler) column.getValueHandler()).writeStreamValue(session.getProgressMonitor(), session.getDataSource(), column, content, out);
                } else {
                    // Content
                    // Inline textual content and handle binaries in some special way
                    DBDContentStorage cs = content.getContents(session.getProgressMonitor());
                    if (cs != null) {
                        if (ContentUtils.isTextContent(content)) {
                            try (Reader contentReader = cs.getContentReader()) {
                                writeStringValue(contentReader);
                            }
                        } else {
                            getSite().writeBinaryData(cs);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn(e);
            } finally {
                content.release();
            }
        } else if (value instanceof File) {
            out.write("@");
            out.write(((File) value).getAbsolutePath());
        } else {
            // If we have disabled "Native Date/Time format" option then we
            // use UI format + enquote value
            boolean needQuotes = false;
            DBDDisplayFormat displayFormat = DBDDisplayFormat.NATIVE;
            if (!useNativeDataFormat && column.getDataKind() == DBPDataKind.DATETIME) {
                displayFormat = DBDDisplayFormat.UI;
                needQuotes = true;
            }
            if (column.getTypeName().toUpperCase(Locale.ROOT).contains("INTERVAL")) {
                needQuotes = session.getDataSource().getInfo().isIntervalNeedQuotes();
            }
            if (column.getTypeName().equalsIgnoreCase("RAW")) {
                needQuotes = true;
            }
            if (session.getDataSource().getInfo().isNeedAppendColumnType() &&
                    Arrays.asList(DBPDataKind.NUMERIC, DBPDataKind.BOOLEAN, DBPDataKind.DATETIME, DBPDataKind.ROW).contains(column.getDataKind())) {
                displayFormat = DBDDisplayFormat.SQL_FORMAT;
            }
            String sqlValue = SQLUtils.convertValueToSQL(
                    session.getDataSource(),
                    column,
                    column.getValueHandler(),
                    value,
                    displayFormat);
            if (needQuotes) out.write('\'');
            out.write(sqlValue);
            if (needQuotes) out.write('\'');
        }
    }

    private void redisExport(Object[] row) throws TransferInterruptException {
        String[] commands = tableName.split(" ");
        if (ArrayUtils.isEmpty(commands)) {
            return;
        }
        PrintWriter out = getWriter();
        String command = commands[0].toUpperCase(Locale.ROOT);
        String keyName = commands[1];
        switch (command) {
            case "GET":
                out.write(String.format("SET %s \"%s\"", keyName, row[0]));
                break;
            case "HGETALL":
                out.write(String.format("HSETNX %s %s \"%s\"", keyName, row[0], row[1]));
                break;
            case "LRANGE":
                out.write(String.format("LPUSH %s \"%s\"", keyName, row[0]));
                break;
            case "SMEMBERS":
                out.write(String.format("SADD %s \"%s\"", keyName, row[0]));
                break;
            case "ZRANGE":
                if (row.length > 1) {
                    out.write(String.format("ZADD %s %s \"%s\"", keyName, row[1], row[0]));
                    break;
                }
            default:
                String errMsg = "文件生成异常，暂不支持该命令导出SQL文件。";
                out.write(errMsg);
                throw new TransferInterruptException(errMsg);
        }
        out.write(";\n");
    }

    private void mongoExport(DBCSession session, Object[] row) throws IOException, DBException {
        PrintWriter out = getWriter();
        // 判断是否为UPDATE类型
        boolean isUpdate = exportSqlType == ExportSqlType.UPDATE;
        if (isUpdate) {
            // updateOne({_id: ...}, {$set: {...}})
            DBDAttributeBinding idColumn = null;
            Object idValue = null;
            StringBuilder setBuilder = new StringBuilder();
            boolean hasSetContent = false;
            for (int i = 0; i < columns.length; i++) {
                DBDAttributeBinding column = columns[i];
                String columnName = column.getLabel();
                if (CommonUtils.isEmpty(columnName)) {
                    columnName = column.getName();
                }
                Object cellValue = row[i];
                if (DBUtils.isNullValue(cellValue)) {
                    continue;
                }
                if ("_id".equals(columnName)) {
                    idColumn = column;
                    idValue = cellValue;
                    continue;
                }
                if (hasSetContent) setBuilder.append(",");
                setBuilder.append("\"").append(JSONUtils.escapeJsonString(columnName)).append("\":");
                // 值处理
                appendMongoValue(session, cellValue, setBuilder, column);
                hasSetContent = true;
            }
            // 输出updateOne语句
            out.write(String.format("db.getCollection(\"%s\").updateOne(", tableName));
            // filter
            out.write("{\"_id\":");

            // _id值输出
            writeMongoValue(session,idValue,out,idColumn);
            out.write("}, {$set:{");
            out.write(setBuilder.toString());
            out.write("}});\n");
        } else {

            out.write(String.format("db.getCollection(\"%s\").insertOne(", tableName));
            // all pages
            if (DTUtils.isJsonDocumentResults(columns, row)) {
                DBDDocument document = (DBDDocument) row[0];
                ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                document.serializeDocument(session.getProgressMonitor(), buffer, StandardCharsets.UTF_8, DBDDocument.DBDDocumentType.BSON, false);
                out.write(buffer.toString(StandardCharsets.UTF_8));
            }
            // curr page
            else {
                out.write("{");
                boolean hasValuedContent = false;
                for (int i = 0; i < columns.length; i++) {
                    DBDAttributeBinding column = columns[i];
                    String columnName = column.getLabel();
                    if (CommonUtils.isEmpty(columnName)) {
                        columnName = column.getName();
                    }
                    Object cellValue = row[i];
                    if (DBUtils.isNullValue(cellValue)) {
                        continue;
                    }
                    if (hasValuedContent) {
                        out.write(",");
                    }
                    out.write("\"" + JSONUtils.escapeJsonString(columnName) + "\":");
                    // 值处理
                    writeMongoValue(session, cellValue, out, column);
                    hasValuedContent = true;
                }
                out.write("}");
            }
            out.write(");\n");
        }
    }

    private void appendMongoValue(DBCSession session, Object cellValue, StringBuilder setBuilder, DBDAttributeBinding column) throws DBCException, IOException {
        if (cellValue instanceof DBNullValue) {
            writeTextCell(setBuilder, null, true);
        } else if (cellValue instanceof DBDContent) {
            DBDContent content = (DBDContent) cellValue;
            try {
                DBDContentStorage cs = content.getContents(session.getProgressMonitor());
                if (cs != null) {
                    if (ContentUtils.isTextContent(content)) {
                        try (Reader in = cs.getContentReader()) {
                            setBuilder.append('"');
                            DataExporterJSON.appendCellValue(setBuilder, in);
                            setBuilder.append('"');
                        }
                    } else {
                        getSite().writeBinaryData(cs);
                    }
                }
            } finally {
                content.release();
            }
        } else if (columns[0].getDataSource() instanceof NoSQLDataSource && (cellValue instanceof Map || cellValue instanceof List)) {
            DBDataWrapper dataWrapper = ((NoSQLDataSource<?>) columns[0].getDataSource()).getDataWrapper();
            writeTextCell(setBuilder, dataWrapper.documentBuilder().toJson(cellValue), true);
        } else if (cellValue instanceof DBMapValue || cellValue instanceof DBListValue) {
            writeTextCell(setBuilder, super.getValueDisplayString(column, cellValue), true);
        } else if (cellValue instanceof Number || cellValue instanceof Boolean) {
            setBuilder.append(cellValue.toString());
        } else if (cellValue instanceof Date && formatDateISO) {
            writeTextCell(setBuilder, JSONUtils.formatDate((Date) cellValue), false);
        } else if (cellValue instanceof DBFunctionObject) {
            writeTextCell(setBuilder, JSONUtils.disappearJsonString(cellValue.toString()), true);
        } else {
            writeTextCell(setBuilder, super.getValueDisplayString(column, cellValue), false);
        }
    }
    // 直接sink
    private void writeMongoValue(DBCSession session, Object cellValue, PrintWriter out, DBDAttributeBinding column) throws DBCException, IOException {
        if (cellValue instanceof DBNullValue) {
            writeTextCell(out, null, true);
        } else if (cellValue instanceof DBDContent) {
            DBDContent content = (DBDContent) cellValue;
            try {
                DBDContentStorage cs = content.getContents(session.getProgressMonitor());
                if (cs != null) {
                    if (ContentUtils.isTextContent(content)) {
                        try (Reader in = cs.getContentReader()) {
                            out.write("\"");
                            DataExporterJSON.writeCellValue(out, in);
                            out.write("\"");
                        }
                    } else {
                        getSite().writeBinaryData(cs);
                    }
                }
            } finally {
                content.release();
            }
        } else if (columns[0].getDataSource() instanceof NoSQLDataSource && (cellValue instanceof Map || cellValue instanceof List)) {
            DBDataWrapper dataWrapper = ((NoSQLDataSource<?>) columns[0].getDataSource()).getDataWrapper();
            writeTextCell(out, dataWrapper.documentBuilder().toJson(cellValue), true);
        } else if (cellValue instanceof DBMapValue || cellValue instanceof DBListValue) {
            writeTextCell(out, super.getValueDisplayString(column, cellValue), true);
        } else if (cellValue instanceof Number || cellValue instanceof Boolean) {
            out.write(cellValue.toString());
        } else if (cellValue instanceof Date && formatDateISO) {
            writeTextCell(out, JSONUtils.formatDate((Date) cellValue), false);
        } else if (cellValue instanceof DBFunctionObject) {
            writeTextCell(out, JSONUtils.disappearJsonString(cellValue.toString()), true);
        } else {
            writeTextCell(out, super.getValueDisplayString(column, cellValue), false);
        }
    }

    private static void writeTextCell(PrintWriter out, @Nullable String value, boolean bare) {
        if (value != null) {
            if (bare) {
                out.write(value);
            } else {
                out.write("\"" + value + "\"");
            }
        } else {
            out.write("null");
        }
    }

    private static void writeTextCell(StringBuilder out, @Nullable String value, boolean bare) {
        if (value != null) {
            if (bare) {
                out.append(value);
            } else {
                out.append('"').append(value).append('"');
            }
        } else {
            out.append("null");
        }
    }

    private void addOnConflictExpression(PrintWriter out) {
        if (insertKeyword == InsertKeyword.ON_CONFLICT) {
            out.write(" " + identifierCase.transform(KEYWORD_ON_CONFLICT) + " " + onConflictExpression);
        } else if (insertKeyword == InsertKeyword.ON_DUPLICATE) {
            out.write(" " + identifierCase.transform(KEYWORD_DUPLICATE_KEY) + " " + onConflictExpression);
        }
    }

    @Override
    public void exportFooter(DBRProgressMonitor monitor) {
        PrintWriter out = getWriter();
        if (rowCount > 0) {
            if (insertKeyword == InsertKeyword.INSERT_ALL) {
                out.write(rowDelimiter + identifierCase.transform(KEYWORD_SELECT_FROM_DUAL) + ";");
            } else if (!oneLineEntry) {
                addOnConflictExpression(out);
                out.write(";");
                out.write(rowDelimiter);
            } else {
                out.write(rowDelimiter);
            }
        }
    }

    private void writeStringValue(String value) {
        PrintWriter out = getWriter();
        out.write(STRING_QUOTE);
        if (dialect != null) {
            out.write(dialect.escapeString(value));
        } else {
            out.write(value);
        }
        out.write(STRING_QUOTE);
    }

    private void writeStringValue(Reader reader) throws IOException {
        try {
            PrintWriter out = getWriter();
            out.write(STRING_QUOTE);
            // Copy reader
            char buffer[] = new char[2000];
            for (; ; ) {
                int count = reader.read(buffer);
                if (count <= 0) {
                    break;
                }
                if (dialect != null) {
                    out.write(dialect.escapeString(String.valueOf(buffer, 0, count)));
                } else {
                    out.write(buffer, 0, count);
                }
            }
            out.write(STRING_QUOTE);
        } finally {
            ContentUtils.close(reader);
        }
    }

    @Override
    public void importData(@NotNull IStreamDataExporterSite site) {
        // This method is called before this.init().
        // No pre-initialization process is needed.
    }

    @Override
    public boolean shouldTruncateOutputFileBeforeExport() {
        return false;
    }

    private SQLDialect.MultiValueInsertMode getDefaultMultiValueInsertMode() {
        SQLDialect.MultiValueInsertMode insertMode = SQLDialect.MultiValueInsertMode.NOT_SUPPORTED;
        if (dialect != null && rowsInStatement != 1) {
            insertMode = dialect.getDefaultMultiValueInsertMode();
        }
        return insertMode;
    }

}
