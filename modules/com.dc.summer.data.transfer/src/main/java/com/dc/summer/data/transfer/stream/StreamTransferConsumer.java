
package com.dc.summer.data.transfer.stream;

import com.dc.summer.data.transfer.stream.exporter.DataExporterXLSX;
import com.dc.summer.model.data.result.DataResultNodeCol;
import com.dc.summer.model.data.result.DataResultNodeRow;
import com.dc.summer.model.data.result.DataResultVisitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ModelPreferences;
import com.dc.summer.data.transfer.exception.TransferInterruptException;
import com.dc.summer.data.transfer.stream.exporter.DataExporterSQL;
import com.dc.summer.data.transfer.stream.exporter.DataExporterTXT;
import com.dc.summer.model.*;
import com.dc.summer.model.app.DBPProject;
import com.dc.summer.model.data.*;
import com.dc.summer.model.document.exec.DocumentResultSet;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.data.PrimaryKeyProcessor;
import com.dc.summer.model.impl.data.SqlTableNameProcessor;
import com.dc.summer.model.meta.DBSerializable;
import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.DBRRunnableContext;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.summer.model.sql.SQLQueryContainer;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSDataContainer;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSObjectContainer;
import com.dc.summer.model.struct.rdb.DBSCatalog;
import com.dc.summer.model.struct.rdb.DBSSchema;
import com.dc.summer.model.task.DBTTask;
import com.dc.summer.runtime.DBWorkbench;
import com.dc.summer.runtime.serialize.DBPObjectSerializer;
import com.dc.summer.data.transfer.DTConstants;
import com.dc.summer.data.transfer.DTUtils;
import com.dc.summer.data.transfer.IDataTransferConsumer;
import com.dc.summer.data.transfer.IDataTransferEventProcessor;
import com.dc.summer.data.transfer.registry.DataTransferEventProcessorDescriptor;
import com.dc.summer.data.transfer.registry.DataTransferRegistry;
import com.dc.summer.utils.*;
import com.dc.type.DatabaseType;
import com.dc.utils.Base64;
import com.dc.utils.CommonUtils;
import com.dc.utils.IOUtils;
import com.dc.utils.io.ByteOrderMark;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.core.runtime.IAdaptable;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * Stream transfer consumer
 */
@DBSerializable("streamTransferConsumer")
public class StreamTransferConsumer implements IDataTransferConsumer<StreamConsumerSettings, IStreamDataExporter> {

    private static final Log log = Log.getLog(StreamTransferConsumer.class);

    private static final String LOB_DIRECTORY_NAME = "files"; //$NON-NLS-1$
    private static final String PROP_FORMAT = "format"; //$NON-NLS-1$

    public static final String NODE_ID = "streamTransferConsumer";

    public static final String VARIABLE_DATASOURCE = "datasource";
    public static final String VARIABLE_RESULT_NAME = "resultName";
    public static final String VARIABLE_CATALOG = "catalog";
    public static final String VARIABLE_SCHEMA = "schema";
    public static final String VARIABLE_INSTANCE = "instanceName";
    public static final String VARIABLE_TABLE = "table";
    public static final String VARIABLE_TIMESTAMP = "timestamp";
    public static final String VARIABLE_INDEX = "index";
    public static final String VARIABLE_DATE = "date";
    public static final String VARIABLE_PROJECT = "project";
    public static final String VARIABLE_CONN_TYPE = "connectionType";
    public static final String VARIABLE_FILE = "file";
    public static final String VARIABLE_SCRIPT_FILE = "scriptFilename";

    public static final String VARIABLE_YEAR = "year";
    public static final String VARIABLE_MONTH = "month";
    public static final String VARIABLE_DAY = "day";
    public static final String VARIABLE_HOUR = "hour";
    public static final String VARIABLE_MINUTE = "minute";

    public static final String[][] VARIABLES = {
            {VARIABLE_RESULT_NAME, "result name"},
            {VARIABLE_DATASOURCE, "source database datasource"},
            {VARIABLE_CATALOG, "source database catalog"},
            {VARIABLE_SCHEMA, "source database schema"},
            {VARIABLE_TABLE, "source database table"},
            {VARIABLE_INDEX, "index of current file (if split is used)"},
            {VARIABLE_PROJECT, "source database project"},
            {VARIABLE_CONN_TYPE, "source database connection type"},
            {VARIABLE_FILE, "output file path"},
            {VARIABLE_SCRIPT_FILE, "source script filename"},
            {VARIABLE_TIMESTAMP, "current timestamp"},
            {VARIABLE_DATE, "current date"},
            {VARIABLE_YEAR, "current year"},
            {VARIABLE_MONTH, "current month"},
            {VARIABLE_DAY, "current day"},
            {VARIABLE_HOUR, "current hour"},
            {VARIABLE_MINUTE, "current minute"},
    };

    public static final int OUT_FILE_BUFFER_SIZE = 100000;

    private IStreamDataExporter processor;
    private StreamConsumerSettings settings;
    private DBSDataContainer dataContainer;

    private OutputStream outputStream;
    private ZipOutputStream zipStream;
    private PrintWriter writer;
    private int multiFileNumber;

    private final AtomicLong bytesWritten = new AtomicLong();

    private DBDAttributeBinding[] columnMetas;

    @Getter
    private DBDAttributeBinding[] columnBindings;
    private File lobDirectory;
    private long lobCount;

    @Getter
    private File outputFile;

    //将转换好的变量名传递出去
    @Getter
    private String customFileName;
    private StreamExportSite exportSite;
    private Map<String, Object> processorProperties;
    private StringWriter outputBuffer;
    private boolean initialized = false;
    private boolean firstRow = true;
    private TransferParameters parameters;

    private final List<File> outputFiles = new ArrayList<>();
    private StatOutputStream statStream;

    private final DBDDataDraw dataDraw;

    @Getter
    private final DBDDataDesensitizeProcessor dataDesensitizeProcessor;

    @Getter
    private final PrimaryKeyProcessor primaryKeyProcessor;

    private final String dateTimeFormat;

    private final String timeFormat;

    private final String dateFormat;

    private final List<SqlFieldData> sqlFieldDataList;

    private final DataResultVisitor dataResultVisitor;

    private final boolean excelUseNumberFormat;

    private final SqlTableNameProcessor sqlTableNameProcessor;

    private final String resultName;

    public StreamTransferConsumer(DBDDataDraw dataDraw,
                                  DBDDataDesensitizeProcessor dataDesensitizeProcessor,
                                  PrimaryKeyProcessor primaryKeyProcessor,
                                  String dateTimeFormat,
                                  String timeFormat,
                                  String dateFormat,
                                  List<SqlFieldData> sqlFieldDataList,
                                  DataResultVisitor dataResultVisitor,
                                  boolean excelUseNumberFormat,
                                  String resultName) {
        this.dataDraw = dataDraw;
        this.dataDesensitizeProcessor = dataDesensitizeProcessor;
        this.primaryKeyProcessor = primaryKeyProcessor;
        this.dateTimeFormat = dateTimeFormat;
        this.timeFormat = timeFormat;
        this.dateFormat = dateFormat;
        this.sqlFieldDataList = sqlFieldDataList;
        this.dataResultVisitor = dataResultVisitor;
        this.excelUseNumberFormat = excelUseNumberFormat;
        this.sqlTableNameProcessor = new SqlTableNameProcessor(sqlFieldDataList);
        this.resultName = resultName;
    }

    protected long getBytesWritten() {
        return statStream == null ? 0 : statStream.getBytesWritten();
    }

    @Override
    public void fetchStart(DBCSession session, DBCResultSet resultSet, long offset, long maxRows) throws DBCException {
        if (!initialized) {
            // Can be invoked multiple times in case of per-segment transfer
            initExporter(session);
        }

        // Prepare columns
        columnMetas = DBUtils.getAttributeBindings(session, dataContainer, resultSet.getMeta());
        boolean isTxtJsonProcessor = processor instanceof DataExporterTXT && columnMetas[0].getDataKind() == DBPDataKind.DOCUMENT;
        boolean isSqlJsonProcessor = processor instanceof DataExporterSQL && columnMetas[0].getDataKind() == DBPDataKind.DOCUMENT;
        if (processor instanceof IDocumentDataExporter || isTxtJsonProcessor || isSqlJsonProcessor) {
            columnBindings = DBUtils.injectAndFilterAttributeBindings(session.getDataSource(), dataContainer, columnMetas, true);
        } else {
            columnBindings = DBUtils.makeLeafAttributeBindings(session, dataContainer, resultSet);
        }

        final StreamMappingContainer mapping = settings.getDataMapping(dataContainer);
        if (mapping != null && mapping.isComplete()) {
            // That's a dirty way of doing things ...
            columnBindings = Arrays.stream(columnBindings)
                    .filter(attr -> {
                        final StreamMappingAttribute attribute = mapping.getAttribute(attr);
                        return attribute == null || attribute.getMappingType() == StreamMappingType.export;
                    })
                    .toArray(DBDAttributeBinding[]::new);
        }

        String label = ModelPreferences.getPreferences().getString(ModelPreferences.VIRTUAL_ROW_ID_LABEL);

        boolean keepRowId = processor instanceof DataExporterSQL && "UPDATE".equals(processorProperties.get(DTConstants.EXPORT_SQL_TYPE));

        if (!keepRowId){
            // skip custom rowid
            for (int i = 0; i < columnBindings.length; i++) {
                DBDAttributeBinding binding = columnBindings[i];
                if (DBPDataKind.ROWID == binding.getDataKind() && label.equals(binding.getLabel())) {
                    List<DBDAttributeBinding> list = new LinkedList<>(List.of(columnBindings));
                    list.remove(i);
                    columnBindings = list.toArray(new DBDAttributeBinding[0]);
                    break;
                }
            }
        }
        if (!initialized) {
            /*// For multi-streams export header only once
            if (!settings.isUseSingleFile() || parameters.orderNumber == 0) */
            {
                exportHeaderInFile(session);
            }
        }

        // 填充脱敏、绑定主键、绑定备注
        DBDAttributeBindingProcessor.notice(columnBindings,
                sqlTableNameProcessor, dataDesensitizeProcessor, primaryKeyProcessor);

        if (dataResultVisitor != null) {
            DataResultNodeCol dataResultNodeCol = new DataResultNodeCol();
            dataResultNodeCol.setSqlFieldDataList(sqlFieldDataList);
            dataResultNodeCol.setBindings(columnBindings);
            dataResultNodeCol.accept(dataResultVisitor);
        }

        if (keepRowId){ // 绑定主键
            for (DBDAttributeBinding binding : columnBindings) {
                if (primaryKeyProcessor.getPrimaryKeyColumns().stream().anyMatch(s -> binding.getName().equalsIgnoreCase(s))) {
                    binding.setPrimaryKey(true);
                }
            }
        }

        initialized = true;
    }

    @Override
    public void fetchRow(DBCSession session, DBCResultSet resultSet) throws DBCException {
        try {
            // Check for file split
            if (settings.isSplitOutFiles() && !firstRow) {
                if (!parameters.isBinary) {
                    writer.flush();
                }
                if (bytesWritten.get() >= settings.getMaxOutFileSize()) {
                    // First add footer for the previous file
                    exportFooterInFile(session.getProgressMonitor());
                    // Make new file with the header
                    createNewOutFile();
                    exportHeaderInFile(session);
                }
            }

            // Get values
            Object[] srcRow = fetchRow(session, resultSet, columnBindings);

            Object[] targetRow = new Object[columnBindings.length];
            for (int i = 0; i < columnBindings.length; i++) {
                Object value = srcRow[i];
                DBDAttributeBinding columnBinding = columnBindings[i];
                if (excelUseNumberFormat) {
                    columnBinding.setExcelUseNumberFormat(true);
                }

                // 使用 web 格式展示：不是 excel ｜｜ 不是时间 ｜｜ 存在脱敏 （使用原始格式）
                if (resultSet instanceof JDBCResultSet) {
                    initDateTimePreferences();
                    if (!(processor instanceof DataExporterXLSX && value instanceof Date && !dataDesensitizeProcessor.isDesensitization(value, i))) {
                        value = dataDraw.makeWebCellValue(
                                columnBinding,
                                value);
                        srcRow[i] = value;
                    }
                }

                int isBigDataType = dataDraw.blobDataFormat(
                        srcRow,
                        i,
                        columnBinding,
                        dataDesensitizeProcessor.isDesensitization(value, i),
                        primaryKeyProcessor,
                        0,
                        columnBindings);

                // NoSQL 通过 Map 的挂载节点脱敏，不会污染原始值。
                if (resultSet instanceof DocumentResultSet) {
                    Object cellValue = dataDraw.makeWebCellValue(
                            columnBinding,
                            value);
                    dataDesensitizeProcessor.execDesensitization(cellValue, i, isBigDataType, columnBindings);
                } else {
                    value = dataDesensitizeProcessor.execDesensitization(value, i, isBigDataType, columnBindings);
                }

                if (value instanceof Map &&
                        ((Map) value).containsKey("$type") &&
                        ((Map) value).containsKey("column_type") &&
                        ((Map) value).containsKey("binary") &&
                        ((Map) value).containsKey("contentType") &&
                        ((Map) value).containsKey("text") &&
                        ((Map) value).containsKey("fileName")) {
                    Object text = ((Map) value).get("text");
                    if (text instanceof Map) {
                        if (((Map) value).get("is_desensitization") != null && (boolean) ((Map) value).get("is_desensitization")) {
                            value = "字段被脱敏无法查看，请先申请“明文展示”权限";
                        } else {
                            value = ((Map) text).get("clob_content");
                        }
                    } else {
                        value = text.toString();
                    }
                } else if (value instanceof Map && ((Map<?, ?>) value).containsKey("$type") && ((Map<?, ?>) value).containsKey("value")) {
                    Map<?, ?> valueMap = (Map<?, ?>) ((Map<?, ?>) value).get("value");
                    List<String> valueList = new ArrayList<>(valueMap.size());
                    for (Object o : valueMap.values()) {
                        valueList.add(o.toString());
                    }
                    value = String.format("(%s)", String.join(",", valueList));
                }

                targetRow[i] = value;
            }
            if (dataResultVisitor != null) {
                DataResultNodeRow dataResultNodeRow = new DataResultNodeRow();
                dataResultNodeRow.setRow(targetRow);
                dataResultNodeRow.accept(dataResultVisitor);
                targetRow = dataResultNodeRow.getRow();
            }
            // Export row
            processor.exportRow(session, resultSet, targetRow);
            firstRow = false;
        } catch (TransferInterruptException e) {
            boolean skipInterrupt = true;
            while (skipInterrupt) skipInterrupt = resultSet.nextRow();
        } catch (IOException e) {
            log.error("IO error", e);
            throw new DBCException("IO error", e);
        } catch (Throwable e) {
            log.error("Error while exporting table row", e);
            throw new DBCException("Error while exporting table row", e);
        }
    }

    private void exportHeaderInFile(@NotNull DBCSession session) throws DBCException {
        try {
            processor.exportHeader(session);
        } catch (DBException e) {
            log.warn("Error while exporting table header", e);
        } catch (IOException e) {
            throw new DBCException("IO error", e);
        }
    }

    private void exportFooterInFile(@NotNull DBRProgressMonitor monitor) {
        if (processor != null) {
            try {
                processor.exportFooter(monitor);
            } catch (Exception e) {
                log.warn("Error while exporting table footer", e);
            }
        }
    }

    private void initDateTimePreferences() {
        DBPPreferenceStore store = ModelPreferences.getPreferences();
        if (StringUtils.isNotBlank(this.dateTimeFormat)) {
            store.setValue("dataformat.type.timestamp.pattern", this.dateTimeFormat);
            store.setValue("dataformat.type.timestamptz.pattern", this.dateTimeFormat);
        } else {
            store.setValue("dataformat.type.timestamp.pattern", "");
            store.setValue("dataformat.type.timestamptz.pattern", "");
        }
        if (StringUtils.isNotBlank(this.dateFormat)) {
            store.setValue("dataformat.type.date.pattern", this.dateFormat);
        } else {
            store.setValue("dataformat.type.date.pattern", "");
        }
        if (StringUtils.isNotBlank(this.timeFormat)) {
            store.setValue("dataformat.type.time.pattern", this.timeFormat);
        } else {
            store.setValue("dataformat.type.time.pattern", "");
        }
    }

    @Override
    public void fetchEnd(DBCSession session, DBCResultSet resultSet) throws DBCException {
    }

    @Override
    public void close() {
        columnBindings = null;
    }

    private File saveContentToFile(DBRProgressMonitor monitor, DBDContent content)
            throws IOException, DBCException {
        DBDContentStorage contents = content.getContents(monitor);
        if (DBUtils.isNullValue(contents)) {
            return null;
        }
        if (lobDirectory == null) {
            lobDirectory = new File(getOutputFolder(), LOB_DIRECTORY_NAME);
            if (!lobDirectory.exists()) {
                if (!lobDirectory.mkdir()) {
                    throw new IOException("Can't create directory for CONTENT files: " + lobDirectory.getAbsolutePath());
                }
            }
        }
        lobCount++;
        Boolean extractImages = (Boolean) processorProperties.get(StreamConsumerSettings.PROP_EXTRACT_IMAGES);
        String fileExt = (extractImages != null && extractImages) ? ".jpg" : ".data";
        File lobFile = new File(lobDirectory, outputFile.getName() + "-" + lobCount + fileExt); //$NON-NLS-1$ //$NON-NLS-2$
        try (InputStream cs = contents.getContentStream()) {
            ContentUtils.saveContentToFile(cs, lobFile, monitor);
        }
        return lobFile;
    }

    private void initExporter(DBCSession session) throws DBCException {
        if (settings.getFormatterProfile() != null && session instanceof DBDFormatSettingsExt) {
            ((DBDFormatSettingsExt) session).setDataFormatterProfile(settings.getFormatterProfile());
        }

        exportSite = new StreamExportSite();

        // Open output streams
        boolean outputClipboard = settings.isOutputClipboard();
        if (parameters.isBinary || !outputClipboard) {
            outputFile = makeOutputFile();
            outputFiles.add(outputFile);
        } else {
            outputFile = null;
        }
        if (processor instanceof IAppendableDataExporter && (settings.isAppendToFileEnd() || (settings.isUseSingleFile() && parameters.orderNumber > 0))) {
            try {
                ((IAppendableDataExporter) processor).importData(exportSite);
            } catch (DBException e) {
                log.warn("Error importing existing data for appending, data loss might occur", e);
            }
        }


        try {
            if (outputClipboard) {
                this.outputBuffer = new StringWriter(2048);
                this.writer = new PrintWriter(this.outputBuffer, true);
            } else {
                openOutputStreams();
            }
        } catch (IOException e) {
            closeExporter();
            throw new DBCException("Data transfer IO error", e);
        }

        try {
            // init exporter
            processor.init(exportSite);
        } catch (DBException e) {
            throw new DBCException("Can't initialize data exporter", e);
        }
    }

    private void closeExporter() {
        if (exportSite != null) {
            try {
                exportSite.flush();
            } catch (IOException e) {
                log.debug(e);
            }
        }

        if (processor != null) {
            // Dispose exporter
            try {
                processor.dispose();
            } catch (Exception e) {
                log.debug(e);
            } finally {
                closeOutputStreams();
                if (!(processor instanceof DataExporterXLSX)){
                    processor.createWaterMark();
                }
            }
            processor = null;
        }
    }

    private void openOutputStreams() throws IOException {
        final boolean truncate;

        if (!settings.isAppendToFileEnd() && settings.isUseSingleFile() && parameters.orderNumber == 0) {
            truncate = true;
        } else if (processor instanceof IAppendableDataExporter && (settings.isAppendToFileEnd() || settings.isUseSingleFile())) {
            truncate = ((IAppendableDataExporter) processor).shouldTruncateOutputFileBeforeExport();
        } else {
            truncate = true;
        }

        this.outputStream = new BufferedOutputStream(new FileOutputStream(outputFile, !truncate), OUT_FILE_BUFFER_SIZE);
        this.outputStream = this.statStream = new StatOutputStream(outputStream);

        if (settings.isCompressResults()) {
            this.zipStream = new ZipOutputStream(this.outputStream);
            this.zipStream.putNextEntry(new ZipEntry(getOutputFileName()));
            this.outputStream = zipStream;
        }

        // If we need to split files - use stream wrapper to calculate file size
        if (settings.isSplitOutFiles()) {
            this.outputStream = new OutputStreamStatProxy(this.outputStream);
        }

        // Check for BOM and write it to the stream
        if (!parameters.isBinary && settings.getOutputEncodingBOM() != null) {
            try {
                final ByteOrderMark bom = ByteOrderMark.fromCharset(settings.getOutputEncodingBOM());
                outputStream.write(bom.getBytes());
                outputStream.flush();
            } catch (IllegalArgumentException e) {
                log.debug("Error writing byte order mask", e);
            }
        }

        if (!parameters.isBinary) {
            this.writer = new PrintWriter(new OutputStreamWriter(this.outputStream, settings.getOutputEncoding()), true);
        }
    }

    private void closeOutputStreams() {
        if (this.writer != null) {
            this.writer.flush();
        }

        // Finish zip stream
        if (zipStream != null) {
            try {
                zipStream.closeEntry();
            } catch (IOException e) {
                log.debug(e);
            }
            try {
                zipStream.finish();
            } catch (IOException e) {
                log.debug(e);
            }
            zipStream = null;
        }

        if (outputStream != null) {
            try {
                outputStream.flush();
            } catch (IOException e) {
                log.debug(e);
            }
            ContentUtils.close(outputStream);
            outputStream = null;
        }
    }

    private void createNewOutFile() throws IOException {
        closeOutputStreams();

        bytesWritten.set(0);
        multiFileNumber++;
        outputFile = makeOutputFile();
        outputFiles.add(outputFile);

        openOutputStreams();
    }

    @Override
    public void initTransfer(DBSObject sourceObject, StreamConsumerSettings settings, TransferParameters parameters, IStreamDataExporter processor, Map<String, Object> processorProperties) {
        this.dataContainer = (DBSDataContainer) sourceObject;
        this.parameters = parameters;
        this.processor = processor;
        this.settings = settings;
        this.processorProperties = processorProperties;
    }

    @Override
    public void startTransfer(DBRProgressMonitor monitor) {
        // do nothing
    }

    @Override
    public void finishTransfer(DBRProgressMonitor monitor, boolean last) {
        if (!last) {
            exportFooterInFile(monitor);

            closeExporter();
            return;
        }

        if (!parameters.isBinary && settings.isOutputClipboard()) {
            if (outputBuffer != null) {
                String strContents = outputBuffer.toString();
                DBWorkbench.getPlatformUI().copyTextToClipboard(strContents, parameters.isHTML);
                outputBuffer = null;
            }
        }

        final DataTransferRegistry registry = DataTransferRegistry.getInstance();
        for (Map.Entry<String, Map<String, Object>> entry : settings.getEventProcessors().entrySet()) {
            final DataTransferEventProcessorDescriptor descriptor = registry.getEventProcessorById(entry.getKey());
            if (descriptor == null) {
                log.debug("Can't find event processor '" + entry.getKey() + "'");
                continue;
            }
            try {
                final IDataTransferEventProcessor<StreamTransferConsumer> processor = descriptor.create();
                processor.processEvent(monitor, IDataTransferEventProcessor.Event.FINISH, this, entry.getValue());
            } catch (DBException e) {
                DBWorkbench.getPlatformUI().showError("Transfer event processor", "Error executing data transfer event processor '" + entry.getKey() + "'", e);
                log.error("Error executing event processor '" + entry.getKey() + "'", e);
            }
        }
    }

    @Override
    public Object getTargetObject() {
        return null;
    }

    @Nullable
    @Override
    public Object getTargetObjectContainer() {
        return null;
    }

    @Override
    public String getObjectName() {
        return settings.isOutputClipboard() ? "Clipboard" : makeOutputFile().getName();
    }

    @Override
    public String getObjectContainerName() {
        return settings.isOutputClipboard() ? "Clipboard" : makeOutputFile().getParentFile().getAbsolutePath();
    }

    @Override
    public boolean isConfigurationComplete() {
        return true;
    }

    public boolean isBeforeFirstRow() {
        return firstRow;
    }

    @NotNull
    public String getOutputFolder() {
        return translatePattern(settings.getOutputFolder(), null);
    }

    @NotNull
    public List<File> getOutputFiles() {
        return outputFiles;
    }

    public String getOutputFileName() {
        Object extension = processorProperties == null ? null : processorProperties.get(StreamConsumerSettings.PROP_FILE_EXTENSION);
        String fileName = translatePattern(
                settings.getOutputFilePattern(),
                null).trim();
        // Can't rememeber why did we need this. It breaks file names in case of multiple tables export (#6911)
//        if (parameters.orderNumber > 0 && !settings.isUseSingleFile()) {
//            fileName += "_" + String.valueOf(parameters.orderNumber + 1);
//        }
        //将转换好的文件名保存，以便传递出去
        customFileName = fileName;
        if (multiFileNumber > 0) {
            fileName += "_" + (multiFileNumber + 1);
        }
        if (extension != null) {
            return fileName + "." + extension;
        } else {
            return fileName;
        }
    }

    @SuppressWarnings("ResultOfMethodCallIgnored")
    public File makeOutputFile() {
        File dir = new File(getOutputFolder());
        if (!dir.exists() && !dir.mkdirs()) {
            log.error("Can't create output directory '" + dir.getAbsolutePath() + "'");
        }
        String fileName = getOutputFileName();
        if (settings.isCompressResults()) {
            fileName += ".zip";
        }
        // 当遇到文件被切分，需要把一个文件改名带编号1
        if (multiFileNumber == 1) {
            File newFile = new File(dir, fileName.replace("_2.", "_1."));
            new File(dir, fileName.replace("_2.", ".")).renameTo(newFile);
            outputFiles.set(0, newFile);
        }
        File file = new File(dir, fileName);
        if (fileName.endsWith(".xlsx") && processor instanceof DataExporterXLSX){
            try {
                // 提前初始化
                processor.init(exportSite);
                ((DataExporterXLSX) processor).inject(file);
            } catch (DBException e) {
                throw new RuntimeException(e);
            }

        }
        return file;
    }

    public String translatePattern(String pattern, final File targetFile) {
        final Date ts;
        if (parameters.startTimestamp != null) {
            // Use saved timestamp (#7352)
            ts = parameters.startTimestamp;
        } else {
            ts = new Date();
        }

        return GeneralUtils.replaceVariables(pattern, name -> {
            switch (name) {
                case VARIABLE_RESULT_NAME: {
                    return stripObjectName(resultName);
                }
                case VARIABLE_DATASOURCE: {
                    if (settings.isUseSingleFile()) {
                        return "";
                    }
                    return stripObjectName(dataContainer.getDataSource().getContainer().getName());
                }
                case VARIABLE_CATALOG: {
                    if (settings.isUseSingleFile()) {
                        return "";
                    }
                    if (!dataContainer.getDataSource().getInfo().supportsCatalog()) {
                        return name;
                    }
                    DBSObjectContainer sourceContainer = getDbsObjectContainer();
                    DBSCatalog catalog = DBUtils.getParentOfType(DBSCatalog.class, sourceContainer);
                    return catalog == null ? "" : stripObjectName(catalog.getName());
                }
                case VARIABLE_SCHEMA: {
                    if (settings.isUseSingleFile()) {
                        return "";
                    }
                    if (dataContainer.getDataSource().getInfo().getSchemaFromQuery(dataContainer.getDataSource()) ||
                            dataContainer.getDataSource().getInfo().getCatalogFromQuery(dataContainer.getDataSource()) ||
                            dataContainer.getDataSource().getInfo().getSchemaFromParser()) {
                        String schemaName = "";
                        if (dataContainer instanceof IAdaptable) {
                            SQLQueryContainer adapter = ((IAdaptable) dataContainer).getAdapter(SQLQueryContainer.class);
                            if (dataContainer.getDataSource().getInfo().getSchemaFromQuery(dataContainer.getDataSource())) {
                                schemaName = DTUtils.getSchemaNameFromQuery(dataContainer.getDataSource(), adapter);
                                if (StringUtils.isNotBlank(schemaName)) {
                                    schemaName = DBUtils.getUnQuotedIdentifier(dataContainer.getDataSource(), schemaName);
                                }
                            } else if (dataContainer.getDataSource().getInfo().getSchemaFromParser()) {
                                String id = dataContainer.getDataSource().getContainer().getDriver().getProviderDescriptor().getId();
                                DatabaseType[] of = DatabaseType.of(id);
                                schemaName = DTUtils.getSchemaNameFromParser(of[0].getValue(), adapter.getQuery().getText());
                            } else {
                                schemaName = DTUtils.getCatalogNameFormContext(dataContainer.getDataSource(), adapter);
                            }
                        }
                        if (StringUtils.isNotBlank(schemaName)) {
                            return stripObjectName(schemaName);
                        } else if (StringUtils.isNotBlank(dataContainer.getDataSource().getInfo().defaultSchemaName())) {
                            return stripObjectName(dataContainer.getDataSource().getInfo().defaultSchemaName());
                        }
                    }
                    DBSObjectContainer sourceContainer = getDbsObjectContainer();
                    DBSSchema schema = DBUtils.getParentOfType(DBSSchema.class, sourceContainer);
                    if (schema != null && !dataContainer.getDataSource().getInfo().catalogIsSchema()) {
                        return stripObjectName(schema.getName());
                    }
                    // Try catalog (#7506)
                    DBSCatalog catalog = DBUtils.getParentOfType(DBSCatalog.class, sourceContainer);
                    return catalog == null ? "" : stripObjectName(catalog.getName());
                }
                case VARIABLE_TABLE: {
                    if (settings.isUseSingleFile()) {
                        return DTConstants.DEFAULT_TABLE_NAME_EXPORT;
                    }
                    if (dataContainer == null) {
                        return null;
                    }
                    String tableName = null;

                    if (dataContainer instanceof IAdaptable) {
                        SQLQueryContainer adapter = ((IAdaptable) dataContainer).getAdapter(SQLQueryContainer.class);
                        tableName = adapter.getTableName();
                    }

                    if (StringUtils.isBlank(tableName)) {
                        if (dataContainer.getDataSource().getInfo().ignoreTableName()) {
                            return tableName;
                        }
                        if (dataContainer instanceof SQLQueryContainer) {
                            tableName = DTUtils.getTableNameFromQueryContainer(dataContainer.getDataSource(), (SQLQueryContainer) dataContainer, false);
                        } else {
                            tableName = DTUtils.getTableName(dataContainer.getDataSource(), dataContainer, true, false);
                        }
                    }

                    if (CommonUtils.isEmpty(tableName)) {
                        if (parameters.orderNumber > 0) {
                            tableName = DTConstants.DEFAULT_TABLE_NAME_EXPORT + "_" + parameters.orderNumber;
                        } else {
                            tableName = DTConstants.DEFAULT_TABLE_NAME_EXPORT;
                        }
                    }
                    return stripObjectName(tableName);
                }
                case VARIABLE_TIMESTAMP:
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat(settings.getOutputTimestampPattern());
                        return sdf.format(ts);
                    } catch (Exception e) {
                        log.error(e);
                        return "BAD_TIMESTAMP";
                    }
                case VARIABLE_DATE:
                    return RuntimeUtils.getCurrentDate();
                case VARIABLE_YEAR:
                    return new SimpleDateFormat("yyyy").format(ts);
                case VARIABLE_MONTH:
                    return new SimpleDateFormat("MM").format(ts);
                case VARIABLE_DAY:
                    return new SimpleDateFormat("dd").format(ts);
                case VARIABLE_HOUR:
                    return new SimpleDateFormat("HH").format(ts);
                case VARIABLE_MINUTE:
                    return new SimpleDateFormat("mm").format(ts);
                case VARIABLE_INDEX:
                    return String.valueOf(parameters.orderNumber + 1);
                case VARIABLE_PROJECT: {
                    if (dataContainer == null) {
                        return null;
                    }
                    DBPProject project = DBUtils.getObjectOwnerProject(dataContainer);
                    return project == null ? "" : project.getName();
                }
                case VARIABLE_FILE:
                    return targetFile == null ? "" : targetFile.getAbsolutePath();
                case VARIABLE_SCRIPT_FILE: {
                    final SQLQueryContainer container = DBUtils.getAdapter(SQLQueryContainer.class, dataContainer);
                    if (container != null) {
                        final File file = container.getScriptContext().getSourceFile();
                        if (file != null) {
                            String filename = file.getName();
                            if (filename.indexOf('.') >= 0) {
                                filename = filename.substring(0, filename.lastIndexOf('.'));
                            }
                            return filename;
                        }
                    }
                    break;
                }
                case VARIABLE_CONN_TYPE:
                    if (dataContainer == null) {
                        return null;
                    }
                    return dataContainer.getDataSource().getContainer().getConnectionConfiguration().getConnectionType().getId();
                case VARIABLE_INSTANCE:
                    return dataContainer.getDataSource().getContainer().getConnectionConfiguration().getInstanceName();
                default:
                    //如果没有匹配上默认直接返回变量名
                    return name;
            }
            final SQLQueryContainer container = DBUtils.getAdapter(SQLQueryContainer.class, dataContainer);
            if (container != null) {
                return CommonUtils.toString(container.getQueryParameters().get(name));
            }
            return null;
        });
    }

    private @org.jetbrains.annotations.Nullable DBSObjectContainer getDbsObjectContainer() {
        DBSObjectContainer sourceContainer = null;
        if (dataContainer instanceof IAdaptable) {
            SQLQueryContainer adapter = ((IAdaptable) dataContainer).getAdapter(SQLQueryContainer.class);
            try {
                sourceContainer = DTUtils.getSourceContainer(new LoggingProgressMonitor(), dataContainer.getDataSource(), dataContainer, adapter.getTableName(), (String) processorProperties.get(DTConstants.CONTEXT_SCHEMA_NAME));
            } catch (DBException e) {
                log.error("导出文件名变量，获取container错误");
            }
        }
        return sourceContainer;
    }

    private static String stripObjectName(String name) {
        StringBuilder result = new StringBuilder();
        boolean lastUnd = false;
        for (int i = 0; i < name.length(); i++) {
            char c = name.charAt(i);
            if (Character.isLetterOrDigit(c)) {
                result.append(c);
                lastUnd = false;
            } else if (!lastUnd) {
                result.append('_');
                lastUnd = true;
            }
            if (result.length() >= 64) {
                break;
            }
        }
        return result.toString();
    }

    @Override
    public DBSObject getDatabaseObject() {
        return null;
    }

    public static Object[] fetchRow(DBCSession session, DBCResultSet resultSet, DBDAttributeBinding[] attributes) throws DBCException {
        int columnCount = attributes.length; // Column count without virtual columns

        Object[] row = new Object[columnCount];
        for (int i = 0; i < columnCount; i++) {
            DBDAttributeBinding attribute = attributes[i];
            DBSAttributeBase metaAttr = attribute.getMetaAttribute();
            if (metaAttr == null) {
                continue;
            }
            try {
                row[i] = attribute.getValueHandler().fetchValueObject(session, resultSet, metaAttr, attribute.getOrdinalPosition());
            } catch (Exception e) {
                log.debug("Error fetching '" + metaAttr.getName() + "' value: " + e.getMessage());
            }
        }
        return row;
    }

    @NotNull
    public StreamConsumerSettings getSettings() {
        return settings;
    }

    private class StreamExportSite implements IStreamDataExporterSite {
        @Override
        public DBPNamedObject getSource() {
            return dataContainer;
        }

        @Override
        public DBDDisplayFormat getExportFormat() {
            Object formatProp = processorProperties.get(PROP_FORMAT);
            if (formatProp != null) {
                return DBDDisplayFormat.valueOf(formatProp.toString().toUpperCase(Locale.ENGLISH));
            }
            return settings.getValueFormat();
        }

        @Override
        public Map<String, Object> getProperties() {
            return processorProperties;
        }

        @Override
        public DBDAttributeBinding[] getAttributes() {
            return columnBindings;
        }

        @Override
        public AtomicLong getBytesWritten() {
            return bytesWritten;
        }

        @Override
        public OutputStream getOutputStream() {
            return outputStream;
        }

        @Nullable
        @Override
        public File getOutputFile() {
            return outputFile;
        }

        @Override
        public List<File> getOutputFiles() {
            return outputFiles;
        }

        @Override
        public PrintWriter getWriter() {
            return writer;
        }

        @Override
        public void flush() throws IOException {
            if (writer != null) {
                writer.flush();
            }
            if (outputStream != null) {
                outputStream.flush();
            }
        }

        @Override
        public void writeBinaryData(@NotNull DBDContentStorage cs) throws IOException {
            if (parameters.isBinary) {
                try (final InputStream stream = cs.getContentStream()) {
                    IOUtils.copyStream(stream, exportSite.getOutputStream());
                }
            } else {
                try (final InputStream stream = cs.getContentStream()) {
                    exportSite.flush();
                    final DBPDataSource dataSource = dataContainer.getDataSource();
                    switch (settings.getLobEncoding()) {
                        case BASE64: {
                            Base64.encode(stream, cs.getContentLength(), writer);
                            break;
                        }
                        case HEX: {
                            writer.write("0x"); //$NON-NLS-1$
                            byte[] buffer = new byte[5000];
                            for (; ; ) {
                                int count = stream.read(buffer);
                                if (count <= 0) {
                                    break;
                                }
                                GeneralUtils.writeBytesAsHex(writer, buffer, 0, count);
                            }
                            break;
                        }
                        case NATIVE: {
                            if (dataSource != null) {
                                ByteArrayOutputStream buffer = new ByteArrayOutputStream((int) cs.getContentLength());
                                IOUtils.copyStream(stream, buffer);

                                final byte[] bytes = buffer.toByteArray();
                                final String binaryString = dataSource.getSQLDialect().getNativeBinaryFormatter().toString(bytes, 0, bytes.length);
                                writer.write(binaryString);
                                break;
                            }
                        }
                        default: {
                            // Binary stream
                            try (Reader reader = new InputStreamReader(stream, cs.getCharset())) {
                                IOUtils.copyText(reader, writer);
                            }
                            break;
                        }
                    }
                }
            }
        }

        @NotNull
        @Override
        public String getOutputEncoding() {
            return settings == null ? StandardCharsets.UTF_8.displayName() : settings.getOutputEncoding();
        }
    }

    private class OutputStreamStatProxy extends OutputStream {
        private final OutputStream out;

        OutputStreamStatProxy(OutputStream outputStream) {
            this.out = outputStream;
        }

        @Override
        public void write(int b) throws IOException {
            this.out.write(b);
            bytesWritten.incrementAndGet();
        }

        @Override
        public void write(@NotNull byte[] b) throws IOException {
            this.out.write(b);
            bytesWritten.getAndAdd(b.length);
        }

        @Override
        public void write(@NotNull byte[] b, int off, int len) throws IOException {
            this.out.write(b, off, len);
            bytesWritten.getAndAdd(len);
        }

        @Override
        public void flush() throws IOException {
            out.flush();
        }

        @Override
        public void close() throws IOException {
            out.close();
        }

    }

    public static class ObjectSerializer implements DBPObjectSerializer<DBTTask, StreamTransferConsumer> {

        @Override
        public void serializeObject(DBRRunnableContext runnableContext, DBTTask context, StreamTransferConsumer object, Map<String, Object> state) {
        }

        @Override
        public StreamTransferConsumer deserializeObject(DBRRunnableContext runnableContext, DBTTask objectContext, Map<String, Object> state) {
            return null;
        }
    }

}
