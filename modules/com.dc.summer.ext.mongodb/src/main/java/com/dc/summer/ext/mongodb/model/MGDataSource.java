package com.dc.summer.ext.mongodb.model;

import com.dc.summer.ext.mongodb.MGUtils;
import com.dc.summer.ext.mongodb.data.MGDataUtils;
import com.dc.summer.ext.mongodb.data.MGDataWrapper;
import com.dc.summer.ext.mongodb.exec.MGClientPool;
import com.dc.summer.ext.mongodb.exec.MGExecutionContext;
import com.dc.summer.ext.mongodb.exec.MGSession;
import com.dc.summer.model.*;
import com.dc.summer.model.document.DocumentDataManager;
import com.dc.summer.model.document.data.DBDataWrapper;
import com.dc.summer.model.document.data.DBMapValue;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.mongodb.BasicDBObject;
import com.mongodb.ConnectionString;
import com.mongodb.MongoCredential;
import com.mongodb.MongoSocketException;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;
import java.io.IOException;
import java.io.Reader;
import java.io.StringWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.bson.BsonTimestamp;
import org.bson.Document;
import org.bson.types.Decimal128;
import org.bson.types.ObjectId;
import org.eclipse.core.runtime.IAdaptable;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.admin.sessions.DBAServerSessionManager;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.meta.Association;
import com.dc.summer.model.net.DBWHandlerConfiguration;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBSStructureAssistant;
import com.dc.summer.model.struct.cache.BasicObjectCache;
import com.dc.utils.CommonUtils;
import com.dc.utils.IOUtils;
import org.osgi.framework.Version;

public abstract class MGDataSource extends NoSQLDataSource<MGExecutionContext> implements DocumentDataManager<MGDataSource, Map<String, Object>>, DBPRefreshableObject, DBPObjectStatisticsCollector, DBPTermProvider, DBPDataTypeProvider, DBPErrorAssistant, IAdaptable {
   private static final Log log = Log.getLog(MGDataSource.class);
   private final Gson JSON_BUILDER;
   private Version serverVersion;
   private DatabaseCache databaseCache = new DatabaseCache();
   private MGDataSourceInfo info;
   private List<MGDataType> dataTypes = new ArrayList();
   private boolean hasStatistics;

   private final MGDataWrapper dataWrapper = new MGDataWrapper();

   protected MGDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
      super(container);
      DBPConnectionConfiguration connectionInfo = this.getContainer().getConnectionConfiguration();
      this.dataTypes.add(new MGDataType(this, 1, "ObjectId", "objectId"));
      this.dataTypes.add(new MGDataType(this, 2, "Document", "document"));
      this.dataTypes.add(new MGDataType(this, 3, "String", "string"));
      this.dataTypes.add(new MGDataType(this, 4, "Number", "number"));
      this.dataTypes.add(new MGDataType(this, 7, "Boolean", "boolean"));
      this.dataTypes.add(new MGDataType(this, 8, "Timestamp", "date"));
      this.dataTypes.add(new MGDataType(this, 9, "Binary", "byte[]"));
      this.dataTypes.add(new MGDataType(this, 10, "List", "array"));
      this.dataTypes.add(new MGDataType(this, 11, "Object", "object"));
      GsonBuilder gsonBuilder = new GsonBuilder()
              .serializeSpecialFloatingPointValues()
              .registerTypeAdapter(ObjectId.class, new MGTypeConverters.ObjectIdTypeConverter())
              .registerTypeAdapter(Date.class, new MGTypeConverters.ISODateTypeConverter())
              .registerTypeAdapter(BsonTimestamp.class, new MGTypeConverters.BsonTimestampConverter())
              .setDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").serializeNulls();
      if (this.supportsDecimal128()) {
         gsonBuilder.registerTypeAdapter(Decimal128.class, new MGTypeConverters.Decimal128TypeConverter());
      }

//      this.JSON_BUILDER = gsonBuilder.disableHtmlEscaping().create();
      this.JSON_BUILDER = MGDataUtils.BSON_BUILDER;
      if (container.isTemporary()) {
         return;
      }
      String defDatabase = connectionInfo.getDatabaseName();
      String url = getConnectionURL(connectionInfo);
      if (MGUtils.isValidURL(url)) {
         url = MGUtils.transformURL(MGUtils.getMongoCredential(this, connectionInfo), url);

         try {
            defDatabase = (new ConnectionString(url)).getDatabase();
         } catch (Throwable var11) {
            log.debug("Bad mongo URL", var11);
         }
      }

      this.executionContext = new MGExecutionContext(this, "Main " + this.getDatabaseType() + " Connection", defDatabase);
      this.executionContext.connect(monitor);
      Document buildInfo = this.executionContext.getBuildInfo();
      Object version = buildInfo.get("version");
      if (version != null) {
         try {
            this.serverVersion = new Version(CommonUtils.toString(version).replaceAll("[^\\d.]", ""));
         } catch (Exception var10) {
            log.debug("Can't extract " + this.getDatabaseType() + " server version", var10);
         }
      }

   }

   @Override
   public DBDataWrapper getDataWrapper() {
      return dataWrapper;
   }

   public Version getServerVersion() {
      return this.serverVersion;
   }

   public boolean isServerVersionAtLeast(int major, int minor) {
      if (this.serverVersion == null) {
         return true;
      } else if (this.serverVersion.getMajor() < major) {
         return false;
      } else {
         return this.serverVersion.getMajor() != major || this.serverVersion.getMinor() >= minor;
      }
   }

   public MGExecutionContext getExecutionContext() {
      return this.executionContext;
   }

   public DatabaseCache getDatabaseCache() {
      return this.databaseCache;
   }

   public @NotNull MongoDatabase getAdminDatabase(MGSession session) throws DBCException {
      return session.getExecutionContext().getClient().getDatabase("admin");
   }

   public @Association Collection<MGDatabase> getDatabases() {
      return this.databaseCache.getCachedObjects();
   }

   public MGDatabase getDatabase(String name) {
      MGDatabase database = this.databaseCache.getCachedObject(name);
      return database == null ? new MGDatabase(this, name) : database;
   }

   public @NotNull DBPDataSourceInfo getInfo() {
      return this.info;
   }

   public Object getDataSourceFeature(String featureId) {
      if (featureId.equals("datasource.document-data-source")) {
         return Boolean.TRUE;
      }
      return null;
   }

   @Override
   public @NotNull MGExecutionContext openIsolatedContext(@NotNull DBRProgressMonitor monitor, @NotNull String purpose, @NotNull DBCExecutionContext initFrom, Boolean autoCommit, DBPConnectionConfiguration configuration) throws DBException {
      MGExecutionContext context = new MGExecutionContext(this, purpose, initFrom instanceof MGExecutionContext ? ((MGExecutionContext)initFrom).getSelectedDatabase() : this.getDefaultDatabase());
      context.connect(monitor);
      return context;
   }

   public void initialize(@NotNull DBRProgressMonitor monitor) throws DBException {
      try {
         this.databaseCache.getAllObjects(monitor, this);
         this.info = new MGDataSourceInfo(this, this.executionContext.getClient());
      } catch (Exception var3) {
         throw new DBException("Error initializing " + this.getDatabaseType() + " context", var3);
      }
   }

   public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) throws DBException {
      this.databaseCache.clearCache();
      this.hasStatistics = false;
      this.initialize(monitor);
      return this;
   }

   public String getDefaultDatabase() {
      return this.executionContext.getSelectedDatabase();
   }

   public <T> T getAdapter(Class<T> adapter) {
      if (adapter == DBSStructureAssistant.class) {
         return null;
      } else {
         return adapter == DBAServerSessionManager.class ? adapter.cast(new MGOperationManager(this)) : null;
      }
   }

   public String getObjectTypeTerm(String path, String objectType, boolean multiple) {
      String term = null;
      if ("cluster".equals(objectType)) {
         term = "Cluster";
      } else if ("keypace".equals(objectType)) {
         term = "Keyspace";
      }

      if (term != null && multiple) {
         term = term + "s";
      }

      return term;
   }

   public MGSQLDialect getSQLDialect() {
      return MGSQLDialect.INSTANCE;
   }

   public Collection<? extends DBSObject> getChildren(@NotNull DBRProgressMonitor monitor) throws DBException {
      return this.getDatabases();
   }

   public DBSObject getChild(@NotNull DBRProgressMonitor monitor, @NotNull String childName) throws DBException {
      MGDatabase database = this.getDatabase(childName);
      return database == null ? new MGDatabase(this, childName) : database;
   }

   public @NotNull Class<? extends DBSObject> getPrimaryChildType(@Nullable DBRProgressMonitor monitor) throws DBException {
      return MGDatabase.class;
   }

   public void cacheStructure(@NotNull DBRProgressMonitor monitor, int scope) throws DBException {
   }

   public @NotNull DBPDataKind resolveDataKind(@NotNull String typeName, int typeID) {
      switch (typeID) {
         case 1:
            return DBPDataKind.STRING;
         case 2:
            return DBPDataKind.DOCUMENT;
         case 3:
            return DBPDataKind.STRING;
         case 4:
            return DBPDataKind.NUMERIC;
         case 5:
         case 6:
         default:
            return DBPDataKind.ANY;
         case 7:
            return DBPDataKind.BOOLEAN;
         case 8:
            return DBPDataKind.DATETIME;
         case 9:
            return DBPDataKind.BINARY;
         case 10:
            return DBPDataKind.ARRAY;
      }
   }

   public MGDataType resolveDataType(@NotNull DBRProgressMonitor monitor, @NotNull String typeFullName) throws DBException {
      MGDataType dataType = this.getLocalDataType(typeFullName);
      return dataType != null && dataType.getTypeID() != 2 ? dataType : null;
   }

   public Collection<MGDataType> getLocalDataTypes() {
      return this.dataTypes;
   }

   public MGDataType getLocalDataType(String typeName) {

      for (MGDataType type : this.dataTypes) {
         if (type.getName().equals(typeName)) {
            return type;
         }
      }

      return null;
   }

   public MGDataType getLocalDataType(int typeID) {

      for (MGDataType type : this.dataTypes) {
         if (type.getTypeID() == typeID) {
            return type;
         }
      }

      return null;
   }

   public MGDataType getDataTypeByJS(String typeName) {

      for (MGDataType type : this.dataTypes) {
         if (type.getJsName().equals(typeName)) {
            return type;
         }
      }

      return this.getDocumentDataType(11);
   }

   public boolean supportsDecimal128() {
      return true;
   }

   public MGDataType getDocumentDataType(int valueType) {
      return this.getLocalDataType(valueType);
   }

   public String getDefaultDataTypeName(@NotNull DBPDataKind dataKind) {
      return "String";
   }

   public DBPErrorAssistant.ErrorType discoverErrorType(@NotNull Throwable error) {
      if (error.getCause() instanceof MongoSocketException){
         return ErrorType.CONNECTION_LOST;
      }
      if (error.getCause() != null && error.getCause().getMessage() != null){
         return discoverErrorType(error.getCause().getMessage());
      }
      return ErrorType.NORMAL;
   }

   @Override
   public ErrorType discoverErrorType(String message) {
      if (message.contains("state should be: open")){
         return ErrorType.CONNECTION_LOST;
      }
      return ErrorType.NORMAL;
   }

   public @Nullable DBPErrorAssistant.ErrorPosition[] getErrorPosition(@NotNull DBRProgressMonitor monitor, @NotNull DBCExecutionContext context, @NotNull String query, @NotNull Throwable error) {
      return null;
   }

   public boolean isStatisticsCollected() {
      return this.hasStatistics;
   }

   public void collectObjectStatistics(DBRProgressMonitor monitor, boolean totalSizeOnly, boolean forceRefresh) throws DBException {
      if (!this.hasStatistics || forceRefresh) {
         this.hasStatistics = true;

         for (MGDatabase db : this.getDatabases()) {
            try {
               db.readStatistics(monitor);
            } catch (DBException var7) {
               log.debug(var7);
            }
         }

      }
   }

   public void serializeDocument(Object document, Writer writer) {
      this.JSON_BUILDER.toJson(MGUtils.unwrapMongoValue(this, (String)null, document), writer);
   }

   public Map<String, Object> deserializeDocument(Reader reader) {
      StringWriter buf = new StringWriter();

      try {
         IOUtils.copyText(reader, buf);
      } catch (IOException var4) {
         log.debug(var4);
      }

      String json = buf.toString();
      return BasicDBObject.parse(json);
   }

   public DBMapValue<MGDataSource> convertNativeDocumentToMap(Map<String, Object> dbObject) {
      return (DBMapValue)MGUtils.wrapMongoValue(this, dbObject, (Object)null);
   }

   public Map<String, Object> convertMapToNativeDocument(DBMapValue<MGDataSource> map) {
      return (Map)MGUtils.unwrapMongoValue(this, (String)null, map.getRawValue());
   }

   public DBWHandlerConfiguration getCustomSSLConfiguration(DBRProgressMonitor monitor, DBPConnectionConfiguration connectionInfo) {
      return null;
   }

   public String getDefaultAuthMechanism() {
      return MongoCredential.SCRAM_SHA_1_MECHANISM;
   }

   public abstract boolean isEvalSupported();

   public abstract String getDatabaseType();

   private boolean isShowAllDatabases() {
      DBPConnectionConfiguration configuration = this.getContainer().getActualConnectionConfiguration();
      return CommonUtils.getBoolean(configuration.getProperty("showAllDatabases"), true) ? true : CommonUtils.isEmpty(this.getDatabaseName());
   }

   private @NotNull String getDatabaseName() {
      DBPConnectionConfiguration configuration = this.getContainer().getActualConnectionConfiguration();
      String name = MGUtils.getDatabaseFromURL(configuration.getUrl());
      return CommonUtils.isNotEmpty(name) ? name : configuration.getDatabaseName();
   }

   public class DatabaseCache extends BasicObjectCache<MGDataSource, MGDatabase> {


      public @NotNull List<MGDatabase> getAllObjects(@NotNull DBRProgressMonitor monitor, @Nullable MGDataSource dataSource) throws DBException {
         setFullCache(false);
         if (!this.isFullyCached()) {
            List<MGDatabase> databaseList = new ArrayList<>();
            if (!MGDataSource.this.isShowAllDatabases()) {
               databaseList.add(new MGDatabase(dataSource, MGDataSource.this.getDatabaseName()));
            } else {
               try {
                  try (MGSession session = DBUtils.openMetaSession(monitor, MGDataSource.this, "Read databases")) {
                     Iterable<String> databaseNames = session.getExecutionContext().getClient().listDatabaseNames();

                     for (String dbName : databaseNames) {
                        MGDatabase db = new MGDatabase(dataSource, dbName);
                        databaseList.add(db);
                     }
                  }
               } catch (Throwable t) {
                  assert dataSource != null;
                  databaseList.add(new MGDatabase(dataSource, MGDataSource.this.getDatabaseName()));
                  log.debug("loading DatabaseCache error : " + t.getMessage());
               }
            }

            this.setCache(databaseList);
         }

         return this.getCachedObjects();
      }
   }

   @Override
   public void closeClient() {
      DBPExclusiveResource exclusiveLock = getContainer().getExclusiveLock();
      MGClientPool pool = MGClientPool.getInstance();
      Object lock = exclusiveLock.acquireExclusiveLock();
      try (MongoClient ignored = pool.removeClient(getContainer().getId())) {
         // nothing to do here
      } finally {
         exclusiveLock.releaseExclusiveLock(lock);
      }
   }

}
