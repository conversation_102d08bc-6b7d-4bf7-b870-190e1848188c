<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="com.dc.summer.dataSourceProvider">
        <datasource
            class="com.dc.summer.ext.mongodb.MGDataSourceProviderMongoDB"
            description="MongoDB Connector"
            id="mongodb"
            label="MongoDB"
            icon="icons/mongodb_icon.png"
            dialect="mongodb">
            <tree path="mongodb" label="mongodb data source">
                <folder type="com.dc.summer.ext.mongodb.model.MGDatabase" label="%tree.databases.node.name" icon="#databases" description="Databases">
                    <items label="%tree.db.node.name" path="database" property="databases" icon="#database">
                        <folder type="com.dc.summer.ext.mongodb.model.MGCollection" label="%tree.collections.node.name" icon="#folder_table" description="Collections">
                            <items label="%tree.collection.node.name" path="collection" property="collections" icon="#table">

                                <folder type="com.dc.summer.ext.mongodb.model.MGIndex" label="%tree.indexes.node.name" icon="#indexes" description="CF indexes">
                                    <items label="%tree.index.node.name" path="index" property="indexes" icon="#index">
                                        <items label="%tree.index_columns.node.name" path="column" property="attributeReferences" icon="#column" navigable="false" inline="true">
                                        </items>
                                    </items>
                                </folder>
                            </items>
                        </folder>
                        <folder type="com.dc.summer.ext.mongodb.model.MGFunction" label="%tree.js.node.name" icon="#folder" description="Java Script">
                            <items label="%tree.function.node.name" path="function" property="functions" icon="#procedure">
                            </items>
                        </folder>
                        <folder type="com.dc.summer.ext.mongodb.model.MGUser" label="%tree.users.node.name" icon="#folder_user" description="Users">
                            <items label="%tree.user.node.name" path="user" property="users" icon="#user">
                            </items>
                        </folder>
                    </items>
                </folder>
                <folder type="" label="%tree.administer.node.name" icon="#folder_admin" description="Administration">
                    <treeContribution category="adminTools"/>
                </folder>

            </tree>

            <drivers managable="false">

                <driver
                    id="cql"
                    label="MongoDB"
                    class="org.jkiss.jdbc.mongodb.mongodbDriver"
                    defaultPort="27017"
                    customEndpoint="true"
                    webURL="https://dbeaver.io/"
                    description="Driver for MongoDB"
                    icon="icons/mongodb_icon.png"
                    iconBig="icons/mongodb_icon_big.png"
                    promoted="1"
                    supportedConfigurationTypes="MANUAL,URL"
                    categories="nosql,bigdata,document">
                    <parameter name="initOnTest" value="true"/>
                    <property name="@summer-default-sql.parameter.enabled" value="false"/>
                </driver>

                <provider-properties drivers="*">
                    <propertyGroup label="Settings">
                        <property id="authMechanism" label="Mechanism" type="string" description="Mechanism"
                                  validValues="NONE,SCRAM-SHA-1,SCRAM-SHA-256,MONGODB-CR,PLAIN,GSSAPI"
                                  defaultValue="NONE"
                        />
                        <property id="authSource" label="Source" type="string" description="Auth source" defaultValue="admin"/>
                    </propertyGroup>
                </provider-properties>
            </drivers>
        </datasource>

        <datasource
            class="com.dc.summer.ext.mongodb.MGDataSourceProviderDocumentDB"
            description="AWS DocumentDB Connector"
            id="aws-documentdb"
            label="AWS DocumentDB"
            icon="icons/aws_documentdb_icon.png"
            dialect="mongodb"
            parent="mongodb">

            <drivers managable="true">
                <driver
                    id="documentdb"
                    label="DocumentDB"
                    icon="icons/aws_documentdb_icon.png"
                    iconBig="icons/aws_documentdb_icon_big.png"
                    class="com.dbeaver.documentdb.DocumentDBDriver"
                    sampleURL="documentdb://{host}[:{port}]/{database}"
                    defaultPort="27017"
                    webURL="https://aws.amazon.com/documentdb/"
                    description="AWS DocumentDB driver (MongoDB Java Driver)"
                    categories="nosql,bigdata,document,aws">

                    <parameter name="initOnTest" value="true"/>
                    <property name="@summer-default-sql.parameter.enabled" value="false"/>
                </driver>
            </drivers>
        </datasource>

    </extension>

    <extension point="com.dc.summer.dataTypeProvider">
        <provider
            class="com.dc.summer.ext.mongodb.data.handlers.MongoValueHandlerProvider"
            description="Mongo data types provider"
            id="com.dc.summer.ext.mongodb.data.handlers.MongoValueHandlerProvider"
            label="mongodb data types provider">

            <datasource id="mongodb"/>
            <datasource id="aws-documentdb"/>
            <type name="*"/>

        </provider>
    </extension>

    <extension point="com.dc.summer.networkHandler">
        <handler
                type="config"
                id="mongo_ssl"
                codeName="SSL"
                label="SSL"
                description="SSL settings"
                secured="false"
                order="100"
                handlerClass="com.dc.summer.ext.mongodb.model.MGSSLHandlerImpl">
            <objectType name="com.dc.summer.ext.mongodb.MGDataSourceProviderMongoDB"/>
        </handler>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="mongodb" parent="nosql" class="com.dc.summer.ext.mongodb.model.MGSQLDialect" label="MongoDB" description="MongoDB dialect." icon="icons/mongodb_icon.png">
        </dialect>
    </extension>

    <extension point="com.dc.summer.objectManager">
        <manager class="com.dc.summer.ext.mongodb.edit.MongoDatabaseManager" objectType="com.dc.summer.ext.mongodb.model.MGDatabase"/>
        <manager class="com.dc.summer.ext.mongodb.edit.MongoCollectionManager" objectType="com.dc.summer.ext.mongodb.model.MGCollection"/>
    </extension>

    <extension point="com.dc.summer.dataSourceAuth">
        <authModel
                id="mongodb_default"
                label="MongoDB authentication"
                description="MongoDB authentication"
                class="com.dc.summer.ext.mongodb.auth.MGAuthModelDefault"
                desktop="false">
            <replace model="native"/>
            <datasource id="mongodb"/>
        </authModel>
        <authModel
                id="documentdb_default"
                label="DocumentDB authentication"
                description="DocumentDB authentication"
                class="com.dc.summer.ext.mongodb.auth.MGAuthModelDocumentDB"
                desktop="false"
                default="true">
            <replace model="native"/>
            <replace model="mongodb_default"/>
            <datasource id="aws-documentdb"/>
        </authModel>
    </extension>

</plugin>
