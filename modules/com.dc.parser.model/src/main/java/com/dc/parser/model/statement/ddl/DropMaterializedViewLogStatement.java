package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Drop materialized view log statement.
 */
@Getter
@Setter
public abstract class DropMaterializedViewLogStatement extends AbstractSQLStatement implements DDLStatement {

    private SimpleTableSegment tableSegment;
}
