package com.dc.parser.rewrite.common.generator;

import com.dc.parser.model.context.SQLStatementContext;

/**
 * SQL token generator.
 */
public interface SQLTokenGenerator {

    /**
     * Judge whether need to generate SQL token.
     *
     * @param sqlStatementContext SQL statement context
     * @return is generate SQL token or not
     */
    boolean isGenerateSQLToken(SQLStatementContext sqlStatementContext);
}
