package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.ddl.index.IndexTypeSegment;
import com.dc.parser.model.segment.ddl.packages.PackageSegment;
import com.dc.parser.model.segment.ddl.type.TypeSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.FunctionSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedList;
import java.util.List;

/**
 * Disassociate statistics statement.
 */
@Getter
@Setter
public abstract class DisassociateStatisticsStatement extends AbstractSQLStatement implements DDLStatement {

    private List<IndexSegment> indexes = new LinkedList<>();

    private List<SimpleTableSegment> tables = new LinkedList<>();

    private List<ColumnSegment> columns = new LinkedList<>();

    private List<FunctionSegment> functions = new LinkedList<>();

    private List<PackageSegment> packages = new LinkedList<>();

    private List<TypeSegment> types = new LinkedList<>();

    private List<IndexTypeSegment> indexTypes = new LinkedList<>();
}
