package com.dc.parser.model.statement;

import com.dc.infra.database.type.DatabaseType;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Error SQL statement for handling parsing exceptions.
 * This statement is returned when SQL parsing fails, containing error information.
 */
@RequiredArgsConstructor
@Getter
public final class ErrorStatement implements SQLStatement {

    private final DatabaseType databaseType;

    private final String originalSql;

    private final String errorMessage;

    private final Throwable cause;

    @Override
    public int getParameterCount() {
        return 0;
    }

    @Override
    public DatabaseType getDatabaseType() {
        return databaseType;
    }
}
