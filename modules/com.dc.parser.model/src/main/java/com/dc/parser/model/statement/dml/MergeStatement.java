package com.dc.parser.model.statement.dml;

import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.dml.expr.ExpressionWithParamsSegment;
import com.dc.parser.model.segment.dml.hint.WithTableHintSegment;
import com.dc.parser.model.segment.dml.merge.MergeWhenAndThenSegment;
import com.dc.parser.model.segment.generic.OutputSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.segment.generic.table.TableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.Collections;
import java.util.Optional;

/**
 * Merge statement.
 */
@Getter
@Setter
public abstract class MergeStatement extends AbstractSQLStatement implements DMLStatement {
    
    private TableSegment target;
    
    private TableSegment source;
    
    private ExpressionWithParamsSegment expression;
    
    private UpdateStatement update;
    
    private InsertStatement insert;
    
    /**
     * Get update statement.
     * 
     * @return update statement
     */
    public Optional<UpdateStatement> getUpdate() {
        return Optional.ofNullable(update);
    }
    
    /**
     * Get insert statement.
     *
     * @return insert statement
     */
    public Optional<InsertStatement> getInsert() {
        return Optional.ofNullable(insert);
    }

    /**
     * Get with segment.
     *
     * @return with segment
     */
    public Optional<WithSegment> getWithSegment() {
        return Optional.empty();
    }

    /**
     * Get with table hint segment.
     *
     * @return with table hint segment
     */
    public Optional<WithTableHintSegment> getWithTableHintSegment() {
        return Optional.empty();
    }

    /**
     * Get output segment.
     *
     * @return output segment
     */
    public Optional<OutputSegment> getOutputSegment() {
        return Optional.empty();
    }

    /**
     * Get when and then segments.
     *
     * @return when and then segments
     */
    public Collection<MergeWhenAndThenSegment> getWhenAndThenSegments() {
        return Collections.emptyList();
    }

    /**
     * Get index segments.
     *
     * @return index segments
     */
    public Collection<IndexSegment> getIndexes() {
        return Collections.emptyList();
    }
}
