package com.dc.parser.model.statement.dml;

import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.prepare.PrepareStatementQuerySegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.Collections;
import java.util.Optional;

/**
 * Copy statement.
 */
@Getter
@Setter
public abstract class CopyStatement extends AbstractSQLStatement implements DMLStatement {

    private SimpleTableSegment table;

    /**
     * Get table.
     *
     * @return table
     */
    public Optional<SimpleTableSegment> getTable() {
        return Optional.ofNullable(table);
    }

    /**
     * Set prepare statement query segment.
     *
     * @param prepareStatementQuery prepare statement query segment
     */
    public void setPrepareStatementQuery(final PrepareStatementQuerySegment prepareStatementQuery) {
    }

    /**
     * Get prepare statement query segment.
     *
     * @return prepare statement query segment
     */
    public Optional<PrepareStatementQuerySegment> getPrepareStatementQuery() {
        return Optional.empty();
    }

    /**
     * Get columns.
     *
     * @return columns
     */
    public Collection<ColumnSegment> getColumns() {
        return Collections.emptyList();
    }
}
