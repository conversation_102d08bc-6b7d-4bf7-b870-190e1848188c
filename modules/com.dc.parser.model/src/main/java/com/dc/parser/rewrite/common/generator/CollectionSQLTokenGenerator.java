package com.dc.parser.rewrite.common.generator;

import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.rewrite.common.pojo.SQLToken;

import java.util.Collection;

/**
 * SQL token generator for collection.
 *
 * @param <T> type of SQL statement context
 */
public interface CollectionSQLTokenGenerator<T extends SQLStatementContext> extends SQLTokenGenerator {

    /**
     * Generate SQL tokens.
     *
     * @param sqlStatementContext SQL statement context
     * @return SQL tokens
     */
    Collection<SQLToken> generateSQLTokens(T sqlStatementContext);
}
