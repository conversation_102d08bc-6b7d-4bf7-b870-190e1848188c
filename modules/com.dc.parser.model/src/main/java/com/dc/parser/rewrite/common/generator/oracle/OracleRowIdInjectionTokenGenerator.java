package com.dc.parser.rewrite.common.generator.oracle;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.model.context.segment.select.projection.Projection;
import com.dc.parser.model.context.segment.select.projection.impl.ColumnProjection;
import com.dc.parser.model.context.statement.dml.SelectStatementContext;
import com.dc.parser.model.segment.dml.item.ColumnProjectionSegment;
import com.dc.parser.model.segment.dml.item.ProjectionSegment;
import com.dc.parser.model.segment.dml.item.ShorthandProjectionSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.dc.parser.rewrite.common.generator.OptionalSQLTokenGenerator;
import com.dc.parser.rewrite.common.pojo.SQLToken;
import com.dc.parser.rewrite.common.pojo.generic.SubstitutableColumnNameToken;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * Oracle ROWIDn生成器
 * 用于将Oracle数据库的 SELECT 语句重写为具体的列名加ROWID。
 * <p>
 * 支持两种重写情况：
 * 1. SELECT * - 重写为所有列加ROWID
 * 2. SELECT col1, col2, ... - 所有投影都是简单列投影时，添加ROWID
 * <p>
 * 核心设计原则：
 * 1. 基于AST/SQLStatementContext
 * 2. 通过generateSQLToken方法返回SubstitutableColumnNameToken
 */
public final class OracleRowIdInjectionTokenGenerator implements OptionalSQLTokenGenerator<SelectStatementContext> {

    @Override
    public boolean isGenerateSQLToken(final SQLStatementContext sqlStatementContext) {
        // 检查是否为Oracle数据库的SELECT语句
        if (!(sqlStatementContext instanceof SelectStatementContext
                && DatabaseType.Constant.ORACLE == sqlStatementContext.getDatabaseType().getType())) {
            return false;
        }

        SelectStatementContext selectStatementContext = (SelectStatementContext) sqlStatementContext;

        // 必须是单表查询
        if (selectStatementContext.getTablesContext().getSimpleTables().size() != 1) {
            return false;
        }

        // 检查是否为无限定符的 * 投影
        if (selectStatementContext.getProjectionsContext().isUnqualifiedShorthandProjection()) {
            return true;
        }

        // 检查是否所有投影都是简单的列投影
        return selectStatementContext.getSqlStatement().getProjections().getProjections()
                .stream()
                .allMatch(projection -> projection instanceof ColumnProjectionSegment);
    }

    @Override
    public SQLToken generateSQLToken(final SelectStatementContext selectStatementContext) {
        // 情况1：处理 SELECT * 
        Optional<ShorthandProjectionSegment> shorthandProjection = findUnqualifiedShorthandProjectionSegment(selectStatementContext);
        if (shorthandProjection.isPresent()) {
            Collection<Projection> projections = selectStatementContext.getProjectionsContext().getExpandProjections();
            ColumnProjection rowIdProjection = new ColumnProjection(null, new IdentifierValue("ROWID"), new IdentifierValue("\"__WHD_DC_JDBC_interal_ROWID__\""), selectStatementContext.getDatabaseType());
            projections.add(rowIdProjection);

            return new SubstitutableColumnNameToken(
                    shorthandProjection.get().getStartIndex(),
                    shorthandProjection.get().getStopIndex(),
                    projections,
                    selectStatementContext.getDatabaseType()
            );
        }

        // 情况2：处理简单列投影
        Collection<ProjectionSegment> projectionSegments = selectStatementContext.getSqlStatement().getProjections().getProjections();
        if (!projectionSegments.isEmpty()) {
            // 转换为列表以便获取起始和结束位置
            List<ProjectionSegment> projectionList = new ArrayList<>(projectionSegments);
            int startIndex = projectionList.get(0).getStartIndex();
            int stopIndex = projectionList.get(projectionList.size() - 1).getStopIndex();

            // 构建新的投影列表：原有的列投影 + ROWID
            Collection<Projection> newProjections = new ArrayList<>();

            // 添加原有的列投影
            for (ProjectionSegment projection : projectionSegments) {
                if (projection instanceof ColumnProjectionSegment) {
                    ColumnProjectionSegment columnProjection = (ColumnProjectionSegment) projection;
                    newProjections.add(new ColumnProjection(
                            columnProjection.getColumn().getOwner().isPresent() ? columnProjection.getColumn().getOwner().get().getIdentifier().getValue() : null,
                            columnProjection.getColumn().getIdentifier().getValue(),
                            columnProjection.getAlias().isPresent() ? columnProjection.getAlias().get().getValue() : null,
                            selectStatementContext.getDatabaseType()
                    ));
                }
            }

            // 添加ROWID投影
            ColumnProjection rowIdProjection = new ColumnProjection(null, new IdentifierValue("ROWID"), new IdentifierValue("\"__WHD_DC_JDBC_interal_ROWID__\""), selectStatementContext.getDatabaseType());
            newProjections.add(rowIdProjection);

            return new SubstitutableColumnNameToken(startIndex, stopIndex, newProjections, selectStatementContext.getDatabaseType());
        }

        return null;
    }

    /**
     * 查找无限定符的 * 投影段
     */
    private Optional<ShorthandProjectionSegment> findUnqualifiedShorthandProjectionSegment(final SelectStatementContext selectStatementContext) {
        for (ProjectionSegment each : selectStatementContext.getSqlStatement().getProjections().getProjections()) {
            if (each instanceof ShorthandProjectionSegment) {
                ShorthandProjectionSegment shorthand = (ShorthandProjectionSegment) each;
                if (shorthand.getOwner().isEmpty()) {
                    return Optional.of(shorthand);
                }
            }
        }
        return Optional.empty();
    }
}
