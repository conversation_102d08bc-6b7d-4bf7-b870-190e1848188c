package com.dc.parser.model.statement.dml;

import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * Call statement.
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public abstract class CallStatement extends AbstractSQLStatement implements DMLStatement {

    private String procedureName;

    private List<ExpressionSegment> parameters;
}
