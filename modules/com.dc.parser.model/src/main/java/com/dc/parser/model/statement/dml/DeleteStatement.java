package com.dc.parser.model.statement.dml;

import com.dc.parser.model.segment.dml.ReturningSegment;
import com.dc.parser.model.segment.dml.order.OrderBySegment;
import com.dc.parser.model.segment.dml.pagination.limit.LimitSegment;
import com.dc.parser.model.segment.dml.predicate.WhereSegment;
import com.dc.parser.model.segment.generic.OutputSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.segment.generic.table.TableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Delete statement.
 */
@Setter
public abstract class DeleteStatement extends AbstractSQLStatement implements DMLStatement {
    
    @Getter
    private TableSegment table;
    
    private WhereSegment where;
    
    /**
     * Get where.
     *
     * @return where segment
     */
    public Optional<WhereSegment> getWhere() {
        return Optional.ofNullable(where);
    }

    /**
     * Get order by.
     *
     * @return order by
     */
    public Optional<OrderBySegment> getOrderBy() {
        return Optional.empty();
    }

    /**
     * Get limit.
     *
     * @return limit
     */
    public Optional<LimitSegment> getLimit() {
        return Optional.empty();
    }

    /**
     * Get output segment.
     *
     * @return output segment
     */
    public Optional<OutputSegment> getOutputSegment() {
        return Optional.empty();
    }

    /**
     * Get with segment.
     *
     * @return with segment
     */
    public Optional<WithSegment> getWithSegment() {
        return Optional.empty();
    }

    /**
     * Set order by segment.
     *
     * @param orderBySegment order by segment
     */
    public void setOrderBy(final OrderBySegment orderBySegment) {
    }

    /**
     * Set limit segment.
     *
     * @param limitSegment limit segment
     */
    public void setLimit(final LimitSegment limitSegment) {
    }

    /**
     * Set output segment.
     *
     * @param outputSegment output segment
     */
    public void setOutputSegment(final OutputSegment outputSegment) {
    }

    /**
     * Set with segment.
     *
     * @param withSegment with segment
     */
    public void setWithSegment(final WithSegment withSegment) {
    }

    /**
     * Get returning segment of delete statement.
     *
     * @return returning segment
     */
    public Optional<ReturningSegment> getReturningSegment() {
        return Optional.empty();
    }
}
