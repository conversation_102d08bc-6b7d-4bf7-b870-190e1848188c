package com.dc.parser.rewrite;

import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.model.metadata.ShardingSphereMetaData;
import com.dc.parser.rewrite.common.generator.SQLTokenGenerator;
import com.dc.parser.rewrite.common.generator.SQLTokenGenerators;
import com.dc.parser.rewrite.common.generator.builder.CustomSQLTokenGeneratorBuilder;
import com.dc.parser.rewrite.common.pojo.SQLToken;
import com.dc.parser.rewrite.config.RuleConfiguration;
import com.dc.parser.rewrite.enums.RewriteRuleType;
import com.dc.parser.rewrite.generator.DMLPrecomputationSQLGenerator;
import com.dc.parser.rewrite.result.SQLRewriteResult;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * SQL重写引擎
 * <p>
 * 此类是SQL重写功能的主入口点。
 * 它协调了所有适用的重写规则，并生成最终的重写结果。
 */
@Slf4j
public final class SQLRewriteEngine {

    /**
     * 自定义规则配置
     */
    private final RuleConfiguration ruleConfiguration;

    /**
     * 默认构造函数，使用默认配置
     */
    public SQLRewriteEngine() {
        this.ruleConfiguration = RuleConfiguration.defaultConfiguration();
    }

    /**
     * 构造函数，允许自定义规则配置
     *
     * @param ruleConfiguration 自定义规则配置
     */
    public SQLRewriteEngine(RuleConfiguration ruleConfiguration) {
        this.ruleConfiguration = ruleConfiguration;
    }

    /**
     * 执行SQL重写
     *
     * @param sqlStatementContext SQL语句上下文
     * @param sql                 原始SQL字符串
     * @param metaData            元数据访问接口
     * @return 自定义重写结果
     */
    public SQLRewriteResult rewrite(SQLStatementContext sqlStatementContext, String sql, ShardingSphereMetaData metaData) {
        log.debug("Starting SQL rewrite for original SQL: [{}]", sql);
        try {
            // 阶段1：使用SQLTokenGenerator模式处理主SQL重写
            String rewrittenSql = rewriteMainSQL(sqlStatementContext, sql, metaData);
            boolean mainSqlRewritten = !sql.equals(rewrittenSql);

            // 阶段2：独立生成伴生SQL（如果需要）
            String companionSql = null;
            boolean companionSqlGenerated = false;

            if (ruleConfiguration.isEnableDMLPrecomputation() &&
                    DMLPrecomputationSQLGenerator.canGenerate(sqlStatementContext)) {
                companionSql = DMLPrecomputationSQLGenerator.generate(sqlStatementContext, sql);
                companionSqlGenerated = (companionSql != null);
            }

            // 构建结果
            if (mainSqlRewritten && companionSqlGenerated) {
                log.info("Rewrite complete for [{}]: Applied both main SQL rewrite and generated companion SQL.", sql);
                return SQLRewriteResult.both(rewrittenSql, companionSql,
                        RewriteRuleType.ORACLE_SELECT_STAR_AND_DML_PRECOMPUTATION.getRuleName(),
                        RewriteRuleType.ORACLE_SELECT_STAR_AND_DML_PRECOMPUTATION.getDescription());
            } else if (mainSqlRewritten) {
                log.info("Rewrite complete for [{}]: Applied main SQL rewrite only.", sql);
                return SQLRewriteResult.mainSqlOnly(rewrittenSql,
                        RewriteRuleType.ORACLE_SELECT_STAR_REWRITE.getRuleName(),
                        RewriteRuleType.ORACLE_SELECT_STAR_REWRITE.getDescription());
            } else if (companionSqlGenerated) {
                log.info("Rewrite complete for [{}]: Generated companion SQL only.", sql);
                return SQLRewriteResult.companionSqlOnly(sql, companionSql,
                        RewriteRuleType.DML_PRECOMPUTATION.getRuleName(),
                        RewriteRuleType.DML_PRECOMPUTATION.getDescription());
            } else {
                log.info("No applicable rewrite rule found for [{}].", sql);
                return SQLRewriteResult.noRewrite(sql);
            }

        } catch (Exception e) {
            log.error("SQL rewrite failed", e);
            return SQLRewriteResult.noRewrite(sql);
        }
    }

    /**
     * 使用SQLTokenGenerator模式重写主SQL
     */
    private String rewriteMainSQL(SQLStatementContext sqlStatementContext, String sql, ShardingSphereMetaData metaData) {
        // 创建自定义Token生成器构建器
        CustomSQLTokenGeneratorBuilder tokenGeneratorBuilder =
                new CustomSQLTokenGeneratorBuilder(sqlStatementContext, ruleConfiguration);

        // 获取适用的Token生成器
        Collection<SQLTokenGenerator> tokenGenerators = tokenGeneratorBuilder.getSQLTokenGenerators();

        if (tokenGenerators.isEmpty()) {
            return sql;
        }

        SQLTokenGenerators sqlTokenGenerators = new SQLTokenGenerators();
        sqlTokenGenerators.addAll(tokenGenerators);

        // 生成SQLToken
        List<SQLToken> sqlTokens = sqlTokenGenerators.generateSQLTokens(sqlStatementContext, Collections.emptyList());

        // 应用Token重写SQL
        return applySQLTokens(sql, sqlTokens);
    }

    /**
     * 应用SQLToken来重写SQL
     */
    private String applySQLTokens(String originalSql, List<SQLToken> sqlTokens) {
        if (sqlTokens.isEmpty()) {
            return originalSql;
        }

        // 按起始位置排序Token
        sqlTokens.sort(SQLToken::compareTo);

        StringBuilder result = new StringBuilder();
        int currentIndex = 0;

        for (SQLToken token : sqlTokens) {
            // 添加Token之前的原始SQL部分
            result.append(originalSql, currentIndex, token.getStartIndex());

            // 添加Token的替换内容
            result.append(token);

            // 更新当前位置
            currentIndex = token.getStopIndex() + 1;
        }

        // 添加最后一个Token之后的原始SQL部分
        result.append(originalSql.substring(currentIndex));

        return result.toString();
    }
}
