
package com.dc.parser.model.statement.tcl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.model.enums.OperationScope;
import com.dc.parser.model.enums.TransactionAccessType;
import com.dc.parser.model.enums.TransactionIsolationLevel;
import com.dc.parser.model.statement.AbstractSQLStatement;

import java.util.Optional;

/**
 * Set transaction statement.
 */
@Getter
@Setter
public abstract class SetTransactionStatement extends AbstractSQLStatement implements TCLStatement {
    
    private TransactionIsolationLevel isolationLevel;
    
    private OperationScope scope;
    
    private TransactionAccessType accessMode;
    
    /**
     * Get isolation level.
     * 
     * @return isolation level
     */
    public Optional<TransactionIsolationLevel> getIsolationLevel() {
        return Optional.ofNullable(isolationLevel);
    }
    
    /**
     * Get access mode.
     * 
     * @return access mode 
     */
    public Optional<TransactionAccessType> getAccessMode() {
        return Optional.ofNullable(accessMode);
    }
}
