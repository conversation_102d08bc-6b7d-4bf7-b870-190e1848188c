package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public abstract class CreateHistogramTemplateStatement extends AbstractSQLStatement implements DDLStatement {

    private IdentifierValue templateName;

    private Integer value;
}
