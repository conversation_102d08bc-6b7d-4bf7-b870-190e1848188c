package com.dc.parser.model.statement.dml;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Lock table statement.
 */
@Getter
@Setter
public abstract class LockTableStatement extends AbstractSQLStatement implements DMLStatement {

    private Collection<SimpleTableSegment> tableSegments = new LinkedList<>();
}
