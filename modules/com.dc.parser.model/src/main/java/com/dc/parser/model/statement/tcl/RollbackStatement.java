
package com.dc.parser.model.statement.tcl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.model.statement.AbstractSQLStatement;

import java.util.Optional;

/**
 * Rollback statement.
 */
@Getter
@Setter
public abstract class RollbackStatement extends AbstractSQLStatement implements TCLStatement {
    
    private String savepointName;
    
    /**
     * Get save point name.
     *
     * @return save point name
     */
    public Optional<String> getSavepointName() {
        return Optional.ofNullable(savepointName);
    }
}
