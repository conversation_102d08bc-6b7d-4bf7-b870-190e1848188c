package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.routine.FunctionNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;

public abstract class CreateFunctionMappingStatement extends AbstractSQLStatement implements DDLStatement {

    public FunctionNameSegment getFunctionName() {
        return null;
    }

    public IdentifierValue getFunctionMappingName() {
        return null;
    }

    public IdentifierValue getSpecificName() {
        return null;
    }
}
