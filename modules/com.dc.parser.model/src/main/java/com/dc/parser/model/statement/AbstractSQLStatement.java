package com.dc.parser.model.statement;

import com.dc.infra.utils.CaseInsensitiveSet;
import com.dc.parser.model.segment.generic.CommentSegment;
import com.dc.parser.model.segment.generic.ParameterMarkerSegment;
import lombok.Getter;
import org.antlr.v4.runtime.tree.ParseTree;

import java.util.Collection;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.LinkedList;

/**
 * SQL statement abstract class.
 */
@Getter
public abstract class AbstractSQLStatement implements SQLStatement {
    
    private final Collection<ParameterMarkerSegment> parameterMarkerSegments = new LinkedHashSet<>();
    
    private final Collection<Integer> uniqueParameterIndexes = new HashSet<>();
    
    private final Collection<CommentSegment> commentSegments = new LinkedList<>();

    private final Collection<String> variableNames = new CaseInsensitiveSet<>();

    private ParseTree parseTree;
    
    @Override
    public int getParameterCount() {
        return uniqueParameterIndexes.size();
    }
    
    /**
     * Add parameter marker segment.
     * 
     * @param parameterMarkerSegments parameter marker segment collection
     */
    public void addParameterMarkerSegments(final Collection<ParameterMarkerSegment> parameterMarkerSegments) {
        for (ParameterMarkerSegment each : parameterMarkerSegments) {
            this.parameterMarkerSegments.add(each);
            uniqueParameterIndexes.add(each.getParameterIndex());
        }
    }

    @Override
    public ParseTree getParseTree() {
        return parseTree;
    }

    public void addParseTree(ParseTree parseTree) {
        this.parseTree = parseTree;
    }
}
