package com.dc.parser.rewrite.common.generator;

import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.rewrite.common.pojo.SQLToken;

/**
 * SQL token generator for optional.
 *
 * @param <T> type of SQL statement context
 */
public interface OptionalSQLTokenGenerator<T extends SQLStatementContext> extends SQLTokenGenerator {

    /**
     * Generate SQL token.
     *
     * @param sqlStatementContext SQL statement context
     * @return SQL token
     */
    SQLToken generateSQLToken(T sqlStatementContext);
}
