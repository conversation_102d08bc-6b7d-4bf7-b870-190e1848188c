package com.dc.parser.rewrite.common.pojo.generic;

import com.dc.infra.database.enums.QuoteCharacter;
import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.database.type.DatabaseTypeRegistry;
import com.dc.parser.model.context.segment.select.projection.Projection;
import com.dc.parser.model.context.segment.select.projection.impl.ColumnProjection;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.dc.parser.rewrite.common.pojo.SQLToken;
import com.dc.parser.rewrite.common.pojo.Substitutable;
import lombok.Getter;

import java.util.Collection;

/**
 * Substitutable column name token.
 */
public final class SubstitutableColumnNameToken extends SQLToken implements Substitutable {

    private static final String COLUMN_NAME_SPLITTER = ", ";

    @Getter
    private final int stopIndex;

    @Getter
    private final Collection<Projection> projections;

    @Getter
    private final DatabaseType databaseType;

    private final QuoteCharacter quoteCharacter;

    public SubstitutableColumnNameToken(final int startIndex, final int stopIndex, final Collection<Projection> projections, final DatabaseType databaseType) {
        super(startIndex);
        this.stopIndex = stopIndex;
        this.projections = projections;
        this.databaseType = databaseType;
        quoteCharacter = new DatabaseTypeRegistry(databaseType).getDialectDatabaseMetaData().getQuoteCharacter();
    }

    @Override
    public String toString() {
        StringBuilder result = new StringBuilder();
        int index = 0;
        for (Projection each : projections) {
            if (index > 0) {
                result.append(COLUMN_NAME_SPLITTER);
            }

            result.append(getColumnExpression(each));
            index++;
        }
        return result.toString();
    }

    private String getColumnExpression(final Projection projection) {
        StringBuilder builder = new StringBuilder();
        if (projection instanceof ColumnProjection) {
            appendColumnProjection((ColumnProjection) projection, builder);
        } else {
            builder.append(quoteCharacter.wrap(projection.getColumnLabel()));
        }
        return builder.toString();
    }

    private void appendColumnProjection(final ColumnProjection columnProjection, final StringBuilder builder) {
        columnProjection.getLeftParentheses().ifPresent(optional -> builder.append("("));
        builder.append(getValueWithQuoteCharacters(columnProjection.getName()));
        columnProjection.getRightParentheses().ifPresent(optional -> builder.append(")"));
        if (columnProjection.getAlias().isPresent()) {
            builder.append(" AS ").append(getValueWithQuoteCharacters(columnProjection.getAlias().get()));
        }
    }

    private String getValueWithQuoteCharacters(final IdentifierValue identifierValue) {
        return QuoteCharacter.NONE == identifierValue.getQuoteCharacter() ? identifierValue.getValue() : quoteCharacter.wrap(identifierValue.getValue());
    }
}
