package com.dc.parser.rewrite.common.generator;

import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.rewrite.common.pojo.SQLToken;

import java.util.*;

/**
 * SQL token generators.
 */
public final class SQLTokenGenerators {

    private final Collection<SQLTokenGenerator> generators = new LinkedList<>();

    /**
     * Add all SQL token generators.
     *
     * @param sqlTokenGenerators SQL token generators
     */
    public void addAll(final Collection<SQLTokenGenerator> sqlTokenGenerators) {
        generators.addAll(sqlTokenGenerators);
    }

    /**
     * Generate SQL tokens.
     *
     * @param sqlStatementContext SQL statement context
     * @param params              SQL parameters
     * @return SQL tokens
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public List<SQLToken> generateSQLTokens(final SQLStatementContext sqlStatementContext, final List<Object> params) {
        Set<SQLToken> resultSet = new LinkedHashSet<>();
        for (SQLTokenGenerator each : generators) {
            if (each instanceof OptionalSQLTokenGenerator) {
                // add 方法会自动处理重复，如果元素已存在则返回 false
                resultSet.add(((OptionalSQLTokenGenerator) each).generateSQLToken(sqlStatementContext));
            } else if (each instanceof CollectionSQLTokenGenerator) {
                resultSet.addAll(((CollectionSQLTokenGenerator) each).generateSQLTokens(sqlStatementContext));
            }
        }
        return new LinkedList<>(resultSet);
    }
}
