package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public abstract class CreateGlobalTemporaryTableStatement extends AbstractSQLStatement implements DDLStatement {

    private SimpleTableSegment table;

    private List<ColumnDefinitionSegment> columnDefinitionSegments;

    private SimpleTableSegment likeTable;

    private SelectStatement select;

}
