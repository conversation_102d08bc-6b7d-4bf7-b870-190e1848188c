package com.dc.parser.rewrite.generator;

import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.model.context.statement.dml.DeleteStatementContext;
import com.dc.parser.model.context.statement.dml.UpdateStatementContext;
import com.dc.parser.model.segment.dml.predicate.WhereSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.segment.generic.table.TableSegment;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.model.statement.dml.UpdateStatement;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 * DML影响行数预估SQL生成器
 * <p>
 * 它的目标是生成一条全新的、伴生的SQL，而不是修改原始的DML语句。
 */
@Slf4j
public final class DMLPrecomputationSQLGenerator {

    /**
     * 生成DML影响行数预估的COUNT查询
     *
     * @param sqlStatementContext SQL语句上下文
     * @param originalSql         原始SQL字符串（用于提取WHERE子句文本）
     * @return 伴生的COUNT查询SQL，如果不适用则返回null
     */
    public static String generate(final SQLStatementContext sqlStatementContext, final String originalSql) {
        if (sqlStatementContext instanceof UpdateStatementContext) {
            return generateFromUpdate((UpdateStatementContext) sqlStatementContext, originalSql);
        } else if (sqlStatementContext instanceof DeleteStatementContext) {
            return generateFromDelete((DeleteStatementContext) sqlStatementContext, originalSql);
        }
        return null;
    }

    /**
     * 从UPDATE语句生成COUNT查询
     */
    private static String generateFromUpdate(final UpdateStatementContext updateContext, final String originalSql) {
        UpdateStatement updateStatement = updateContext.getSqlStatement();
        return generateCountQuery(updateStatement.getTable(), updateStatement.getWhere(), originalSql);
    }

    /**
     * 从DELETE语句生成COUNT查询
     */
    private static String generateFromDelete(final DeleteStatementContext deleteContext, final String originalSql) {
        DeleteStatement deleteStatement = deleteContext.getSqlStatement();
        return generateCountQuery(deleteStatement.getTable(), deleteStatement.getWhere(), originalSql);
    }

    /**
     * 通用的COUNT查询生成方法
     *
     * @param tableSegment 表段
     * @param whereSegment WHERE条件段
     * @param originalSql  原始SQL字符串
     * @return 生成的COUNT查询SQL，如果无法生成则返回null
     */
    private static String generateCountQuery(final TableSegment tableSegment, final Optional<WhereSegment> whereSegment, final String originalSql) {
        // 获取表名
        String tableName = extractTableName(tableSegment);
        if (tableName == null) {
            return null;
        }

        // 构建COUNT查询
        StringBuilder countSql = new StringBuilder("SELECT COUNT(*) as COUNT FROM ").append(tableName);

        // 添加WHERE条件（如果存在）
        if (whereSegment.isPresent()) {
            String whereClause = extractWhereClause(whereSegment.get(), originalSql);
            if (whereClause != null) {
                countSql.append(" ").append(whereClause);
            }
        }

        return countSql.toString();
    }

    /**
     * 提取表名
     */
    private static String extractTableName(final TableSegment tableSegment) {
        if (!(tableSegment instanceof SimpleTableSegment)) {
            log.warn("Cannot extract table name: Unsupported table segment type '{}'. Only SimpleTableSegment is supported.", tableSegment.getClass().getName());
            return null;
        }

        SimpleTableSegment simpleTable = (SimpleTableSegment) tableSegment;
        return simpleTable.getTableName().getIdentifier().getValueWithQuoteCharacters();
    }

    /**
     * 提取WHERE子句文本
     */
    private static String extractWhereClause(final WhereSegment whereSegment, final String originalSql) {
        try {
            return originalSql.substring(whereSegment.getStartIndex(), whereSegment.getStopIndex() + 1);
        } catch (Exception e) {
            // 如果提取失败，返回null
            log.error("Failed to extract WHERE clause from original SQL due to index out of bounds.", e);
            return null;
        }
    }

    /**
     * 检查是否可以为给定的SQL语句上下文生成COUNT查询
     */
    public static boolean canGenerate(final SQLStatementContext sqlStatementContext) {
        return sqlStatementContext instanceof UpdateStatementContext || sqlStatementContext instanceof DeleteStatementContext;
    }
}
