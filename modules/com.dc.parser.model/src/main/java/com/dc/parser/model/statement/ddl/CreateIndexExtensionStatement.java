package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.collection.CollectionValue;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public abstract class CreateIndexExtensionStatement extends AbstractSQLStatement implements DDLStatement {


    public IdentifierValue getIndexExtensionName() {
        return null;
    }

    public CollectionValue<SQLSegment> getParamList() {
        return null;
    }

}
