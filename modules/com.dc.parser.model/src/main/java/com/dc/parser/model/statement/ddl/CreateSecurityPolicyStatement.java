package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.collection.CollectionValue;
import com.dc.parser.model.value.identifier.IdentifierValue;

/**
 * Create security policy statement.
 */
public abstract class CreateSecurityPolicyStatement extends AbstractSQLStatement implements DDLStatement {


    public IdentifierValue getPolicyName() {
        return null;
    }

    public CollectionValue<IdentifierValue> getComponentNames() {
        return null;
    }
}
