package com.dc.parser.model.statement;

import com.dc.infra.database.type.DatabaseType;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class IgnoredSQLStatement implements SQLStatement {

    private final DatabaseType databaseType;

    @Override
    public int getParameterCount() {
        return 0;
    }

    @Override
    public DatabaseType getDatabaseType() {
        return databaseType;
    }
}
