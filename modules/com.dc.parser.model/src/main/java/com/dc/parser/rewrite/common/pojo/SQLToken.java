package com.dc.parser.rewrite.common.pojo;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * SQL Token.
 */
@RequiredArgsConstructor
@Getter
public abstract class SQLToken implements Comparable<SQLToken> {

    private final int startIndex;

    @Override
    public final int compareTo(final SQLToken sqlToken) {
        return startIndex - sqlToken.startIndex;
    }

    /**
     * Get stop index.
     *
     * @return stop index
     */
    public abstract int getStopIndex();
}
