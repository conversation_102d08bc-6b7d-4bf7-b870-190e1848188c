package com.dc.parser.model.statement.dml;

import com.dc.parser.model.segment.database.impala.SelectFromValuesSegment;
import com.dc.parser.model.segment.dml.assignment.InsertValuesSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;
import java.util.Optional;

@Setter
public abstract class ValuesStatement extends AbstractSQLStatement implements DMLStatement {

    @Getter
    private final Collection<InsertValuesSegment> values = new LinkedList<>();


    public Optional<SelectFromValuesSegment> getSelectFromValuesSegment() {
        return Optional.empty();
    }

    public void setSelectFromValuesSegment(SelectFromValuesSegment selectFromValuesSegment) {
    }
}
