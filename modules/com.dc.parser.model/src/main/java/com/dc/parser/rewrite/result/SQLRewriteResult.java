package com.dc.parser.rewrite.result;

import com.dc.parser.rewrite.enums.RewriteRuleType;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

/**
 * 自定义重写结果容器
 * <p>
 * 此类能够同时容纳被重写后的主SQL和伴生的COUNT(*)SQL。
 * 它区分了"SQL修改"和"伴生SQL生成"两种不同的重写场景。
 */
@RequiredArgsConstructor
@Getter
public final class SQLRewriteResult {

    /**
     * 重写后的主SQL（如果发生了重写）
     */
    private final String rewrittenSql;

    /**
     * 伴生的SQL（如DML影响行数预估的COUNT查询）
     */
    private final String companionSql;

    /**
     * 应用的重写规则名称
     */
    private final String appliedRuleName;

    /**
     * 重写描述
     */
    private final String description;

    /**
     * 是否发生了主SQL重写
     */
    private final boolean mainSqlRewritten;

    /**
     * 是否生成了伴生SQL
     */
    private final boolean companionSqlGenerated;

    /**
     * 创建仅包含主SQL重写的结果
     */
    public static SQLRewriteResult mainSqlOnly(String rewrittenSql, String ruleName, String description) {
        return new SQLRewriteResult(rewrittenSql, null, ruleName, description, true, false);
    }

    /**
     * 创建仅包含伴生SQL的结果
     */
    public static SQLRewriteResult companionSqlOnly(String originalSql, String companionSql, String ruleName, String description) {
        return new SQLRewriteResult(originalSql, companionSql, ruleName, description, false, true);
    }

    /**
     * 创建同时包含主SQL重写和伴生SQL的结果
     */
    public static SQLRewriteResult both(String rewrittenSql, String companionSql, String ruleName, String description) {
        return new SQLRewriteResult(rewrittenSql, companionSql, ruleName, description, true, true);
    }

    /**
     * 创建无重写的结果
     */
    public static SQLRewriteResult noRewrite(String originalSql) {
        return new SQLRewriteResult(originalSql, null,
                RewriteRuleType.NONE.getRuleName(),
                RewriteRuleType.NONE.getDescription(),
                false, false);
    }

    /**
     * 获取伴生SQL（如果存在）
     */
    public Optional<String> getCompanionSql() {
        return Optional.ofNullable(companionSql);
    }

    /**
     * 检查是否有任何形式的重写发生
     */
    public boolean hasAnyRewrite() {
        return mainSqlRewritten || companionSqlGenerated;
    }
}
