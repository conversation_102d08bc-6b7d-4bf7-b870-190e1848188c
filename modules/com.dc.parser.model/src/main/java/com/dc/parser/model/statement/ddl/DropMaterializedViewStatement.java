package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Drop materialized view statement.
 */
@Getter
@Setter
public abstract class DropMaterializedViewStatement extends AbstractSQLStatement implements DDLStatement {

    private SimpleTableSegment view;
}
