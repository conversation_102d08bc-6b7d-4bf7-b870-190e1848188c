
package com.dc.parser.model.statement.dml;

import com.dc.parser.model.segment.dml.combine.CombineSegment;
import com.dc.parser.model.segment.dml.item.ProjectionsSegment;
import com.dc.parser.model.segment.dml.order.GroupBySegment;
import com.dc.parser.model.segment.dml.order.OrderBySegment;
import com.dc.parser.model.segment.dml.pagination.limit.LimitSegment;
import com.dc.parser.model.segment.dml.predicate.HavingSegment;
import com.dc.parser.model.segment.dml.predicate.LockSegment;
import com.dc.parser.model.segment.dml.predicate.WhereSegment;
import com.dc.parser.model.segment.generic.ModelSegment;
import com.dc.parser.model.segment.generic.WindowSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.segment.generic.table.TableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Select statement.
 */
@Getter
@Setter
public abstract class SelectStatement extends AbstractSQLStatement implements DMLStatement {
    
    private ProjectionsSegment projections;
    
    private TableSegment from;
    
    private WhereSegment where;
    
    private GroupBySegment groupBy;
    
    private HavingSegment having;
    
    private OrderBySegment orderBy;
    
    private CombineSegment combine;

    private WithSegment withSegment;

    /**
     * Get from.
     *
     * @return from table segment
     */
    public Optional<TableSegment> getFrom() {
        return Optional.ofNullable(from);
    }
    
    /**
     * Get where.
     *
     * @return where segment
     */
    public Optional<WhereSegment> getWhere() {
        return Optional.ofNullable(where);
    }
    
    /**
     * Get group by segment.
     *
     * @return group by segment
     */
    public Optional<GroupBySegment> getGroupBy() {
        return Optional.ofNullable(groupBy);
    }
    
    /**
     * Get having segment.
     *
     * @return having segment
     */
    public Optional<HavingSegment> getHaving() {
        return Optional.ofNullable(having);
    }

    /**
     * Get limit segment.
     *
     * @return limit segment
     */
    public Optional<LimitSegment> getLimit() {
        return Optional.empty();
    }

    /**
     * Set limit segment.
     *
     * @param limitSegment limit segment
     */
    public void setLimit(final LimitSegment limitSegment) {
    }

    /**
     * Get order by segment.
     *
     * @return order by segment
     */
    public Optional<OrderBySegment> getOrderBy() {
        return Optional.ofNullable(orderBy);
    }

    /**
     * Get lock segment.
     *
     * @return lock segment
     */
    public Optional<LockSegment> getLock() {
        return Optional.empty();
    }

    /**
     * Set lock segment.
     *
     * @param lockSegment lock segment
     */
    public void setLock(final LockSegment lockSegment) {
    }

    /**
     * Get combine segment.
     *
     * @return combine segment
     */
    public Optional<CombineSegment> getCombine() {
        return Optional.ofNullable(combine);
    }

    /**
     * Get with segment.
     *
     * @return with segment.
     */
    public Optional<WithSegment> getWithSegment() {
        return Optional.ofNullable(withSegment);
    }

    /**
     * Get window segment.
     *
     * @return window segment
     */
    public Optional<WindowSegment> getWindow() {
        return Optional.empty();
    }

    /**
     * Set window segment.
     *
     * @param windowSegment window segment
     */
    public void setWindow(final WindowSegment windowSegment) {
    }

    /**
     * Get model segment.
     *
     * @return model segment
     */
    public Optional<ModelSegment> getModelSegment() {
        return Optional.empty();
    }

    /**
     * Set model segment.
     *
     * @param modelSegment model segment
     */
    public void setModelSegment(final ModelSegment modelSegment) {
    }

    /**
     * Get into segment.
     *
     * @return into table segment
     */
    public Optional<TableSegment> getIntoSegment() {
        return Optional.empty();
    }

    /**
     * Set into segment.
     *
     * @param intoSegment table into segment
     */
    public void setIntoSegment(final TableSegment intoSegment) {
    }
}
