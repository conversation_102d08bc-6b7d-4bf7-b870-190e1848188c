
package com.dc.parser.model.statement;

import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.spi.TypedSPILoader;
import com.dc.parser.model.api.ASTNode;
import org.antlr.v4.runtime.tree.ParseTree;

import java.util.Collection;
import java.util.Collections;

/**
 * SQL statement.
 */
public interface SQLStatement extends ASTNode {

    /**
     * Get count of parameters.
     *
     * @return count of parameters
     */
    int getParameterCount();

    /**
     * Get database type.
     *
     * @return database type
     */
    default DatabaseType getDatabaseType() {
        return TypedSPILoader.getService(DatabaseType.class, "SQL92");
    }

    /**
     * Get variable names.
     *
     * @return variable names
     */
    default Collection<String> getVariableNames() {
        return Collections.emptyList();
    }

    default ParseTree getParseTree() {
        return null;
    }
}