package com.dc.parser.model.statement.tcl;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Lock statement.
 */
@Getter
@Setter
public abstract class LockStatement extends AbstractSQLStatement implements TCLStatement {

    private final Collection<SimpleTableSegment> tables = new LinkedList<>();
}
