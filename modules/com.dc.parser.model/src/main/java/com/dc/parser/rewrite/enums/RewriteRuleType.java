package com.dc.parser.rewrite.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * SQL重写规则类型枚举
 * <p>
 * 定义了所有支持的SQL重写规则的名称和描述，
 * 避免在代码中使用硬编码字符串，提高可维护性和类型安全性。
 */
@RequiredArgsConstructor
@Getter
public enum RewriteRuleType {

    /**
     * Oracle SELECT * 重写规则
     * 将 SELECT * 查询重写为具体列名加 ROWID
     */
    ORACLE_SELECT_STAR_REWRITE(
            "ORACLE_SELECT_STAR_REWRITE",
            "Applied Oracle SELECT * rewrite"
    ),

    /**
     * DML预计算规则
     * 为DML语句生成对应的COUNT查询以预估影响行数
     */
    DML_PRECOMPUTATION(
            "DML_PRECOMPUTATION",
            "Generated DML precomputation SQL"
    ),

    /**
     * 组合规则：Oracle SELECT * 重写 + DML预计算
     * 同时应用两种重写规则
     */
    ORACLE_SELECT_STAR_AND_DML_PRECOMPUTATION(
            "ORACLE_SELECT_STAR_AND_DML_PRECOMPUTATION",
            "Applied Oracle SELECT * rewrite and generated DML precomputation SQL"
    ),

    /**
     * 无重写规则
     * 表示没有找到适用的重写规则
     */
    NONE(
            "NONE",
            "No applicable rewrite rule found"
    );

    /**
     * 规则名称
     */
    private final String ruleName;

    /**
     * 规则描述
     */
    private final String description;

    /**
     * 根据规则名称查找对应的枚举值
     *
     * @param ruleName 规则名称
     * @return 对应的枚举值，如果未找到则返回 NONE
     */
    public static RewriteRuleType fromRuleName(String ruleName) {
        if (ruleName == null) {
            return NONE;
        }

        for (RewriteRuleType type : values()) {
            if (type.getRuleName().equals(ruleName)) {
                return type;
            }
        }

        return NONE;
    }

    /**
     * 检查是否为有效的重写规则（非NONE）
     *
     * @return 如果是有效的重写规则返回true，否则返回false
     */
    public boolean isValidRewrite() {
        return this != NONE;
    }
}
