package com.dc.parser.rewrite;

import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.model.metadata.ShardingSphereMetaData;
import com.dc.parser.rewrite.config.RuleConfiguration;
import com.dc.parser.rewrite.result.SQLRewriteResult;

/**
 * CustomSQLRewriteEngine使用示例
 * <p>
 * 展示如何使用SQL重写引擎
 */
public class SQLRewriteEngineExample {

    /**
     * 示例1：Oracle ROWID注入
     */
    public void demonstrateOracleRowIdInjection() {
        // 创建仅启用Oracle ROWID注入的配置
        RuleConfiguration config = RuleConfiguration.oracleRowIdInjectionOnly();

        // 创建重写引擎
        SQLRewriteEngine engine = new SQLRewriteEngine(config);

        // 模拟SQL语句上下文和元数据（实际使用中需要真实的对象）
        SQLStatementContext sqlStatementContext = null; // 从解析器获取
        String originalSql = "SELECT * FROM employees WHERE department_id = 10";
        ShardingSphereMetaData metaData = null; // 从元数据服务获取

        // 执行重写
        SQLRewriteResult result = engine.rewrite(sqlStatementContext, originalSql, metaData);

        // 处理结果
        if (result.isMainSqlRewritten()) {
            System.out.println("原始SQL: " + originalSql);
            System.out.println("重写后SQL: " + result.getRewrittenSql());
            System.out.println("应用规则: " + result.getAppliedRuleName());
            System.out.println("描述: " + result.getDescription());

            // 预期输出类似：
            // SELECT employee_id, first_name, last_name, email, phone_number, hire_date, 
            // job_id, salary, commission_pct, manager_id, department_id, ROWID 
            // FROM employees WHERE department_id = 10
        }
    }

    /**
     * 示例2：Oracle 简单列投影 ROWID 注入
     */
    public void demonstrateOracleSimpleColumnRowIdInjection() {
        // 创建仅启用Oracle ROWID注入的配置
        RuleConfiguration config = RuleConfiguration.oracleRowIdInjectionOnly();

        // 创建重写引擎
        SQLRewriteEngine engine = new SQLRewriteEngine(config);

        // 模拟SQL语句上下文和元数据（实际使用中需要真实的对象）
        SQLStatementContext sqlStatementContext = null; // 从解析器获取
        String originalSql = "SELECT emp_id, emp_name, department FROM employees WHERE department_id = 10";
        ShardingSphereMetaData metaData = null; // 从元数据服务获取

        // 执行重写
        SQLRewriteResult result = engine.rewrite(sqlStatementContext, originalSql, metaData);

        // 处理结果
        if (result.isMainSqlRewritten()) {
            System.out.println("原始SQL: " + originalSql);
            System.out.println("重写后SQL: " + result.getRewrittenSql());
            System.out.println("应用规则: " + result.getAppliedRuleName());
            System.out.println("描述: " + result.getDescription());

            // 预期输出类似：
            // SELECT emp_id, emp_name, department, ROWID AS __WHD_DC_JDBC_interal_ROWID__
            // FROM employees WHERE department_id = 10
            System.out.println("说明: 为简单列投影查询自动添加了 ROWID 列");
        }
    }

    /**
     * 示例3：DML影响行数预估
     */
    public void demonstrateDMLPrecomputation() {
        // 创建仅启用DML预估的配置
        RuleConfiguration config = RuleConfiguration.dmlPrecomputationOnly();

        // 创建重写引擎
        SQLRewriteEngine engine = new SQLRewriteEngine(config);

        // 模拟UPDATE语句
        SQLStatementContext sqlStatementContext = null; // UpdateStatementContext
        String originalSql = "UPDATE employees SET salary = salary * 1.1 WHERE department_id = 10";
        ShardingSphereMetaData metaData = null;

        // 执行重写
        SQLRewriteResult result = engine.rewrite(sqlStatementContext, originalSql, metaData);

        // 处理结果
        if (result.isCompanionSqlGenerated()) {
            System.out.println("原始DML: " + originalSql);
            System.out.println("伴生COUNT查询: " + result.getCompanionSql().orElse(""));
            System.out.println("应用规则: " + result.getAppliedRuleName());

            // 预期输出：
            // SELECT COUNT(*) as "COUNT" FROM employees WHERE department_id = 10
        }
    }

    /**
     * 示例4：同时应用两种重写规则
     */
    public void demonstrateBothRules() {
        // 使用默认配置（启用所有规则）
        SQLRewriteEngine engine = new SQLRewriteEngine();

        // 如果SQL同时满足两种重写条件，将同时应用
        // 例如：一个复杂的业务场景，需要同时重写SELECT *并生成影响行数预估
    }

    /**
     * 示例5：集成到现有系统
     */
    public SQLRewriteResult integrateWithExistingSystem(
            SQLStatementContext sqlStatementContext,
            String sql,
            ShardingSphereMetaData metaData) {

        // 创建自定义配置
        RuleConfiguration config = new RuleConfiguration(
                true,  // 启用Oracle ROWID注入
                true,  // 启用DML预估
                100    // Oracle ROWID注入优先级
        );

        // 创建引擎并执行重写
        SQLRewriteEngine engine = new SQLRewriteEngine(config);
        return engine.rewrite(sqlStatementContext, sql, metaData);
    }

    /**
     * 示例6：错误处理
     */
    public void demonstrateErrorHandling() {
        SQLRewriteEngine engine = new SQLRewriteEngine();

        try {
            SQLRewriteResult result = engine.rewrite(null, "INVALID SQL", null);

            // 引擎会优雅地处理错误，返回原始SQL
            if (!result.hasAnyRewrite()) {
                System.out.println("重写失败，返回原始SQL: " + result.getRewrittenSql());
            }
        } catch (Exception e) {
            // 引擎内部已处理异常，正常情况下不会抛出
            System.err.println("意外错误: " + e.getMessage());
        }
    }
}
