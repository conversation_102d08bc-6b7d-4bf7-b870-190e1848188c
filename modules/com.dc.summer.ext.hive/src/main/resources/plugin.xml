<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="com.dc.summer.generic.meta">
        <meta id="hive" class="com.dc.summer.ext.hive.model.HiveMetaModel" driverClass="org.apache.hive.jdbc.HiveDriver"
              dialect="hive"/>
        <meta id="impala" class="com.dc.summer.ext.hive.model.ImpalaMetaModel"
              driverClass="com.cloudera.impala.jdbc.Driver"
              dialect="hive"/>
    </extension>

    <extension point="com.dc.summer.dataSourceProvider">
        <datasource
                class="com.dc.summer.ext.hive.HiveDataSourceProvider"
                description="Hive datasource"
                id="hive"
                parent="generic"
                label="Hive"
                icon="platform:/plugin/com.dc.summer.ext.generic/icons/hive_icon.png"
                dialect="hive">
            <drivers managable="true">

                <driver
                        id="apache_hive2"
                        label="Apache Hive"
                        icon="platform:/plugin/com.dc.summer.ext.generic/icons/hive_icon.png"
                        iconBig="platform:/plugin/com.dc.summer.ext.generic/icons/hive_icon_big.png"
                        class="org.apache.hive.jdbc.HiveDriver"
                        sampleURL="jdbc:hive2://{host}[:{port}][/{database}]"
                        defaultPort="10000"
                        description="Apache Hive JDBC"
                        webURL="https://cwiki.apache.org/confluence/display/Hive/Home"
                        category="Hadoop"
                        categories="hadoop">
                    <replace provider="generic" driver="apache_hive2"/>

                    <file type="jar" path="maven:/net.minidev:json-smart:1.3.1" bundle="!drivers.hive"/>
                    <file type="jar" path="https://github.com/timveil/hive-jdbc-uber-jar/releases/download/v1.9-2.6.5/hive-jdbc-uber-*******-292.jar" bundle="!drivers.hive"/>
                    <file type="jar" path="maven:/org.apache.hadoop:hadoop-common:2.10.2" bundle="!drivers.hive"/>
                    <file type="jar" path="drivers/hive" bundle="drivers.hive"/>
                    <parameter name="supports-delimiter-in-views" value="false"/>
                    <parameter name="supports-limits" value="false"/>
                    <parameter name="supports-references" value="false"/>
                    <parameter name="query-get-active-db" value="SELECT CURRENT_DATABASE()"/>
                    <parameter name="query-set-active-db" value="USE ?"/>
                    <parameter name="meta-model" value="hive"/>
                </driver>

                <driver
                        id="hw_hive"
                        label="HuaWei Hive"
                        icon="platform:/plugin/com.dc.summer.ext.generic/icons/hive_icon.png"
                        iconBig="platform:/plugin/com.dc.summer.ext.generic/icons/hive_icon_big.png"
                        class="org.apache.hive.jdbc.HiveDriver"
                        sampleURL="jdbc:hive2://{host}[:{port}][/{database}]"
                        defaultPort="10000"
                        description="HuaWei Hive JDBC"
                        webURL="https://cwiki.apache.org/confluence/display/Hive/Home"
                        category="Hadoop"
                        categories="hadoop">

                    <file type="jar" path="maven:/net.minidev:json-smart:1.3.1" bundle="!drivers.hive"/>
                    <file type="jar" path="maven:/com.huaweicloud:hive-jdbc:RELEASE[3.1.0]" bundle="!drivers.hive"/>
                    <file type="jar" path="maven:/org.apache.hadoop:hadoop-common:2.10.2" bundle="!drivers.hive"/>
                    <file type="jar" path="drivers/hive" bundle="drivers.hive"/>

                    <parameter name="supports-delimiter-in-views" value="false"/>
                    <parameter name="supports-limits" value="false"/>
                    <parameter name="supports-references" value="false"/>
                    <parameter name="query-get-active-db" value="SELECT CURRENT_DATABASE()"/>
                    <parameter name="query-set-active-db" value="USE ?"/>
                    <parameter name="meta-model" value="hive"/>
                </driver>

                <driver
                        id="cdh42"
                        label="CDH42 Hive"
                        class="com.cloudera.hive.jdbc.HS2Driver"
                        sampleURL="jdbc:hive2://{host}[:{port}][/{database}]"
                        defaultPort="10000"
                        description="CDH42 Hive JDBC"
                        webURL="https://cwiki.apache.org/confluence/display/Hive/Home"
                        category="Hadoop"
                        categories="hadoop">

                    <file type="jar" path="maven:/net.minidev:json-smart:1.3.1" bundle="!drivers.hive"/>
                    <file type="jar" path="maven:/com.cloudera:HiveJDBC42:RELEASE[2.6.21.1025]" bundle="!drivers.hive"/>
                    <file type="jar" path="maven:/org.apache.hadoop:hadoop-common:2.10.2" bundle="!drivers.hive"/>

                    <file type="jar" path="drivers/hive" bundle="drivers.hive"/>
                    <parameter name="supports-delimiter-in-views" value="false"/>
                    <parameter name="supports-limits" value="false"/>
                    <parameter name="supports-references" value="false"/>
                    <parameter name="query-get-active-db" value="SELECT CURRENT_DATABASE()"/>
                    <parameter name="query-set-active-db" value="USE ?"/>
                    <parameter name="omit-catalog" value="true"/>
                    <parameter name="meta-model" value="hive"/>
                </driver>

                <driver
                        id="cdh41"
                        label="CDH41 Hive"
                        class="com.cloudera.hive.jdbc41.HS2Driver"
                        sampleURL="jdbc:hive2://{host}[:{port}][/{database}]"
                        defaultPort="10000"
                        description="CDH41 Hive JDBC"
                        webURL="https://cwiki.apache.org/confluence/display/Hive/Home"
                        category="Hadoop"
                        categories="hadoop">

                    <file type="jar" path="maven:/net.minidev:json-smart:1.3.1" bundle="!drivers.hive"/>
                    <file type="jar" path="maven:/com.cloudera:HiveJDBC41:RELEASE[2.6.21.1025]" bundle="!drivers.hive"/>
                    <file type="jar" path="maven:/org.apache.hadoop:hadoop-common:2.10.2" bundle="!drivers.hive"/>

                    <file type="jar" path="drivers/hive" bundle="drivers.hive"/>
                    <parameter name="supports-delimiter-in-views" value="false"/>
                    <parameter name="supports-limits" value="false"/>
                    <parameter name="supports-references" value="false"/>
                    <parameter name="query-get-active-db" value="SELECT CURRENT_DATABASE()"/>
                    <parameter name="query-set-active-db" value="USE ?"/>
                    <parameter name="omit-catalog" value="true"/>
                    <parameter name="meta-model" value="hive"/>
                </driver>

                <!-- Cloudera -->
                <driver
                        id="impala42"
                        label="Impala42 Hive"
                        icon="icons/impala_icon.png"
                        iconBig="icons/impala_icon_big.png"
                        class="com.cloudera.impala.jdbc.Driver"
                        sampleURL="jdbc:impala://{host}:{port}/{database}"
                        defaultPort="21050"
                        description="Cloudera Impala. You can download JDBC driver files from https://www.cloudera.com/"
                        webURL="https://www.cloudera.com/downloads/connectors/impala/jdbc/2-5-41.html"
                        category="Hadoop"
                        categories="hadoop">
                    <file type="jar" path="maven:/net.minidev:json-smart:1.3.1" bundle="!drivers.hive"/>
                    <file type="jar" path="maven:/com.cloudera:ImpalaJDBC42:RELEASE[2.6.30]" bundle="!drivers.hive"/>
                    <file type="jar" path="maven:/org.apache.hadoop:hadoop-common:2.10.2" bundle="!drivers.hive"/>

                    <parameter name="query-get-active-db" value="select current_database()"/>
                    <parameter name="query-set-active-db" value="use ?"/>
                    <parameter name="string-escape-char" value="\"/>
                    <parameter name="omit-catalog" value="true"/>
                    <parameter name="supports-references" value="false"/>
                    <parameter name="supports-transaction-isolation" value="false"/>
                    <parameter name="meta-model" value="impala"/>
                </driver>

                <driver
                        id="impala41"
                        label="Impala41 Hive"
                        icon="icons/impala_icon.png"
                        iconBig="icons/impala_icon_big.png"
                        class="com.cloudera.impala.jdbc1.Driver"
                        sampleURL="jdbc:impala://{host}:{port}/{database}"
                        defaultPort="21050"
                        description="Cloudera Impala. You can download JDBC driver files from https://www.cloudera.com/"
                        webURL="https://www.cloudera.com/downloads/connectors/impala/jdbc/2-5-41.html"
                        category="Hadoop"
                        categories="hadoop">
                    <file type="jar" path="maven:/net.minidev:json-smart:1.3.1" bundle="!drivers.hive"/>
                    <file type="jar" path="maven:/com.cloudera:ImpalaJDBC41:RELEASE[2.6.30]" bundle="!drivers.hive"/>
                    <file type="jar" path="maven:/org.apache.hadoop:hadoop-common:2.10.2" bundle="!drivers.hive"/>

                    <parameter name="query-get-active-db" value="select current_database()"/>
                    <parameter name="query-set-active-db" value="use ?"/>
                    <parameter name="string-escape-char" value="\"/>
                    <parameter name="omit-catalog" value="true"/>
                    <parameter name="supports-references" value="false"/>
                    <parameter name="supports-transaction-isolation" value="false"/>
                    <parameter name="meta-model" value="impala"/>
                </driver>

                <treeInjection path="generic/catalog/schema/table"
                               changeFolderType="com.dc.summer.ext.hive.model.HiveTable"/>
                <treeInjection path="generic/catalog/schema/table/attribute"
                               changeFolderType="com.dc.summer.ext.hive.model.HiveTableColumn"/>
                <treeInjection path="generic/catalog/schema/table/index"
                               changeFolderType="com.dc.summer.ext.hive.model.HiveIndex"/>
            </drivers>
        </datasource>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="hive" parent="generic" class="com.dc.summer.ext.hive.model.HiveSQLDialect" label="Hive"
                 description="Apache Hive SQL dialect."
                 icon="platform:/plugin/com.dc.summer.ext.generic/icons/hive_icon.png">
        </dialect>
    </extension>

    <extension point="com.dc.summer.objectManager">
        <manager class="com.dc.summer.ext.hive.model.edit.HiveTableManager"
                 objectType="com.dc.summer.ext.hive.model.HiveTable"/>
        <manager class="com.dc.summer.ext.hive.model.edit.HiveTableColumnManager"
                 objectType="com.dc.summer.ext.hive.model.HiveTableColumn"/>
        <manager class="com.dc.summer.ext.hive.model.edit.HiveIndexManager"
                 objectType="com.dc.summer.ext.hive.model.HiveIndex"/>
    </extension>

    <extension point="com.dc.summer.dataSourceAuth">
        <authModel
                id="hive_kerberos"
                desktop="true"
                label="Kerberos"
                description="Kerberos authentication"
                class="com.dc.summer.ext.hive.model.auth.HiveAuthModelKerberos">
            <datasource id="hive"/>
        </authModel>
    </extension>

    <extension point="com.dc.summer.dataTypeProvider">
        <provider
                class="com.dc.summer.ext.hive.model.data.HiveValueHandlerProvider"
                description="hive data types provider"
                id="com.dc.summer.ext.hive.model.data.HiveValueHandlerProvider"
                label="hive data types provider">

            <datasource id="hive"/>
            <type kind="STRUCT"/>
        </provider>
    </extension>

    <extension point="com.dc.summer.bindingStatement">
        <binding id="hive" class="com.dc.summer.ext.hive.model.statement.HiveBindStatementProvider" description="hive bindparameter"/>
    </extension>

</plugin>
