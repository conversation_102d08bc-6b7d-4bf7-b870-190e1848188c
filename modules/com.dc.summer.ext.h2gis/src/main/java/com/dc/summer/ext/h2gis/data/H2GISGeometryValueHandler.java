
package com.dc.summer.ext.h2gis.data;

import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.summer.data.gis.handlers.GISGeometryValueHandler;
import com.dc.summer.model.exec.DBCSession;

import java.sql.SQLException;

/**
 * H2GIS Server geometry handler
 */
public class H2GISGeometryValueHandler extends GISGeometryValueHandler {

    public static final H2GISGeometryValueHandler INSTANCE = new H2GISGeometryValueHandler();

    @Override
    protected Object fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws DBCException, SQLException {
        return getValueFromObject(session, type,
            resultSet.getObject(index),
            false, false);
    }

    /*
    protected void bindGeometryParameter(@NotNull JDBCSession session, @NotNull JDBCPreparedStatement statement, int paramIndex, @NotNull Geometry value) throws SQLException, DBCException {
        statement.setString(paramIndex, value.toString());
    }

    @Override
    protected byte[] convertGeometryToBinaryFormat(DBCSession session, Geometry geometry) throws DBCException {
        throw new DBCException("Saving in SQL Server binary format not supported yet");
    }

    @Override
    public String makeQueryBind(DBSAttributeBase attribute, Object value) throws DBCException {
        int srid = 0;
        if (value instanceof DBGeometry) {
            srid = ((DBGeometry) value).getSRID();
        } else if (value instanceof Geometry) {
            srid = ((Geometry) value).getSRID();
        }
        return "geometry::STGeomFromText(?," + srid + ")";
    }
*/
}
