/*
 * H2GIS ecplise plugin to register a H2GIS spatial database to 
 * <PERSON><PERSON><PERSON>, the  Universal Database Manager
 *
 * For more information, please consult: <http://www.h2gis.org/>
 * or contact directly: info_at_h2gis.org
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.dc.summer.ext.h2gis;

import com.dc.summer.DBException;
import com.dc.summer.ext.generic.GenericDataSourceProvider;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.ext.h2.model.H2MetaModel;
import com.dc.summer.ext.h2gis.model.H2GISDataSource;

/**
 * Used to create an H2GIS datasource that loads the H2GIS driver from an
 * eclipse extension point, see plugin.xml
 *
 * <AUTHOR> Bocher, CNRS
 * <AUTHOR> Rider (<EMAIL>)
 */
public class H2GISDataSourceProvider extends GenericDataSourceProvider {

    public H2GISDataSourceProvider() {
    }

    @NotNull
    @Override
    public DBPDataSource openDataSource(@NotNull DBRProgressMonitor monitor, @NotNull DBPDataSourceContainer container) throws DBException {
        return new H2GISDataSource(monitor, container, new H2MetaModel());
    }
}
