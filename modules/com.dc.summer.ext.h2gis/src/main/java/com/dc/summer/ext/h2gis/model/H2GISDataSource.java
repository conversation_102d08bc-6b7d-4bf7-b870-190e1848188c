/*
 * H2GIS ecplise plugin to register a H2GIS spatial database to 
 * <PERSON><PERSON><PERSON>, the  Universal Database Manager
 *
 * For more information, please consult: <http://www.h2gis.org/>
 * or contact directly: info_at_h2gis.org
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.dc.summer.ext.h2gis.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.ext.h2.model.H2DataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.exec.jdbc.JDBCStatement;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;

import java.sql.SQLException;

/**
 * Used to create an H2GIS datasource that initializes the H2GIS spatial
 * functions
 *
 * <AUTHOR> Bocher, CNRS
 * <AUTHOR> Rider (<EMAIL>)
 */
public class H2GISDataSource extends H2DataSource {

    public H2GISDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, GenericMetaModel metaModel)
            throws DBException {
        super(monitor, container, metaModel);
    }

    @Override
    public void initialize(@NotNull DBRProgressMonitor monitor) throws DBException {
        super.initialize(monitor);
        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Load H2GIS function")) {
            try (JDBCStatement dbStat = session.createStatement()) {
                dbStat.execute("CREATE ALIAS IF NOT EXISTS H2GIS_SPATIAL FOR \"org.h2gis.functions.factory.H2GISFunctions.load\";CALL H2GIS_SPATIAL();");

            } catch (SQLException e) {
                throw new DBException("Cannot load H2GIS functions", e);
            }

        }
    }

}
