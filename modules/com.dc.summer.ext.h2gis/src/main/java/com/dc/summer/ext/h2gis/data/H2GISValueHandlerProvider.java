
package com.dc.summer.ext.h2gis.data;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.data.DBDValueHandlerProvider;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.code.Nullable;
import com.dc.summer.model.data.DBDFormatSettings;

/**
 * H2GIS data types provider
 */
public class H2GISV<PERSON>ueHandlerProvider implements DBDValueHandlerProvider {

    @Nullable
    @Override
    public DBDValueHandler getValueHandler(DBPDataSource dataSource, DBDFormatSettings preferences, DBSTypedObject typedObject)
    {
        if (typedObject.getTypeName().toUpperCase().startsWith("GEOMETRY")) {
            return H2GISGeometryValueHandler.INSTANCE;
        } else {
            return null;
        }
    }

}