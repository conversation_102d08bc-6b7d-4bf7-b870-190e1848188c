<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dc</groupId>
        <artifactId>summer.modules</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>com.dc.summer.parser.sql.gsp</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.summer.parser.sql</artifactId>
            <version>${version}</version>
        </dependency>

        <dependency>
            <groupId>com.parser.dc</groupId>
            <artifactId>sqlparser</artifactId>
            <version>enterprise6.1.0.0</version>
        </dependency>

        <!--<dependency>
            <groupId>com.dc</groupId>  &lt;!&ndash;自定义&ndash;&gt;
            <artifactId>sqlparser</artifactId>    &lt;!&ndash;自定义&ndash;&gt;
            <version>3.0</version> &lt;!&ndash;自定义&ndash;&gt;
            <scope>system</scope> &lt;!&ndash;system，类似provided，需要显式提供依赖的jar以后，Maven就不会在Repository中查找它&ndash;&gt;
            <systemPath>${basedir}/src/main/resources/lib/sqlparser.jar</systemPath>
        </dependency>-->

    </dependencies>

</project>