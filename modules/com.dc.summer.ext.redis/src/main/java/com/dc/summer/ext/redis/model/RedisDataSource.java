package com.dc.summer.ext.redis.model;

import com.dc.summer.ext.redis.RedisUtils;
import com.dc.summer.ext.redis.data.RedisDataWrapper;
import com.dc.summer.ext.redis.exec.RedisClientPool;
import com.dc.summer.ext.redis.exec.RedisExecutionContext;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import com.dc.summer.model.*;
import com.dc.summer.model.document.data.DBDataWrapper;
import org.eclipse.jface.text.BadLocationException;
import org.eclipse.jface.text.Document;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.meta.Association;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLSyntaxManager;
import com.dc.summer.model.sql.parser.SQLRuleManager;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.text.parser.TPRuleBasedScanner;
import com.dc.summer.model.text.parser.TPToken;
import com.dc.utils.ArrayUtils;
import com.dc.utils.CommonUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.Protocol;
import redis.clients.jedis.Protocol.Command;
import redis.clients.jedis.UnifiedJedis;

public class RedisDataSource extends NoSQLDataSource<RedisExecutionContext> implements DBPRefreshableObject, DBPObjectStatisticsCollector, DBPErrorAssistant{
   private static final Log log = Log.getLog(RedisDataSource.class);
   private static final int CLUSTER_MAX_ATTEMPTS = 5;
   private List<RedisDatabase> databases;
   private RedisDataSourceInfo info;
   private TPRuleBasedScanner commandRuleManager;
   private Map<String, Method[]> commandMethods = new HashMap();
   private final Set<String> supportedCommands = new HashSet();
   private RedisSQLDialect dialect;
   private DBPConnectionConfiguration configuration;
   private final RedisDataWrapper redisDataWrapper = new RedisDataWrapper();

   public RedisDataSource(DBRProgressMonitor monitor, @NotNull DBPDataSourceContainer container) throws DBCException {
      super(container);
      this.configuration = container.getActualConnectionConfiguration();
      this.initSQLDialect();
      if (container.isTemporary()) {
         return;
      }
      this.executionContext = new RedisExecutionContext(this, "Main Redis Connection", 0);
      this.executionContext.connect(monitor, getContainer().getActualConnectionConfiguration());
   }

   public boolean isUseCluster() {
      return CommonUtils.toBoolean(configuration.getProviderProperty("redis.use.cluster"));
   }

   private void initSQLDialect() {
      this.initClientMethods();
      this.dialect = new RedisSQLDialect(this);
      SQLSyntaxManager commandSyntaxManager = new SQLSyntaxManager();
      commandSyntaxManager.init(this.getSQLDialect(), this.getContainer().getPreferenceStore());
      SQLRuleManager ruleManager = new SQLRuleManager(commandSyntaxManager);
      ruleManager.loadRules(this, false);
      this.commandRuleManager = new TPRuleBasedScanner();
      this.commandRuleManager.setRules(ruleManager.getAllRules());
   }

   public Set<String> getSupportedCommands() {
      return this.supportedCommands;
   }

   /** @deprecated */
   @Deprecated
   public Map<String, Method[]> getCommandMethods() {
      return this.commandMethods;
   }

   /** @deprecated */
   @Deprecated
   public Method[] getCommandMethods(String command) {
      return (Method[])this.commandMethods.get(command);
   }

   private void initClientMethods() {
      Class<?> clientClass = CommonUtils.toBoolean(configuration.getProviderProperty("redis.use.cluster")) ? JedisCluster.class : Jedis.class;
      Protocol.Command[] var5;
      int var4 = (var5 = Command.values()).length;

      for(int var3 = 0; var3 < var4; ++var3) {
         Protocol.Command pc = var5[var3];
         this.supportedCommands.add(pc.name());
      }

      this.supportedCommands.addAll(Arrays.asList("JSON", "MGET", "TYPE", "STRAPPEND", "STRLEN", "ARRAPPEND", "ARRINDEX", "ARRINSERT", "ARRLEN", "ARRPOP", "ARRTRIM", "NUMINCRBY", "NUMMULTBY", "OBJKEYS", "OBJLEN"));
      Method[] jedisMethods = clientClass.getMethods();
      Method[] var6 = jedisMethods;
      int var14 = jedisMethods.length;

      for(var4 = 0; var4 < var14; ++var4) {
         Method method = var6[var4];
         if (Modifier.isPublic(method.getModifiers()) && !Modifier.isStatic(method.getModifiers()) && !method.isAnnotationPresent(Deprecated.class) && method.getReturnType() != byte[].class) {
            boolean hasBadParams = false;
            Class[] var11;
            int var10 = (var11 = method.getParameterTypes()).length;

            for(int var9 = 0; var9 < var10; ++var9) {
               Class<?> paramType = var11[var9];
               if (paramType.isArray() && paramType.getComponentType() != String.class) {
                  hasBadParams = true;
                  break;
               }
            }

            if (!hasBadParams) {
               String commandName = method.getName().toLowerCase(Locale.ENGLISH);
               Method[] methods = (Method[])this.commandMethods.get(commandName);
               if (methods == null) {
                  methods = new Method[]{method};
               } else {
                  methods = (Method[])ArrayUtils.add(Method.class, methods, method);
               }

               this.commandMethods.put(commandName, methods);
            }
         }
      }

   }

   public boolean isCommand(String command) {
      return this.supportedCommands.contains(command.toUpperCase(Locale.ENGLISH));
   }

   public RedisSQLDialect getSQLDialect() {
      return this.dialect;
   }

   @Override
   public DBDataWrapper getDataWrapper() {
      return redisDataWrapper;
   }

   public @NotNull DBPDataSourceInfo getInfo() {
      return this.info;
   }

   public Object getDataSourceFeature(String featureId) {
      return null;
   }

   public void initialize(@NotNull DBRProgressMonitor monitor) throws DBException {
      this.info = new RedisDataSourceInfo(this);

      try {
         this.readDatabases();
         String databaseName = this.getContainer().getConnectionConfiguration().getDatabaseName();
         if (databaseName != null) {
            int defDatabase = CommonUtils.toInt(databaseName.replaceFirst("db|DB", ""));
            if (defDatabase > 0 && defDatabase < 16) {
               RedisDatabase database = this.getDatabase(defDatabase);
               if (database == null) {
                  database = this.addDatabase(defDatabase);
               }

               if (database != null) {
                  this.executionContext.setDefaultCatalog(monitor, database, null, false);
               }
            }
         }
      } catch (Exception var4) {
         throw new DBException("Error initializing Redis connection", var4);
      }
   }

   private void readDatabases() {
      this.databases = new ArrayList<>();
      if (this.isUseCluster()) {
         RedisDatabase clusterDatabase = new RedisDatabase(this, "cluster", -1L);
         this.databases.add(clusterDatabase);
         this.executionContext.setDefaultDatabase(0);
      } else {
         Object keySpaceInfo = this.getDefaultContext().getClient().sendCommand(Command.INFO, "keyspace");
         if (keySpaceInfo instanceof byte[]) {
            keySpaceInfo = new String((byte[])keySpaceInfo);
         }

         if (this.databases.isEmpty()) {
            Map<String, Object> dbMap = RedisUtils.parseInfo((String)keySpaceInfo);
            for (Map.Entry<String, Object> stringObjectEntry : dbMap.entrySet()) {
               long keyNumber = -1L;
               Optional<String> keysParam = Arrays.stream(CommonUtils.toString(stringObjectEntry.getValue()).split(","))
                       .filter((s) -> s.startsWith("keys=")).findFirst();
               if (keysParam.isPresent()) {
                  String keys = keysParam.get();
                  int divPos = keys.indexOf("=");
                  if (divPos != -1) {
                     keyNumber = CommonUtils.toLong(keys.substring(divPos + 1));
                  }
               }

               RedisDatabase database = new RedisDatabase(this, stringObjectEntry.getKey(), keyNumber);
               this.databases.add(database);
            }
         }
      }

   }

   @Override
   public @NotNull RedisExecutionContext openIsolatedContext(@NotNull DBRProgressMonitor monitor, @NotNull String purpose, @Nullable DBCExecutionContext initFrom, Boolean autoCommit, DBPConnectionConfiguration configuration) throws DBException {
      RedisExecutionContext context = new RedisExecutionContext(this, purpose, initFrom instanceof RedisExecutionContext ? ((RedisExecutionContext)initFrom).getDefaultDatabase() : this.executionContext.getDefaultDatabase());
      context.connect(monitor, configuration);
      this.configuration = configuration;
      return context;
   }

   public @Association List<RedisDatabase> getDatabases() {
      return this.databases;
   }

   public RedisDatabase getDatabase(int dbNum) {
      for (RedisDatabase db : this.databases) {
         if (db.getId() == dbNum) {
            return db;
         }
      }
      return null;
   }

   public RedisDatabase addDatabase(int dbNum) {
      RedisDatabase newDatabase = new RedisDatabase(this, "db" + dbNum, dbNum);
      this.databases.add(newDatabase);
      DBUtils.fireObjectAdd(newDatabase, Collections.emptyMap());
      return newDatabase;
   }

   public Collection<? extends DBSObject> getChildren(@NotNull DBRProgressMonitor monitor) {
      return this.databases;
   }

   public RedisDatabase getChild(@NotNull DBRProgressMonitor monitor, @NotNull String childName) {
      RedisDatabase redisDatabase = DBUtils.findObject(this.databases, childName.toLowerCase());
      return redisDatabase == null ? new RedisDatabase(this, childName.toLowerCase(), -1) : redisDatabase;
   }

   public @NotNull Class<? extends DBSObject> getPrimaryChildType(@Nullable DBRProgressMonitor monitor) {
      return RedisDatabase.class;
   }

   public void cacheStructure(@NotNull DBRProgressMonitor monitor, int scope) {
   }

   public String getKeyDivider() {
      String delimiter = this.getContainer().getConnectionConfiguration().getProviderProperty("@dbeaver-redis.key.divider");
      return !CommonUtils.isEmpty(delimiter) ? delimiter : ":";
   }

   public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) throws DBException {
      this.readDatabases();
      return this;
   }

   public synchronized String[] parseCommand(String query) {
      Document document = new Document(query);
      this.commandRuleManager.setRange(document, 0, query.length());
      List<String> parts = new ArrayList();
      StringBuilder part = new StringBuilder();

      while(true) {
         while(true) {
            TPToken token = this.commandRuleManager.nextToken();
            if (!token.isEOF() && !token.isWhitespace()) {
               try {
                  String tokenText = document.get(this.commandRuleManager.getTokenOffset(), this.commandRuleManager.getTokenLength());
                  part.append(tokenText);
               } catch (BadLocationException var7) {
                  log.debug(var7);
               }
            } else {
               if (part.length() > 0) {
                  parts.add(part.toString());
                  part.setLength(0);
               }

               if (token.isEOF()) {
                  return parts.toArray(new String[0]);
               }
            }
         }
      }
   }

   public boolean isStatisticsCollected() {
      return true;
   }

   public void collectObjectStatistics(DBRProgressMonitor monitor, boolean totalSizeOnly, boolean forceRefresh) throws DBException {
   }

   @Override
   public void closeClient() {
      DBPExclusiveResource exclusiveLock = getContainer().getExclusiveLock();
      RedisClientPool pool = RedisClientPool.getInstance();
      Object lock = exclusiveLock.acquireExclusiveLock();
      try (UnifiedJedis ignored = pool.removeClient(getContainer().getId())) {
         // nothing to do here
      } finally {
         exclusiveLock.releaseExclusiveLock(lock);
      }
   }

   @Override
   public ErrorType discoverErrorType(Throwable error) {
      if (error.getCause() != null && error.getCause().getMessage() != null){
         return discoverErrorType(error.getCause().getMessage());
      }
      return ErrorType.NORMAL;
   }

   @Override
   public ErrorType discoverErrorType(String message) {
      if (message.contains("Could not get a resource from the pool") || message.contains("Pool not open")){
         return ErrorType.CONNECTION_LOST;
      }
      return ErrorType.NORMAL;
   }

   @Override
   public ErrorPosition[] getErrorPosition(DBRProgressMonitor monitor, DBCExecutionContext context, String query, Throwable error) {
      return null;
   }
}
