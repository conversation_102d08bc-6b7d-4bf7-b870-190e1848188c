package com.dc.summer.ext.redis.exec;

import com.dc.config.ConfigInstance;
import com.dc.config.ConfigInstanceType;
import com.dc.summer.ext.redis.config.RedisConfig;
import com.dc.summer.ext.redis.model.RedisDataSource;
import com.dc.summer.ext.redis.model.RedisDatabase;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.Duration;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.DBPExclusiveResource;
import com.dc.summer.model.DBPTransactionIsolation;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.impl.AbstractExecutionContext;
import com.dc.summer.model.impl.net.SSLHandlerTrustStoreImpl;
import com.dc.summer.model.net.DBWHandlerConfiguration;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.struct.DBSInstance;
import com.dc.summer.model.struct.rdb.DBSSchema;
import com.dc.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.core.runtime.Platform;
import redis.clients.jedis.*;
import redis.clients.jedis.commands.DatabaseCommands;
import redis.clients.jedis.commands.ProtocolCommand;
import redis.clients.jedis.util.SafeEncoder;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLParameters;
import javax.net.ssl.SSLSocketFactory;

@Slf4j
public class RedisExecutionContext extends AbstractExecutionContext<RedisDataSource> implements DBCExecutionContextDefaults<RedisDatabase, DBSSchema>, DBCTransactionManager {
    private Integer defaultDatabase;

    private Jedis jedis;
    @NotNull
    private UnifiedJedis jedisClient;
    private boolean allowCloseClient;

    public RedisExecutionContext(RedisDataSource dataSource, String purpose, Integer defaultDatabase) {
        super(dataSource, purpose);
        this.defaultDatabase = defaultDatabase;
    }

    public void connect(DBRProgressMonitor monitor, DBPConnectionConfiguration configuration) throws DBCException {
        this.connect(monitor, null, null, configuration);
    }

    public void connect(DBRProgressMonitor monitor, Boolean autoCommit, @Nullable Integer txnLevel, DBPConnectionConfiguration configuration) throws DBCException {

        String hostName = configuration.getHostName();
        String hostPort = configuration.getHostPort();
        String url = configuration.getUrl();

        RedisConfig redisConfig = ConfigInstance.getInstance(RedisConfig.class);
        ConfigInstanceType redisConfigType = redisConfig.getType();
        this.allowCloseClient = redisConfigType.isAllowCloseClient();

        int connectTimeout = CommonUtils.toInt(configuration.getProviderProperty("@dbeaver-redis.timeout.connect"), redisConfig.getConnectTimeoutMS());
        int socketTimeout = CommonUtils.toInt(configuration.getProviderProperty("@dbeaver-redis.timeout.socket"), redisConfig.getSocketTimeoutMS());
        String userName = configuration.getUserName();
        if (CommonUtils.isEmpty(userName)) {
            userName = null;
        }

        String userPassword = configuration.getUserPassword();
        if (userName == null && CommonUtils.isEmpty(userPassword)) {
            userPassword = null;
        }

        boolean useSSL = false;
        SSLSocketFactory sslFactory = null;
        SSLParameters sslParameters = null;
        HostnameVerifier hostVerifier = null;
        DBWHandlerConfiguration sslConfig = configuration.getHandler("redis_ssl");
        boolean skipHostValidation;
        if (sslConfig != null && sslConfig.isEnabled()) {
            useSSL = true;
            skipHostValidation = sslConfig.getBooleanProperty("@dbeaver-redis.ssl.skipHostValidation");
            if (skipHostValidation) {
                hostVerifier = (s, sslSession) -> true;
            }

            try {
                SSLHandlerTrustStoreImpl.initializeTrustStore(monitor, getDataSource(), sslConfig);
                sslFactory = SSLHandlerTrustStoreImpl.createTrustStoreSslSocketFactory(getDataSource(), sslConfig);
            } catch (Exception var27) {
                throw new DBCException("Error initializing SSL trust store", var27);
            }

            sslParameters = new SSLParameters();
        }

        skipHostValidation = CommonUtils.toBoolean(configuration.getProviderProperty("redis.set.client"));
        String clientName = skipHostValidation ? Platform.getProduct().getId() : null;
        DefaultJedisClientConfig.Builder configBuilder = DefaultJedisClientConfig.builder()
                .socketTimeoutMillis(socketTimeout)
                .connectionTimeoutMillis(connectTimeout)
                .user(userName)
                .password(userPassword)
                .clientName(clientName)
                .ssl(useSSL);
        if (sslFactory != null) {
            configBuilder.sslSocketFactory(sslFactory);
        }

        if (sslParameters != null) {
            configBuilder.sslParameters(sslParameters);
        }

        if (hostVerifier != null) {
            configBuilder.hostnameVerifier(hostVerifier);
        }

        JedisClientConfig config = configBuilder.build();
        ConnectionPoolConfig poolConfig = new ConnectionPoolConfig();
        poolConfig.setMaxTotal(redisConfig.getMaxTotal());
        poolConfig.setMaxIdle(redisConfig.getMaxIdle());
        poolConfig.setMinIdle(redisConfig.getMinIdle());
        poolConfig.setMaxWait(Duration.ofMillis(redisConfig.getMaxWaitMS()));
        poolConfig.setTimeBetweenEvictionRuns(Duration.ofMillis(redisConfig.getTimeBetweenEvictionRunsMS()));
        poolConfig.setMinEvictableIdleTime(Duration.ofMillis(redisConfig.getMinEvictableIdleTimeMS()));
        poolConfig.setSoftMinEvictableIdleTime(Duration.ofMillis(redisConfig.getSoftMinEvictableIdleMS()));
        poolConfig.setTestOnCreate(redisConfig.getTestOnCreate());
        poolConfig.setTestOnBorrow(redisConfig.getTestOnBorrow());
        poolConfig.setTestOnReturn(redisConfig.getTestOnReturn());
        poolConfig.setTestWhileIdle(redisConfig.getTestWhileIdle());
        poolConfig.setNumTestsPerEvictionRun(redisConfig.getNumTestsPerEvictionRun());
        HostAndPort hostAndPort = null;
        URI serviceURI = null;
        if (CommonUtils.isEmpty(url)) {
            hostAndPort = new HostAndPort(hostName, CommonUtils.toInt(hostPort));
        } else {
            try {
                serviceURI = new URI(url);
                if (CommonUtils.isEmpty(serviceURI.getUserInfo()) && !CommonUtils.isEmpty(userName)) {
                    int divPos = url.indexOf("://");
                    if (divPos != -1) {
                        String userInfo = userName + ":" + CommonUtils.notEmpty(userPassword);
                        String urlWithAuth = url.substring(0, divPos + 3) + userInfo + "@" + url.substring(divPos + 3);
                        serviceURI = new URI(urlWithAuth);
                    }
                }
            } catch (URISyntaxException var26) {
                throw new DBCException("Invalid Redis URI: " + url);
            }
        }

        log.info("==> ConnectionURL: " + getDataSource().getConnectionURL(configuration));

        try {
            long ctm = System.currentTimeMillis();
            try {
                this.jedis = newJedis(url, config, hostAndPort, serviceURI);
                this.jedisClient = newUnifiedJedis(configuration, redisConfigType, getDataSource().getContainer().getId(), url, connectTimeout, socketTimeout, config, poolConfig, hostAndPort, serviceURI);

                checkContextAlive(new VoidProgressMonitor());

            } finally {
                log.info("<== ConnectionTime: " + (System.currentTimeMillis() - ctm) + " ms");
            }

            DBExecUtils.startContextInitiation((this.getDataSource()).getContainer());

            monitor.subTask("Open redis session");
            super.initContextBootstrap(monitor, true);
            this.getDataSource().addExecutionContext(this);
        } catch (Exception e) {
            this.closeClient();
            if (e instanceof DBCException) {
                throw e;
            }
            throw new DBCException(e, this);
        } finally {
            DBExecUtils.finishContextInitiation(this.getDataSource().getContainer());
        }

    }

    private Jedis newJedis(String url, JedisClientConfig config, HostAndPort hostAndPort, URI serviceURI) {
        if (CommonUtils.isEmpty(url)) {
            return new Jedis(hostAndPort, config);
        } else {
            return new Jedis(serviceURI, config);
        }
    }

    private UnifiedJedis create(String url, int connectTimeout, int socketTimeout, JedisClientConfig config, ConnectionPoolConfig poolConfig, HostAndPort hostAndPort, URI serviceURI) {
        UnifiedJedis unifiedJedis;
        if (getDataSource().isUseCluster()) {
            unifiedJedis = new JedisCluster(hostAndPort, config, 5, poolConfig);
        } else if (CommonUtils.isEmpty(url)) {
            unifiedJedis = new JedisPooled(poolConfig, hostAndPort, config);
        } else {
            unifiedJedis = new JedisPooled(poolConfig, serviceURI, connectTimeout, socketTimeout);
        }
        return unifiedJedis;
    }

    private UnifiedJedis newUnifiedJedis(DBPConnectionConfiguration configuration,
                                         ConfigInstanceType redisConfigType,
                                         String id,
                                         String url,
                                         int connectTimeout,
                                         int socketTimeout,
                                         JedisClientConfig config,
                                         ConnectionPoolConfig poolConfig,
                                         HostAndPort hostAndPort,
                                         URI serviceURI) throws DBCException {
        if (redisConfigType.equals(ConfigInstanceType.SINGLE)) {
            return create(url, connectTimeout, socketTimeout, config, poolConfig, hostAndPort, serviceURI);
        }

        RedisClientPool pool = RedisClientPool.getInstance();

        if (!pool.hasClient(id) || changeUnifiedJedisType(pool.getClient(id)) || configuration.isRefreshPool()) {
            UnifiedJedis unifiedJedis = null;
            DBPExclusiveResource exclusiveLock = getDataSource().getContainer().getExclusiveLock();
            Object lock = exclusiveLock.acquireExclusiveLock();
            try {
                if (!pool.hasClient(id) || changeUnifiedJedisType(pool.getClient(id)) || configuration.isRefreshPool()) {
                    unifiedJedis = create(url, connectTimeout, socketTimeout, config, poolConfig, hostAndPort, serviceURI);

                    unifiedJedis.sendCommand(() -> SafeEncoder.encode("PING"));

                    if (changeUnifiedJedisType(pool.getClient(id)) || configuration.isRefreshPool()) {
                        closeClient(pool.getClient(id));
                    }

                    pool.registerClient(id, unifiedJedis);
                    configuration.setRefreshPool(false);
                }
            } catch (Exception e) {
                if (unifiedJedis != null) {
                    closeClient(unifiedJedis);
                }
                throw new DBCException("无法打开 Redis 客户端。", e);
            } finally {
                exclusiveLock.releaseExclusiveLock(lock);
            }
        }

        log.info("Get dataSource: {}'s createConnectionThread: {}", pool.scanStatus(id), pool.getThread(id));

        return pool.getClient(id);
    }

    private void closeClient(UnifiedJedis unifiedJedis) {
        try {
            unifiedJedis.close();
        } catch (Exception e) {
            log.error("无法关闭 Redis 客户端。", e);
        }
    }

    private boolean changeUnifiedJedisType(UnifiedJedis unifiedJedis) {
        return (getDataSource().isUseCluster() && unifiedJedis instanceof JedisPooled) || (!getDataSource().isUseCluster() && unifiedJedis instanceof JedisCluster);
    }

    public @NotNull RedisSession openSession(@NotNull DBRProgressMonitor monitor, @NotNull DBCExecutionPurpose purpose, @NotNull String taskTitle) {
        RedisSessionImpl redisSession = new RedisSessionImpl(monitor, purpose, taskTitle, this);
        return getProxySession(redisSession);
    }

    public UnifiedJedis getClient() {
        return this.jedisClient;
    }

    public Jedis getJedis() {
        return this.jedis;
    }

    public void checkContextAlive(DBRProgressMonitor monitor) throws DBCException {
        if (!getDataSource().isUseCluster()) {
            try {
                this.getJedis().ping();
            } catch (Throwable var5) {
                throw new DBCException("Error pinging Redis : " + var5.getMessage());
            }
        } else {
            try {
                JedisCluster cmd = (JedisCluster) this.jedisClient;
                cmd.getClusterNodes();
            } catch (Throwable var4) {
                throw new DBCException("Error pinging Redis : " + var4.getMessage());
            }
        }
    }

    public DBSInstance getOwnerInstance() {
        return (DBSInstance) this.getDataSource();
    }

    public boolean isConnected() {
        return true;
    }

    @NotNull
    public DBCExecutionContext.InvalidateResult invalidateContext(@NotNull DBRProgressMonitor monitor, boolean closeOnFailure, DBPConnectionConfiguration configuration,boolean shortConnect) throws DBException {
        this.connect(monitor, null, null, configuration == null ? this.getConfiguration() : configuration);
        this.setInterrupted(false);
        return InvalidateResult.RECONNECTED;
    }

    public void closeClient() {
        if (this.jedis != null && this.allowCloseClient) {
            try {
                this.jedis.close();
            } catch (Exception e) {
                log.error("Redis this.jedis.close", e);
            }
        }
        if (this.jedisClient != null && this.allowCloseClient) {
            try {
                this.jedisClient.close();
            } catch (Exception e) {
                log.error("Redis this.jedisClient.close", e);
            }
        }
        super.closeContext();
    }

    public void close() {
        synchronized (this) {
            this.setInterrupted(true);
            this.getDataSource().removeExecutionContext(this);
            this.closeClient();
        }
    }

    public Integer getDefaultDatabase() {
        return this.defaultDatabase;
    }

    public void setDefaultDatabase(Integer defaultDatabase) {
        this.defaultDatabase = defaultDatabase;
    }

    public @Nullable DBCExecutionContextDefaults getContextDefaults() {
        return this;
    }

    @Override
    public String getProcessId() {
        // TODO redis
        return null;
    }

    @Override
    public void execPrefs(DBRProgressMonitor monitor, String prefs) throws DBCException {
    }

    @Override
    public DBPConnectionConfiguration getConfiguration() {
        return getDataSource().getContainer().getActualConnectionConfiguration();
    }

    public RedisDatabase getDefaultCatalog() {
        for (RedisDatabase database : this.getDataSource().getDatabases()) {
            if (database.getId() == defaultDatabase && !getDataSource().isUseCluster()) {
                return database;
            }
        }
        return new RedisDatabase(getDataSource(), "db" + defaultDatabase, -1);
    }

    public DBSSchema getDefaultSchema() {
        return null;
    }

    public boolean supportsCatalogChange() {
        return true;
    }

    public boolean supportsSchemaChange() {
        return false;
    }

    @Override
    public void setDefaultCatalog(DBRProgressMonitor monitor, RedisDatabase catalog, DBSSchema schema, boolean force) throws DBCException {
        RedisDatabase oldSelectedDB = this.getDefaultCatalog();
        if (oldSelectedDB != catalog && !force) {
            if (!getDataSource().isUseCluster()) {
                DatabaseCommands commands = this.getJedis();
                commands.select(catalog.getId());
                this.defaultDatabase = catalog.getId();
            }

            if (oldSelectedDB != null) {
                DBUtils.fireObjectSelect(oldSelectedDB, false);
            }

            DBUtils.fireObjectSelect(catalog, true);
        }
    }

    @Override
    public void setDefaultSchema(DBRProgressMonitor monitor, DBSSchema schema, boolean force) throws DBCException {
    }

    public boolean refreshDefaults(DBRProgressMonitor monitor, boolean useBootstrapSettings) throws DBException {
        this.defaultDatabase = this.getDefaultDatabase();
        return true;
    }

    @Override
    public DBPTransactionIsolation getTransactionIsolation() throws DBCException {
        return new RedisTransactionIsolation();
    }

    @Override
    public void setTransactionIsolation(DBRProgressMonitor monitor, DBPTransactionIsolation transactionIsolation) throws DBCException {
        throw new DBCFeatureNotSupportedException();
    }

    @Override
    public boolean isAutoCommit() throws DBCException {
        return true;
    }

    @Override
    public void setAutoCommit(DBRProgressMonitor monitor, boolean autoCommit) throws DBCException {
    }

    @Override
    public boolean supportsSavepoints() {
        return false;
    }

    @Override
    public DBCSavepoint setSavepoint(DBRProgressMonitor monitor, String name) throws DBCException {
        throw new DBCFeatureNotSupportedException();
    }

    @Override
    public void releaseSavepoint(DBRProgressMonitor monitor, DBCSavepoint savepoint) throws DBCException {
        throw new DBCFeatureNotSupportedException();
    }

    @Override
    public void commit() throws DBCException {
    }

    @Override
    public void rollback() throws DBCException {
    }

    @Override
    public boolean isSupportsTransactions() {
        return false;
    }
}
