
package com.dc.parser.ext.mysql.statement.dcl;

import com.dc.parser.model.segment.dcl.RoleOrPrivilegeSegment;
import lombok.Getter;
import lombok.Setter;
import com.dc.parser.model.segment.generic.GrantLevelSegment;
import com.dc.parser.model.statement.dcl.GrantStatement;
import com.dc.parser.ext.mysql.statement.MySQLStatement;
import com.dc.parser.ext.mysql.segment.UserSegment;

import java.util.Collection;
import java.util.LinkedList;

/**
 * MySQL grant statement.
 */
@Getter
@Setter
public final class MySQLGrantStatement extends GrantStatement implements MySQLStatement {
    
    private final Collection<RoleOrPrivilegeSegment> roleOrPrivileges = new LinkedList<>();
    
    private boolean allPrivileges;
    
    private final Collection<UserSegment> users = new LinkedList<>();
    
    private String aclObject;
    
    private GrantLevelSegment level;

    private boolean withGrantOption;
}
