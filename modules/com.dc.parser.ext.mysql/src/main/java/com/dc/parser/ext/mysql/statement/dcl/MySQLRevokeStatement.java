
package com.dc.parser.ext.mysql.statement.dcl;

import com.dc.parser.model.segment.dcl.RoleOrPrivilegeSegment;
import lombok.Getter;
import lombok.Setter;
import com.dc.parser.model.segment.generic.GrantLevelSegment;
import com.dc.parser.model.statement.dcl.RevokeStatement;
import com.dc.parser.ext.mysql.statement.MySQLStatement;
import com.dc.parser.ext.mysql.segment.UserSegment;

import java.util.Collection;
import java.util.LinkedList;

/**
 * MySQL revoke statement.
 */
@Getter
@Setter
public final class MySQLRevokeStatement extends RevokeStatement implements MySQLStatement {
    
    private final Collection<RoleOrPrivilegeSegment> roleOrPrivileges = new LinkedList<>();
    
    private boolean allPrivileges;
    
    private UserSegment onUser;
    
    private final Collection<UserSegment> fromUsers = new LinkedList<>();
    
    private String aclObject;
    
    private GrantLevelSegment level;

    private boolean grantedOption;

    private boolean proxy;
}
