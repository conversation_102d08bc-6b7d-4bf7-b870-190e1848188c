<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>


<plugin>

    <extension point="com.dc.summer.dataSourceProvider">
        <datasource
                class="com.dc.summer.ext.gaussdb.GaussDBDataSourceProvider"
                description="%datasource.gaussdb.description"
                id="gaussdb"
                label="GaussDB"
                icon="icons/gaussdb_icon.png"
                dialect="gaussdb">

            <drivers managable="true">
                <driver
                        id="gaussdbjdbc"
                        label="GaussDB"
                        class="com.huawei.gaussdb.jdbc.Driver"
                        sampleURL="jdbc:gaussdb://{host}[:{port}]/[{database}]"
                        useURL="true"
                        defaultPort="8000"
                        webURL="https://support.huaweicloud.com/gaussdb/index.html"
                        description="%driver.gaussdb.description"
                        promoted="1"
                        categories="sql"
                        dialect="gaussdb">
                    <file type="jar" path="maven:/gaussdbjdbc:gaussdbjdbc:1.2" bundle="!drivers.gaussdb"/>

                    <property name="stringtype" value="unspecified"/>
                </driver>
                <driver
                        id="gaussdb"
                        label="GaussDB-DWS"
                        class="com.huawei.gauss200.jdbc.Driver"
                        sampleURL="jdbc:gaussdb://{host}[:{port}]/[{database}]"
                        useURL="true"
                        defaultPort="8000"
                        webURL="https://carrier.huawei.com/cn/products/it-new/cloud/dws/"
                        description="%driver.gaussdb-dws.description"
                        promoted="1"
                        categories="sql"
                        dialect="gaussdb">
                    <file type="jar" path="maven:/com.huaweicloud.dws:huaweicloud-dws-jdbc:8.1.1.300-200" bundle="!drivers.gaussdb"/>

                    <property name="stringtype" value="unspecified"/>
                </driver>

                <driver
                        id="opengauss"
                        label="OpenGauss"
                        class="com.huawei.opengauss.jdbc.Driver"
                        sampleURL="jdbc:opengauss://{host}[:{port}]/[{database}]"
                        useURL="true"
                        defaultPort="8000"
                        webURL="https://support.huaweicloud.com/gaussdb/index.html"
                        description="%driver.opengauss.description"
                        promoted="1"
                        categories="sql"
                        dialect="gaussdb">
                    <file type="jar" path="maven:/com.huaweicloud.gaussdb:opengaussjdbc:503.2.T35" bundle="!drivers.gaussdb"/>

                    <property name="stringtype" value="unspecified"/>
                </driver>

            </drivers>
        </datasource>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="gaussdb" parent="postgresql" class="com.dc.summer.ext.gaussdb.model.GaussDBDialect" label="GaussDB" description="GaussDB Dialect on JDBC API information." icon="#database_icon_default" hidden="true">
        </dialect>
    </extension>

    <extension point="com.dc.summer.dataTypeProvider">
        <provider
                class="com.dc.summer.ext.gaussdb.model.data.GaussDBValueHandlerProvider"
                description="%provider.data.type.gaussdb.description"
                id="com.dc.summer.ext.gaussdb.model.data.GaussDBValueHandlerProvider"
                label="%provider.data.type.gaussdb.name">

            <datasource id="gaussdb"/>

            <type name="*"/>
        </provider>
    </extension>
</plugin>
