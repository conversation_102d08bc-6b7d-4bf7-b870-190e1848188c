
package com.dc.summer.ext.gaussdb.model.data;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.ext.gaussdb.GaussDBConstants;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCDateTimeValueHandler;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.SQLException;
import java.sql.Types;
import java.util.Calendar;
import java.util.Date;

/**
 * MySQL datetime handler
 */
public class GaussDBDateTimeValueHandler extends JDBCDateTimeValueHandler {

    private static final Date ZERO_DATE = new Date(0l);
    private static final Date ZERO_TIMESTAMP = new Date(0l);

    private static final String ZERO_DATE_STRING = "0000-00-00";
    private static final String ZERO_TIMESTAMP_STRING = "0000-00-00 00:00:00";

    public GaussDBDateTimeValueHandler(DBDFormatSettings formatSettings)
    {
        super(formatSettings);
    }

    @Override
    public Object fetchValueObject(@NotNull DBCSession session, @NotNull DBCResultSet resultSet, @NotNull DBSTypedObject type, int index) throws DBCException {
        if (resultSet instanceof JDBCResultSet) {
            JDBCResultSet dbResults = (JDBCResultSet) resultSet;
            try {
                if (GaussDBConstants.TYPE_YEAR.equalsIgnoreCase(type.getTypeName())) {
                    int year = dbResults.getInt(index + 1);
                    if (dbResults.wasNull()) {
                        return null;
                    }
                    return year;
                }

                //先判断year类型再判断是否采用原格式
                if (formatSettings.isUseNativeDateTimeFormat()) {
                    try {
                        return dbResults.getString(index + 1);
                    } catch (SQLException e) {
                        log.debug("Can't read date/time value as string: " + e.getMessage());
                    }
                }

                if (GaussDBConstants.TYPE_DATETIME.equalsIgnoreCase(type.getTypeName())) {
                    return dbResults.getTimestamp(index + 1);
                }

                if (type.getTypeID() == Types.TIME) {
                    return dbResults.getTime(index + 1);
                }
            } catch (SQLException e) {
                log.debug("Exception caught when fetching date/time value", e);
            }
        }

        return super.fetchValueObject(session, resultSet, type, index);
    }

    @Override
    public void bindValueObject(@NotNull DBCSession session, @NotNull DBCStatement statement, @NotNull DBSTypedObject type, int index, @Nullable Object value) throws DBCException {
        if (value == ZERO_DATE || value == ZERO_TIMESTAMP) {
            // Workaround for zero values (#1127)
            try {
                JDBCPreparedStatement dbStat = (JDBCPreparedStatement)statement;
                if (value == ZERO_DATE) {
                    dbStat.setString(index + 1, ZERO_DATE_STRING);
                } else {
                    dbStat.setString(index + 1, ZERO_TIMESTAMP_STRING);
                }
            }
            catch (SQLException e) {
                throw new DBCException(ModelMessages.model_jdbc_exception_could_not_bind_statement_parameter, e);
            }
        } else if (GaussDBConstants.TYPE_YEAR.equalsIgnoreCase(type.getTypeName())) {
            try {
                JDBCPreparedStatement dbStat = (JDBCPreparedStatement)statement;
                if (value instanceof Number) {
                    dbStat.setInt(index + 1, ((Number) value).intValue());
                } else if (value instanceof Date) {
                    Calendar cal = Calendar.getInstance();
                    cal.setTime((Date) value);
                    dbStat.setInt(index + 1, cal.get(Calendar.YEAR));
                } else if (value instanceof String) {
                    dbStat.setString(index + 1, (String) value);
                } else {
                    dbStat.setObject(index + 1, value);
                }
            }
            catch (SQLException e) {
                throw new DBCException(ModelMessages.model_jdbc_exception_could_not_bind_statement_parameter, e);
            }
        } else {
            super.bindValueObject(session, statement, type, index, value);
        }
    }

    @NotNull
    @Override
    public String getValueDisplayString(@NotNull DBSTypedObject column, Object value, @NotNull DBDDisplayFormat format) {
        if (value == ZERO_DATE) {
            return ZERO_DATE_STRING;
        } else if (value == ZERO_TIMESTAMP) {
            return ZERO_TIMESTAMP_STRING;
        }

        return super.getValueDisplayString(column, value, format);
    }

    public String numberAfterFillZero(String str, int length) {
        StringBuffer buffer = new StringBuffer(str);
        if (buffer.length() >= length) {
            return buffer.toString();
        } else {
            while (buffer.length() < length) {
                buffer.append("0");
            }
        }
        return buffer.toString();
    }

    @Override
    public Object getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException {
        if (object instanceof String) {
            switch (type.getTypeID()) {
                case Types.DATE:
                    if (object.equals(ZERO_DATE_STRING)) {
                        return ZERO_DATE;
                    }
                    break;
                default:
                    if (object.equals(ZERO_TIMESTAMP_STRING)) {
                        return ZERO_TIMESTAMP;
                    }
                    break;
            }
        }
        return super.getValueFromObject(session, type, object, copy, validateValue);
    }
}
