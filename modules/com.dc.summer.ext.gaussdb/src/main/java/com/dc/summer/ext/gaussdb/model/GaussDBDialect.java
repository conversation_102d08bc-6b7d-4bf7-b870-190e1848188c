package com.dc.summer.ext.gaussdb.model;

import com.dc.summer.ext.gaussdb.GaussDBConstants;
import com.dc.summer.ext.postgresql.model.PostgreDialect;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;
import com.dc.summer.model.sql.SQLConstants;
import com.dc.summer.model.struct.DBSTypedObject;

public class GaussDBDialect extends PostgreDialect {

    public String[][] DEFAULT_IDENTIFIER_QUOTES = new String[][]{{SQLConstants.DEFAULT_IDENTIFIER_QUOTE, SQLConstants.DEFAULT_IDENTIFIER_QUOTE}};
    public GaussDBDialect() {
        super("GaussDB", "gaussdb");
    }

    public static final String[] ADVANCED_KEYWORDS = {
            "PACKAGE",
    };

    @Override
    public void initDriverSettings(JDBCSession session, JDBCDataSource dataSource, JDBCDatabaseMetaData metaData) {
        for (String kw : ADVANCED_KEYWORDS) {
            addSQLKeyword(kw);
        }
        boolean m = dataSource instanceof GaussDBDataSource && "M".equals((((GaussDBDataSource) dataSource).getCompatibility()));
        if (m){
            DEFAULT_IDENTIFIER_QUOTES = new String[][]{
                    {"`", "`"},
                    {"\"", "\""},
            };
        }else {
            DEFAULT_IDENTIFIER_QUOTES = new String[][]{{SQLConstants.DEFAULT_IDENTIFIER_QUOTE, SQLConstants.DEFAULT_IDENTIFIER_QUOTE}};
        }
        super.initDriverSettings(session, dataSource, metaData);
    }

    @Override
    public boolean needsDelimiterFor(String firstKeyword, String lastKeyword) {
        return SQLConstants.KEYWORD_PACKAGE.equalsIgnoreCase(firstKeyword) && SQLConstants.KEYWORD_END.equalsIgnoreCase(lastKeyword);
    }

    @Override
    public boolean needReplaceFirstKeyword(String firstKeyword, String lastKeyword) {
        return SQLConstants.KEYWORD_CREATE.equalsIgnoreCase(firstKeyword) && SQLConstants.KEYWORD_PACKAGE.equalsIgnoreCase(lastKeyword);
    }

    public String[][] getIdentifierQuoteStrings() {
        return DEFAULT_IDENTIFIER_QUOTES;
    }

    @Override
    public String escapeScriptValue(DBSTypedObject attribute, Object value, String strValue) {

        if (GaussDBConstants.TYPE_YEAR.equalsIgnoreCase(attribute.getTypeName())) {
            return strValue;
        }

        return super.escapeScriptValue(attribute, value, strValue);

    }
}
