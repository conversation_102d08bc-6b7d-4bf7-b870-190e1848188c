package com.dc.summer.ext.gaussdb.model;

import com.dc.summer.ext.postgresql.model.PostgreDataSourceInfo;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import lombok.extern.slf4j.Slf4j;

import java.util.Locale;

/**
 * GaussDBDataSourceInfo
 */
@Slf4j
class GaussDBDataSourceInfo extends PostgreDataSourceInfo {

    private final GaussDBDataSource dataSource;

    public GaussDBDataSourceInfo(GaussDBDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super(dataSource, metaData);
        this.dataSource = dataSource;
    }

    /**
     * 获取标识符引号字符
     * M 模式使用反引号（`），其他模式使用双引号（"）
     */
    @Override
    protected String getIdentifierQuote() {
        return "M".equalsIgnoreCase(dataSource.getCompatibility()) ? "`" : "\"";
    }

    @Override
    public String getTableRealNameSql(String schemaName, String tableName) {
        if ("M".equalsIgnoreCase(dataSource.getCompatibility())) {
            return String.format("SELECT b.relname as \"tablename\"\n" +
                    "    from pg_catalog.pg_class b \n" +
                    "    join pg_catalog.pg_namespace a \n" +
                    "    on b.relnamespace = a.oid \n" +
                    "    where b.relkind in ('r','t','f','p') and a.nspname = '%s' and (b.relname = '%s' or b.relname='%s' or b.relname='%s')", schemaName, tableName.toLowerCase(Locale.ROOT),tableName.toUpperCase(Locale.ROOT), tableName);
        }
        return super.getTableRealNameSql(schemaName, tableName);
    }

}