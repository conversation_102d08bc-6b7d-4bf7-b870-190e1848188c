package com.dc.summer.ext.gaussdb.model.data;

import com.dc.summer.ext.gaussdb.GaussDBConstants;
import com.dc.summer.ext.gaussdb.model.GaussDBDataSource;
import com.dc.summer.ext.postgresql.model.data.PostgreValueHandlerProvider;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCContentValueHandler;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCNumberValueHandler;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCObjectValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

public class GaussDBValueHandlerProvider extends PostgreValueHandlerProvider {

    @Override
    public DBDValueHandler getValueHandler(DBPDataSource dataSource, DBDFormatSettings preferences, DBSTypedObject typedObject) {
        // M模式
        boolean m =  dataSource instanceof GaussDBDataSource && "M".equals(((GaussDBDataSource) dataSource).getCompatibility());
        if (DBPDataKind.UNKNOWN.equals(typedObject.getDataKind()) || m){
            String typeName = typedObject.getTypeName();
            switch (typeName) {
                case GaussDBConstants.TYPE_MEDIUMINT:
                    return new JDBCNumberValueHandler(typedObject,preferences);
                case GaussDBConstants.TYPE_BINARY:
                case GaussDBConstants.TYPE_BLOB:
                case GaussDBConstants.TYPE_VARBINARY_LOWER:
                    return GaussDBContentValueHandler.INSTANCE;
                case GaussDBConstants.TYPE_INT2VECTOR:
                case GaussDBConstants.TYPE_OIDVECTOR:
                    return GaussDBStringValueHandler.INSTANCE;
                case GaussDBConstants.TYPE_DATETIME:
                case GaussDBConstants.TYPE_YEAR:
                    return new GaussDBDateTimeValueHandler(preferences);
                case GaussDBConstants.TYPE_JSON:
                case GaussDBConstants.TYPE_JSONM:
                    return JDBCContentValueHandler.INSTANCE;
                case GaussDBConstants.TYPE_ENUM:
                case GaussDBConstants.TYPE_SET:
                    return JDBCObjectValueHandler.INSTANCE;
            }
        }

        if (typedObject.getTypeName().equals(GaussDBConstants.TYPE_BLOB)) {
            return GaussDBContentValueHandler.INSTANCE;
        }
        return super.getValueHandler(dataSource, preferences, typedObject);
    }
}
