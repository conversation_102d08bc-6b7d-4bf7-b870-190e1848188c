package com.dc.summer.ext.gaussdb.model;


import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.model.PostgreDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCURL;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import org.apache.commons.lang3.StringUtils;

public class GaussDBDataSource extends PostgreDataSource {

    private String compatibility;


    @Override
    public void initialize(DBRProgressMonitor monitor) throws DBException {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Read sql_compatibility")) {
            compatibility = JDBCUtils.queryString(session, "SHOW sql_compatibility");
        } catch (Exception e) {
            compatibility = "";
        }
        super.initialize(monitor);
    }

    public String getCompatibility() {
        return compatibility;
    }

    public GaussDBDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        super(monitor, container, new GaussDBDialect());
    }

    @Override
    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, JDBCDatabaseMetaData metaData) {
        return new GaussDBDataSourceInfo(this, metaData);
    }

    @Override
    public StringBuilder generateQueryDataTypeSql(boolean readAllTypes, boolean supportsSysTypColumn) {
        StringBuilder sql = new StringBuilder(256);
        sql.append("SELECT t.oid,t.*,c.relkind,").append(getBaseTypeNameClause()).append(
                ", d.description\nFROM @_catalog.@_type t");
        if (!readAllTypes && supportsSysTypColumn) {
            sql.append("\nLEFT OUTER JOIN @_catalog.@_type et ON et.oid=t.typelem "); // If typelem is not 0 then it identifies another row in pg_type
        }
        sql.append("\nLEFT OUTER JOIN @_catalog.@_class c ON c.oid=t.typrelid" +
                "\nLEFT OUTER JOIN @_catalog.@_description d ON t.oid=d.objoid" +
                "\nWHERE t.typname IS NOT NULL");
        if (!readAllTypes) {
            sql.append("\nAND (c.relkind IS NULL OR c.relkind = 'c')");
            if (supportsSysTypColumn) {
                sql.append(" AND (et.typcategory IS NULL OR et.typcategory <> 'C')");
            }
        }
        return sql;
    }

     String getBaseTypeNameClause() {
        // 兼容 gaussDb的mysql类型
        if ("M".equalsIgnoreCase(compatibility)) {
            return "NULL as base_type_name";
        } else {
            return "format_type(nullif(t.typbasetype, 0), t.typtypmod) as base_type_name";
        }
    }

    @Override
    public String getConnectionURL(DBPConnectionConfiguration connectionInfo) {
        String url = connectionInfo.getUrl();
        String databaseName = connectionInfo.getDatabaseName();
        if (StringUtils.isNotBlank(url) && StringUtils.isNotBlank(databaseName)){
            DBPDriver driver = getContainer().getDriver();
            DBPConnectionConfiguration configuration = JDBCURL.extractConfigurationFromUrl(driver.getSampleURL(), connectionInfo.getUrl());
            if (configuration != null && !databaseName.equals(configuration.getDatabaseName())){
                // 切换数据库
                configuration.setDatabaseName(databaseName);
                return getContainer().getDriver().getConnectionURL(configuration);
            }
        }
        return super.getConnectionURL(connectionInfo);
    }
}