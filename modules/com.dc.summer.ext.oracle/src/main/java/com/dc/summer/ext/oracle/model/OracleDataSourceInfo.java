
package com.dc.summer.ext.oracle.model;

import com.dc.annotation.SQL;
import com.dc.summer.DBException;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceInfo;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.TransactionStatus;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.model.struct.DBSObjectType;
import com.dc.summer.utils.ContentUtils;
import com.dc.utils.DateValidator;
import com.dc.utils.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * OracleDataSourceInfo
 */
@Slf4j
public class OracleDataSourceInfo extends JDBCDataSourceInfo {

    private final OracleDataSource dataSource;

    public OracleDataSourceInfo(OracleDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super(metaData);
        this.dataSource = dataSource;
    }

    @Override
    public DBSObjectType[] getSupportedObjectTypes() {
        return OracleObjectType.values();
    }

    @Override
    public boolean needsTableMetaForColumnResolution() {
        return false;
    }

    @Override
    public boolean supportsLocalTransaction() {
        this.dataSource.getContainer().setLocalTransactional((dbrProgressMonitor, dbcExecutionContext) -> {
            AtomicBoolean hasTransactional = new AtomicBoolean(false);
            try {
                DBExecUtils.executeQuery(
                        dbrProgressMonitor,
                        dbcExecutionContext,
                        "runLocalTransaction",
                        "select dbms_transaction.LOCAL_TRANSACTION_ID from dual",
                        dbcResultSet -> {
                            if (dbcResultSet.nextRow()) {
                                if (dbcResultSet.getAttributeValue("LOCAL_TRANSACTION_ID") != null) {
                                    hasTransactional.set(true);
                                }
                            }
                        });
            } catch (DBException e) {
                log.error(e.getMessage(), e);
                return TransactionStatus.EXECUTION_FAILED;
            }
            return hasTransactional.get() ? TransactionStatus.HAS_TRANSACTIONAL : TransactionStatus.NO_TRANSACTIONAL;
        });
        return true;
    }

    @Override
    public String getFormatColumnName(String column) {
        return String.format("\"%s\"", column);
    }

    @Override
    public List<String> getBigDataColumnType() {
        return Arrays.asList(
                "BLOB", "BFILE", "LONG",
                "BINARY", "TEXT", "RAW", "LONG RAW",
                "TIMESTAMP", "TIMESTAMP WITH TIME ZONE", "TIMESTAMP WITH LOCAL TIME ZONE", "SYS.XMLTYPE", "CLOB", "NCLOB"
        );
    }

    @Override
    public String getTableColumnSql(String schemaName, String tableName) {
        String tabStr = ContentUtils.getTabCondition(Collections.singletonList(tableName));

        boolean queryComment = isQueryComment();

        String query = "SELECT a.table_name, a.column_name, a.data_type " +
                " , (case \n" +
                "                            when a.data_type = 'NUMBER' \n" +
                "                            then (CASE\n" +
                "\tWHEN a.data_precision IS NULL THEN\n" +
                "\t\t0\n" +
                "\tELSE\n" +
                "\t\ta.data_precision end\n" +
                ")     \n" +
                "                            when a.data_type in ('CHAR','VARCHAR2','NVARCHAR2')\n" +
                "                            then char_col_decl_length\n" +
                "                            when LOWER(a.data_type) in ('binary_double','binary_float','blob','clob','long',\n" +
                "                            'long raw','nclob','raw','date')\n" +
                "                            then data_length\n" +
                "                            else 0\n" +
                "                            end\n" +
                "                        ) as data_length  " +
                " , CASE  " +
                "  WHEN a.nullable = 'Y' THEN 1 " +
                "  ELSE 0 " +
                " END AS nullable, a.data_default ";
        if (queryComment) {
            query += ", b.comments ";
        }
        query += " , CASE  " +
                "  WHEN t.column_name IS NOT NULL THEN 1 " +
                "  ELSE 0 " +
                " END AS is_primary_key " +
                "FROM " +
                OracleUtils.getAdminAllViewPrefix(dataSource.getMonitor(), dataSource, "TAB_COLUMNS") + " a ";
        if (queryComment) {
            query += " INNER JOIN " +
                    OracleUtils.getAdminAllViewPrefix(dataSource.getMonitor(), dataSource, "COL_COMMENTS") + " b " +
                    " ON a.owner = b.owner " +
                    "  AND a.table_name = b.table_name " +
                    "  AND a.column_name = b.column_name ";
        }
        query += " LEFT JOIN ( " +
                "  SELECT cu.column_name AS column_name " +
                "  FROM " +
                OracleUtils.getAdminAllViewPrefix(dataSource.getMonitor(), dataSource, "CONS_COLUMNS") + " cu, " +
                OracleUtils.getAdminAllViewPrefix(dataSource.getMonitor(), dataSource, "CONSTRAINTS") + " au " +
                "  WHERE cu.owner = '" + schemaName + "' " +
                "   AND cu.owner = au.owner " +
                "   AND cu.constraint_name = au.constraint_name " +
                "   AND au.constraint_type = 'P' " +
                "   AND au.table_name IN (" + tabStr + ") " +
                " ) t " +
                " ON a.column_name = t.column_name " +
                "WHERE a.owner = '" + schemaName + "' " +
                " AND a.Table_Name IN (" + tabStr + ") " +
                "ORDER BY column_id ASC";
        return query;
    }

    /**
     * 由于oracle 有私有同义词和公有同义词，所以联查条件增加查PUBLIC的同义词
     *
     * @param schemaName schema name
     * @param synonym    synonym
     * @return sql
     */
    @Override
    public String getSynonymSql(String schemaName, String synonym) {
        @SQL
        String sql = "SELECT TABLE_NAME AS \"source_table\", TABLE_OWNER AS \"source_schema\"\n" +
                "FROM (SELECT s.TABLE_OWNER, s.TABLE_NAME\n" +
                "      FROM " +
                OracleUtils.getAdminAllViewPrefix(dataSource.getMonitor(), dataSource, "SYNONYMS") + " s\n" +
                "               JOIN " +
                OracleUtils.getAdminAllViewPrefix(dataSource.getMonitor(), dataSource, "OBJECTS") + " o\n" +
                "                    ON s.TABLE_OWNER = o.OWNER\n" +
                "                        AND s.TABLE_NAME = o.OBJECT_NAME\n" +
                "      WHERE s.SYNONYM_NAME = upper('" + synonym + "')\n" +
                "        AND (s.OWNER = upper('" + schemaName + "') OR s.OWNER = upper('PUBLIC'))\n" +
                "        AND o.OBJECT_TYPE = 'TABLE'\n" +
                "      ORDER BY CASE WHEN s.OWNER = upper('" + schemaName + "') THEN 1 ELSE 2 END) TOTN\n" +
                "WHERE ROWNUM = 1";
        return sql;
    }

    @Override
    public String getPrimaryKeySql(String schemaName, String tableName) {

        String dblinkName = "";
        if (tableName.contains("@")) {
            dblinkName = tableName.substring(tableName.indexOf("@"));
            tableName = tableName.substring(0, tableName.indexOf("@")).trim();
        }

        return "select a.column_name, (case when t.column_name is not null then 1 else 0 end) as is_primary_key \n" +
                "from " +
                OracleUtils.getAdminAllViewPrefix(dataSource.getMonitor(), dataSource, "TAB_COLUMNS") +
                dblinkName + " a \n" +
                "LEFT JOIN ( \n" +
                "     select cu.column_name as column_name \n" +
                "     from " +
                OracleUtils.getAdminAllViewPrefix(dataSource.getMonitor(), dataSource, "CONS_COLUMNS") +
                dblinkName + " cu," +
                OracleUtils.getAdminAllViewPrefix(dataSource.getMonitor(), dataSource, "CONSTRAINTS") +
                dblinkName + " au \n" +
                "     where cu.owner = '" + schemaName + "' and cu.owner = au.owner " +
                "     and cu.constraint_name = au.constraint_name and au.constraint_type = 'P' " +
                "     and au.table_name = '" + tableName + "') t \n" +
                "on a.column_name = t.column_name " +
                "where a.owner = '" + schemaName + "' and a.Table_Name = '" + tableName + "'";

    }

    @Override
    public List<String> getPrimaryKeyColumns(List<Map<String, Object>> list) {

        List<String> columns = new ArrayList<>();

        for (Map<String, Object> map : list) {
            if (map.get("is_primary_key") != null && "1".equals(map.get("is_primary_key").toString())) {
                if (map.get("COLUMN_NAME") != null) {
                    columns.add((String) map.get("COLUMN_NAME"));
                } else if (map.get("column_name") != null) {
                    columns.add((String) map.get("column_name"));
                }
            } else if (map.get("IS_PRIMARY_KEY") != null && "1".equals(map.get("IS_PRIMARY_KEY").toString())) {
                if (map.get("COLUMN_NAME") != null) {
                    columns.add((String) map.get("COLUMN_NAME"));
                } else if (map.get("column_name") != null) {
                    columns.add((String) map.get("column_name"));
                }
            }
        }

        return columns;
    }

    @Override
    public String getTableRealNameSql(String schemaName, String tableName) {
        return "select object_name as table_name  from " +
                OracleUtils.getAdminAllViewPrefix(dataSource.getMonitor(), dataSource, "OBJECTS") +
                " where upper(owner) = upper('" + schemaName + "') and upper(object_name) = upper('" + tableName + "') and object_type='TABLE' ";
    }

    @Override
    public String getTableRealName(List<Map<String, Object>> list) {

        String realName = "";

        for (Map<String, Object> map : list) {
            if (map.get("TABLE_NAME") != null) {
                realName = map.get("TABLE_NAME").toString();
                break;
            } else if (map.get("table_name") != null) {
                realName = map.get("table_name").toString();
                break;
            }
        }

        return realName;
    }

    @Override
    public boolean isIntervalNeedQuotes() {
        return false;
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content) {
        if (list == null) {
            return "";
        }
        List<String> fields = list.stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
        List<String> data = new ArrayList<>();
        for (SqlFieldData item : list) {
            String regExp = ".*\\(\\d+\\).*";
            String regReplace = "\\(\\d+\\)";
            String replace = "";
            String columnType;
            if (Pattern.matches(regExp, item.getFieldType())) {
                columnType = item.getFieldType().replaceAll(regReplace, replace);
            } else {
                columnType = item.getFieldType();
            }
            Object fieldValue = item.getFieldValue();
            if (Arrays.asList("DATE", "TIMESTAMP", "TIMESTAMP WITH LOCAL TIME ZONE", "TIMESTAMP WITH TIME ZONE").contains(columnType) && null != fieldValue && StringUtils.isNotBlank(fieldValue.toString())) {
                processDateType(data, fieldValue);
                continue;
            } else if ("INTERVAL DAY TO SECOND".equals(columnType) && null != fieldValue && StringUtils.isNotBlank(fieldValue.toString())) {
                data.add(String.format("INTERVAL '%s' DAY TO SECOND", fieldValue));
                continue;
            } else if ("INTERVAL YEAR TO MONTH".equals(columnType) && null != fieldValue && StringUtils.isNotBlank(fieldValue.toString())) {
                data.add(String.format("INTERVAL '%s' YEAR TO MONTH", fieldValue));
                continue;
            }
            if (fieldValue instanceof BigDecimal) {
                BigDecimal bigDecimal = new BigDecimal(fieldValue.toString());
                data.add(String.format("'%s'", bigDecimal.toPlainString()));
                continue;
            }
            if (null != fieldValue && StringUtils.isNotBlank(fieldValue.toString())) {
                if (Arrays.asList("CHAR", "NCHAR", "VARCHAR", "VARCHAR2", "NVARCHAR", "NVARCHAR2").contains(columnType.toUpperCase(Locale.ROOT))) {
                    String varcharData = fieldValue.toString().contains("'") ? fieldValue.toString().replace("'", "''") : fieldValue.toString();
                    data.add(String.format("'%s'", varcharData));
                } else {
                    data.add(String.format("'%s'", fieldValue));
                }
            } else {
                data.add(String.format("%s", "NULL"));
            }
        }
        String columns = StringUtils.join(fields, "\",\"");
        String values = StringUtils.join(data, ",");

        if (null != schemaName) {
            return String.format("INSERT INTO %s.\"%s\" (%s) VALUES (%s)", schemaName, tableName, "\"" + columns + "\"", values);
        }
        return String.format("INSERT INTO \"%s\" (%s) VALUES (%s)", tableName, "\"" + columns + "\"", values);
    }

    /**
     * INSERT ALL
     *     INTO t (coll, col2, col3) VALUES ('val1_1', 'val1_2', 'val1_3')
     *     INTO t (col1, col2, col3) VALUES ('val2_1', 'val2_2', 'val2_3')
     *     INTO t (coll, col2, col3) VALUES ('val3_1', 'val3_2', 'val3_3')
     * ：
     * SELECT 1 FROM DUAL;
     */
    @Override
    public String generateInsertSqlBatch(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        // "field1","field2","field3"
        String fields = list.get(0).stream().map(SqlFieldData::getFieldName).collect(Collectors.joining("\",\"", "\"", "\""));

        String insertStatements = list.stream().map(sqlFieldDataList -> {
            List<String> data = new ArrayList<>();
            for (SqlFieldData item : sqlFieldDataList) {
                String regExp = ".*\\(\\d+\\).*";
                String regReplace = "\\(\\d+\\)";
                String replace = "";
                String columnType;
                if (Pattern.matches(regExp, item.getFieldType())) {
                    columnType = item.getFieldType().replaceAll(regReplace, replace);
                } else {
                    columnType = item.getFieldType();
                }
                Object fieldValue = item.getFieldValue();

                if (Arrays.asList("DATE", "TIMESTAMP", "TIMESTAMP WITH LOCAL TIME ZONE", "TIMESTAMP WITH TIME ZONE").contains(columnType) && null != fieldValue && StringUtils.isNotBlank(fieldValue.toString())) {
                    processDateType(data, fieldValue);
                    continue;
                } else if ("INTERVAL DAY TO SECOND".equals(columnType) && null != fieldValue && StringUtils.isNotBlank(fieldValue.toString())) {
                    data.add(String.format("INTERVAL '%s' DAY TO SECOND", fieldValue));
                    continue;
                } else if ("INTERVAL YEAR TO MONTH".equals(columnType) && null != fieldValue && StringUtils.isNotBlank(fieldValue.toString())) {
                    data.add(String.format("INTERVAL '%s' YEAR TO MONTH", fieldValue));
                    continue;
                }
                if (fieldValue instanceof BigDecimal) {
                    BigDecimal bigDecimal = new BigDecimal(fieldValue.toString());
                    data.add(String.format("'%s'", bigDecimal.toPlainString()));
                    continue;
                }
                if (null != fieldValue && StringUtils.isNotBlank(fieldValue.toString())) {
                    if (Arrays.asList("CHAR", "NCHAR", "VARCHAR", "VARCHAR2", "NVARCHAR", "NVARCHAR2").contains(columnType.toUpperCase(Locale.ROOT))) {
                        String varcharData = fieldValue.toString().contains("'") ? fieldValue.toString().replace("'", "''") : fieldValue.toString();
                        data.add(String.format("'%s'", varcharData));
                    } else {
                        data.add(String.format("'%s'", fieldValue));
                    }
                } else {
                    data.add(String.format("%s", "NULL"));
                }
            }
            String values = StringUtils.join(data, ",");
            if (schemaName != null) {
                return String.format("INTO %s.\"%s\" (%s) VALUES (%s)", schemaName, tableName, fields, values);
            }
            return String.format("INTO \"%s\" (%s) VALUES (%s)", tableName, fields, values);
        }).collect(Collectors.joining("\n    ", "    ", "\n SELECT 1 FROM DUAL"));

        return String.format("INSERT ALL \n%s", insertStatements);
    }

    private void processDateType(List<String> data, Object fieldValue) {
        String date = DateValidator.validateAndFormatDate(fieldValue.toString());
        if (Objects.nonNull(date)) {
            data.add(String.format("DATE '%s'", date));
            return;
        }
        String timestamp = DateValidator.validateAndFormatTimestamp(fieldValue.toString());
        if (Objects.nonNull(timestamp)) {
            data.add(String.format("TIMESTAMP '%s'", timestamp));
            return;
        }
        data.add(String.format("TIMESTAMP '%s'", fieldValue));
    }

    @Override
    public String generateTruncateSql(String schemaName, String tableName) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(schemaName)) {
            return String.format("TRUNCATE TABLE \"%s\".\"%s\"", schemaName, tableName);
        } else {
            return String.format("TRUNCATE TABLE \"%s\"", tableName);
        }
    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {

        List<Map<String, Object>> list = DBExecUtils.executeQuery(monitor, context, "get schema list",
                "select username as owner from "+
                        OracleUtils.getAdminAllViewPrefix(dataSource.getMonitor(), dataSource, "USERS") +
                        " group by username");

        List<Map<String, Object>> listCount = DBExecUtils.executeQuery(monitor, context, "get schema count",
                "select owner,count(0) as count from " +
                        OracleUtils.getAdminAllViewPrefix(dataSource.getMonitor(), dataSource, "OBJECTS") +
                        " where object_type='TABLE' group by owner");

        List<Map<String, Object>> listCharset = DBExecUtils.executeQuery(monitor, context, "get schema charset",
                "select value from nls_database_parameters where parameter='NLS_CHARACTERSET'");

        ArrayList<Map<String, Object>> returnList = new ArrayList<>();
        for (Map<String, Object> map : list) {
            Map<String, Object> returnMap = new LinkedHashMap<>();
            returnMap.put("addLabel", "dc_oracle_db_schema");
            returnMap.put("username", map.get("OWNER"));
            returnMap.put("charset", listCharset.size() > 0 ? listCharset.get(0).get("VALUE") : "");

            if (Arrays.asList("ANONYMOUS", "CTXSYS", "DBSNMP", "EXFSYS", "LBACSYS",
                            "MDSYS", "MGMT_VIEW", "OLAPSYS", "ORDDATA", "OWBSYS",
                            "ORDPLUGINS", "ORDSYS", "OUTLN", "SI_INFORMTN_SCHEMA", "SYS",
                            "SYSMAN", "SYSTEM", "WK_TEST", "WKSYS", "WKPROXY",
                            "WMSYS", "XDB", "APEX_PUBLIC_USER", "DIP", "FLOWS_040100",
                            "FLOWS_040100", "MDDATA", "ORACLE_OCM", "SPATIAL_CSW_ADMIN_USR", "SPATIAL_WFS_ADMIN_USR",
                            "XS$NULL", "BI", "HR", "OE", "PM",
                            "IX", "SH", "PUBLIC", "APEX_030200")
                    .contains(((String) map.get("OWNER")).toUpperCase())) {
                returnMap.put("is_sys", 1);
            } else {
                returnMap.put("is_sys", 0);
            }

            returnMap.put("count", 0L);
            for (Map<String, Object> mapCount : listCount) {
                if (map.get("OWNER").toString().equals(mapCount.get("OWNER").toString())) {
                    returnMap.put("count", Long.parseLong(mapCount.get("COUNT").toString()));
                    break;
                }
            }
            returnList.add(returnMap);
        }

        return returnList;
    }

    @Override
    public boolean isSystemSchema(String name) {
        return "PUBLIC".equalsIgnoreCase(name);
    }

    @Override
    public boolean showInteger(DBDAttributeBinding column) {
        return column.getPrecision() > 0 &&
                column.getScale() <= 0 &&
                column.getScale() != -127;
    }

    @Override
    public String generateSystemPrivilegeSql(){
        return "SELECT DISTINCT PRIVILEGE FROM DBA_SYS_PRIVS ORDER BY PRIVILEGE";
    }

    @Override
    public List<String> getDirectories(DBRProgressMonitor monitor, DBCExecutionContext context) {
        try {
            List<Map<String, Object>> list = DBExecUtils.executeQuery(monitor, context, "get directories list",
                    "select object_name from "+
                            OracleUtils.getAdminAllViewPrefix(dataSource.getMonitor(), dataSource, "OBJECTS")+
                    " WHERE OBJECT_TYPE = 'DIRECTORY'");

            return list.stream().map(r -> (String)r.get("OBJECT_NAME")).collect(Collectors.toList());
        } catch (DBException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String getObjectType(DBRProgressMonitor monitor,DBCExecutionContext context ,String objectName, String owner) {
        try {
            String whereClause;
            if (StringUtils.isNotBlank(owner)) {
                whereClause = " WHERE object_name = " + "'" + objectName + "' AND owner = " + "'" + owner + "'";
            } else {
                whereClause = " WHERE object_name = " + "'" + objectName + "'";
            }

            List<Map<String, Object>> list = DBExecUtils.executeQuery(monitor, context, "get directories list",
                    "select object_type from "+
                            OracleUtils.getAdminAllViewPrefix(dataSource.getMonitor(), dataSource, "OBJECTS") + whereClause);
            if (list.isEmpty()) {
                return "";
            }
            return (String) list.get(0).get("OBJECT_TYPE");
        } catch (DBException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String generateConditionSql(String key, Pair<String, String> pair, String conditionSql, String and) {
        String column = getFormatColumnName(key);
        String typeName = pair.getFirst();
        String value = pair.getSecond();
        if (typeName.equalsIgnoreCase("DATE") || typeName.equalsIgnoreCase("DATETIME") || typeName.equalsIgnoreCase("TIMESTAMP")) {
            conditionSql = conditionSql + and + column + " = TIMESTAMP '" + value + "'";
        } else {
            conditionSql = conditionSql + and + column + " = '" + value + "'";
        }
        return conditionSql;
    }
}
