package com.dc.summer.model.data.result;

import com.dc.summer.model.connection.DBPConnectionConfiguration;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


import java.util.Map;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class DataResultNodeInfo extends DataResultNode {

    @Override
    public void accept(DataResultVisitor visitor) {
        visitor.visit(this);
    }

    private DBPConnectionConfiguration connectionConfiguration;

    /**
     * 是否导出
     */
    private boolean isExport;
    /**
     * 是否异步查询
     */
    private boolean isAsyncExecute;

    /**
     * 是否脱敏
     */
    private boolean isDesens;

    /**
     * 是否兜底
     */
    private boolean isBackstop;

    /**
     * 窗口类型
     */
    private ConsoleType consoleType;
    /**
     * 规则是否脱敏文导出
     */
    private RuleExportDesensitizationType ruleExportDesensitizationType;

    private boolean isLevel ;

    private Boolean hasDefaultPlainAuth;
    /**
     * 权限追踪
     */
    private Object maskAuthObject;
    /**
     * 复制权限
     */
    private  Map<String, Integer>  enableDesensitizationCopy;

    private Map<Integer, Integer> sensitiveLevelToAuthLevel;

    private String databaseName;
    /**
     * pingan orcl dbName 实时传值
     */
    private String dbName;

}
