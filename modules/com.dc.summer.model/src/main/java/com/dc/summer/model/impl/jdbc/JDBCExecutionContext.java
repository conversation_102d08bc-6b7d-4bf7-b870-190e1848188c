
package com.dc.summer.model.impl.jdbc;

import com.dc.summer.model.DBUtils;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.impl.AbstractExecutionContext;
import com.dc.summer.model.impl.jdbc.exec.JDBCConnectionImpl;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.utils.RuntimeUtils;
import org.eclipse.core.runtime.IAdaptable;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBPTransactionIsolation;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.exec.JDBCSavepointImpl;
import com.dc.summer.model.qm.QMUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.utils.CommonUtils;

import java.lang.reflect.InvocationTargetException;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;
import java.sql.Savepoint;

/**
 * JDBCExecutionContext.
 * Implements transaction manager and execution context defaults.
 * Both depend on datasource implementation.
 */
public class JDBCExecutionContext extends AbstractExecutionContext<JDBCDataSource> implements DBCTransactionManager, IAdaptable {
    public static final String TYPE_MAIN = "Main";
    public static final String TYPE_METADATA = "Metadata";

    protected static final String TASK_TITLE_SET_SCHEMA = "Set active schema"; //$NON-NLS-1$

    private static final Log log = Log.getLog(JDBCExecutionContext.class);

    // Time to wait for txn level/auto-commit detection
    static final int TXN_INFO_READ_TIMEOUT = 5000;

    @NotNull
    private volatile JDBCRemoteInstance instance;
    private volatile Connection connection;
    private volatile Boolean autoCommit;
    private volatile Integer transactionIsolationLevel;
    private transient volatile boolean txnIsolationLevelReadInProgress;

    private volatile String processId;
    private DBPConnectionConfiguration configuration;

    public JDBCExecutionContext(@NotNull JDBCRemoteInstance instance, String purpose) {
        super(instance.getDataSource(), purpose);
        this.instance = instance;
    }

    @Override
    public DBPConnectionConfiguration getConfiguration() {
        return configuration;
    }

    @Override
    public JDBCRemoteInstance getOwnerInstance() {
        return instance;
    }

    protected void setOwnerInstance(@NotNull JDBCRemoteInstance instance) {
        this.instance = instance;
    }

    @NotNull
    private Connection getConnection() throws DBCException {
        Connection dbCon = this.connection;
        if (dbCon == null) {
            throw new DBCException("Disconnected");
        }
        return dbCon;
    }

    @Override
    public void execPrefs(DBRProgressMonitor monitor, String prefs) throws DBCException {
    }

    public void connect(DBRProgressMonitor monitor) throws DBCException {
        DBPConnectionConfiguration connectionConfiguration = getConfiguration() == null ? getDataSource().getContainer().getConnectionConfiguration() : getConfiguration();
        connect(monitor, null, null, null, true, connectionConfiguration);
    }

    protected void connect(@NotNull DBRProgressMonitor monitor, Boolean autoCommit,
                           @Nullable Integer txnLevel, JDBCExecutionContext initFrom,
                           boolean addContext, DBPConnectionConfiguration configuration) throws DBCException {

        this.configuration = configuration;

        /*
         如果存在连接，并且想要添加上下文，那么就会删除 JDBCRemoteInstance 中的当前对象。
         注意，这个 JDBCRemoteInstance 是多线程共享的。
         然后，继续执行下面的连接操作，在把自己添加进 JDBCRemoteInstance。
         */
        if (connection != null && addContext) {
            log.error("Reopening not-closed connection");
            close();
        }
        // 没有用到该指向
        boolean connectionReadOnly = getDataSource().getContainer().isConnectionReadOnly();
        // 这个实例的连接，被同步给当前的局部对象，感觉这么写没什么意义。
        final JDBCRemoteInstance currentInstance = this.instance;
        // 在初始化隧道的时候，也调用了这个方法，因为 DataSourceDescriptor 是 DBPDataSourceContainer 的实现类
        DBExecUtils.startContextInitiation(getDataSource().getContainer());
        // 使用ds独占锁初始化连接
//        Object exclusiveLock = currentInstance.getExclusiveLock().acquireExclusiveLock();
        try {
            // 在这里打开连接，不同的数据库有不同的实现。但是核心调用方式，是一样的。
            this.connection = getDataSource().openConnection(monitor, this, purpose, configuration);
            if (this.connection == null) {
                throw new DBCException("Null connection returned");
            }
            // 进度条子任务
            monitor.subTask("Set connection defaults");
            // 从首选项获取默认值。是否自动提交。
            // Get defaults from preferences
            if (autoCommit == null) {
                autoCommit = getDataSource().getContainer().isDefaultAutoCommit();
            }
            // 获取默认事务隔离级别
            // Get default txn isolation level
            if (txnLevel == null) {
                txnLevel = getDataSource().getContainer().getDefaultTransactionsIsolation();
            }
            // 设置事务隔离级别
            if (txnLevel != null && getDataSource().getInfo().isSupportsTransactionIsolation()) {
                try {
                    this.getConnection().setTransactionIsolation(txnLevel);
                    this.transactionIsolationLevel = txnLevel;
                } catch (Throwable e) {
                    log.error("无法设置事务隔离级别！");
                }
            }
            // 设置自动提交
            try {
                connection.setAutoCommit(autoCommit);
                this.autoCommit = autoCommit;
            } catch (Throwable e) {
                log.debug("Can't set auto-commit state: " + e.getMessage()); //$NON-NLS-1$
            }
            {
                // 缓存自动提交
                // Cache auto-commit
                try {
                    this.autoCommit = connection.getAutoCommit();
                } catch (Throwable e) {
                    log.debug("Can't check auto-commit state", e); //$NON-NLS-1$
                    this.autoCommit = false;
                }
            }

            try {
                DBCExecutionPurpose executionPurpose = DBCExecutionPurpose.getByTitle(purpose);
                if (executionPurpose.isUser()) {
                    // 初始化上下文引导
                    this.initContextBootstrap(monitor, autoCommit);
                }
            } catch (DBCException e) {
                log.warn("Error while running context bootstrap: " + e.getMessage());
            }

            if (addContext) {
                // 将自身添加到上下文列表
                // Add self to context list
                currentInstance.addContext(this);
            }
        } finally {
            DBExecUtils.finishContextInitiation(getDataSource().getContainer());
//            currentInstance.getExclusiveLock().releaseExclusiveLock(exclusiveLock);
        }

        // 现在初始化上下文状态
        // Now initialize context state
        // 在独占锁之外进行操作，以避免死锁
        // Do it outside of exclusive lock to avoid dead locks
        {
            try {
                /* 初始化（或复制）上下文状态
                简单说，干了两件事，都是设置数据库名：
                一、如果 initFrom 有值，那么获取里面的数据库名，去执行一下，use dbName;
                二、否则从引导设置中，拿到默认的数据库名，再去执行，use dbName;
                这块代码保障了，直接后续直接执行 SQL，不用设置数据库名的问题，可以直接使用。
                 */
                // Init (or copy) context state
                if (!DBUtils.isForTest(configuration)) {
                    this.getDataSource().initializeContextState(monitor, this, initFrom);
                }
            } catch (DBException e) {
                log.warn("Error while initializing context state", e);
            }

            try {
                // 提交事务。我们可以执行init-SQL，这可能会锁定一些资源
                // Commit transaction. We can perform init SQL which potentially may lock some resources
                // 让我们释放他们。
                // Let's free them.
                if (!this.autoCommit) {
                    try (JDBCSession session = openSession(monitor, DBCExecutionPurpose.META, "Start transaction")) {
                        // 禁用日志记录以避免智能提交恢复激活
                        session.enableLogging(false); // Disable logging to avoid smart commit recovery activation
                        // 提交事务
                        session.commit();
                    }
                }
            } catch (Throwable e) {
                log.error("Error ending transaction after context initialize", e);
            }
        }

    }

    protected void disconnect() {
        // [JDBC] Need sync here because real connection close could take some time
        // while UI may invoke callbacks to operate with connection
        synchronized (this) {
            // If we cannot determine if connection is in autocommit mode, assume that it is not
            if (connection != null && !getDataSource().closeConnection(connection, purpose, !isAutoCommit(false))) {
                log.debug("Connection close timeout");
            }
            this.connection = null;
        }
        // Notify QM
        super.closeContext();
    }

    @NotNull
    public Connection getConnection(DBRProgressMonitor monitor) throws SQLException {
        if (connection == null) {
            try {
                connect(monitor);
            } catch (DBCException e) {
                if (e.getCause() instanceof SQLException) {
                    throw (SQLException) e.getCause();
                } else {
                    throw new SQLException(e);
                }
            }
        }
        return connection;
    }

    @NotNull
    @Override
    public JDBCSession openSession(@NotNull DBRProgressMonitor monitor, @NotNull DBCExecutionPurpose purpose, @NotNull String taskTitle) {
        JDBCConnectionImpl jdbcConnection = getDataSource().createConnection(monitor, this, purpose, taskTitle);
        return getProxySession(jdbcConnection);
    }

    @Override
    public void checkContextAlive(DBRProgressMonitor monitor) throws DBException {
        if (!JDBCUtils.isConnectionAlive(getDataSource(), getConnection())) {
            throw new DBCException("Connection is dead");
        }
    }

    @Override
    public boolean isConnected() {
        return connection != null;
    }

    @NotNull
    @Override
    public DBCExecutionContext.InvalidateResult invalidateContext(@NotNull DBRProgressMonitor monitor, boolean closeOnFailure, DBPConnectionConfiguration configuration,boolean shortConnect)
            throws DBException {
        try {
            if (this.connection == null && !shortConnect) {
                connect(monitor);
                return DBCExecutionContext.InvalidateResult.CONNECTED;
            }

            Boolean prevAutocommit = autoCommit;
            Integer txnLevel = transactionIsolationLevel;
            if (!shortConnect){
                closeContext(false);
            }
            // Try to connect again.
            // If connect will fail then context will remain in the list but with null connection.
            // On next invalidate it will try to reopen

            connect(monitor, prevAutocommit, txnLevel, this, shortConnect, configuration != null ? configuration : this.configuration);

            if (configuration != null) {
                this.configuration = configuration;
            }

            return DBCExecutionContext.InvalidateResult.RECONNECTED;
        } finally {
            this.setInterrupted(false);
        }
    }

    @Override
    public String getProcessId() {
        return this.processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }
    @Override
    public void close() {
        this.setInterrupted(true);
        closeContext(true);
    }

    private void closeContext(boolean removeContext) {
        // We remove context before it is actually closed.
        // Because disconnect may (potentially) hang in socket forever
        if (removeContext) {
            // Remove self from context list
            this.instance.removeContext(this);
        }

        disconnect();
    }

    //////////////////////////////////////////////////////////////
    // Transaction manager
    //////////////////////////////////////////////////////////////

    @Override
    public DBPTransactionIsolation getTransactionIsolation()
            throws DBCException {
        if (transactionIsolationLevel == null) {
            if (!txnIsolationLevelReadInProgress) {
                txnIsolationLevelReadInProgress = true;
                try {
                    if (!RuntimeUtils.runTask(monitor -> {
                        try {
                            DBExecUtils.tryExecuteRecover(this, getDataSource(), param -> {
                                try {
                                    transactionIsolationLevel = getConnection().getTransactionIsolation();
                                } catch (Throwable e) {
                                    transactionIsolationLevel = Connection.TRANSACTION_NONE;
                                    log.error("Error getting transaction isolation level", e);
                                }
                            });
                        } catch (DBException e) {
                            throw new InvocationTargetException(e);
                        }
                    }, "Get transaction isolation level", TXN_INFO_READ_TIMEOUT, true)) {
                        throw new DBCException("Can't determine transaction isolation - timeout");
                    }
                } finally {
                    txnIsolationLevelReadInProgress = false;
                }
            }
            if (transactionIsolationLevel == null) {
                transactionIsolationLevel = Connection.TRANSACTION_NONE;
                log.error("Cannot determine transaction isolation level due to connection hanging. Setting to NONE.");
            }
        }
        return JDBCTransactionIsolation.getByCode(transactionIsolationLevel);
    }

    @Override
    public void setTransactionIsolation(@NotNull DBRProgressMonitor monitor, @NotNull DBPTransactionIsolation transactionIsolation)
            throws DBCException {
        if (!(transactionIsolation instanceof JDBCTransactionIsolation)) {
            throw new DBCException(ModelMessages.model_jdbc_exception_invalid_transaction_isolation_parameter);
        }
        JDBCTransactionIsolation jdbcTIL = (JDBCTransactionIsolation) transactionIsolation;
        try {
            getConnection().setTransactionIsolation(jdbcTIL.getCode());
            transactionIsolationLevel = jdbcTIL.getCode();
        } catch (SQLException e) {
            throw new JDBCException(e, this);
        } finally {
            QMUtils.getDefaultHandler().handleTransactionIsolation(this, transactionIsolation);
        }

        //QMUtils.getDefaultHandler().handleTransactionIsolation(getConnection(), jdbcTIL);
    }

    @Override
    public boolean isAutoCommit() throws DBCException {
        if (autoCommit == null) {
            // Run in task with timeout
            if (!RuntimeUtils.runTask(monitor -> {
                try {
                    DBExecUtils.tryExecuteRecover(this, getDataSource(), monitor1 -> {
                        try {
                            autoCommit = getConnection().getAutoCommit();
                        } catch (Exception e) {
                            log.error("Error getting auto commit state", e);
                        }
                    });
                } catch (DBException e) {
                    throw new InvocationTargetException(e);
                }
            }, "Get auto commit state", TXN_INFO_READ_TIMEOUT)) {
                throw new DBCException("Can't determine auto-commit state - timeout");
            }
            if (autoCommit == null) {
                log.error("Cannot determine autocommit state due to connection hanging. Setting to manual commit mode.");
                autoCommit = false;
            }
        }
        return autoCommit;
    }

    public boolean isAutoCommit(boolean defaultValue) {
        try {
            return isAutoCommit();
        } catch (DBCException e) {
            log.debug("Unable to determine if connection is in autocommit mode", e);
            return defaultValue;
        }
    }

    @Override
    public void setAutoCommit(@NotNull DBRProgressMonitor monitor, boolean autoCommit)
            throws DBCException {
        monitor.subTask("Set JDBC connection auto-commit " + autoCommit);
        try {
            Connection dbCon = getConnection();
            dbCon.setAutoCommit(autoCommit);
            this.autoCommit = dbCon.getAutoCommit();
        } catch (SQLException e) {
            throw new JDBCException(e, this);
        } finally {
            QMUtils.getDefaultHandler().handleTransactionAutocommit(this, autoCommit);
        }
    }

    @Override
    public DBCSavepoint setSavepoint(@NotNull DBRProgressMonitor monitor, String name)
            throws DBCException {
        Savepoint savepoint;
        try {
            Connection dbCon = getConnection();
            if (name == null) {
                savepoint = dbCon.setSavepoint();
            } else {
                savepoint = dbCon.setSavepoint(name);
            }
        } catch (SQLException e) {
            throw new DBCException(e, this);
        }
        return new JDBCSavepointImpl(this, savepoint);
    }

    @Override
    public boolean supportsSavepoints() {
        return getDataSource().getInfo().supportsSavepoints();
    }

    @Override
    public void releaseSavepoint(@NotNull DBRProgressMonitor monitor, @NotNull DBCSavepoint savepoint)
            throws DBCException {
        try {
            Connection dbCon = getConnection();
            if (savepoint instanceof JDBCSavepointImpl) {
                dbCon.releaseSavepoint(((JDBCSavepointImpl) savepoint).getOriginal());
            } else if (savepoint instanceof Savepoint) {
                dbCon.releaseSavepoint((Savepoint) savepoint);
            } else {
                throw new SQLFeatureNotSupportedException(ModelMessages.model_jdbc_exception_bad_savepoint_object);
            }
        } catch (SQLException e) {
            throw new JDBCException(e, this);
        }
    }

    @Override
    public void commit()
            throws DBCException {
        try {
            getConnection().commit();
        } catch (SQLException e) {
            throw new JDBCException(e, this);
        }
    }

    public void rollback(@NotNull DBCSession session, DBCSavepoint savepoint)
            throws DBCException {
        try {
            Connection dbCon = getConnection();
            if (savepoint != null) {
                if (savepoint instanceof JDBCSavepointImpl) {
                    dbCon.rollback(((JDBCSavepointImpl) savepoint).getOriginal());
                } else if (savepoint instanceof Savepoint) {
                    dbCon.rollback((Savepoint) savepoint);
                } else {
                    throw new SQLFeatureNotSupportedException(ModelMessages.model_jdbc_exception_bad_savepoint_object);
                }
            } else {
                dbCon.rollback();
            }
        } catch (SQLException e) {
            throw new JDBCException(e, this);
        } finally {
            if (session.isLoggingEnabled()) {
                QMUtils.getDefaultHandler().handleTransactionRollback(this, savepoint);
            }
        }
    }

    @Override
    public void rollback()
            throws DBCException {
        try {
            Connection dbCon = getConnection();
            dbCon.rollback();
        } catch (SQLException e) {
            throw new JDBCException(e, this);
        }
    }

    @Override
    public boolean isSupportsTransactions() {
        return instance.getDataSource().getInfo().supportsTransactions();
    }

    public void reconnect(DBRProgressMonitor monitor) throws DBCException {
        try {
            close();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        connect(monitor, null, null, this, true, configuration);
    }

    @Override
    public <T> T getAdapter(Class<T> adapter) {
        if (adapter == DBCTransactionManager.class) {
            return adapter.cast(this);
        }
        return null;
    }

    @Override
    public String toString() {
        if (CommonUtils.equalObjects(instance.getName(), getDataSource().getName())) {
            return super.toString();
        }
        return getDataSource().getName() + " - " + instance.getName() + " - " + getContextName();
    }
}
