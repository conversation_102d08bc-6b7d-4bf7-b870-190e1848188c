
package com.dc.summer.model.impl.jdbc;

import com.dc.function.RuntimeRunnable;
import com.dc.summer.Log;
import com.dc.summer.model.*;
import com.dc.summer.model.access.DBAAuthModel;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCFactory;
import com.dc.summer.model.exec.jdbc.JDBCStatement;
import com.dc.summer.model.impl.AbstractDataSource;
import com.dc.summer.model.impl.DefaultServerOutputReader;
import com.dc.summer.model.impl.jdbc.exec.JDBCConnectionImpl;
import com.dc.summer.model.impl.jdbc.exec.JDBCFactoryDefault;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.struct.*;
import com.dc.summer.registry.connection.ExternalConnectionProviderRegistry;
import com.dc.summer.utils.RuntimeUtils;
import lombok.Getter;
import org.eclipse.core.runtime.IAdaptable;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.access.DBAAuthCredentials;
import com.dc.summer.model.connection.DBPAuthModelDescriptor;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.runtime.DBRRunnableWithProgress;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.sql.SQLState;
import com.dc.summer.utils.GeneralUtils;
import com.dc.summer.utils.SecurityManagerUtils;
import com.dc.utils.CommonUtils;

import java.io.PrintWriter;
import java.lang.reflect.Field;
import java.net.SocketException;
import java.sql.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * JDBC data source
 */
public abstract class JDBCDataSource extends AbstractDataSource
        implements
        DBPDataTypeProvider,
        DBPErrorAssistant,
        DBPRefreshableObject,
        DBSObject,
        DBSObjectContainer,
        DBSInstanceContainer,
        DBCQueryTransformProvider,
        IAdaptable,
        DBCServerOutputContainer {
    private static final Log log = Log.getLog(JDBCDataSource.class);

    @NotNull
    protected volatile DBPDataSourceInfo dataSourceInfo;
    protected final SQLDialect sqlDialect;
    protected final JDBCFactory jdbcFactory;
    private JDBCRemoteInstance defaultRemoteInstance;

    private int databaseMajorVersion = 0;
    private int databaseMinorVersion = 0;

    private final DBCServerOutputReader serverOutputReader;

    private final transient Set<Connection> closingConnections = Collections.newSetFromMap(new ConcurrentHashMap<>());

    @Getter
    private boolean initContext;

    @Getter
    private DBRProgressMonitor monitor;

    public JDBCDataSource(@NotNull DBRProgressMonitor monitor, @NotNull DBPDataSourceContainer container, @NotNull SQLDialect dialect)
            throws DBException {
        this(monitor, container, dialect, !container.isTemporary());
    }

    public JDBCDataSource(@NotNull DBRProgressMonitor monitor, @NotNull DBPDataSourceContainer container, @NotNull SQLDialect dialect, boolean initContext)
            throws DBException {
        this(container, dialect);
        this.monitor = monitor;
        if (initContext) {
            initializeRemoteInstance(monitor);
        }
        this.initContext = initContext;
    }

    public JDBCDataSource(@NotNull DBPDataSourceContainer container, @NotNull SQLDialect dialect) {
        super(container);
        this.dataSourceInfo = new JDBCDataSourceInfo(container);
        this.sqlDialect = dialect;
        this.jdbcFactory = createJdbcFactory();
        this.serverOutputReader = new DefaultServerOutputReader();
    }

    @NotNull
    @Override
    public JDBCDataSource getDataSource() {
        return this;
    }

    protected void initializeRemoteInstance(@NotNull DBRProgressMonitor monitor) throws DBException {
        this.defaultRemoteInstance = new JDBCRemoteInstance(monitor, this, false);
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    protected Connection openConnection(@NotNull DBRProgressMonitor monitor, @Nullable JDBCExecutionContext context, @NotNull String purpose, @Nullable DBPConnectionConfiguration configuration)
            throws DBCException {
        // 容器中获取驱动
        DBPDriver driver = getContainer().getDriver();

        // 转换配置文件信息，并生成相应的属性
        DBPConnectionConfiguration connectionInfo = configuration != null ? configuration : new DBPConnectionConfiguration(container.getActualConnectionConfiguration());

        Properties connectProps = getAllConnectionProperties(monitor, context, purpose, connectionInfo);

        // 一个适配器，把当前数据源，转成 JDBCConnectionConfigurer
        final JDBCConnectionConfigurer connectionConfigurer = GeneralUtils.adapt(this, JDBCConnectionConfigurer.class);

        // 获取身份验证模型
        DBPAuthModelDescriptor authModelDescriptor = driver.getDataSourceProvider().detectConnectionAuthModel(driver, connectionInfo);
        DBAAuthModel authModel = authModelDescriptor.getInstance();

        boolean isInvalidURL = false;
        // 获取连接url
        final String url = getConnectionURL(connectionInfo);
        log.info("==> ConnectionURL: " + url);

        // 获取连接
        // Obtain connection
        try {

            // 先初始化身份验证（它可能会影响驱动程序属性或驱动程序配置，甚至驱动程序库）
            // Init authentication first (it may affect driver properties or driver configuration or even driver libraries)
            try {
                DBAAuthCredentials credentials = authModel.loadCredentials(getContainer(), connectionInfo);

                if (REFRESH_CREDENTIALS_ON_CONNECT) {
                    // Refresh credentials
                    authModel.refreshCredentials(monitor, getContainer(), connectionInfo, credentials);
                }
                authModel.initAuthentication(monitor, this, credentials, connectionInfo, connectProps);
            } catch (DBException e) {
                throw new DBCException("Authentication error: " + e.getMessage());
            }

            // 调用'在连接之前'的接口，为了子类进行扩展
            if (connectionConfigurer != null) {
                connectionConfigurer.beforeConnection(monitor, connectionInfo, connectProps);
            }

            monitor.subTask("Connecting " + purpose);
            Connection[] connection = new Connection[1];
            Exception[] error = new Exception[1];
            // 从首选项中，获取连接打开超时时间
            int openTimeout = getContainer().getPreferenceStore().getInt(ModelPreferences.CONNECTION_OPEN_TIMEOUT);

            // 它必须是JDBC驱动程序。
            // It MUST be a JDBC driver
            Driver driverInstance = null;
            if (driver.isInstantiable() && !CommonUtils.isEmpty(driver.getDriverClassName())) {
                try {
                    // 驱动的实例
                    driverInstance = getDriverInstance(monitor);
                } catch (DBException e) {
                    throw new DBCConnectException("Can't create driver instance", e, this);
                }
            } else {
                if (!CommonUtils.isEmpty(driver.getDriverClassName())) {
                    try {
                        // 从全局库中，当前库中，加载驱动。
                        driver.loadDriver(monitor);
                        // 指定驱动的 CLass 和 类加载器
                        Class.forName(driver.getDriverClassName(), true, driver.getClassLoader());
                    } catch (Exception e) {
                        throw new DBCException("Driver class '" + driver.getDriverClassName() + "' not found", e);
                    }
                }
            }

            if (driverInstance != null) {
                try {
                    if (!driverInstance.acceptsURL(url)) {
                        // 只要设定目标。一些驱动程序的编码很差，在这里总是返回false。
                        // Just set the mark. Some drivers are poorly coded and always returns false here.
                        isInvalidURL = true;
                    }
                } catch (Throwable e) {
                    log.debug("Error in " + driverInstance.getClass().getName() + ".acceptsURL() - " + url, e);
                }
            }

            final Driver driverInstanceFinal = driverInstance;

            // 加载扩展连接
            DBPExternalConnection externalConnection = null;

            DBPExternalConnectionProvider externalConnectionProvider = ExternalConnectionProviderRegistry.getInstance().getExternalConnectionProvider(connectionInfo);
            if (externalConnectionProvider != null) {
                externalConnection = externalConnectionProvider.openExternalConnection(getContainer(), driverInstance,
                        getInternalConnectionProperties(monitor, getContainer().getDriver(), context, purpose, connectionInfo));
            }

            final DBPExternalConnection finalExternalConnection = externalConnection;

            this.fillConnectionProperties(configuration, connectProps);

            DBRRunnableWithProgress connectTask = monitor1 -> {
                // Set context class loaded to driver class loader
                ClassLoader originalClassLoader = Thread.currentThread().getContextClassLoader();
                Thread.currentThread().setContextClassLoader(driver.getClassLoader());
                long ctm = System.currentTimeMillis();
                try {
                    // Reset DriverManager cache
                    try {
                        Field driversInitializedField = DriverManager.class.getDeclaredField("driversInitialized");
                        driversInitializedField.setAccessible(true);
                        driversInitializedField.set(null, false);
                    } catch (Throwable e) {
                        // Just ignore it
                    }
                    // Open connection
                    if (finalExternalConnection != null) {
                        connection[0] = finalExternalConnection.obtain(url, connectProps);
                    } else if (driverInstanceFinal == null) {
                        connection[0] = DriverManager.getConnection(url, connectProps);
                    } else {
                        connection[0] = driverInstanceFinal.connect(url, connectProps);
                    }
                } catch (Exception e) {
                    error[0] = e;
                } finally {
                    log.info("<== ConnectionTime: " + (System.currentTimeMillis() - ctm) + " ms");
                    if (connectionConfigurer != null) {
                        try {
                            // 调用'在连接之后'的接口，为了子类进行扩展
                            connectionConfigurer.afterConnection(monitor, connectionInfo, connectProps, connection[0], error[0]);
                        } catch (Exception e) {
                            log.debug(e);
                        }
                    }
                    Thread.currentThread().setContextClassLoader(originalClassLoader);
                }
            };

            boolean openTaskFinished;
            // 包装驱动程序操作，就是在真正执行连接任务之前，对本地访问控制，进行相关操作，不必深入研究。
            openTaskFinished = SecurityManagerUtils.wrapDriverActions(getContainer(), authModel, () -> {
                if (openTimeout <= 0) {
                    connectTask.run(monitor);
                    return true;
                } else {
                    return RuntimeUtils.runTask(connectTask, "Opening database connection", openTimeout + 2000);
                }
            });

            if (error[0] != null) {
                throw error[0];
            }
            if (!openTaskFinished) {
                throw new DBCException("Connection has timed out");
            }
            if (connection[0] == null) {
                if (isInvalidURL) {
                    throw new DBCException("Invalid JDBC URL: " + url);
                } else {
                    throw new DBCException("Null connection returned");
                }
            }

            // 设置只读标志
            // Set read-only flag
            if (container.isConnectionReadOnly() && !isConnectionReadOnlyBroken()) {
                connection[0].setReadOnly(true);
            }

            return connection[0];
        } catch (SQLException ex) {
            throw new DBCConnectException(ex.getMessage(), ex, this);
        } catch (DBCException ex) {
            throw ex;
        } catch (Throwable e) {
            throw new DBCConnectException("Unexpected driver error occurred while connecting to the database", e);
        } finally {
            // 结束身份验证
            authModel.endAuthentication(container, connectionInfo, connectProps);
        }
    }

    protected void fillConnectionProperties(DBPConnectionConfiguration configuration, Properties connectProps) throws DBCException {
        if (configuration != null && configuration.getProperties() != null) {
            if (dataSourceInfo.isRepeatProperties(configuration)) {
                throw new DBCException("驱动属性与基础信息配置冲突，请修改后再次尝试");
            }
            for (Map.Entry<String, String> prop : configuration.getProperties().entrySet()) {
                connectProps.setProperty(CommonUtils.toString(prop.getKey()), CommonUtils.toString(prop.getValue()));
            }
        }
    }

    protected Properties getAllConnectionProperties(@NotNull DBRProgressMonitor monitor, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration connectionInfo) throws DBCException {
        // Set properties
        Properties connectProps = new Properties();

        // Use properties defined by datasource itself
        Map<String, String> internalProps = getInternalConnectionProperties(monitor, getContainer().getDriver(), context, purpose, connectionInfo);
        if (internalProps != null) {
            connectProps.putAll(internalProps);
        }

        return connectProps;
    }

    /**
     * Closes JDBC connection.
     * Do actual close in separate thread.
     * After ModelPreferences.CONNECTION_CLOSE_TIMEOUT delay returns false.
     *
     * @return true on successful connection close
     */
    public boolean closeConnection(final Connection connection, String purpose, boolean doRollback) {
        if (connection != null) {
            boolean alreadyClosing = !closingConnections.add(connection);
            if (alreadyClosing) {
                return true;
            }
            // Close datasource (in async task)
            return RuntimeUtils.runTask(monitor -> {
                        if (doRollback) {
                            try {
                                // If we in transaction - rollback it.
                                // Any valuable transaction changes should be committed by UI
                                // so here we do it just in case to avoid error messages on close with open transaction
                                connection.rollback();
                            } catch (Throwable e) {
                                // Do not write warning because connection maybe broken before the moment of close
                                log.debug("Error closing active transaction", e);
                            }
                        }
                        try {
                            SecurityManagerUtils.wrapDriverActions(getContainer(), null, () -> {
                                connection.close();
                                return null;
                            });
                        } catch (Throwable ex) {
                            log.debug("Error closing connection", ex);
                        }
                        closingConnections.remove(connection);
                    }, "Close JDBC connection (" + purpose + ")",
                    getContainer().getPreferenceStore().getInt(ModelPreferences.CONNECTION_CLOSE_TIMEOUT));
        } else {
            log.debug("Null connection parameter");
            return true;
        }
    }

/*
    @Override
    public JDBCSession openSession(DBRProgressMonitor monitor, DBCExecutionPurpose purpose, String taskTitle)
    {
        if (metaContext != null && (purpose == DBCExecutionPurpose.META || purpose == DBCExecutionPurpose.META_DDL)) {
            return createConnection(monitor, this.metaContext, purpose, taskTitle);
        }
        return createConnection(monitor, executionContext, purpose, taskTitle);
    }
*/

    protected void initializeContextState(@NotNull DBRProgressMonitor monitor, @NotNull JDBCExecutionContext context, JDBCExecutionContext initFrom) throws DBException {

    }

    @NotNull
    protected JDBCConnectionImpl createConnection(
            @NotNull DBRProgressMonitor monitor,
            @NotNull JDBCExecutionContext context,
            @NotNull DBCExecutionPurpose purpose,
            @NotNull String taskTitle) {
        return new JDBCConnectionImpl(context, monitor, purpose, taskTitle);
    }

    @NotNull
    @Override
    public DBPDataSourceInfo getInfo() {
        return dataSourceInfo;
    }

    @NotNull
    @Override
    public SQLDialect getSQLDialect() {
        return sqlDialect;
    }

    @NotNull
    public JDBCFactory getJdbcFactory() {
        return jdbcFactory;
    }

    @NotNull
    @Override
    public JDBCRemoteInstance getDefaultInstance() {
        return defaultRemoteInstance;
    }

    @NotNull
    @Override
    public List<? extends JDBCRemoteInstance> getAvailableInstances() {
        JDBCRemoteInstance defaultInstance = getDefaultInstance();
        return defaultInstance == null ?
                Collections.emptyList() :
                Collections.singletonList(defaultInstance);
    }

    @Override
    public void shutdown(DBRProgressMonitor monitor) {
        try {
            for (JDBCRemoteInstance instance : getAvailableInstances()) {
                Object exclusiveLock = instance.getExclusiveLock().acquireExclusiveLock();
                try {
                    monitor.subTask("Disconnect from '" + instance.getName() + "'");
                    instance.shutdown(monitor);
                    monitor.worked(1);
                } finally {
                    instance.getExclusiveLock().releaseExclusiveLock(exclusiveLock);
                }
            }
            defaultRemoteInstance = null;
        } finally {
            DBPConnectionConfiguration configuration = getContainer().getActualConnectionConfiguration();
            DBPExternalConnectionProvider externalConnectionProvider = ExternalConnectionProviderRegistry.getInstance().getExternalConnectionProvider(configuration);
            if (externalConnectionProvider != null) {
                monitor.subTask("Close External connection");
                externalConnectionProvider.closeExternalConnection(getContainer());
            }
        }
    }

    @Override
    public void initialize(@NotNull DBRProgressMonitor monitor)
            throws DBException {
        // 初始化元上下文
        getDefaultInstance().initializeMetaContext(monitor);
        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, ModelMessages.model_jdbc_read_database_meta_data)) {
            /*
             DatabaseMetaData和ResultSetMetaData就是两个常用的获取数据库元数据相关信息的接口。
             这里有个 DatabaseMetaData，主要是数据库相关元数据：
             获取数据库信息，获取数据库所有Schema，获取某用户下所有的表，获取表信息，获取表主键信息，获取表索引信息，获取表中列值信息。
             */
            JDBCDatabaseMetaData metaData = session.getMetaData();

            // 读取数据库服务器版本
            readDatabaseServerVersion(metaData);

            // MySQLDialect。SQL 方言用来做语法分析。
            if (this.sqlDialect instanceof JDBCSQLDialect) {
                try {
                    // 初始化驱动程序设置
                    ((JDBCSQLDialect) this.sqlDialect).initDriverSettings(session, this, metaData);
                } catch (Throwable e) {
                    log.error("Error initializing dialect driver settings", e);
                }
            }

            try {
                // 创建数据源信息
                dataSourceInfo = createDataSourceInfo(monitor, metaData);
            } catch (Throwable e) {
                log.error("Error obtaining database info", e);
            }
        } catch (SQLException ex) {
            throw new DBException("Error getting JDBC meta data", ex, this);
        } finally {
            if (dataSourceInfo == null) {
                log.warn("NULL datasource info was created");
                dataSourceInfo = new JDBCDataSourceInfo(container);
            }
        }
    }

    protected void readDatabaseServerVersion(DatabaseMetaData metaData) {
        if (databaseMajorVersion <= 0 && databaseMinorVersion <= 0) {
            try {
                databaseMajorVersion = metaData.getDatabaseMajorVersion();
            } catch (Throwable e) {
                log.error("Error determining server major version: " + e.getMessage());
            }
            try {
                databaseMinorVersion = metaData.getDatabaseMinorVersion();
            } catch (Throwable e) {
                log.error("Error determining server minor version: " + e.getMessage());
            }
        }
    }

    public boolean isServerVersionAtLeast(int major, int minor) {
        if (databaseMajorVersion < major) {
            return false;
        } else return databaseMajorVersion != major || databaseMinorVersion >= minor;
    }

    public boolean isDriverVersionAtLeast(int major, int minor) {
        try {
            Driver driver = getDriverInstance(new VoidProgressMonitor());
            int majorVersion = driver.getMajorVersion();
            if (majorVersion < major) {
                return false;
            } else return majorVersion != major || driver.getMinorVersion() >= minor;
        } catch (DBException e) {
            log.debug("Can't obtain driver instance", e);
            return false;
        }
    }

    @Override
    public DBSObject refreshObject(@NotNull DBRProgressMonitor monitor) throws DBException {
        this.dataSourceInfo = new JDBCDataSourceInfo(container);
        return this;
    }

    protected JDBCExecutionContext createExecutionContext(JDBCRemoteInstance instance, String type) {
        return new JDBCExecutionContext(instance, type);
    }

    @Nullable
    @Override
    public DBCQueryTransformer createQueryTransformer(@NotNull DBCQueryTransformType type) {
//        if (type == DBCQueryTransformType.ORDER_BY) {
//
//        } else if (type == DBCQueryTransformType.FILTER) {
//
//        }
        return null;
    }

    private static int getValueTypeByTypeName(@NotNull String typeName, int valueType) {
        // [JDBC: SQLite driver uses VARCHAR value type for all LOBs]
        if (valueType == Types.OTHER || valueType == Types.VARCHAR) {
            if ("BLOB".equalsIgnoreCase(typeName)) {
                return Types.BLOB;
            } else if ("CLOB".equalsIgnoreCase(typeName)) {
                return Types.CLOB;
            } else if ("NCLOB".equalsIgnoreCase(typeName)) {
                return Types.NCLOB;
            }
        } else if (valueType == Types.BIT) {
            // Workaround for MySQL (and maybe others) when TINYINT(1) == BOOLEAN
            if ("TINYINT".equalsIgnoreCase(typeName)) {
                return Types.TINYINT;
            }
        }
        return valueType;
    }

    @NotNull
    public DBPDataKind resolveDataKind(@NotNull String typeName, int valueType) {
        return getDataKind(typeName, valueType);
    }

    @NotNull
    public static DBPDataKind getDataKind(@NotNull String typeName, int valueType) {
        switch (getValueTypeByTypeName(typeName, valueType)) {
            case Types.BOOLEAN:
                return DBPDataKind.BOOLEAN;
            case Types.CHAR:
            case Types.VARCHAR:
            case Types.NVARCHAR:
            case Types.LONGVARCHAR:
            case Types.LONGNVARCHAR:
                return DBPDataKind.STRING;
            case Types.BIGINT:
            case Types.DECIMAL:
            case Types.DOUBLE:
            case Types.FLOAT:
            case Types.INTEGER:
            case Types.NUMERIC:
            case Types.REAL:
            case Types.SMALLINT:
                return DBPDataKind.NUMERIC;
            case Types.BIT:
            case Types.TINYINT:
                if (typeName.toLowerCase().contains("bool")) {
                    // Declared as numeric but actually it's a boolean
                    return DBPDataKind.BOOLEAN;
                }
                return DBPDataKind.NUMERIC;
            case Types.DATE:
            case Types.TIME:
            case Types.TIME_WITH_TIMEZONE:
            case Types.TIMESTAMP:
            case Types.TIMESTAMP_WITH_TIMEZONE:
                return DBPDataKind.DATETIME;
            case Types.BINARY:
            case Types.VARBINARY:
            case Types.LONGVARBINARY:
                return DBPDataKind.BINARY;
            case Types.BLOB:
            case Types.CLOB:
            case Types.NCLOB:
                return DBPDataKind.CONTENT;
            case Types.SQLXML:
                return DBPDataKind.CONTENT;
            case Types.STRUCT:
                return DBPDataKind.STRUCT;
            case Types.ARRAY:
                return DBPDataKind.ARRAY;
            case Types.ROWID:
                return DBPDataKind.ROWID;
            case Types.REF:
                return DBPDataKind.REFERENCE;
            case Types.OTHER:
                // TODO: really?
                return DBPDataKind.OBJECT;
        }
        return DBPDataKind.UNKNOWN;
    }

    @Nullable
    @Override
    public DBSDataType resolveDataType(@NotNull DBRProgressMonitor monitor, @NotNull String typeFullName) throws DBException {
        return getLocalDataType(typeFullName);
    }

    @Override
    public DBSDataType getLocalDataType(int typeID) {
        for (DBSDataType dataType : getLocalDataTypes()) {
            if (dataType.getTypeID() == typeID) {
                return dataType;
            }
        }
        return null;
    }

    @Override
    public String getDefaultDataTypeName(@NotNull DBPDataKind dataKind) {
        String typeName = getStandardSQLDataTypeName(dataKind);
        DBSDataType dataType = getLocalDataType(typeName);
        if (dataType == null) {
            // No such data type
            // Try to find first data type of this kind
            for (DBSDataType type : getLocalDataTypes()) {
                if (type.getDataKind() == dataKind) {
                    return type.getName();
                }
            }
        }
        return typeName;
    }

    @NotNull
    protected String getStandardSQLDataTypeName(@NotNull DBPDataKind dataKind) {
        switch (dataKind) {
            case BOOLEAN:
                return "BOOLEAN";
            case NUMERIC:
                return "NUMERIC";
            case STRING:
                return "VARCHAR";
            case DATETIME:
                return "TIMESTAMP";
            case BINARY:
                return "BLOB";
            case CONTENT:
                return "BLOB";
            case STRUCT:
                return "VARCHAR";
            case ARRAY:
                return "VARCHAR";
            case OBJECT:
                return "VARCHAR";
            case REFERENCE:
                return "VARCHAR";
            case ROWID:
                return "ROWID";
            case ANY:
                return "VARCHAR";
            default:
                return "VARCHAR";
        }
    }

    /////////////////////////////////////////////////
    // Overridable functions

    protected boolean isConnectionReadOnlyBroken() {
        return false;
    }

    @Nullable
    protected Driver getDriverInstance(@NotNull DBRProgressMonitor monitor)
            throws DBException {
        return container.getDriver().getDriverInstance(monitor);
    }

    /**
     * Could be overridden by extenders. May contain any additional connection properties.
     * Note: these properties may be overwritten by connection advanced properties.
     *
     * @return predefined connection properties
     */
    @Nullable
    protected Map<String, String> getInternalConnectionProperties(DBRProgressMonitor monitor, DBPDriver driver, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration connectionInfo)
            throws DBCException {
        return null;
    }

    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, @NotNull JDBCDatabaseMetaData metaData) {
        return new JDBCDataSourceInfo(metaData);
    }

    @NotNull
    protected JDBCFactory createJdbcFactory() {
        return new JDBCFactoryDefault();
    }

    /////////////////////////////////////////////////
    // Error assistance

    @Override
    public ErrorType discoverErrorType(@NotNull Throwable error) {
        String sqlState = SQLState.getStateFromException(error);
        if (sqlState != null) {
            if (SQLState.SQL_08000.getCode().equals(sqlState) ||
                    SQLState.SQL_08001.getCode().equals(sqlState) ||
                    SQLState.SQL_08003.getCode().equals(sqlState) ||
                    SQLState.SQL_08006.getCode().equals(sqlState) ||
                    SQLState.SQL_08007.getCode().equals(sqlState) ||
                    SQLState.SQL_08S01.getCode().equals(sqlState)) {
                return ErrorType.CONNECTION_LOST;
            }
            if (SQLState.SQL_23000.getCode().equals(sqlState) ||
                    SQLState.SQL_23505.getCode().equals(sqlState)) {
                return ErrorType.UNIQUE_KEY_VIOLATION;
            }
        }
        Throwable rootCause = GeneralUtils.getRootCause(error);
        if (rootCause instanceof SocketException ||
                rootCause instanceof SQLRecoverableException) {
            return ErrorType.CONNECTION_LOST;
        }
        if (error instanceof DBCConnectException) {
            if (rootCause instanceof ClassNotFoundException) {
                // Looks like bad driver configuration
                return ErrorType.DRIVER_CLASS_MISSING;
            }
        }

        return ErrorType.NORMAL;
    }

    @Override
    public ErrorType discoverErrorType(String message) {
        return ErrorType.NORMAL;
    }

    @Nullable
    @Override
    public ErrorPosition[] getErrorPosition(@NotNull DBRProgressMonitor monitor, @NotNull DBCExecutionContext context, @NotNull String query, @NotNull Throwable error) {
        return null;
    }

    @Override
    public <T> T getAdapter(Class<T> adapter) {
        if (adapter == DBCTransactionManager.class) {
            return adapter.cast(DBUtils.getDefaultContext(getDefaultInstance(), false));
        } else if (adapter == DBCQueryTransformProvider.class) {
            return adapter.cast(this);
        }
        return null;
    }

    /////////////////////////////////////////////////
    // DBDFormatSettings

    public void cancelStatementExecute(DBRProgressMonitor monitor, JDBCStatement statement) throws DBException {
        try {
            statement.cancel();
        } catch (SQLException e) {
            throw new DBException(e, this);
        }
    }

    public void killConnection(DBRProgressMonitor monitor, JDBCExecutionContext executionContext) throws DBException {
        if (executionContext.isInterrupted()) {
            return;
        }
        try {
            String processId = executionContext.getProcessId();
            if (processId == null) {
                throw new NullPointerException("connection [" + executionContext.getContextId() + "] processId is null.");
            }
            JDBCExecutionContext defaultContext = getDefaultInstance().getDefaultContext(monitor, true);
            RuntimeRunnable runnable = killConnection(monitor, defaultContext, processId);
            if (runnable != null) {
                runnable.run();
            } else {
                throw new DBCConnectException("not supports this kill connection method.", null);
            }
        } catch (Exception e) {
            throw new DBException("error kill connection, " + e.getMessage(), e, this);
        } finally {
            executionContext.setInterrupted(true);
        }
    }

    protected RuntimeRunnable killConnection(DBRProgressMonitor monitor, JDBCExecutionContext defaultContext, String processId) {
        return null;
    }

    protected static String killConnection() {
        return "Kill Connection";
    }

    @Override
    public List<String> readServerOutput(DBRProgressMonitor monitor, DBCExecutionContext context, DBCExecutionResult executionResult, DBCStatement statement, PrintWriter output) throws DBCException {
        return serverOutputReader.readServerOutput(monitor, context, executionResult, statement, output);
    }
}
