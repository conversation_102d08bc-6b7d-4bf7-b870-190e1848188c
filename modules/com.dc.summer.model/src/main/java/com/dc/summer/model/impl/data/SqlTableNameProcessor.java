package com.dc.summer.model.impl.data;

import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.data.DBDAttributeBindingProcessor;
import com.dc.summer.model.impl.jdbc.exec.JDBCColumnMetaData;
import com.dc.summer.model.sql.SqlFieldData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class SqlTableNameProcessor implements DBDAttributeBindingProcessor {

    private final List<SqlFieldData> sqlFieldDataList;

    public SqlTableNameProcessor(List<SqlFieldData> sqlFieldDataList) {
        this.sqlFieldDataList = sqlFieldDataList;
    }

    @Override
    public void processAndBind(DBDAttributeBinding[] bindings) {

        if (CollectionUtils.isEmpty(sqlFieldDataList)) {
            return;
        }

        // 是否三层架构由parser传过来的sqlFieldDataList判断，不从MetaData获取
        String catalogName = sqlFieldDataList.get(0).getCatalogName();

        boolean jdbcSupportedTableName = true;

        for (DBDAttributeBinding binding : bindings) {
            String concatTableName = null;
            if (binding.getMetaAttribute() instanceof JDBCColumnMetaData) {
                JDBCColumnMetaData metaAttribute = (JDBCColumnMetaData) binding.getMetaAttribute();
                concatTableName = makeConcatTableName(catalogName, metaAttribute.getSchemaName(), metaAttribute.getEntityName());
                if (StringUtils.isNotBlank(concatTableName)) {
                    binding.getTableNames().add(concatTableName);
                }
            }
            jdbcSupportedTableName &= StringUtils.isNotBlank(concatTableName);
        }

        // 如果 jdbc 支持获取表名，那么不需要后续的匹配
        if (jdbcSupportedTableName) {
            return;
        }

        int length = bindings.length;

        // 未处理的绑定下标
        Set<Integer> indexes = new HashSet<>();

        // 全尺寸匹配，需要鉴定真实名和别名
        if (length == sqlFieldDataList.size()) {

            for (int i = 0; i < bindings.length; i++) {
                DBDAttributeBinding binding = bindings[i];
                SqlFieldData sqlFieldData = sqlFieldDataList.get(i);
                if (binding.getName().equalsIgnoreCase(sqlFieldData.getFieldName()) ||
                        binding.getLabel().equalsIgnoreCase(sqlFieldData.getFieldAlias())) {
                    String makeConcatTableName = makeConcatTableName(sqlFieldData.getCatalogName(), sqlFieldData.getSchemaName(), sqlFieldData.getTableName());
                    binding.getTableNames().add(makeConcatTableName);
                } else {
                    indexes.add(i);
                }
            }

        }
        // 尺寸不匹配
        else {
            IntStream.range(0, length).forEach(indexes::add);
        }

        if (CollectionUtils.isNotEmpty(indexes)) {

            Map<String, List<SqlFieldData>> groupAlias = sqlFieldDataList
                    .stream()
                    .filter(sqlFieldData -> StringUtils.isNotBlank(sqlFieldData.getFieldAlias()))
                    .collect(Collectors.groupingBy(sqlFieldData -> sqlFieldData.getFieldAlias().toUpperCase(Locale.ROOT)));

            Map<String, List<SqlFieldData>> groupField = sqlFieldDataList
                    .stream()
                    .filter(sqlFieldData -> StringUtils.isNotBlank(sqlFieldData.getFieldName()))
                    .collect(Collectors.groupingBy(sqlFieldData -> sqlFieldData.getFieldName().toUpperCase(Locale.ROOT)));

            for (Integer index : indexes) {

                DBDAttributeBinding binding = bindings[index];

                List<SqlFieldData> labelData = groupAlias.get(binding.getMetaAttribute().getLabel().toUpperCase(Locale.ROOT));
                List<SqlFieldData> nameData = groupField.get(binding.getMetaAttribute().getName().toUpperCase(Locale.ROOT));

                // 1. 匹配唯一真实名
                if (CollectionUtils.isNotEmpty(nameData) && nameData.size() == 1) {
                    for (SqlFieldData sqlFieldData : nameData) {
                        String makeConcatTableName = makeConcatTableName(sqlFieldData.getCatalogName(), sqlFieldData.getSchemaName(), sqlFieldData.getTableName());
                        binding.getTableNames().add(makeConcatTableName);
                    }
                }
                // 2. 匹配唯一别名
                else if (CollectionUtils.isNotEmpty(labelData) && labelData.size() == 1) {
                    for (SqlFieldData sqlFieldData : labelData) {
                        String makeConcatTableName = makeConcatTableName(sqlFieldData.getCatalogName(), sqlFieldData.getSchemaName(), sqlFieldData.getTableName());
                        binding.getTableNames().add(makeConcatTableName);
                    }
                }
                // 3. 给定默认值
                else {
                    binding.getTableNames().clear();
                }

            }

        }

    }

    private static String makeConcatTableName(String catalogName, String schemaName, String tableName) {
        if (StringUtils.isNotBlank(catalogName) && StringUtils.isNotBlank(schemaName)) {
            return schemaName + "." + tableName;
        } else {
            return tableName;
        }
    }

}
