
package com.dc.summer.model.impl;

import com.dc.summer.ModelPreferences;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.*;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.Log;
import com.dc.summer.model.connection.DBPConnectionBootstrap;
import com.dc.summer.model.impl.preferences.SimplePreferenceStore;
import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.summer.model.proxy.DBPHandlerProxy;
import com.dc.summer.model.qm.QMUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.utils.GeneralUtils;
import com.dc.utils.CommonUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.lang.reflect.Constructor;
import java.util.*;

/**
 * Abstract execution context.
 * All regular DBCExecutionContext implementations should extend this class.
 * It provides bootstrap init functions and QM notifications.
 */
public abstract class AbstractExecutionContext<DATASOURCE extends DBPDataSource> implements DBCExecutionContext {
    private static final Log log = Log.getLog(AbstractExecutionContext.class);

    private static long idSequence = 0;

    @NotNull
    private final DATASOURCE dataSource;
    protected final String purpose;
    protected final long id;
    private final Map<String, Object> contextAttributes = new LinkedHashMap<>();

    private final String sessionId;
    private volatile Class<? extends DBPHandlerProxy<?>> sessionProxy;

    private String charset;

    private volatile boolean isInterrupted;

    private final DBPPreferenceStore preferenceStore;

    public AbstractExecutionContext(@NotNull DATASOURCE dataSource, String purpose) {
        this.dataSource = dataSource;
        this.purpose = purpose;
        this.id = generateContextId();
        this.sessionId = UUID.randomUUID().toString().replaceAll("-", "_");
        this.preferenceStore = new SimplePreferenceStore(ModelPreferences.getPreferences()) {
            @Override
            public void save() throws IOException {
            }
        };
        log.debug("Execution context opened (" + dataSource.getName() + "; " + purpose + "; " + this.id + ")");
    }

    @Override
    public String getSessionId() {
        return sessionId;
    }

    public static synchronized long generateContextId() {
        return idSequence++;
    }

    @Override
    public long getContextId() {
        return this.id;
    }

    @NotNull
    @Override
    public String getContextName() {
        return purpose;
    }

    @NotNull
    @Override
    public DATASOURCE getDataSource() {
        return dataSource;
    }

    @Nullable
    @Override
    public DBCExecutionContextDefaults getContextDefaults() {
        return null;
    }

    @NotNull
    protected DBPConnectionBootstrap getBootstrapSettings() {
        return getDataSource().getContainer().getActualConnectionConfiguration().getBootstrap();
    }

    @Override
    public void setSessionProxy(Class<? extends DBPHandlerProxy<?>> sessionProxy) {
        this.sessionProxy = sessionProxy;
    }

    protected final <T> T getProxySession(T session) {
        if (this.sessionProxy != null) {
            try {
                Constructor<? extends DBPHandlerProxy<?>> constructor = this.sessionProxy.getDeclaredConstructor(DBCSession.class, DBPConnectionConfiguration.class);
                DBPHandlerProxy<?> proxy = constructor.newInstance(session, getConfiguration());
                return (T) proxy.getInstance();
            } catch (Exception e) {
                log.error("获取代理Session失败！", e);
            }
        }
        return session;
    }

    /**
     * 上下文引导过程。
     * 执行引导查询和其他初始化函数。
     * 所有实现都必须调用此函数。
     * Context boot procedure.
     * Executes bootstrap queries and other init functions.
     * This function must be called by all implementations.
     */
    protected List<Map<String, Object>> initContextBootstrap(@NotNull DBRProgressMonitor monitor, boolean autoCommit) throws DBCException {
        return execContextBootstrap(monitor, autoCommit);
    }

    protected List<Map<String, Object>> execContextBootstrap(@NotNull DBRProgressMonitor monitor, boolean autoCommit) throws DBCException {
        // 获取查询管理处理器，处理上下文打开
        // Notify QM
//        QMUtils.getDefaultHandler().handleContextOpen(this, !autoCommit);

        /*
         执行引导查询。虽然这里没有执行，但是，作者设计这块的思路，应该是，用来在初始化的时候，做一个自定义的查询操作。
         经过排查，源头来自于配置文件，通过打打开连接的过程中，解析变量中读取。
         */
        // Execute bootstrap queries
        DBPConnectionBootstrap bootstrap = getBootstrapSettings();
        List<String> initQueries = bootstrap.getInitQueries();
        List<Map<String, Object>> queryResults = new ArrayList<>(initQueries.size());
        if (!CommonUtils.isEmpty(initQueries)) {
            try (DBCSession session = openSession(monitor, DBCExecutionPurpose.UTIL, "Run bootstrap queries")) {
                if (initQueries.isEmpty()) {
                    return queryResults;
                }
                for (String query : initQueries) {
                    // 替换变量，然后进行执行操作。
                    // Replace variables
                    query = GeneralUtils.replaceVariables(query, getDataSource().getContainer().getVariablesResolver(true));
                    try (DBCStatement dbStat = session.prepareStatement(DBCStatementType.SCRIPT, query, false, false, false)) {
                        dbStat.executeStatement();
                        try (DBCResultSet resultSet = dbStat.openResultSet()) {
                            while (resultSet.nextRow()) {
                                DBCResultSetMetaData meta = resultSet.getMeta();
                                List<DBCAttributeMetaData> attributes = meta.getAttributes();
                                Map<String, Object> queryMap = new HashMap<>();
                                for (DBCAttributeMetaData attribute : attributes) {
                                    String name = attribute.getName();
                                    Object value = resultSet.getAttributeValue(name);
                                    queryMap.put(name, value);
                                }
                                queryResults.add(queryMap);
                            }
                        }
                    } catch (Exception e) {
                        String message = "Error executing bootstrap query: " + query;
                        if (bootstrap.isIgnoreErrors()) {
                            log.warn(message);
                        } else {
                            throw new DBCException(message, e, this);
                        }
                    }
                }
            }
        }
        return queryResults;
    }

    protected void closeContext() {
        QMUtils.getDefaultHandler().handleContextClose(this);

        log.debug("Execution context closed (" + getDataSource().getName() + ", " + this.id + ")");
    }

    @Override
    public Map<String, ?> getContextAttributes() {
        return new LinkedHashMap<>(contextAttributes);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T getContextAttribute(String attributeName) {
        return (T) contextAttributes.get(attributeName);
    }

    @Override
    public <T> void setContextAttribute(String attributeName, T attributeValue) {
        contextAttributes.put(attributeName, attributeValue);
    }

    @Override
    public void removeContextAttribute(String attributeName) {
        contextAttributes.remove(attributeName);
    }

    @Override
    public void setCharset(String charset) {
        this.charset = charset;
    }

    @Override
    public String getCharset() {
        if (StringUtils.isBlank(charset)) {
            charset = getDataSource().getContainer().getActualConnectionConfiguration().getCharset();
        }
        return charset;
    }

    @Override
    public String toString() {
        return getDataSource().getName() + " " + purpose;
    }

    @Override
    public DBPPreferenceStore getPreferenceStore() {
        return preferenceStore;
    }

    @Override
    public boolean isInterrupted() {
        return isInterrupted;
    }
    @Override
    public void setInterrupted(boolean isInterrupted) {
        this.isInterrupted = isInterrupted;
    }

}
