
package com.dc.summer.model.exec;

import com.dc.summer.model.DBPContextWithAttributes;
import com.dc.summer.model.DBPDataSource;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPCloseableObject;
import com.dc.summer.model.DBPObject;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.summer.model.proxy.DBPHandlerProxy;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSInstance;

/**
 * Execution context.
 * Provides access to execution sessions.
 * Usually contains some kind of physical database connection inside
 */
public interface DBCExecutionContext extends DBPObject,DBPCloseableObject, DBPContextWithAttributes, DBCExecutionCharset
{
    enum InvalidateResult {
        DISCONNECTED,
        CONNECTED,
        RECONNECTED,
        ALIVE,
        ERROR
    }

    /**
     * Unique context ID. Generated in the moment of context creation and never changes during context lifetime.
     */
    long getContextId();

    /**
     * Context name. Like MAin, Metadata, Script X, etc.
     */
    @NotNull
    String getContextName();

    /**
     * Owner datasource
     */
    @NotNull
    DBPDataSource getDataSource();

    DBSInstance getOwnerInstance();

    /**
     * Checks this context is really connected to remote database.
     * Usually DBPDataSourceContainer.getDataSource() returns datasource only if datasource is connected.
     * But in some cases (e.g. connection invalidation) datasource remains disconnected for some period of time.
     */
    boolean isConnected();

    /**
     * Opens new session
     * @param monitor progress monitor
     * @param purpose context purpose
     * @param task task description
     * @return execution context
     */
    @NotNull
    DBCSession openSession(@NotNull DBRProgressMonitor monitor, @NotNull DBCExecutionPurpose purpose, @NotNull String task);

    void setSessionProxy(Class<? extends DBPHandlerProxy<?>> sessionProxy);

    /**
     * Checks whether this context is alive and underlying network connection isn't broken.
     * Implementation should perform server round-trip.
     * This function is also used for keep-alive function.
     * @param monitor    monitor
     * @throws DBException on any network errors
     */
    void checkContextAlive(DBRProgressMonitor monitor)
        throws DBException;

    /**
     * Checks context is alive and reconnects if needed.
     *
     * @throws DBException on any error
     * @param monitor progress monitor
     * @param closeOnFailure
     * @return true if reconnect was applied false if connection is alive and nothing was done.
     */
    @NotNull
    InvalidateResult invalidateContext(@NotNull DBRProgressMonitor monitor, boolean closeOnFailure, DBPConnectionConfiguration configuration,boolean shortConnect) throws DBException;

    /**
     * Defaults reader/writer.
     * @return null if defaults are not supported
     */
    @Nullable
    DBCExecutionContextDefaults getContextDefaults();

    /**
     * 获取进程ID，用来强制关闭。
     */
    String getProcessId();

    /**
     * 执行首选项，每个数据库上下文的解析方法，和执行内容，由各自实现。
     * @param prefs 首选项串
     */
    void execPrefs(DBRProgressMonitor monitor, String prefs) throws DBCException;

    DBPConnectionConfiguration getConfiguration();

    String getSessionId();

    @NotNull
    DBPPreferenceStore getPreferenceStore();

    boolean isInterrupted();

    void setInterrupted(boolean interrupted);
}
