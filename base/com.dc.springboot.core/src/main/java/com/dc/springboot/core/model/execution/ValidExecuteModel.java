package com.dc.springboot.core.model.execution;

import com.dc.springboot.core.model.log.SensitiveAuthDetail;
import com.dc.springboot.core.model.log.SqlHistory;
import com.dc.springboot.core.model.log.SqlRecord;
import com.dc.springboot.core.model.parser.dto.ActionModelDto;
import com.dc.springboot.core.model.type.ReviewType;
import com.dc.summer.model.sql.SqlFieldData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * SQL窗口等执行用，带各种链。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("有效执行模型")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ValidExecuteModel extends BatchExecuteModel {

    @Valid
    @NotNull
    @ApiModelProperty(value = "权限模型", required = true)
    private PermissionModel permissionModel;

    @Valid
    @ApiModelProperty(value = "SQL历史", hidden = true)
    private SqlHistory sqlHistory;

    @ApiModelProperty(value = "敏感审计", hidden = true)
    private List<SensitiveAuthDetail> sensitiveAuthDetailList;

    @Valid
    @NotNull
    @ApiModelProperty(value = "SQL记录", required = true)
    private SqlRecord sqlRecord;

    @ApiModelProperty(value = "权限字典动作（自定义告警）", example = "select,insert")
    private List<String> allOperations;

    @ApiModelProperty(value = "sql中是否含有where条件（自定义告警）", example = "id")
    private String whereClause;

    @ApiModelProperty(value = "数据库类型", hidden = true)
    private Integer dbType;

    @ApiModelProperty(value = "来源", example = "1")
    private Integer origin;

    @ApiModelProperty(value = "操作列表")
    private List<ActionModelDto> actionList;

    @ApiModelProperty(value = "可以导出", example = "true")
    private boolean canExport;

    @ApiModelProperty(value = "无权限对象对应的信息", allowEmptyValue = true)
    private List<Map<String, Object>> noPermissionObjects;

    @ApiModelProperty(value = "使用场景 - 是视图还是表")
    private int scene;

    @ApiModelProperty(value = "可以生成SQL")
    private boolean canGenerateSql;

    @ApiModelProperty(value = "真实的schema", example = "SCOTT")
    private String singleSchema;

    @ApiModelProperty(value = "可以查询总行数", example = "true")
    private boolean canCountTotal;

    @ApiModelProperty(value = "是否需要同步scheme")
    private boolean needSyncSchema;

    @ApiModelProperty(value = "包含DB链接")
    private boolean containsDblink;

    @ApiModelProperty(value = "不记录访问频次", hidden = true)
    private boolean notRecordFrequency;

    @ApiModelProperty(value = "复核", example = "false")
    private boolean needReview;

    @ApiModelProperty(value = "复核类型", example = "0", required = true)
    private Integer reviewType = ReviewType.OTHER.getValue();

    @ApiModelProperty(value = "sqlId")
    private String sqlId;

    @ApiModelProperty(value = "序列号")
    private Integer serialNumber;

    @ApiModelProperty(value = "执行SQL下标 - 前端判断条数使用")
    private int execSqlIndex;

    @ApiModelProperty(value = "查询数据大小控制")
    private boolean queryDataSizeControl;

    @ApiModelProperty(value = "跨库模型")
    private List<CrossDatabaseQueryModel> crossDatabaseQueryModels = Collections.emptyList();

    @ApiModelProperty(value = "私有表权限，是否需要记录特有表(dc_db_private_table)")
    private boolean needRecordInPrivateTable;

    @ApiModelProperty(value = "字段元信息列表")
    private List<SqlFieldData> sqlFieldDataList;
}
