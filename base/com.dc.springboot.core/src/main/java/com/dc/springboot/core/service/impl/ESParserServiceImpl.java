package com.dc.springboot.core.service.impl;

import com.dc.summer.model.parser.AntlrSplitParser;
import com.dc.summer.model.parser.ElasticsearchSplitParser;
import com.dc.summer.model.parser.SQLSplit;
import com.dc.springboot.core.model.script.ParseScriptMessage;
import com.dc.springboot.core.service.ParserService;
import com.dc.springboot.core.model.script.WebSQLQueryInfo;
import com.dc.springboot.core.model.script.WebSQLScriptInfo;
import com.dc.type.DatabaseType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ESParserServiceImpl implements ParserService {

    @Override
    public boolean supportsThisType(DatabaseType databaseType) {
        return databaseType == DatabaseType.ELASTIC_SEARCH;
    }

    @Override
    public WebSQLScriptInfo parseSqlScript(ParseScriptMessage message) {

        AntlrSplitParser antlrSplitParser = new ElasticsearchSplitParser();
        List<SQLSplit> queries = antlrSplitParser.split(message.getScript());

        List<WebSQLQueryInfo> queriesInfo = queries.stream()
                .map(query -> new WebSQLQueryInfo(query.getOffset(),
                        query.getOffset() + query.getText().length(),
                        "",
                        query.getText()))
                .collect(Collectors.toList());
        return new WebSQLScriptInfo(queriesInfo);
    }

}
