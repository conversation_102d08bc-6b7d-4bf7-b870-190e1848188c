package com.dc.springboot.core.model.thread;

import com.dc.summer.model.DBPNamedObject;
import com.dc.utils.time.TimeNumberUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public abstract class AbstractExecuteThread<T> extends ThreadPoolExecutor implements DBPNamedObject, ThreadFactory, TypeObject<T> {

    @Resource
    private ThreadConfig threadConfig;

    @Getter
    private static final TimeUnit UNIT = TimeUnit.SECONDS;

    private final AtomicInteger atomicInteger = new AtomicInteger();

    protected AbstractExecuteThread() {
        super(0, 20, 60, UNIT, new ThreadPoolQueue());
        this.setRejectedExecutionHandler(new ThreadPoolRejected());
        this.setThreadFactory(this);
    }

    @PostConstruct
    private void init() {
        ThreadConfig.ThreadPool threadPool = threadConfig.getThreadPools().stream()
                .filter(tp -> tp.getName().equals(this.getClass().getSimpleName()))
                .peek(tp -> {
                    if (tp.getCorePoolSize() == null) {
                        tp.setCorePoolSize(threadConfig.getThreadPool().getCorePoolSize());
                    }
                    if (tp.getMaximumPoolSize() == null) {
                        tp.setMaximumPoolSize(threadConfig.getThreadPool().getMaximumPoolSize());
                    }
                    if (tp.getKeepAliveTime() == null) {
                        tp.setKeepAliveTime(threadConfig.getThreadPool().getKeepAliveTime());
                    }
                })
                .findFirst()
                .orElse(threadConfig.getThreadPool());
        this.setMaximumPoolSize(threadPool.getMaximumPoolSize());
        this.setCorePoolSize(threadPool.getCorePoolSize());
        this.setKeepAliveTime(threadPool.getKeepAliveTime(), UNIT);
    }

    @Override
    public Thread newThread(@NotNull Runnable r) {
        Thread thread = Executors.defaultThreadFactory().newThread(r);
        thread.setName(String.format("dc-%s-%d", getName(), atomicInteger.incrementAndGet()));
        return thread;
    }

    @Override
    public void execute(@NotNull Runnable command) {
        final String serialNumber = TimeNumberUtils.getSerialNumber();
        log.info("启动任务: {}({})",
                this.getClass().getSimpleName(),
                serialNumber);
        super.execute(new SerialNumberRunnable(Thread.currentThread(), serialNumber, command, this));
    }

    public static class ThreadPoolQueue extends LinkedTransferQueue<Runnable> {
        @Override
        public boolean offer(Runnable e) {
            return tryTransfer(e);
        }
    }

    public static class ThreadPoolRejected implements RejectedExecutionHandler {
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            try {
                executor.getQueue().put(r);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }


}
