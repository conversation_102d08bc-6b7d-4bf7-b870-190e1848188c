package com.dc.springboot.core.model.data;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Map;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("首选项消息")
@NoArgsConstructor
public class PreferencesMessage extends Message {

    /**
     * 如果不传首选项，不打印生命周期日志。（动态SQL）
     */
    @ApiModelProperty(value = "首选项", example = "auto_select_sql:0,use_native_format:0,date_format:yyyy-MM+dd,date_time_format: yyyy-MM-dd HH:mm:ss.SSS,time_format: HH:mm:ss")
    private Map<String, Object> preferences;

    @ApiModelProperty(value = "自动提交", example = "true")
    private Boolean autoCommit;

    public PreferencesMessage(String token, Map<String, Object> preferences) {
        super(token);
        this.preferences = preferences;
    }

    @JsonIgnore
    public Boolean getAutoConnect() {
        return preferences != null ? (Boolean) preferences.get("auto_connect") : null;
    }

    @JsonIgnore
    public Boolean getShortConnect() {
        return preferences != null ? (Boolean) preferences.get("short_connect") : null;
    }

    @JsonIgnore
    public void setAutoConnect(Boolean autoConnect) {
        if (autoConnect != null && preferences != null) {
            preferences.put("auto_connect", autoConnect);
        }
    }

}
