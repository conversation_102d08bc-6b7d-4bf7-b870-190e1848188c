package com.dc.springboot.core.component;

import com.dc.springboot.core.model.data.Message;
import com.dc.summer.exec.model.counter.HandlerCounter;
import com.dc.utils.LoggerUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Aspect
@Slf4j
@Component
public class ApiLog {

    @Resource
    private JSON json;

    @Value("${logging.sub-length}")
    private int subLength;

    @Resource
    private FilterIgnoreFiled filterIgnoreFiled;

    private static final List<String> IGNORE_URLS = List.of("task-info");

    private static final List<String> IGNORE_RESPONSE = List.of("task-result", "task-react");

    private static final List<String> ALLOW_WHOLE_BODY = List.of("/preCheck", "/sql");


    /**
     * 以 controller 包下定义的所有请求为切入点
     */
    @Pointcut("execution(public * com.dc.*.controller..*.*(..))")
    public void controller() {
    }

    /**
     * 环绕
     */
    @Around("controller()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();

        boolean toLog = false;
        boolean toResponse = false;
        // 开始打印请求日志
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            String url = request.getRequestURL().toString();
            toLog = IGNORE_URLS.stream().noneMatch(url::contains);
            toResponse = IGNORE_RESPONSE.stream().noneMatch(url::contains);
            if (toLog) {
                LoggerUtils slf4jUtils = LoggerUtils.receive(LoggerUtils.API);
                slf4jUtils.append("URL", url);
                slf4jUtils.append("Method", request.getMethod());

                HashMap<String, String> headerMap = new HashMap<>();
                Enumeration<String> headerNames = request.getHeaderNames();
                while (headerNames.hasMoreElements()) {
                    String key = headerNames.nextElement();
                    String value = request.getHeader(key);
                    headerMap.put(key, value);
                }

                slf4jUtils.append("Headers", JSON.toJSONString(headerMap));

                // 打印请求入参
                Object[] args = joinPoint.getArgs();
                String param = "";
                List<Object> messageList = new ArrayList<>();
                if (args != null) {
                    if (args.length == 1) {
                        param = json.getObjectMapper().writeValueAsString(args[0]);
                        messageList.add(args[0]);
                    } else {
                        List<Object> collect = Arrays.stream(args)
                                .filter(object -> !(object instanceof HttpServletRequest || object instanceof HttpServletResponse))
                                .collect(Collectors.toList());
                        param = json.getObjectMapper().writeValueAsString(collect);
                        messageList.addAll(collect);
                    }
                }
                for (Object message : messageList) {
                    if (message instanceof Message) {
                        HandlerCounter.setToken(((Message) message).getToken());
                        break;
                    }
                }

                slf4jUtils.append("Body", filterIgnoreFiled.handle(param));
                slf4jUtils.info();
            }
        }

        Object result = joinPoint.proceed();
        if (toLog) {
            HttpServletResponse response = attributes.getResponse();
            HttpServletRequest request = attributes.getRequest();

            String stringResult = json.getObjectMapper().writeValueAsString(result);
            int length = subLength == 0 ||
                    subLength >= stringResult.length() ||
                    ALLOW_WHOLE_BODY.stream().anyMatch(path ->
                            request.getRequestURI().startsWith(request.getContextPath() + path)) ?
                    stringResult.length() : subLength;
            LoggerUtils slf4jUtils = LoggerUtils.send(LoggerUtils.API);
            slf4jUtils.append("Status", Objects.requireNonNull(response).getStatus());
            HashMap<String, String> headerMap = new HashMap<>();
            Collection<String> headerNames = response.getHeaderNames();
            for (String key : headerNames) {
                String value = response.getHeader(key);
                headerMap.put(key, value);
            }
            slf4jUtils.append("Headers", JSON.toJSONString(headerMap));
            if (toResponse) {
                slf4jUtils.append("Body", filterIgnoreFiled.handle(stringResult.substring(0, length)));
            }
            slf4jUtils.append("Size (byte)", stringResult.length());
            slf4jUtils.append("Duration (ms)", System.currentTimeMillis() - startTime);
            slf4jUtils.info();
        }
        HandlerCounter.release();
        return result;
    }

}
