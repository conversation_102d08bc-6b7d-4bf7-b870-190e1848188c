package com.dc.springboot.core.model.execution;

import com.dc.springboot.core.model.data.Message;
import com.dc.springboot.core.model.data.ResultFormat;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.type.ExportType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("任务导出信息")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class JobExportMessage extends Message {

    @Valid
    @NotNull
    @ApiModelProperty(value = "令牌配置 - 修改里面配置的时候，重新打开会话就行，不会生成新的连接。", required = true)
    private TokenConfig tokenConfig;

    @Valid
    @NotNull
    @ApiModelProperty(value = "批处理执行模型", required = true)
    private List<BatchExecuteModel> batchExecuteModels;

    @NotNull
    @ApiModelProperty(value = "遇到错误是否继续 - true，继续 false，回滚", example = "true")
    private boolean isErrorContinue;

    @ApiModelProperty(value = "结果集格式")
    private ResultFormat resultFormat = new ResultFormat();

    @ApiModelProperty(value = "是否导出", example = "true")
    private boolean isExport;

    @ApiModelProperty(value = "导出类型", example = "XLSX")
    private ExportType exportType;

    @ApiModelProperty(value = "MySQL中的记录ID", example = "123")
    private Long logId;

    @ApiModelProperty(value = "任务名称")
    private String jobName;

    @ApiModelProperty(value = "用户id", example = "123")
    private String userId;

    @Valid
    @ApiModelProperty(value = "告警消息")
    private ExecuteEvent executeEvent = new ExecuteEvent();

    @ApiModelProperty(value = "文件发送方式", example = "1")
    private List<Integer> exportModes;

    @ApiModelProperty(value = "导出的邮箱", example = "123")
    private String exportEmail;

    @ApiModelProperty(value = "邮件标题", example = "${code}")
    private String emailTitle;

    @ApiModelProperty(value = "code", example = "123")
    private String code;

    @ApiModelProperty(value = "实例描述", example = "123")
    private String instanceDesc;

    @ApiModelProperty(value = "内容", example = "123")
    private String content;

    @ApiModelProperty(value = "schema模式名", example = "123")
    private String schemaName;

    @ApiModelProperty(value = "是否脱敏导出")
    private boolean exportDesensitize;

    @ApiModelProperty(value = "加密密码", example = "xxx")
    private String encryptPassword;

    @ApiModelProperty(value = "导出为INSERT或UPDATE",example = "INSERT/UPDATE")
    private String exportSqlType;

}
