package com.dc.springboot.core.model.recovery;

import com.dc.springboot.core.model.parser.ParserCheckMessage;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.type.TransactionStatus;
import com.dc.type.BackupVerifyType;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("备份模型")
public class BackupModel {

    @ApiModelProperty(value = "备份用的 select 语句", example = "select * from zyn_test3.time where id = 1")
    private String sql;

    @ApiModelProperty(value = "模式名称", example = "zyn_test3")
    private String schemaName;

    @ApiModelProperty(value = "表名", example = "time")
    private String backupTableName;

    @ApiModelProperty(value = "模式ID", example = "ID-123")
    private String schemaId;

//    @ApiModelProperty(value = "解析返回的动作类型")
//    private Map<String, Object> actions = new HashMap<>();

    @ApiModelProperty(value = "事务状态", example = "0")
    private TransactionStatus transactionStatus = TransactionStatus.UNCHANGED;

    @ApiModelProperty(value = "是否可以备份", example = "false")
    private boolean canBackUp;

    @ApiModelProperty(value = "update语句的变更字段", example = "t2")
    private List<String> updateSetColumns = new ArrayList<>();
    @ApiModelProperty(value = "insert语句的插入字段", example = "t2")
    private List<String> insertSetColumns = new ArrayList<>();
    @Valid
    @ApiModelProperty(value = "主键更新值", allowEmptyValue = true)
    private List<UpdateSetClauseModel> primaryKeyUpdateValue = new ArrayList<>();

    @ApiModelProperty(value = "实例名称", example = "NAME-123")
    private String instanceName;

    @ApiModelProperty(value = "实例id", example = "123")
    private String connectId;

    @ApiModelProperty(value = "数据库类型", example = "1")
    private Integer dbType;

    @ApiModelProperty(value = "主键列", example = "[id]")
    private List<String> primaryKeyColumnsReal;

    @ApiModelProperty(value = "主键列", example = "[id]")
    private String backupOperation;

    @ApiModelProperty(value = "误操作恢复开启事务情况", example = "true")
    private Boolean forceBackup;

    @ApiModelProperty(value = "误操作恢复insertIntoSelect", example = "true")
    private boolean insertIntoSelect = false;

    @ApiModelProperty(value = "备份行数是否超过限制")
    private boolean exceedsRowLimit;

    @ApiModelProperty(value = "备份限制行数")
    private int backupDataRowLimit;

    @ApiModelProperty(value = "显示不备份的原因")
    private boolean showReason;

    @ApiModelProperty(value = "是否为 insert ignore 语句")
    private boolean isIgnore;

    @ApiModelProperty(value = "是否为 replace into 语句")
    private boolean isReplace;

    @ApiModelProperty(value = "解析时备份表是否存在")
    private boolean backupTableExists = true;

    public BackupModel() {
    }

    public BackupModel(String connectId, Integer dbType, String instanceName, WebSQLParserResult singleExecuteSQL) {
        this.setBackupTableName(singleExecuteSQL.getBackupTableName());
        this.setCanBackUp(singleExecuteSQL.isCanBackUp());
        this.setConnectId(connectId);
        this.setDbType(dbType);
        this.setInstanceName(instanceName);
        this.setPrimaryKeyUpdateValue(singleExecuteSQL.getPrimaryKeyUpdateValue());
        this.setPrimaryKeyColumnsReal(singleExecuteSQL.getPrimaryKeyColumnsReal());
        if (singleExecuteSQL.getFirstSchema() != null) {
            this.setSchemaId(singleExecuteSQL.getFirstSchema().getUniqueKey());
            this.setSchemaName(singleExecuteSQL.getFirstSchema().getSchemaName());
        }
        this.setSql(singleExecuteSQL.getBackUpSql());
        this.setUpdateSetColumns(singleExecuteSQL.getUpdateSetColumns());
        this.setInsertSetColumns(singleExecuteSQL.getInsertSetColumns());
        this.setTransactionStatus(TransactionStatus.of(singleExecuteSQL.getTransactionStatus()));

        this.setExceedsRowLimit(BackupVerifyType.BACKUP_REQUIRED_BUT_EXCEEDED_LIMIT.getValue().equals(singleExecuteSQL.getBackupVerify()));
        if (singleExecuteSQL.getBackupDataRowLimit() != null) {
            this.setBackupDataRowLimit(singleExecuteSQL.getBackupDataRowLimit().intValue());
        }
        this.setShowReason(singleExecuteSQL.isShowReason());
        this.setBackupTableExists(singleExecuteSQL.isBackupTableExists());
        this.setIgnore(singleExecuteSQL.isIgnore());
        this.setReplace(singleExecuteSQL.isReplace());
    }
}
