package com.dc.springboot.core.model.sensitive;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 权限追踪实体类
 * <AUTHOR>
 * @Date 2023/9/12 14:52
 */
@Data
@ApiModel("权限追踪")
public class MaskRule {

    @ApiModelProperty(value = "权限来源")
    private Integer origin;

    @ApiModelProperty(value = "类型 表，列")
    private String ruleType;

    @ApiModelProperty(value = "名称")
    private String ruleObject;

    @ApiModelProperty(value = "")
    private String authTime;

    @ApiModelProperty(value = "")
    private Long authTimeStart;

    @ApiModelProperty(value = "")
    private Long authTimeEnd;

    @ApiModelProperty(value = "授权人")
    private String authUser;

    @ApiModelProperty(value = "组织")
    private String authUserOrganization;

    @ApiModelProperty(value = "工单号")
    private String orderCode;

    @ApiModelProperty(value = "敏感权限追踪-权限来源-运维单号")
    private String orderRelevance;

    @ApiModelProperty(value = "权限 明文｜半脱敏｜无权限")
    private String desensiteType;

    @ApiModelProperty(value = "")
    private String schemaId;

    @ApiModelProperty(value = "表名")
    private String tableName;

    @ApiModelProperty(value = "是否明文复制")
    private int enableDesensitizationCopy; // 0:不能复制明文 1:能复制明文

    @ApiModelProperty(value = "")
    private String connectId;

    @ApiModelProperty(value = "授权类型 1:敏感字段，2:敏感等级")
    private Integer grantType;

    @ApiModelProperty(value = "敏感等级的unique_key")
    private String sensitiveLevel;

    @ApiModelProperty(value = "敏感数据负责人的名称")
    private String sensitiveHead;

}
