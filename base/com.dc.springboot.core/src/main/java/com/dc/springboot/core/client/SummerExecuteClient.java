package com.dc.springboot.core.client;

import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.data.Message;
import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.database.ConnectionTokenMessage;
import com.dc.springboot.core.model.database.TestConnectionMessage;
import com.dc.springboot.core.model.database.TestData;
import com.dc.springboot.core.model.execution.BindingExecuteMessage;
import com.dc.springboot.core.model.execution.SingleSyncExecuteMessage;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.springboot.core.model.result.WebSQLExecuteInfo;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class SummerExecuteClient extends BaseRestClient {

    public TestData testConnection(Client client, TestConnectionMessage message) {
        return postJava(client.toSummerClient().getUrl("/execute/test-connection"), message, new ParameterizedTypeReference<Result<TestData>>() {
        });
    }

    public ResponseEntity<Result<Object>> openSession(Client client, ConnectionTokenMessage message) {
        return postJavaWithResponseEntity(client.toSummerClient().getUrl("/execute/open-session"), message, new ParameterizedTypeReference<Result<Object>>() {
        }, Collections.emptyMap());
    }

    public List<WebSQLQueryResult> singleExecuteSql(Client client, SingleSyncExecuteMessage message, String cookieValue) {
        ResponseEntity<Result<WebSQLExecuteInfo>> webSQLExecuteInfo  = postJavaWithResponseEntity(client.toSummerClient().getUrl("/execute/single-execute-sql"), message, new ParameterizedTypeReference<Result<WebSQLExecuteInfo>>() {
        }, Collections.singletonMap(HttpHeaders.COOKIE, cookieValue));
        return webSQLExecuteInfo.getBody().getBody().getQueryResults();
    }

    public List<WebSQLQueryResult> bindingExecuteSql(Client client, BindingExecuteMessage message, String cookieValue) {
        ResponseEntity<Result<WebSQLExecuteInfo>> webSQLExecuteInfo = postJavaWithResponseEntity(client.toSummerClient().getUrl("/execute/binding-execute-sql"), message, new ParameterizedTypeReference<Result<WebSQLExecuteInfo>>() {
        }, Collections.singletonMap(HttpHeaders.COOKIE, cookieValue));
        return webSQLExecuteInfo.getBody().getBody().getQueryResults();
    }

    public void closeSession(Client client, Message message, String cookieValue) {
        postJavaWithResponseEntity(client.toSummerClient().getUrl("/execute/close-session"), message, new ParameterizedTypeReference<Result<Object>>() {
        }, Collections.singletonMap(HttpHeaders.COOKIE, cookieValue));
    }

    public String getRemotePassword(Client client, DatabaseConnectionDto instance) {
        String response = postJava(client.toSummerClient().getUrl("/api/get-remote-password"), instance, new ParameterizedTypeReference<Result<String>>() {
        });
        return response;
    }
}
