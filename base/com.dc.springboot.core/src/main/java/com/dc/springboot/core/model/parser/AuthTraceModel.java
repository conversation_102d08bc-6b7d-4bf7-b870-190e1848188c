package com.dc.springboot.core.model.parser;

import java.util.List;
import java.util.Objects;

public class AuthTraceModel {
    private String origin;
    private String rule_type;
    private String rule_object;
    private List<String> rule_condition;
    private Long auth_time_start;
    private Long auth_time_end;
    private Long auth_time; // 时间戳
    private String auth_user;
    private String auth_user_organization;
    private Integer is_market; // 权限生效(华泰需求)：0:不包括开市时间; 1:包括开市时间
    private Integer is_desensite; // 脱敏(华泰需求)：0:不脱敏; 1:脱敏(需有脱敏配置)
    private String group_name; // 用户组
    private String order_code; // 关联工单号
    private String order_relevance; // 运维单号
    private String user_id; // 用户unique_key
    private String organization_id; // 组织unique_key
    private String user_operator; // 操作人unique_key

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getRule_type() {
        return rule_type;
    }

    public void setRule_type(String rule_type) {
        this.rule_type = rule_type;
    }

    public String getRule_object() {
        return rule_object;
    }

    public void setRule_object(String rule_object) {
        this.rule_object = rule_object;
    }

    public List<String> getRule_condition() {
        return rule_condition;
    }

    public void setRule_condition(List<String> rule_condition) {
        this.rule_condition = rule_condition;
    }

    public Long getAuth_time_start() {
        return auth_time_start;
    }

    public void setAuth_time_start(Long auth_time_start) {
        this.auth_time_start = auth_time_start;
    }

    public Long getAuth_time_end() {
        return auth_time_end;
    }

    public void setAuth_time_end(Long auth_time_end) {
        this.auth_time_end = auth_time_end;
    }

    public Long getAuth_time() {
        return auth_time;
    }

    public void setAuth_time(Long auth_time) {
        this.auth_time = auth_time;
    }

    public String getAuth_user() {
        return auth_user;
    }

    public void setAuth_user(String auth_user) {
        this.auth_user = auth_user;
    }

    public String getAuth_user_organization() {
        return auth_user_organization;
    }

    public void setAuth_user_organization(String auth_user_organization) {
        this.auth_user_organization = auth_user_organization;
    }

    public Integer getIs_market() {
        return is_market;
    }

    public void setIs_market(Integer is_market) {
        this.is_market = is_market;
    }

    public Integer getIs_desensite() {
        return is_desensite;
    }

    public void setIs_desensite(Integer is_desensite) {
        this.is_desensite = is_desensite;
    }

    public String getGroup_name() {
        return group_name;
    }

    public void setGroup_name(String group_name) {
        this.group_name = group_name;
    }

    public String getOrder_code() {
        return order_code;
    }

    public void setOrder_code(String order_code) {
        this.order_code = order_code;
    }

    public String getOrder_relevance() {
        return order_relevance;
    }

    public void setOrder_relevance(String order_relevance) {
        this.order_relevance = order_relevance;
    }

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getOrganization_id() {
        return organization_id;
    }

    public void setOrganization_id(String organization_id) {
        this.organization_id = organization_id;
    }

    public String getUser_operator() {
        return user_operator;
    }

    public void setUser_operator(String user_operator) {
        this.user_operator = user_operator;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        AuthTraceModel that = (AuthTraceModel) o;
        return Objects.equals(rule_type, that.rule_type) && Objects.equals(rule_object, that.rule_object) && Objects.equals(rule_condition, that.rule_condition) && Objects.equals(is_market, that.is_market) && Objects.equals(is_desensite, that.is_desensite) && Objects.equals(user_id, that.user_id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(rule_type, rule_object, rule_condition, is_market, is_desensite, user_id);
    }
}
