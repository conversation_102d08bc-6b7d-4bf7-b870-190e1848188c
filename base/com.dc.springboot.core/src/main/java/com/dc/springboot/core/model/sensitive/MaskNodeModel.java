package com.dc.springboot.core.model.sensitive;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Locale;

@NoArgsConstructor
@Data
@ApiModel("脱敏节点")
@Slf4j
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class MaskNodeModel {

    private String columnScript;
    private String columnName;
    private String columnAlias;
    //[ database ].[framework ].[ table ]
    private String schemaName;
    private String framework;
    private String tableName;

    private String uniqueKey;
    private String unionLabel;

    private Integer unionIndex;

    //业务节点用于脱敏,比如union sql 多条sql情况下用到
    private boolean isBizColumn = false;

    public MaskNodeModel(String schemaName, String framework, String tableName) {
        this.schemaName = schemaName;
        this.framework = framework;
        this.tableName = tableName;
    }

    public MaskNodeModel(String columnScript, String columnName, String columnAlias, String tableName) {
        this.columnScript = columnScript;
        this.columnName = columnName;
        this.columnAlias = columnAlias;
        this.tableName = tableName;
    }

    public MaskNodeModel(String columnScript, String columnName, String columnAlias, String tableName, Boolean isBizColumn) {
        this.columnScript = columnScript;
        this.columnName = columnName;
        this.columnAlias = columnAlias;
        this.tableName = tableName;
        this.isBizColumn = isBizColumn;
    }

    @JsonIgnore
    public List<String> getValues() {
        return Arrays.asList(this.columnScript, this.columnName, this.columnAlias);
    }

    @JsonIgnore
    public Integer getToken() {
        StringBuilder sb = new StringBuilder();
        sb.append(columnScript).append(",");
        sb.append(columnName).append(",");
        sb.append(columnAlias).append(",");
        sb.append(tableName);
        return sb.toString().hashCode();
    }

    @JsonIgnore
    private String base64TableName;

    public void setTableName(String tableName) {
        this.tableName = tableName;
        String temp = new String(Base64.getEncoder().encode(tableName.toUpperCase(Locale.ROOT).getBytes(StandardCharsets.UTF_8)));
        try {
            this.base64TableName = URLEncoder.encode(temp, "gbk");
        } catch (UnsupportedEncodingException e) {
            this.base64TableName = temp;
            log.error("URLEncoder.encode error.", e);
        }
    }

}
