package com.dc.springboot.core.model.database;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("令牌配置")
public class TokenConfig {

    /**
     * @see com.dc.summer.model.exec.DBCExecutionPurpose
     */
    @NotNull
    @ApiModelProperty(value = "意图 - 0（用户查询）、1（带有其他过滤器的用户查询）、2（用户脚本查询）", required = true, example = "0")
    private Integer purpose;

    @ApiModelProperty(value = "SQL Server 首选项", example = "1，2，3，4")
    private String prefs;

    @NotNull
    @ApiModelProperty(value = "自动提交", required = true, example = "true")
    private Boolean autoCommit;

    @ApiModelProperty(value = "智能提交", example = "true")
    private Boolean smartCommit;

    @ApiModelProperty(value = "结果集过期时间（单位：秒）", example = "300")
    private Long expirationTime;

    @ApiModelProperty(value = "字符集", example = "1208")
    private String charset;

    /**
     * @see com.dc.summer.model.data.result.ConsoleType
     */
    @ApiModelProperty(value = "窗口 - 0 密文 、 1 脱敏文", example = "0")
    private Integer console;

    // 平安新增运行脱敏文/明文 导出传参
    private Integer rule_export_desensitization;

    @ApiModelProperty(value = "自动重连", example = "true")
    private Boolean autoConnect;

    @ApiModelProperty(value = "用于pingan oracle  dbName传值", example = "orcl")
    private String dbName;

    @ApiModelProperty(value = "短连接：执行SQL之后，自动关闭连接", example = "true")
    private Boolean shortConnect;

}
