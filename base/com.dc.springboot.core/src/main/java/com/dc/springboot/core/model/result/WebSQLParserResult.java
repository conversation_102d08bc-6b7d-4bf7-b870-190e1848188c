package com.dc.springboot.core.model.result;

import com.dc.springboot.core.model.execution.CrossDatabaseQueryModel;
import com.dc.springboot.core.model.parser.AuthTraceModel;
import com.dc.springboot.core.model.parser.SchemaAttribute;
import com.dc.springboot.core.model.parser.dto.ActionModelDto;
import com.dc.springboot.core.model.parser.dto.CheckResultDto;
import com.dc.springboot.core.model.parser.dto.SqlFieldDataDto;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.springboot.core.model.recovery.UpdateSetClauseModel;
import com.dc.springboot.core.model.sensitive.DataMask;
import com.dc.springboot.core.model.sensitive.GradedClassifiedModel;
import com.dc.springboot.core.model.sensitive.MaskRule;
import com.dc.springboot.core.model.sensitive.MaskNodeModel;
import com.dc.type.DatabaseType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class WebSQLParserResult {

    // 基础信息
    private String sqlId; // sql标识
    private Integer status; // 状态(是否可以执行)
    private String message; // 报错信息(没有权限等信息)
    private String operation = ""; // 操作(SELECT、CREATE等)
    private String sql; // 原始SQL(显示用)
    private String executeSql; // 执行sql(原始sql可能会被修改)
    private String charset; // 当前窗口schema的字符集
    @JsonProperty("is_update_clause")
    private boolean isUpdateClause; // 是否是select for update语句

    /**
     * key operation
     * value fullNameList
     */
    private Map<String, List<String>> actions; // 对象名称、类型以及操作等信息
    private List<ActionModelDto> actionList; // 所有对象名称、类型以及操作
    private boolean needCommit = false; // 是否需要提交回滚高亮
    private boolean isDefaultSchema = false; // 是否不带schemaName
    private String useDatabase; // 切换数据库后的数据库(use语句对应的database)
    private String catalogName; // 切换schema后的catalog(3层结构的数据库，如pg)
    private String authOperation; // 权限字典name_key
    private String singleSchema; // 单表名对应的真实schema
    private int transactionStatus; // 是否开启了事物
    private boolean needSyncSchema = false; // 是否需要同步scheme
    @JsonProperty("contains_dblink")
    private boolean containsDblink; // 包含dblink的select查询
    ///for私有表权限功能
    //iceage 需要响应的结果需要多出一个参数。
    private boolean needRecordInPrivateTable; //是否需要在dc_db_private_table表中记录数据

    // 复核
    private boolean needReview; // 是否需要复核

    // 高危判断需要的所有operation
    private List<String> allOperations;

    // 影响行数
    private Integer limit;
    private Integer offset;
    private Integer executeLimit; // select查询条数限制
    private Integer affectedRows; // 影响行数(1000行高危提示用)
    private Boolean isLimit = false; // 影响行数超过限定为true
    private Integer rowsLimit; // 允许执行的影响行数
    private Integer exportLimit; //本地导出的最大行数限制

    // 脱敏
    private List<DataMask> dataMask; // 脱敏信息
    private List<MaskRule> maskRules; // 脱敏权限追踪
    private List<MaskNodeModel> nodeModel; // sql字段解析模型(半脱敏用)
    @JsonProperty("is_sensitive_select")
    private boolean isSensitiveSelect; // 是否允许全脱敏
    private GradedClassifiedModel gradedClassifiedModel; // 支持分级分类的现场的脱敏权限

    // 可编辑
    private boolean readonly = true; // 是否可编辑
    private Integer scene = 0; // 不可编辑的原因
    private List<String> primaryKeyColumns; // 单表的主键字段(可编辑用)
    private String tableName; // 单表名字,可编辑用
    private boolean hasPrimaryKey; // 单表、视图查询是否含有主键
    private boolean sqlHasChanged; // 可编辑改变了执行sql
    @JsonProperty("can_generate_sql")
    private boolean canGenerateSql; // 结果集能够生成sql

    // 误操作恢复
    @JsonProperty("primary_key_columns_real")
    private List<String> primaryKeyColumnsReal; // 单表的主键字段(误操作恢复用)
    private boolean canBackUp = false; // 是否可以备份
    private String backUpSql; // 备份用的 select 语句
    private List<String> updateSetColumns; // update语句的变更字段
    private List<String> insertSetColumns;  //insert语句插入的字段
    private List<UpdateSetClauseModel> primaryKeyUpdateValue; // 表主键变更后的键值
    private List<Map<String, String>> insertKeyValues; // insert语句备份解析
    boolean notRealPk = false; // 是否是真实主键
    private String backupTableName; // 备份单表名字,误操作恢复用
    private boolean backupTableExists = true;
    private SchemaAttribute firstSchema; // 备份单表对应的schema信息,误操作恢复用
    private String backupOperation;// on duplicate key update 操作所需字段，用于判断具体操作类型
    private Boolean forceBackup; // 强制需要备份
    private boolean insertIntoSelect = false; // 是否是insert into select语句
    private Integer backupVerify = 0; // 操作备份行数限制的情况
    private Long backupDataRowLimit; // 设定的操作行数备份限制
    @JsonProperty("show_reason")
    private boolean showReason; // 显示不备份的原因
    private boolean isIgnore = false; // 是否是 ignore 语句
    private boolean isReplace = false; // 是否是 replace into 语句

    // 权限校验
    private List<AuthTraceModel> rules; // 权限追踪
    private List<Map<String, Object>> noPermissionObjects; // 无权限对象对应的信息(快捷申请)
    private boolean canExport; // 是否能导出
    private boolean canCountTotal; // 是否能查询总行数

    // 查询字段
    private List<SqlFieldDataDto> sqlFieldDataList = new ArrayList<>(); // select的结果集字段集合

    // sql审核
    private List<CheckResultDto> checkRuleList = new ArrayList<>(); // sql审核触发的规则
    private boolean alert; // 是否触发告警

    // 跨库查询
    private List<CrossDatabaseQueryModel> crossDatabaseQueryModels = new ArrayList<>();

    //访问管控
    private boolean accessControlCanExport = true;

    //权限纳管
    private PrivilegeModel privilegeModel;

    //结果集导出工单，用户没有当前语句的哪些权限
    private Set<String> unvalidatedAuths;


    /**
     * 缓存当前结果集的默认 Schema
     */
    @JsonIgnore
    private String defaultSchemaName;

    /**
     * 缓存当前结果的默认 Catalog
     */
    @JsonIgnore
    private String defaultCatalogName;

    public WebSQLParserResult() {
        this.affectedRows = 0;
        this.message = "";
        this.rowsLimit = 0;
        this.tableName = "";
        this.dataMask = new ArrayList<>();
        this.primaryKeyColumns = new ArrayList<>();
        this.primaryKeyColumnsReal = new ArrayList<>();
        this.rules = new ArrayList<>();
        this.gradedClassifiedModel = new GradedClassifiedModel();
    }

    @JsonIgnore
    public String getSqlOperation() {
        StringBuilder sqlOperation = new StringBuilder();
        if (actions != null) {
            for (String key : actions.keySet()) {
                List<String> objects = actions.get(key);
                String object = "";
                if (objects != null) {
                    object = String.join("、", objects);
                }
                if (sqlOperation.length() == 0) {
                    sqlOperation.append(String.format("%s:%s", key, object));
                } else {
                    sqlOperation.append(String.format(", %s:%s", key, object));
                }
            }
        }
        return sqlOperation.toString();
    }

    @JsonIgnore
    public String getSusceptibleColumn(Integer dbType) {
        StringBuilder susceptibleColumn = new StringBuilder();
        if (dataMask != null) {
            for (DataMask mask : dataMask) {
                String columnName = mask.getColumnName();
                if (DatabaseType.ELASTIC_SEARCH.getValue().equals(dbType) && columnName.contains("_source.")) {
                    columnName = columnName.substring(columnName.indexOf(".") + 1);
                }
                if (susceptibleColumn.length() == 0) {
                    susceptibleColumn.append(String.format("%s.%s", mask.getTableName(), columnName));
                } else {
                    susceptibleColumn.append(String.format("、%s.%s", mask.getTableName(), columnName));
                }
            }
        }
        return susceptibleColumn.toString();
    }

}
