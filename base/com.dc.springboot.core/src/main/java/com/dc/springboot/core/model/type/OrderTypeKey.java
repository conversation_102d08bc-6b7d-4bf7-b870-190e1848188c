package com.dc.springboot.core.model.type;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum OrderTypeKey {
    NONE(null, null),
    APPLY_SQL("dc_script_apply_sql", "脚本变更-SQL语句"),
    APPLY_SQL_SCRIPT("dc_script_apply_sql_script", "脚本变更-脚本文件"),
    SCRIPT_WAREHOUSE_UPDATE("dc_script_warehouse_update", "脚本仓库-SQL脚本更新"),
    EXPORT_SQL("dc_export_data", "数据导出-结果集导出"),
    EOA_EXPORT_SQL("dc_eoa_export_data", "数据导出-EOA结果集导出"),

    SCRIPT_WAREHOUSE_SCRIPT("dc_script_warehouse_script", "脚本仓库-SQL脚本申请-附件"),
    SCRIPT_WAREHOUSE_TEXT("dc_script_warehouse_text", "脚本仓库-SQL脚本申请-文本"),

    IMPORT_DATA_EXCEL("dc_import_data_xlsx", "脚本变更-数据导入-Excel"),
    IMPORT_DATA_CSV("dc_import_data_csv", "脚本变更-数据导入-CSV"),
    IMPORT_DATA_TEXT("dc_import_data_txt", "脚本变更-数据导入-TEXT"),

    PRIVILEGE_CHANGE("dc_account_change_apply", "权限变更"),
    ACCOUNT_APPLICATION("dc_database_user_apply", "账号申请"),

    APPLY_SQL_SCRIPT_BATCH("dc_script_batch_change", "脚本批量变更-脚本文件")
    ;

    private final String key;
    private final String name;


    public static OrderTypeKey of(String key, String scriptType, Integer fileTypeCode) {
        if (StringUtils.isNotBlank(scriptType)) {
            key += "_" + scriptType;
        } else {
            FileType fileType = FileType.getTypeByCode(fileTypeCode);
            if (fileType != null) {
                key += "_" + fileType.getOrderTypeKey();
            }
        }

        for (OrderTypeKey value : OrderTypeKey.values()) {
            if (value != NONE) {
                if (value.getKey().equals(key)) {
                    return value;
                }
            }
        }
        return NONE;
    }

    public boolean isImportData() {
        return this == IMPORT_DATA_TEXT || this == IMPORT_DATA_CSV || this == IMPORT_DATA_EXCEL;
    }

    public boolean isScriptChanges() {
        return this == APPLY_SQL || this == APPLY_SQL_SCRIPT;
    }

    public boolean isScriptWarehouse() {
        return this == SCRIPT_WAREHOUSE_SCRIPT || this == SCRIPT_WAREHOUSE_TEXT || this == SCRIPT_WAREHOUSE_UPDATE;
    }

    public boolean isExecuteSql() {
        return this == APPLY_SQL || this == EXPORT_SQL || this == EOA_EXPORT_SQL;
    }

    public boolean isExportSql() {
        return this == EXPORT_SQL || this == EOA_EXPORT_SQL;
    }

    public boolean isPrivilege() {
        return this == PRIVILEGE_CHANGE || this == ACCOUNT_APPLICATION;
    }

    public boolean isBatchScript() {
        return this == APPLY_SQL_SCRIPT_BATCH;
    }

    public OriginType getOriginType() {
        if (isImportData()) {
            return OriginType.IMPORT;
        }
        if (isScriptChanges()) {
            return OriginType.SCRIPT;
        }
        if (isExecuteSql()) {
            return OriginType.ORDER_EXPORT;
        }
        if (isBatchScript()) {
            return OriginType.BATCH_SCRIPT;
        }
        return OriginType.NONE;
    }
}
