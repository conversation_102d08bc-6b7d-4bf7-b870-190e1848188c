package com.dc.repository.mysql.column;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.dc.repository.mysql.model.WriteMode;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;


@Data
public class OrderApplyContent {

    private Integer db_type;
    private Integer environment;
    private String connect_id;
    private String connect_user;
    private Integer connection_pattern;
    private String connection_pattern_zh;
    private String connection_desc;
    private String instance_name;
    private String schema_id;
    private String schema_name;
    // 平安新增 脱敏文, 密文参数
    private Integer console;
    // 平安新增 脱敏文/明文 导出传参
    private Integer rule_export_desensitization;
    // 平安新增 dbName 实时传值
    private String db_name;

    //脚本仓库类型 1新增,2更新
    private Integer apply_type;

    private Integer file_type;

    private String label_id;
    private String label_name;

    //脚本部分
    private Object sql_script;
    //回退脚本部分
    private Object rollback_sql;
    private String name;    //重试某个脚本
    private String script_path;
    private String charset;

    //导入部分
    private String import_path;

    //误脱敏部分
    private Object fields;

    /**
     * 字段映射配置
     */
    private FieldMap field_mapping;

    //导入回退部分
    private String rollback_script_name;
    private String rollback_script_path;
    private String table_name;

    /**
     * 记录分隔符
     */
    private Integer line_delimiter;

    /**
     * 分段分隔符
     */
    private Integer column_delimiter;

    /**
     * 文本识别符
     */
    private Integer text_identifier;

    /**
     * 其他符号
     */
    private String other_delimiter;


    /**
     * 启用脱敏类型
     */
    private int enable_desensitize_type;

    /**
     * 敏感数据保护-脱敏：1开启，0关闭。
     */
    private String sensitive_data_protection_desensitization;

    /**
     * 全脱标识符号
     */
    private String symbol;

    /**
     * 导出类型
     */
    private Integer export_type;

    /**
     * 单sql导出行数
     */
    private int single_sql_export_num;

    private String encrypt_password;

    private String download_limit;

    private String export_way;

    private String lxfkzmd;

    private int excel_use_original_format;

    private String excel_datetime_format;

    private Integer execute_submit_way;

    private boolean need_truncate;

    /**
     * 数据处理:  1单行  2批量
     */
    private int data_handle;

    /**
     * 数据导入处理数据行数
     */
    private int data_handle_line;

    private WriteMode write_mode;

    private boolean excel_use_number_format;

    private List<Integer> user_catalog_ids;

    private String user_catalog;

    private List<Integer> instance_catalog_ids;

    private String instance_catalog;

    /** 权限变更*/
    private List<ResourceContent> resource_list;
    private AuthGrant auth_grant;
    private AuthRevoke auth_revoke;

    /**
     * 用户申请
     */
    private BaseInfo base_info;

    /**
     * 批量变更工单所有的schema信息
     */
    private List<SchemaInfo> schema_data;

    /**
     * origin = 2 表示为SPI提单
     */
    private Integer origin;

    /**
     * 是否跳过预检查
     */
    private Integer is_skip_precheck;
    private Integer is_import;

    private String export_sql_type;


    public List<SqlScript> getSqlScripts() {
        if (sql_script instanceof String) {
            SqlScript sqlScript = new SqlScript();
            sqlScript.setScript(sql_script.toString());
            sqlScript.setName("SQL语句");
            return Collections.singletonList(sqlScript);
        } else {
            Gson gson = new GsonBuilder()
                    .serializeNulls()
                    .create();
            String content = gson.toJson(sql_script);
            return gson.fromJson(content, new TypeToken<List<SqlScript>>() {
            }.getType());
        }
    }

    public String getScriptType() {
        List<SqlScript> sqlScripts = getSqlScripts();
        return CollectionUtils.isNotEmpty(sqlScripts) ? sqlScripts.get(0).getType() : null;
    }

    public SqlScript getSqlImport() {
        SqlScript sqlScript = new SqlScript();
        sqlScript.setName(getName());
        sqlScript.setScript(getImport_path());
        return sqlScript;
    }

    // SQL 语句工单使用
    public List<RollbackSqlScript> getRollbackScriptsForSql() {
        if (rollback_sql == null) {
            return Collections.emptyList();
        }

        if (rollback_sql.toString().isEmpty()) {
            return Collections.emptyList();
        }

        RollbackSqlScript sqlScript = new RollbackSqlScript();
        sqlScript.setScript(rollback_sql.toString());
        sqlScript.setName("回退语句");
        return Collections.singletonList(sqlScript);
    }

    public List<RollbackSqlScript> getRollbackScripts() {
        if (rollback_sql == null) {
            return List.of();
        }
        if (rollback_sql instanceof String) {
            RollbackSqlScript sqlScript = new RollbackSqlScript();
            sqlScript.setScript(rollback_sql.toString());
            sqlScript.setName("回退语句");
            return Collections.singletonList(sqlScript);
        } else {
            Gson gson = new GsonBuilder()
                    .serializeNulls()
                    .create();
            String content = gson.toJson(rollback_sql);
            return gson.fromJson(content, new TypeToken<List<RollbackSqlScript>>() {
            }.getType());
        }

    }

    public RollbackSqlScript getRollbackImport() {
        RollbackSqlScript sqlScript = new RollbackSqlScript();
        sqlScript.setName(rollback_script_name);
        sqlScript.setScript(rollback_script_path);
        return sqlScript;
    }

    @Data
    public static class FieldMap {

        private String origin_table_name;

        private List<Field> fields;

        @Data
        public static class Field {
            private String origin_field;
            private String target_field;
            private String target_field_type;
            private boolean is_skip;

        }
    }
}
