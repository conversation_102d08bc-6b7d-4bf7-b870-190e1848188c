package com.dc.repository.mysql.column;

import lombok.Data;

@Data
public class BaseInfo {
    private String username;
    private String password;
    private boolean externally;
    private String default_tablespace_type;
    private String default_tablespace;
    private String default_tablespace_size;
    private String default_tablespace_unit;
    private String temporary_tablespace_type;
    private String temporary_tablespace;
    private String temporary_tablespace_size;
    private String temporary_tablespace_unit;
    private String profile;
    private boolean account_status;


    // for mysql
    private String host;
    private Integer max_queries_per_hour;
    private Integer max_updates_per_hour;
    private Integer max_connection_per_hour;
    private Integer max_user_connections;
}