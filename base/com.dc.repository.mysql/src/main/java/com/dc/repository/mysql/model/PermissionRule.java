package com.dc.repository.mysql.model;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class PermissionRule {

    private String action_key;
    private String connect_id;
    private String schema_id;
    private Integer resource_type;
    private String object_name;
    private String column_name;
    private Integer origin;
    private Long begin_time;
    private Long end_time;
    private String gmt_create;
    private String operator;
    private String group_name;
    private String order_code; // 关联工单号
    private String order_relevance; // 运维单号
    private String user_id; // 用户unique_key
    private String organization_id; // 组织unique_key
    private String user_operator; // 操作人unique_key
    private Integer enable_desensitization_copy; // 是否明文复制
    private Integer grant_type; // 授权类型 1:敏感字段，2:敏感等级
    private Integer is_instance_role; // 是否是实例角色
    private String sensitive_level; // 敏感等级的unique_key
    private Integer base_type; // 1 是数据权限 2 敏感权限 3账号权限

}
