<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.GroupUserMapper">

    <select id="getInstanceRole" parameterType="map" resultType="com.dc.repository.mysql.model.InstanceRole">
        select
            role.unique_key,
            role.role_name,
            role.operate_range,
            role.operate_rule,
            role.sensitive_rule,
            GROUP_CONCAT(relation.action_key SEPARATOR ',') action_key
        from
            dc_instance_role role
            left join dc_instance_role_relation relation on relation.role_id = role.unique_key
            and relation.is_delete = 0
        where
            role.is_delete = 0
        and (
                role.sensitive_rule in (1, 2)
                or role.operate_rule in (2, 3)
            )
        group by
            role.unique_key,
            role.role_name,
            role.operate_range,
            role.operate_rule,
            role.sensitive_rule
    </select>

    <select id="getPrivateTableInstanceRole" resultType="com.dc.repository.mysql.model.InstanceRole">
        select unique_key,role_name,operate_range,operate_rule,sensitive_rule
        from dc_instance_role
        where unique_key = "private_table_instance_auth" and is_built_in = 1 and is_delete = 0; <!--目前status不受影响，不需要判断status-->
    </select>

    <select id="getUserAuth" parameterType="map" resultType="com.dc.repository.mysql.model.PermissionRule">
        select distinct
        user.begin_time, user.end_time, user.gmt_create, user.user_id, user.organization_id, user.operator as user_operator,
        (case when grp.group_type = 2 then '' else grp.group_name end) group_name,
        rule.connect_id, rule.schema_id, rule.resource_type, rule.object_name, rule.column_name,
        (case grp.group_type when 2 then rule.origin else user.origin end) as origin,
        (case when grp.group_type=2 then rule.order_code else user.order_code end ) as order_code,
        (case when grp.group_type=2 then rule.order_relevance else user.order_relevance end ) as order_relevance,
        rule.operator, rule.grant_type, rule.is_instance_role,rule.base_type,
        permission.action_key, permission.enable_desensitization_copy, permission.sensitive_level
        from dc_ci_group_user user
        join dc_ci_group grp on user.group_id = grp.id and grp.is_delete = 0 and grp.is_active = 1
        join dc_ci_group_rule rule on rule.group_id = grp.id and rule.is_delete = 0
        and rule.connect_id = #{connect_id}
        and (
        rule.resource_type = 1
        <if test="schema_ids!=null and schema_ids.size()>0">
            or (
            rule.schema_id in
            <foreach item="schema_id" collection="schema_ids" separator="," open="(" close=")">
                #{schema_id}
            </foreach>
            and (
            rule.resource_type = 2
            <if test="object_names!=null and object_names.size()>0">
                or (
                rule.resource_type in
                <foreach item="resource_type" collection="resource_types" separator="," open="(" close=")">
                    #{resource_type}
                </foreach>
                and upper(rule.object_name) in
                <foreach item="object_name" collection="object_names" separator="," open="(" close=")">
                    #{object_name}
                </foreach>
                )
            </if>
            )
            )
        </if>
        )
        join dc_ci_group_permission permission on permission.rule_id = rule.id and permission.is_delete = 0
        and permission.action_key in
        <foreach item="action_key" collection="action_keys" separator="," open="(" close=")">
            #{action_key}
        </foreach>
        where
        (
        user.user_id = #{user_id}
        <if test="organizations!=null and organizations.size()>0">
            or
            user.organization_id in
            <foreach item="organization" collection="organizations" separator="," open="(" close=")">
                #{organization}
            </foreach>
        </if>
        )
        and user.is_delete = 0
        and user.begin_time &lt;= #{now}
        and user.end_time &gt;= #{now}
    </select>

    <select id="getUserAccountDirectConnectionAuth" parameterType="map" resultType="com.dc.repository.mysql.model.PermissionRule">
        select r.connect_id, r.schema_id, r.resource_type, r.object_name, r.column_name,
               (case g.group_type when 2 then r.origin else u.origin end) as origin,
               (case when g.group_type=2 then r.order_code else u.order_code end ) as order_code,
               r.base_type, r.apply_type, r.operator, r.grant_type, r.is_instance_role,
               u.user_id, u.begin_time, u.end_time, u.gmt_create, u.organization_id, u.operator as user_operator,
               (case when g.group_type = 2 then '' else g.group_name end) group_name,
               p.action_key, p.sensitive_level
        from dc_ci_group_rule r
        join dc_ci_group_user u on r.group_id = u.group_id and u.is_delete = 0
        join dc_ci_group g on g.id = u.group_id and g.is_active = 1 and g.is_delete = 0
        join dc_ci_group_permission p on r.id = p.rule_id and p.is_delete = 0 and r.is_delete = 0
        where
        (
        u.user_id = #{user_id}
        <if test="organizations!=null and organizations.size()>0">
            or
            u.organization_id in
            <foreach item="organization" collection="organizations" separator="," open="(" close=")">
                #{organization}
            </foreach>
        </if>
        )
        and r.base_type = 3
        and r.resource_type = 123
        and r.connect_id = #{connect_id}
        and u.begin_time &lt;= #{now}
        and u.end_time &gt;= #{now}
    </select>

    <select id="getUserTableGroupAuth" parameterType="map" resultType="com.dc.repository.mysql.model.PermissionRule">
        select distinct
        user.begin_time, user.end_time, user.gmt_create, user.user_id, user.organization_id, user.operator as user_operator,
        (case when grp.group_type=2 then '' else grp.group_name end) group_name,
        rule.connect_id, rule.schema_id, rule.resource_type, rule.object_name, rule.column_name,
        (case grp.group_type when 2 then rule.origin else user.origin end) as origin,
        (case when grp.group_type=2 then rule.order_code else user.order_code end ) as order_code,
        rule.operator, rule.grant_type, rule.is_instance_role,
        permission.action_key, permission.sensitive_level
        from dc_ci_group_user user
        join dc_ci_group grp on user.group_id = grp.id and grp.is_delete = 0 and grp.is_active = 1
        join dc_ci_group_rule rule on rule.group_id = grp.id and rule.is_delete = 0
        and rule.connect_id = #{connect_id}
        and rule.resource_type = 4
        and rule.apply_type = 9
        join dc_ci_group_permission permission on permission.rule_id = rule.id and permission.is_delete = 0
        and permission.action_key in
        <foreach item="action_key" collection="action_keys" separator="," open="(" close=")">
            #{action_key}
        </foreach>
        where
        (
        user.user_id = #{user_id}
        <if test="organizations!=null and organizations.size()>0">
            or
            user.organization_id in
            <foreach item="organization" collection="organizations" separator="," open="(" close=")">
                #{organization}
            </foreach>
        </if>
        )
        and user.is_delete = 0
        and user.begin_time &lt;= #{now}
        and user.end_time &gt;= #{now}
    </select>

    <select id="hasConnectionAuth" parameterType="map" resultType="java.lang.Boolean">
        SELECT exists(
                       SELECT 1
                       FROM (SELECT id,
                                    group_id,
                                    base_type,
                                    apply_type,
                                    connect_id,
                                    schema_id,
                                    resource_type,
                                    object_name
                             FROM dc_ci_group_rule
                             WHERE is_delete = 0
                               AND base_type = 1
                               AND connect_id = #{connect_id}) r
                                JOIN (SELECT id,
                                             group_id,
                                             user_id,
                                             organization_id,
                                             begin_time,
                                             end_time,
                                             operator,
                                             gmt_create
                                      FROM dc_ci_group_user
                                      WHERE user_id != ''
                                        AND is_delete = 0
                                        AND user_id = #{user_id}
                                        and begin_time&lt;= #{now}
                                        and end_time >= #{now}
                                      UNION
                                      (
                                      SELECT
                                          a.id,
                                          a.group_id,
                                          b.user_id,
                                          a.organization_id,
                                          a.begin_time,
                                          a.end_time,
                                          a.operator,
                                          a.gmt_create
                                      FROM
                                          ( SELECT id, group_id, begin_time, end_time, operator, organization_id, gmt_create FROM dc_ci_group_user WHERE
                                          organization_id !='' AND is_delete = 0 and begin_time&lt;= #{now} and end_time >= #{now}) AS a
                                          JOIN ( SELECT uid AS user_id, org_id FROM dc_sys_org_user WHERE is_delete = 0 AND uid =
                                          #{user_id} ) AS b
                                      ON a.organization_id = b.org_id
                                          )) u ON u.group_id = r.group_id
                                JOIN dc_ci_group g ON r.group_id = g.id
                           AND g.is_active = 1
                           AND g.is_delete = 0
                       WHERE 1 = 1
                         AND u.user_id = #{user_id})

    </select>

    <update id="deleteByUserId" parameterType="java.util.List">
        update dc_ci_group_user set is_delete = 1
        where user_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    
    <select id="getUserSoonExpireAuth" resultType="java.util.Map">
        select t1.user_id user_id, GROUP_CONCAT(DISTINCT c.instance_name) instance_names from
            (select
                r.connect_id,u.user_id,u.begin_time,u.end_time
            from dc_ci_group_rule r
                     join dc_ci_group_user u on r.group_id = u.group_id and u.is_delete = 0 and r.is_delete=0
                     join dc_ci_group g on g.id = u.group_id and g.is_active=1 and g.is_delete=0
            where  u.end_time BETWEEN #{now} and #{nowPlusThreshold} and u.is_delete=0 and r.is_delete=0) t1
        left join dc_db_connection c on t1.connect_id = c.unique_key
        group by t1.user_id
    </select>

</mapper>
