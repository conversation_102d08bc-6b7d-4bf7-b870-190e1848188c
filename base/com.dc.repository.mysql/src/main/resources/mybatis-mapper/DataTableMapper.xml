<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DataTableMapper">

    <resultMap id="InsertTable" type="com.dc.repository.mysql.model.DataTable">
        <result column="id" property="id"/>
    </resultMap>

    <insert id="add" parameterType="com.dc.repository.mysql.model.DataTable" keyColumn="id"
            useGeneratedKeys="true" keyProperty="id">
        ${sql}
    </insert>

    <select id="getDataMap" parameterType="com.dc.repository.mysql.model.DataTable"
            resultType="java.util.Map">
        SELECT * from ${tableName} where `row_id` = #{row_id} and `transaction_index` = #{transaction_index} limit 1
    </select>

    <select id="getDataIds" parameterType="com.dc.repository.mysql.model.DataTable"
            resultType="java.lang.Long">
        SELECT * from ${tableName} where `transaction_index` = #{transaction_index};
    </select>

    <delete id="deleteDataByIds" parameterType="com.dc.repository.mysql.model.DataTable">
        delete from ${tableName} where id in
        <foreach collection="idList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDataByRowIds" parameterType="map">
        delete from ${table_name}
        where `transaction_index` = #{transaction_index}
        and `row_id` in
        <foreach collection="row_ids" item="row_id" open="(" close=")" separator=",">
            #{row_id}
        </foreach>
    </delete>

    <select id="getDataTableCount" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ${table_name}
    </select>

    <update id="dropDataTable" parameterType="map">
        DROP TABLE ${table_name}
    </update>

</mapper>