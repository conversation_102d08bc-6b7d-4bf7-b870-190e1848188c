<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dc.repository.mysql.mapper.DcWorkOrderMapper">

    <resultMap id="OrderInfo" type="com.dc.repository.mysql.model.Order">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="bpm_id" property="bpm_id"/>
        <result column="flow_service_id" property="flow_service_id"/>
        <result column="apply_user" property="apply_user"/>
        <result column="commit_user" property="commit_user"/>
        <result column="commit_time" property="commit_time"/>
        <result column="apply_reason" property="apply_reason"/>
        <result column="apply_content" property="apply_content" typeHandler="com.baomidou.mybatisplus.extension.handlers.GsonTypeHandler"/>
        <result column="carbon_copy" property="carbon_copy"/>
        <result column="current_status" property="current_status"/>
        <result column="third_order_uuid" property="third_order_uuid"/>
        <result column="third_order_sn" property="third_order_sn"/>
        <result column="callback_uuid" property="callback_uuid"/>
        <result column="sub_order" property="sub_order"/>
        <result column="is_delete" property="is_delete"/>
        <result column="gmt_create" property="gmt_create"/>
        <result column="gmt_modified" property="gmt_modified"/>
        <result column="audit_status" property="audit_status"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.code,
		t.bpm_id,
		t.flow_service_id,
		t.apply_user,
		t.commit_user,
		t.commit_time,
        t.apply_reason,
		t.apply_content,
		t.carbon_copy,
		t.current_status,
		t.third_order_uuid,
		t.third_order_sn,
		t.callback_uuid,
		t.sub_order,
		t.gmt_create,
		t.gmt_modified,
        t.audit_status,
        t.consumer_token_list,
        t.producer_token,
        t.check_fail_reason,
        t.execute_fail_reason,
        t.rollback_fail_reason
    </sql>

    <select id="getById" resultMap="OrderInfo" parameterType="java.lang.Integer">
        SELECT <include refid="Base_Column_List"/>
        FROM `dc_work_order` AS t
        WHERE t.id = #{id}
    </select>

    <select id="existsOrderExecute" resultType="java.lang.Integer">
        SELECT EXISTS (
            SELECT 1
            FROM work_order_execute_result AS r
                     JOIN work_order_execute AS e ON r.order_execute_id = e.id
            WHERE e.order_id = #{orderId} AND e.is_delete = 0 AND r.status = 2
        ) AS has_value
    </select>

    <update id="updateStatus" parameterType="com.dc.repository.mysql.model.Order">
        UPDATE `dc_work_order`
        SET current_status = #{current_status}, gmt_modified   = now()
        WHERE id = #{id}
    </update>

    <update id="updateStatusWithReason" parameterType="com.dc.repository.mysql.model.Order">
        UPDATE `dc_work_order`
        SET current_status = #{current_status}, gmt_modified   = now(), execute_fail_reason = #{execute_fail_reason}
        WHERE id = #{id}
    </update>

    <update id="updateFailStatus" parameterType="Map">
        UPDATE `dc_work_order`
        SET current_status = #{current_status}, audit_status = #{audit_status}, gmt_modified = now()
        WHERE id = #{id}
    </update>

    <update id="updateAuditStatus" parameterType="Map">
        UPDATE `dc_work_order`
        SET audit_status = #{audit_status}, gmt_modified = now()
        WHERE id = #{id}
    </update>

    <update id="updateApplyContent" parameterType="com.dc.repository.mysql.model.Order">
        UPDATE `dc_work_order`
        SET apply_content = #{apply_content,typeHandler=com.baomidou.mybatisplus.extension.handlers.GsonTypeHandler}, gmt_modified   = now()
        WHERE id = #{id}
    </update>

    <update id="updateByStatus">
        UPDATE `dc_work_order`
        SET current_status = #{order.current_status}, gmt_modified   = now()
        WHERE id = #{order.id} and current_status = #{status}
    </update>

    <update id="updateToken">
        UPDATE `dc_work_order`
        SET consumer_token_list = #{order.consumer_token_list}, producer_token = #{order.producer_token}
        WHERE id = #{order.id}
    </update>
    <update id="updateCheckFailReason">
        update `dc_work_order`
        set check_fail_reason = #{check_fail_reason}
        where id = #{id}
    </update>
    <update id="updateStatusAndCheckFailReason">
        update `dc_work_order`
        set current_status = #{current_status},
            check_fail_reason = #{check_fail_reason},
            gmt_modified      = now()
        where id = #{id}
    </update>
    <update id="updateExecuteFailReason" parameterType="com.dc.repository.mysql.model.Order">
        UPDATE `dc_work_order`
        <set>
            gmt_modified = now()
            <if test="execute_fail_reason != null">
                execute_fail_reason = #{execute_fail_reason},
            </if>
            <if test="rollback_fail_reason != null">
                rollback_fail_reason = #{rollback_fail_reason},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateOrderStatus" parameterType="com.dc.repository.mysql.model.Order">
        UPDATE `dc_work_order`
        <set>
            gmt_modified = now(),
            <if test="current_status != null">
                current_status = #{current_status},
            </if>
            <if test="audit_status != null">
                audit_status = #{audit_status},
            </if>
            <if test="check_fail_reason != null">
                check_fail_reason = #{check_fail_reason},
            </if>
        </set>
        WHERE id = #{id}
    </update>
</mapper>