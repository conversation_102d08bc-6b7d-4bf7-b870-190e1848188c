package com.dc.test.exec;

import com.dc.annotation.SQL;
import com.dc.function.ConsumerFunction;
import com.dc.summer.registry.center.Global;
import com.dc.summer.Log;
import com.dc.summer.Slf4jHandler;
import com.dc.summer.exec.config.HandlerConfig;
import com.dc.summer.exec.handler.DataSourceConnectionHandler;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.data.DBDAttributeBindingMeta;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.AbstractExecutionSource;
import com.dc.summer.model.impl.struct.SimpleContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.summer.registry.DataSourceProviderRegistry;
import com.dc.summer.registry.datatype.DataTypeProviderRegistry;
import lombok.SneakyThrows;

import java.util.*;

public class BaseExecTest {

    protected static final DBRProgressMonitor monitor = new LoggingProgressMonitor();

    private final Integer purpose = DBCExecutionPurpose.UTIL.getId();

    private final String token = "Test Exec";

    private final boolean autoCommit = true;
    private final boolean autoConnect = true;

    private final String prefs = "";

    protected final ConnectionConfiguration connectionConfiguration = new ConnectionConfiguration();

    private DBCExecutionContext executionContext;

    protected boolean isErrorContinue = true;

    private long overNum;

    {

        Global.Config config = new Global.Config();
        Global.setConfig(config);
        config.setRoot("C:\\myData\\gitRepository\\summerNew\\summer");

        HandlerConfig.setActivateRecord(false);

        HandlerConfig.setForceClose(false);

        Log.setLogHandler(new Slf4jHandler());

        DataSourceProviderRegistry.getInstance();

        DataTypeProviderRegistry.getInstance();

        connectionConfiguration.setConnectionId("TEST ConnectionId");
    }

    @SneakyThrows
    public DBCExecutionContext getExecutionContext() {
        if (executionContext == null) {
            DataSourceConnectionHandler handler = DataSourceConnectionHandler.handle(connectionConfiguration);
            executionContext = handler.getExecutionContext(token, purpose, autoCommit, autoConnect,false, prefs, connectionConfiguration.getCharset(), connectionConfiguration);
        }
        return executionContext;
    }

    @SneakyThrows
    protected void execute(@SQL String sql, Object... args) {
        try (DBCSession session = getExecutionContext().openSession(monitor, DBCExecutionPurpose.UTIL, getClass().getSimpleName())) {
            DBPDataSource dataSource = session.getDataSource();
            SimpleContainer simpleContainer = new SimpleContainer(dataSource);
            AbstractExecutionSource source = new AbstractExecutionSource(simpleContainer, executionContext, null, sql);
            try (DBCStatement dbcStatement = DBUtils.makeStatement(source, session, DBCStatementType.SCRIPT, sql, 0L, 0L)) {
                if (args != null && dbcStatement instanceof JDBCPreparedStatement) {
                    int parameterIndex = 1;
                    for (Object arg : args) {
                        try {
                            ((JDBCPreparedStatement) dbcStatement).setObject(parameterIndex, arg);
                            parameterIndex++;
                        } catch (Exception ex) {
                            try {
                                ((JDBCPreparedStatement) dbcStatement).setObject(parameterIndex, ex.getMessage());
                            } catch (Exception exc) {
                                ((JDBCPreparedStatement) dbcStatement).setObject(parameterIndex, null);
                            }
                        }
                    }
                }
                boolean b = dbcStatement.executeStatement();
            }
        } catch (Exception e) {
            if (!isErrorContinue) {
                throw e;
            }
            e.printStackTrace();
        } finally {
            overNum++;
            System.out.printf("execute over, \n\tsql is (%s), \n\targs is %s, \n\tnum is %d%n", sql, Arrays.toString(args), overNum);
        }
    }

    @SneakyThrows
    public void query(@SQL String sql, ConsumerFunction<DBCResultSet> consumer) {

        try (DBCSession session = getExecutionContext().openSession(monitor, DBCExecutionPurpose.USER, getClass().getSimpleName())) {
            try (DBCStatement dbcStatement = DBUtils.makeStatement(session, sql, true)) {
                if (dbcStatement.executeStatement()) {
                    try (DBCResultSet resultSet = dbcStatement.openResultSet()) {
                        consumer.accept(resultSet);
                    }
                }
            }
        } catch (Exception e) {
            if (!isErrorContinue) {
                throw e;
            }
            e.printStackTrace();
        }
    }

    public void print(@SQL String sql) {
        query(sql, dbcResultSet -> {
            DBCResultSetMetaData meta = dbcResultSet.getMeta();
            List<DBCAttributeMetaData> attributes = meta.getAttributes();
            while (dbcResultSet.nextRow()) {
                JDBCResultSet resultSet = (JDBCResultSet) dbcResultSet;
                for (int i = 0; i < attributes.size(); i++) {
                    System.out.print(resultSet.getObject(i + 1) + " ");
                }
                System.out.println();
            }
        });
    }

    public void fetch(@SQL String sql) {

        query(sql, dbResult -> {

            DBCResultSetMetaData meta = dbResult.getMeta();
            List<DBCAttributeMetaData> attributes = meta.getAttributes();
            DBDAttributeBinding[] bindings = new DBDAttributeBindingMeta[attributes.size()];
            for (int i = 0; i < attributes.size(); i++) {
                DBCAttributeMetaData attrMeta = attributes.get(i);
                bindings[i] = new DBDAttributeBindingMeta(new SimpleContainer(executionContext.getDataSource()), dbResult.getSession(), attrMeta);
            }

            while (dbResult.nextRow()) {
                for (int i = 0; i < bindings.length; i++) {
                    DBDAttributeBinding binding = bindings[i];
                    try {
                        Object cellValue = binding.getValueHandler().fetchValueObject(
                                dbResult.getSession(),
                                dbResult,
                                binding.getMetaAttribute(),
                                i);
                        System.out.print(cellValue);
                        if (i < bindings.length - 1) {
                            System.out.print(",");
                        }
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                }
                System.out.println();
            }

        });
    }

}
