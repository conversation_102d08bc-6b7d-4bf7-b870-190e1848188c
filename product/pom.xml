<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>summer</artifactId>
        <groupId>com.dc</groupId>
        <version>1.0</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <artifactId>summer.product</artifactId>

    <modules>
        <module>com.dc.summer</module>
        <module>com.dc.job</module>
        <module>com.dc.iceage</module>
<!--        <module>com.dc.broker</module>-->
    </modules>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

</project>