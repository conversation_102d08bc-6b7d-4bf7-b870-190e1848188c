package com.dc.iceage.model.jdbc;

import com.dc.summer.DBException;
import com.dc.summer.exec.handler.DataSourceConnectionHandler;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.exec.DBExecUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.UUID;

public class ExecutionContainer implements AutoCloseable {

    private final ConnectionConfiguration connectionConfiguration;
    private final DataSourceConnectionHandler handler;
    private final String schemaName;
    private final String catalogName;

    private DBCExecutionContext executionContext;

    private final String token = "Iceage - " + UUID.randomUUID();

    private final boolean autoCloseDataSource;

    private final String connectionId;

    private final String charset;

    public ExecutionContainer(ConnectionConfiguration connectionConfiguration) {
        this(connectionConfiguration, false);
    }

    public ExecutionContainer(ConnectionConfiguration connectionConfiguration, boolean autoCloseDataSource) {
        this.connectionConfiguration = connectionConfiguration;
        this.handler = DataSourceConnectionHandler.handle(connectionConfiguration);
        this.schemaName = connectionConfiguration.getSchemaName();
        this.catalogName = connectionConfiguration.getCatalogName();
        this.autoCloseDataSource = autoCloseDataSource;
        this.connectionId = connectionConfiguration.getConnectionId();
        this.charset = connectionConfiguration.getCharset();
    }

    public DBCExecutionContext getAndSetExecutionContextDefaults() throws DBException {

        if (executionContext == null) {
            executionContext = handler.getExecutionContext(token,
                    DBCExecutionPurpose.UTIL.getId(),
                    true,
                    true,
                    false,
                    null,
                    charset,
                    connectionConfiguration);
            if (StringUtils.isNotBlank(schemaName)) {
                DBExecUtils.setExecutionContextDefaults(
                        handler.getMonitor(),
                        executionContext.getDataSource(),
                        executionContext,
                        catalogName,
                        null,
                        schemaName);
            }
        }

        return executionContext;
    }

    public DBCExecutionContext getExecutionContext() throws DBException {

        if (executionContext == null) {
            executionContext = handler.getExecutionContext(token,
                    DBCExecutionPurpose.UTIL.getId(),
                    true,
                    true,
                    false,
                    null,
                    charset,
                    connectionConfiguration);
        }

        return executionContext;
    }

    public DBPDataSource getDataSource() throws DBException {
        return handler != null ? handler.getDataSource() : null;
    }

    @Override
    public void close() {
        if (handler != null) {
            if (executionContext != null) {
                handler.closeExecutionContext(token);
            }
            if (autoCloseDataSource) {
                handler.closeDataSource(connectionId, false);
            }
        }
    }
}
