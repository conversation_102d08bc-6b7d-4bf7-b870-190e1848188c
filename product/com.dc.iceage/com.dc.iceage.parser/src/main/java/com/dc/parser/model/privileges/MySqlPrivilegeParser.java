package com.dc.parser.model.privileges;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.ext.mysql.segment.UserSegment;
import com.dc.parser.ext.mysql.statement.dcl.MySQLGrantStatement;
import com.dc.parser.ext.mysql.statement.dcl.MySQLRevokeStatement;
import com.dc.parser.model.segment.dcl.PrivilegeSegment;
import com.dc.parser.model.segment.dcl.RoleOrPrivilegeSegment;
import com.dc.parser.model.statement.dcl.AlterUserStatement;
import com.dc.parser.model.statement.dcl.GrantStatement;
import com.dc.parser.model.statement.dcl.RevokeStatement;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class MySqlPrivilegeParser implements PrivilegeParser {

    @Override
    public void parseGrant(GrantStatement stmt, PrivilegeModel model, DatabaseType databaseType) {

        MySQLGrantStatement mysqlStmt = (MySQLGrantStatement) stmt;

        String schema = "";
        if (!StringUtils.isBlank(mysqlStmt.getLevel().getDatabaseName())) {
            schema = mysqlStmt.getLevel().getDatabaseName();
        }

        String obj = mysqlStmt.getLevel().getTableName();

        List<String> privileges = new ArrayList<>();
        List<String> grantees = new ArrayList<>();
        List<String> hosts = new ArrayList<>();

        mysqlStmt.getRoleOrPrivileges().stream()
                .map(RoleOrPrivilegeSegment::getPrivilege)
                .map(PrivilegeSegment::getPrivilegeType)
                .forEach(privileges::add);

        if (privileges.isEmpty() && mysqlStmt.isAllPrivileges()) {
            privileges.add("ALL PRIVILEGES");
        }

        mysqlStmt.getUsers().stream()
                .map(UserSegment::getUser)
                .forEach(grantees::add);

        mysqlStmt.getUsers().stream()
                .map(UserSegment::getHost)
                .forEach(hosts::add);

        model.setOperateType(1);

        model.setHosts(hosts);

        if (schema.equals("*") && schema.equals(obj)) {

            model.setSystemPrivileges(privileges);

            model.setGrantees(grantees);

            model.setWithGrantOption(mysqlStmt.isWithGrantOption());

            return;
        }

        if (obj.equals("*")) {

            model.setOwner(schema);

            model.setObjPrivileges(privileges);

            model.setGrantees(grantees);

            model.setWithGrantOption(mysqlStmt.isWithGrantOption());

            return;
        }

        model.setNeeded(false);
    }

    @Override
    public void parseRevoke(RevokeStatement stmt, PrivilegeModel model, DatabaseType databaseType) {

        MySQLRevokeStatement mysqlStmt = (MySQLRevokeStatement) stmt;

        if (mysqlStmt.isProxy()) {
            model.setNeeded(false);
            return;
        }

        model.setOperateType(3);

        String schema = "";
        if (!StringUtils.isBlank(mysqlStmt.getLevel().getDatabaseName())) {
            schema = mysqlStmt.getLevel().getDatabaseName();
        }
        String obj = mysqlStmt.getLevel().getTableName();

        List<String> privileges = new ArrayList<>();
        List<String> grantees = new ArrayList<>();

        mysqlStmt.getRoleOrPrivileges().stream()
                .map(RoleOrPrivilegeSegment::getPrivilege)
                .map(PrivilegeSegment::getPrivilegeType).forEach(privileges::add);

        mysqlStmt.getFromUsers().stream().map(UserSegment::getUser).forEach(grantees::add);

        if (mysqlStmt.isAllPrivileges() && privileges.isEmpty()) {
            privileges.add("ALL PRIVILEGES");
        }

        if (schema.equals("*") && schema.equals(obj)) {

            model.setSystemPrivileges(privileges);

            model.setGrantees(grantees);


            return;
        }

        if (obj.equals("*")) {

            model.setOwner(schema);

            model.setObjPrivileges(privileges);

            model.setGrantees(grantees);

            return;
        }

        model.setNeeded(false);

    }

    @Override
    public void parseAlterUser(AlterUserStatement stmt, PrivilegeModel model, DatabaseType databaseType) {
        model.setNeeded(false);
    }
}
