package com.dc.parser.service.impl;

import com.dc.repository.mysql.mapper.UserLimitMapper;
import com.dc.repository.mysql.model.UserLimit;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.parser.dto.ParamsConfigDto;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.utils.model.SqlParseModel;
import com.dc.parser.service.SqlAffectedRowsService;
import com.dc.type.DatabaseType;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.parser.util.ExecuteSqlUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class SqlAffectedRowsServiceImpl implements SqlAffectedRowsService {

    @Override
    public int getRows(ParserParamDto paramDTO, SqlParseModel sqlParserModel) {
        int rows = 0;

        try {

            if (DatabaseType.CLICKHOUSE.getValue().equals(paramDTO.getDbType()) && sqlParserModel.getAst() != null) {
                String ckInsertSelectSql = sqlParserModel.getAction().getCkInsertSelectSql();
                if (StringUtils.isNotBlank(ckInsertSelectSql)) {
                    rows = ExecuteSqlUtil.getCKAffectedRows(paramDTO, SqlConstant.KEY_INSERT, "", ckInsertSelectSql);
                } else {
                    String type = sqlParserModel.getAction().getCkOperation();
                    String whereClause = "";
                    if (sqlParserModel.getAction().getCkWhere() != null) {
                        whereClause = sqlParserModel.getAction().getCkWhere();
                    }
                    String tableName = "";
                    if (sqlParserModel.getSqlAuthModelList().size() > 0) {
                        tableName = sqlParserModel.getSqlAuthModelList().get(0).getSchemaName() + "." + sqlParserModel.getSqlAuthModelList().get(0).getName();
                    }
                    if (type != null && !tableName.isEmpty()) {
                        rows = ExecuteSqlUtil.getCKAffectedRows(paramDTO, type, tableName, whereClause);
                    }
                }
            } else if (DatabaseType.MONGODB.getValue().equals(paramDTO.getDbType()) && sqlParserModel.getCustomSqlStatement() != null) {
                if (sqlParserModel.getCustomSqlStatement().getAffectedRows() != 0) {
                    rows = sqlParserModel.getCustomSqlStatement().getAffectedRows();
                } else {
                    String tableName = sqlParserModel.getSqlAuthModelList().size() > 0
                            ? sqlParserModel.getSqlAuthModelList().get(0).getName()
                            : "";
                    String whereClause = sqlParserModel.getCustomSqlStatement().getWhereCondition() != null
                            ? sqlParserModel.getCustomSqlStatement().getWhereCondition().getStringValue()
                            : "";
                    rows = ExecuteSqlUtil.getMongoDBAffectedRows(paramDTO, sqlParserModel.getOperation(), tableName, whereClause);
                }
            } else if (!Arrays.asList(DatabaseType.REDIS.getValue(), DatabaseType.ELASTIC_SEARCH.getValue()).contains(paramDTO.getDbType())) {
                rows = ExecuteSqlUtil.getAffectedRows(paramDTO, sqlParserModel.getOperation(), sqlParserModel.gettCustomSqlStatement());
            }

        } catch (Exception e) {
            log.error("get rows error!", e);
        }

        return rows;
    }

    @Override
    public long getRowsByExplain(ParserParamDto paramDTO, String query, String schemaName, String tableName) {
        try {

            if (DatabaseType.ORACLE.getValue().equals(paramDTO.getDbType())) {
                // 手动收集统计信息
                ExecuteSqlUtil.executeScript(paramDTO.getExecutionContext(),
                        "BEGIN\n" +
                                "   DBMS_STATS.GATHER_TABLE_STATS('" + schemaName + "', '" + tableName + "');\n" +
                                "END;");
                try {
                    DBExecUtils.execute(new VoidProgressMonitor(), paramDTO.getExecutionContext(), "/", "/");
                } catch (Exception ignored) {
                    // nothing to do here
                }
                String uuid = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 10);
                ExecuteSqlUtil.executeSql(paramDTO.getExecutionContext(), "EXPLAIN PLAN SET STATEMENT_ID = '" + uuid + "' INTO PLAN_TABLE FOR " + query);
                List<Map<String, Object>> list = ExecuteSqlUtil.executeSql(paramDTO.getExecutionContext(), "SELECT CARDINALITY FROM PLAN_TABLE where STATEMENT_ID = '" + uuid + "'");
                if (!list.isEmpty()) {
                    Map<String, Object> stringObjectMap = list.get(0); // oracle有两条结果，取第一条
                    if (stringObjectMap.get("CARDINALITY") != null) {
                        String string = stringObjectMap.get("CARDINALITY").toString();
                        return Long.parseLong(string);
                    }
                }
            } else if (DatabaseType.MYSQL.getValue().equals(paramDTO.getDbType())) {
                List<Map<String, Object>> list = ExecuteSqlUtil.executeSql(paramDTO.getExecutionContext(), "explain " + query);
                if (!list.isEmpty()) {
                    Map<String, Object> stringObjectMap = list.get(0);
                    if (stringObjectMap.get("rows") != null) {
                        String string = stringObjectMap.get("rows").toString();
                        return Long.parseLong(string);
                    }
                }
            } else if (DatabaseType.SQL_SERVER.getValue().equals(paramDTO.getDbType())) {
                ExecuteSqlUtil.executeScript(paramDTO.getExecutionContext(), "SET showplan_xml ON");
                List<Map<String, Object>> list = ExecuteSqlUtil.executeScript(paramDTO.getExecutionContext(), query);
                ExecuteSqlUtil.executeScript(paramDTO.getExecutionContext(), "SET showplan_xml OFF");
                if (!list.isEmpty()) {
                    Map<String, Object> stringObjectMap = list.get(0);
                    for (Map.Entry<String, Object> entry : stringObjectMap.entrySet()) {
                        String string = entry.getValue().toString();

                        XmlMapper xmlMapper = new XmlMapper(); // 创建XML Mapper
                        JsonNode jsonNode = xmlMapper.readTree(string); // 从XML字符串读取数据并解析为JsonNode
                        ObjectMapper jsonMapper = new ObjectMapper(); // 创建JSON Mapper
                        String json = jsonMapper.writeValueAsString(jsonNode); // 将JsonNode转换为JSON字符串

                        Map<String, Object> jsonMap = (Map<String, Object>) JSON.parseObject(json, Map.class);
                        jsonMap = (Map<String, Object>) jsonMap.get("BatchSequence");
                        jsonMap = (Map<String, Object>) jsonMap.get("Batch");
                        jsonMap = (Map<String, Object>) jsonMap.get("Statements");
                        jsonMap = (Map<String, Object>) jsonMap.get("StmtSimple");
                        string = jsonMap.get("StatementEstRows").toString();
                        return Long.parseLong(string);
                    }
                }
            } else if (DatabaseType.DM.getValue().equals(paramDTO.getDbType())) {
                List<Map<String, Object>> list = ExecuteSqlUtil.executeSql(paramDTO.getExecutionContext(), "explain for " + query);
                if (list.size() > 0) {
                    for (Map<String, Object> stringObjectMap : list) {
                        if (stringObjectMap.get("LEVEL_ID") != null && "0".equals(stringObjectMap.get("LEVEL_ID").toString()) && stringObjectMap.get("ROW_NUMS") != null) {
                            String string = stringObjectMap.get("ROW_NUMS").toString();
                            return Long.parseLong(string);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("get rows by explain error!", e);
        }

        return 0L;
    }


    public int getTruncateRows(ParserParamDto paramDTO, String schemaName, String tableName) {
        int rows = 0;
        try {
            String checkSql = "";
            if (Arrays.asList(DatabaseType.ORACLE.getValue(), DatabaseType.DM.getValue()).contains(paramDTO.getDbType())) {
                checkSql ="SELECT NUM_ROWS as \"ROWS\" FROM ALL_TABLES WHERE TABLE_NAME = '" + tableName + "' AND OWNER = '" + schemaName + "'";
            } else if(DatabaseType.DB2.getValue().equals(paramDTO.getDbType())) {
                checkSql = "SELECT CARD as ROWS FROM SYSCAT.TABLES WHERE TABSCHEMA = '" + schemaName + "' AND TABNAME = '" + tableName + "'";
            } if (DatabaseType.SQL_SERVER.getValue().equals(paramDTO.getDbType())) {
                checkSql = "SELECT SUM(p.rows) AS ROWS FROM sys.partitions p JOIN sys.objects o ON p.object_id = o.object_id WHERE o.object_id = OBJECT_ID('" + tableName + "')   AND p.index_id IN (0, 1);  ";
            } else if (Arrays.asList(DatabaseType.GOLDEN_DB.getValue(), DatabaseType.MARIA_DB.getValue(), DatabaseType.MYSQL.getValue(), DatabaseType.TDMYSQL.getValue()).contains(paramDTO.getDbType())) {
                checkSql = "SELECT TABLE_ROWS as 'ROWS' FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '" + tableName + "' AND TABLE_SCHEMA = '" + schemaName + "'";
            }
            List<Map<String, Object>> list = ExecuteSqlUtil.executeSql(paramDTO.getExecutionContext(), checkSql, true);
            if (!list.isEmpty()) {
                log.debug(list.toString());
                String string = list.get(0).get("ROWS").toString();
                return Integer.parseInt(string);
            }

        } catch (Exception e) {
            log.error("get rows error!", e);
        }
        return rows;
    }
}
