package com.dc.parser.model.chain.impl;

import com.dc.parser.model.chain.SqlCheckParserChain;
import com.dc.parser.service.MetaDataStoreService;
import com.dc.parser.service.SqlAffectedRowsService;
import com.dc.parser.service.sql.WebSQLParserInfo;
import com.dc.repository.mysql.mapper.RecycleBinMapper;
import com.dc.repository.mysql.model.RecycleBin;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.parser.dto.ParamsConfigDto;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.type.DefaultSwitchParamType;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.springboot.core.model.type.ParserExecuteType;
import com.dc.summer.model.DBUtils;
import com.dc.type.SecurityRuleType;
import com.dc.parser.util.GenerateSqlUtil;
import com.dc.parser.util.MapValueUtils;
import com.dc.springboot.core.component.DefaultMapper;
import com.dc.type.BackupVerifyType;
import com.dc.parser.util.ExecuteSqlUtil;
import com.dc.summer.parser.sql.model.TableColumnModel;
import com.dc.springboot.core.model.parser.SchemaAttribute;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.dc.summer.parser.utils.model.SqlParseModel;
import com.dc.springboot.core.model.recovery.UpdateSetClauseModel;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.parser.service.PreParedService;
import com.dc.type.DatabaseType;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.parser.type.UpdateSetValueType;
import com.dc.summer.parser.utils.CommonUtil;
import com.dc.parser.util.SqlTransUtil;
import com.dc.sqlparser.types.EExpressionType;
import com.dc.sqlparser.nodes.*;
import com.dc.sqlparser.stmt.TInsertSqlStatement;
import com.dc.sqlparser.stmt.TSelectSqlStatement;
import com.dc.sqlparser.stmt.TUpdateSqlStatement;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class SqlBackupChain extends SqlCheckParserChain {

    private final PreParedService preParedService = Resource.getBeanRequireNonNull(PreParedService.class);
    private final SqlAffectedRowsService sqlAffectedRowsService = Resource.getBeanRequireNonNull(SqlAffectedRowsService.class);
    private final DefaultMapper parserMapper = DefaultMapper.INSTANCE;
    private final RecycleBinMapper recycleBinMapper = Resource.getBeanRequireNonNull(RecycleBinMapper.class);
    private final MetaDataStoreService metaDataStoreService = Resource.getBeanRequireNonNull(MetaDataStoreService.class);

    public SqlBackupChain(ParserParamDto parserParamDto, SqlParseModel sqlParseModel, WebSQLParserInfo webSQLParserInfo) {
        super(parserParamDto, sqlParseModel, webSQLParserInfo);
    }

    @Override
    public boolean proceed(WebSQLParserResult webSQLParserResult) {

        if (!DatabaseType.getIdentCode(DatabaseType.supportSqlBackup()).contains(parserParamDto.getDbType())) {
            return true;
        }

        try {

            final Integer affectedRows = webSQLParserResult.getAffectedRows();

            boolean canBackUp = this.canBackUp(parserParamDto, sqlParseModel, webSQLParserResult);

            if (canBackUp) {
                webSQLParserResult.setCanBackUp(true);
            } else {
                webSQLParserResult.setCanBackUp(false);
                return true;
            }

            // 拿到真实需要备份的表
            SqlAuthModel sqlAuthModel = sqlParseModel.getSqlAuthModelList().get(0);
            if (sqlParseModel.getAction().isContainsBackupTable()) {
                for (SqlAuthModel sqlAuthModelTemp : sqlParseModel.getSqlAuthModelList()) {
                    if (sqlAuthModelTemp.isCanBackup()) {
                        sqlAuthModel = sqlAuthModelTemp;
                        break;
                    }
                }
            }

            // 获取表名
            String tableName = sqlAuthModel.getFullName();
            String schemaName = sqlAuthModel.getSchemaName();

            webSQLParserResult.setBackupTableName(tableName);
            SchemaAttribute schemaAttribute = new SchemaAttribute(parserParamDto.getDbType(), schemaName, sqlAuthModel.getCharset(), sqlAuthModel.getSchemaUniqueKey());
            webSQLParserResult.setFirstSchema(schemaAttribute);

            // 获取字段名
            List<TableColumnModel> tableColumns = metaDataStoreService.getTableColumns(parserParamDto, tableName, schemaName, true);

            StringBuilder sb = new StringBuilder();
            for (TableColumnModel tableColumn : tableColumns) {
                sb.append(CommonUtil.assembleColumnName(tableColumn.getColumnName(), parserParamDto.getDbType())).append(",");
            }
            String tableColumn = sb.length() > 0 ? sb.substring(0, sb.length() - 1) : "*";

            String topClause = "";
            if (sqlParseModel.gettCustomSqlStatement() != null && sqlParseModel.gettCustomSqlStatement().getTopClause() != null) {
                topClause = sqlParseModel.gettCustomSqlStatement().getTopClause().toString();
                tableColumn = topClause + " " + tableColumn;
            }

            // 获取主键
            List<String> columnPrimaryKey = metaDataStoreService.getColumnPrimaryKey(parserParamDto, tableName, schemaName);
            if (!columnPrimaryKey.isEmpty()) {
                webSQLParserResult.setPrimaryKeyColumnsReal(columnPrimaryKey);
            }

            boolean sqlserverHasIdentity = false; // sqlserver是否存在自增列
            List<String> identityColumns = null; // sqlserver自增列
            if (DatabaseType.SQL_SERVER.getValue().equals(parserParamDto.getDbType())) {
                identityColumns = this.getIncrementField(parserParamDto, schemaName + "." + tableName);
                if (!identityColumns.isEmpty()) {
                    sqlserverHasIdentity = true;
                }
            }

            // 给表名和 schema名，添加标识符引用
            if (parserParamDto.getDbType().equals(DatabaseType.SQL_SERVER.getValue())) {
                String frameWorkName = sqlAuthModel.getFrameworkName();
                String name = sqlAuthModel.getName();
                tableName = DBUtils.getQuotedIdentifier(parserParamDto.getExecutionContext().getDataSource(),frameWorkName) + "."
                        + DBUtils.getQuotedIdentifier(parserParamDto.getExecutionContext().getDataSource(),name);
            }else {
                tableName = DBUtils.getQuotedIdentifier(parserParamDto.getExecutionContext().getDataSource(),tableName);
            }
            schemaName = DBUtils.getQuotedIdentifier(parserParamDto.getExecutionContext().getDataSource(),schemaName);

            // 数据备份 转换 select sql
            String sql = "";
            if (Arrays.asList(SqlConstant.KEY_UPDATE, SqlConstant.KEY_DELETE).contains(sqlParseModel.getOperation())) {
                sql = SqlTransUtil.getSelectSql4BK(parserParamDto.getDbType(), sqlParseModel.getOperation(), sqlParseModel.gettCustomSqlStatement(), tableColumn);
            } else if (SqlConstant.KEY_TRUNCATE.equals(sqlParseModel.getOperation())) {
                sql = "select " + tableColumn + " from " + schemaName + "." + tableName;
            }
            webSQLParserResult.setBackUpSql(sql);
            if (tableColumns.isEmpty()) {
                webSQLParserResult.setBackupTableExists(false);
            }

            // 构建其他备份信息
            if (sqlParseModel.getAction().isDuplicateKeyUpdate()) {
                duplicateKeyUpdate(webSQLParserResult, parserParamDto, sqlParseModel, tableName, schemaName, tableColumns, columnPrimaryKey, sqlserverHasIdentity, identityColumns, tableColumn, topClause);
            } else if (sqlParseModel.gettCustomSqlStatement() instanceof TUpdateSqlStatement) {
                update(webSQLParserResult, sqlParseModel, tableColumns, columnPrimaryKey);
            } else if (sqlParseModel.gettCustomSqlStatement() instanceof TInsertSqlStatement) {
                insert(webSQLParserResult, parserParamDto, sqlParseModel, tableName, schemaName, tableColumns, columnPrimaryKey, sqlserverHasIdentity, identityColumns, tableColumn, topClause);
            }

            if (parserParamDto.getParserExecuteTypes().contains(ParserExecuteType.SQL_BACKUP)) {
                verifyBackup(parserParamDto, sqlParseModel, webSQLParserResult, affectedRows);
            }

        } catch (Exception e) {
            log.error("数据备份，解析异常：", e);
            webSQLParserResult.setStatus(SqlExecuteStatus.FAIL.getValue());
            webSQLParserResult.setMessage("数据备份-解析异常! " + e.getMessage());
            return false;
        } finally {
            webSQLParserResult.setShowReason(sqlParseModel.getAction().isShowReason());
        }

        return true;
    }

    public List<String> getIncrementField(ParserParamDto paramDTO, String tableName) {
        List<String> columns = new ArrayList<>();
        try {
            String query = GenerateSqlUtil.getSqlserverIdentitySql(tableName);
            List<Map<String, Object>> list = ExecuteSqlUtil.executeSql(paramDTO.getExecutionContext(), query);
            for (Map<String, Object> map : list) {
                columns.add(MapValueUtils.getIgnoreCase(map, "COLUMN_NAME").toString());
            }
        } catch (Exception e) {
            log.error("get increment field error!", e);
        }
        return columns;
    }

    public void verifyBackup(ParserParamDto paramDTO, SqlParseModel sqlParserModel, WebSQLParserResult webSQLParserResult, Integer affectedRows) {

        try {
            String pValue;
            ParamsConfigDto paramsConfig = webSQLParserInfo.getParamsConfig();
            if (paramsConfig != null && paramsConfig.getBackupDataRowLimit() != null) {
                pValue = paramsConfig.getBackupDataRowLimit();
            } else {
                pValue = webSQLParserInfo.getSystemParamConfig().get(SecurityRuleType.BACKUP_DATA_ROW_LIMIT.getName());
            }

            if (StringUtils.isBlank(pValue) || "0".equals(pValue.trim())) {
                webSQLParserResult.setBackupDataRowLimit(0L);
                webSQLParserResult.setBackupVerify(BackupVerifyType.BACKUP_REQUIRED.getValue());
                return;
            }

            long backupDataRowLimit = Long.parseLong(pValue);
            webSQLParserResult.setBackupDataRowLimit(backupDataRowLimit);

            Integer backupVerify = null;

            if (Arrays.asList(SqlConstant.KEY_UPDATE, SqlConstant.KEY_DELETE, SqlConstant.KEY_INSERT).contains(sqlParserModel.getOperation())) {
                if (affectedRows == null || affectedRows == 0) {
                    affectedRows = sqlAffectedRowsService.getRows(paramDTO, sqlParserModel);
                }

                if (affectedRows > backupDataRowLimit) {
                    backupVerify = BackupVerifyType.BACKUP_REQUIRED_BUT_EXCEEDED_LIMIT.getValue();
                } else {
                    backupVerify = BackupVerifyType.BACKUP_REQUIRED.getValue();
                }
            }  else if (SqlConstant.KEY_TRUNCATE.equals(sqlParserModel.getOperation())) {
                if (affectedRows == null || affectedRows == 0) {
                    affectedRows = sqlAffectedRowsService.getTruncateRows(paramDTO, webSQLParserResult.getFirstSchema().getSchemaName(),
                            webSQLParserResult.getBackupTableName());
                }
                if (affectedRows > backupDataRowLimit) {
                    backupVerify = BackupVerifyType.BACKUP_REQUIRED_BUT_EXCEEDED_LIMIT.getValue();
                } else {
                    backupVerify = BackupVerifyType.BACKUP_REQUIRED.getValue();
                }

            } else {
                if (webSQLParserResult.getSql() != null && StringUtils.isNotBlank(webSQLParserResult.getSql())) {
                    long explainRows = sqlAffectedRowsService.getRowsByExplain(paramDTO, webSQLParserResult.getSql(),
                            webSQLParserResult.getFirstSchema().getSchemaName(),
                            webSQLParserResult.getBackupTableName());
                    if (explainRows > backupDataRowLimit) {
                        backupVerify = BackupVerifyType.BACKUP_REQUIRED_BUT_EXCEEDED_LIMIT.getValue();
                    } else {
                        backupVerify = BackupVerifyType.BACKUP_REQUIRED.getValue();
                    }
                } else {
                    backupVerify = BackupVerifyType.NO_BACKUP_REQUIRED.getValue();
                }
            }

            webSQLParserResult.setBackupVerify(backupVerify);
        } catch (Exception e) {
            log.error("verify backup error!", e);
        }

    }

    /**
     * 备份语句为 update
     */
    private void update(WebSQLParserResult webSQLParserResult, SqlParseModel sqlParserModel,
                        List<TableColumnModel> tableColumns, List<String> columnPrimaryKey) {
        // update语句中被修改的字段
        List<String> updateSetColumns = new ArrayList<>();
        List<UpdateSetClauseModel> updateSetValues = new ArrayList<>();

        if (sqlParserModel.gettCustomSqlStatement() instanceof TUpdateSqlStatement) {

            TUpdateSqlStatement update = (TUpdateSqlStatement) sqlParserModel.gettCustomSqlStatement();

            if (update.getResultColumnList() != null) {

                for (int i = 0; i < update.getResultColumnList().size(); i++) {
                    TResultColumn field = update.getResultColumnList().getResultColumn(i);
                    if (field.getExpr().getLeftOperand() != null && field.getExpr().getRightOperand() != null) {
                        TExpressionList exprLeftList = field.getExpr().getLeftOperand().getExprList();
                        TExpressionList exprRightList = field.getExpr().getRightOperand().getExprList();
                        if (exprLeftList != null) {
                            for (int j=0; j<exprLeftList.size(); j++) {
                                if (exprRightList != null) {
                                    setUpdateValues(updateSetValues, updateSetColumns, exprLeftList.getExpression(j), exprRightList.getExpression(j), columnPrimaryKey);
                                } else {
                                    setUpdateValues(updateSetValues, updateSetColumns, exprLeftList.getExpression(j), field.getExpr().getRightOperand(), columnPrimaryKey);
                                }
//
                            }

                        } else {
                            setUpdateValues(updateSetValues, updateSetColumns, field.getExpr().getLeftOperand(), field.getExpr().getRightOperand(),columnPrimaryKey);
                        }
                       /* String columnName = "";
                        if (field.getExpr().getLeftOperand().getExpressionType() == EExpressionType.simple_object_name_t
                                && field.getExpr().getLeftOperand().getObjectOperand() != null
                                && field.getExpr().getLeftOperand().getObjectOperand().getColumnNameOnly() != null) {
                            columnName = field.getExpr().getLeftOperand().getObjectOperand().getColumnNameOnly();
                        }

                        String columnValue = ""; // new column value
                        UpdateSetValueType columnType = UpdateSetValueType.unknown;
                        if (field.getExpr().getRightOperand().getExpressionType() == EExpressionType.simple_constant_t) {
                            if (field.getExpr().getRightOperand().getConstantOperand() != null
                                    && field.getExpr().getRightOperand().getConstantOperand().getValueToken() != null) {
                                columnValue = field.getExpr().getRightOperand().getConstantOperand().getValueToken().toString();
                                columnType = UpdateSetValueType.constant;
                            }
                        } else if (field.getExpr().getRightOperand().getExpressionType() == EExpressionType.simple_object_name_t) {
                            if (field.getExpr().getRightOperand().getObjectOperand() != null
                                    && field.getExpr().getRightOperand().getObjectOperand().getPartToken() != null) {
                                columnValue = field.getExpr().getRightOperand().getObjectOperand().getPartToken().toString();
                                columnType = UpdateSetValueType.object_name;
                            }
                        } else if (field.getExpr().getRightOperand().getExpressionType() == EExpressionType.subquery_t) {
                            if (field.getExpr().getRightOperand().getSubQuery() != null) {
                                try {
                                    TSelectSqlStatement subQuery = field.getExpr().getRightOperand().getSubQuery();
                                    columnValue = subQuery.toString();
                                    columnType = UpdateSetValueType.sub_query;
                                } catch (Exception e) {
                                    log.error("get new column value from subQuery error!", e);
                                }
                            }
                        } else if (field.getExpr().getRightOperand().getExpressionType() == EExpressionType.function_t) {
                            if (field.getExpr().getRightOperand().getFunctionCall() != null) {
                                try {
                                    TFunctionCall functionCall = field.getExpr().getRightOperand().getFunctionCall();
                                    columnValue = functionCall.getFunctionName().toString();
                                    columnType = UpdateSetValueType.function;
                                } catch (Exception e) {
                                    log.error("get new column value from function error!", e);
                                }
                            }
                        } else if (field.getExpr().getRightOperand().getExpressionType() == EExpressionType.unary_minus_t) {
                            if (field.getExpr().getRightOperand().getOperatorToken() != null
                                    && field.getExpr().getRightOperand().getRightOperand() != null) {
                                String minus = field.getExpr().getRightOperand().getOperatorToken().toString();
                                String number = field.getExpr().getRightOperand().getRightOperand().toString();
                                columnValue = minus + number;
                                columnType = UpdateSetValueType.constant;
                            }
                        }

                        if (!columnName.isEmpty()) {
                            updateSetColumns.add(columnName);

                            UpdateSetClauseModel updateSetClauseModel = new UpdateSetClauseModel();
                            updateSetClauseModel.setColumnName(columnName);
                            updateSetClauseModel.setColumnValue(columnValue);
                            updateSetClauseModel.setColumnType(columnType.getValue());
                            updateSetValues.add(updateSetClauseModel);
                        }*/

                    }
                }

                // 被更改的字段
                if (!updateSetColumns.isEmpty()) {
                    List<String> newUpdateSetColumns = new ArrayList<>();
                    for (String updateCol : updateSetColumns) {
                        for (TableColumnModel tableColumnModel : tableColumns) {
                            String tableCol = tableColumnModel.getColumnName();
                            // 需要是表的真实字段
                            if (CommonUtil.replace(updateCol).equalsIgnoreCase(CommonUtil.replace(tableCol))) {
                                newUpdateSetColumns.add(CommonUtil.replace(tableCol));
                                break;
                            }
                        }
                    }
                    webSQLParserResult.setUpdateSetColumns(newUpdateSetColumns);
                }

            }

        }

        if (!columnPrimaryKey.isEmpty()) {
            webSQLParserResult.setPrimaryKeyColumnsReal(columnPrimaryKey);

            // 主键值变更,记录新的主键值
            if (!updateSetValues.isEmpty()) {
                List<UpdateSetClauseModel> primaryKeyUpdateValue = new ArrayList<>();
                for (String key : columnPrimaryKey) {
                    for (UpdateSetClauseModel setColumn : updateSetValues) {
                        setColumn.setColumnName(CommonUtil.replace(setColumn.getColumnName()));
                        if (key.equalsIgnoreCase(setColumn.getColumnName())) {
                            setColumn.setColumnValue(CommonUtil.replaceParserColumnValue(setColumn.getColumnValue(), true));
                            primaryKeyUpdateValue.add(setColumn);
                            break;
                        }
                    }
                }
                webSQLParserResult.setPrimaryKeyUpdateValue(primaryKeyUpdateValue);
            }

        }
    }

    private void setUpdateValues(List<UpdateSetClauseModel>updateSetValues, List<String> updateSetColumns, TExpression leftOperand, TExpression rightOperand, List<String> columnPrimaryKey)
    {
        String columnName = "";
        if (leftOperand.getExpressionType() == EExpressionType.simple_object_name_t
                && leftOperand.getObjectOperand() != null
                && leftOperand.getObjectOperand().getColumnNameOnly() != null) {
            columnName = leftOperand.getObjectOperand().getColumnNameOnly();
        }

        String columnValue = ""; // new column value
        UpdateSetValueType columnType = UpdateSetValueType.unknown;
        if (rightOperand.getExpressionType() == EExpressionType.simple_constant_t) {
            if (rightOperand.getConstantOperand() != null
                    && rightOperand.getConstantOperand().getValueToken() != null) {
                columnValue = rightOperand.getConstantOperand().getValueToken().toString();
                columnType = UpdateSetValueType.constant;
            }
        } else if (rightOperand.getExpressionType() == EExpressionType.simple_object_name_t) {
            if (rightOperand.getObjectOperand() != null
                    && rightOperand.getObjectOperand().getPartToken() != null) {
                columnValue = rightOperand.getObjectOperand().getPartToken().toString();
                columnType = UpdateSetValueType.object_name;
            }
        } else if (rightOperand.getExpressionType() == EExpressionType.subquery_t) {
            if (rightOperand.getSubQuery() != null) {
                try {
                    TSelectSqlStatement subQuery = rightOperand.getSubQuery();
                    columnValue = subQuery.toString();
                    columnType = UpdateSetValueType.sub_query;
                } catch (Exception e) {
                    log.error("get new column value from subQuery error!", e);
                }
            }
        } else if (rightOperand.getExpressionType() == EExpressionType.function_t) {
            if (rightOperand.getFunctionCall() != null) {
                try {
                    TFunctionCall functionCall = rightOperand.getFunctionCall();
                    columnValue = functionCall.getFunctionName().toString();
                    columnType = UpdateSetValueType.function;
                } catch (Exception e) {
                    log.error("get new column value from function error!", e);
                }
            }
        } else if (rightOperand.getExpressionType() == EExpressionType.unary_minus_t) {
            if (rightOperand.getOperatorToken() != null
                    && rightOperand.getRightOperand() != null) {
                String minus = rightOperand.getOperatorToken().toString();
                String number = rightOperand.getRightOperand().toString();
                columnValue = minus + number;
                columnType = UpdateSetValueType.constant;
            }
        }

        if (!columnName.isEmpty()) {
            updateSetColumns.add(columnName);

            UpdateSetClauseModel updateSetClauseModel = new UpdateSetClauseModel();
            updateSetClauseModel.setColumnName(columnName);
            updateSetClauseModel.setColumnValue(columnValue);
            updateSetClauseModel.setColumnType(columnType.getValue());
            updateSetValues.add(updateSetClauseModel);
        }
    }
    /**
     * 备份语句为 insert
     */
    private void insert(WebSQLParserResult webSQLParserResult, ParserParamDto paramDTO, SqlParseModel sqlParserModel,
                        String tableName, String schemaName, List<TableColumnModel> tableColumns,
                        List<String> columnPrimaryKey, Boolean sqlserverHasIdentity, List<String> identityColumns,
                        String tableColumn, String topClause) {
        // insert需要解析出主键值是否输入以及需要备份的其他列值
        TInsertSqlStatement insert = (TInsertSqlStatement) sqlParserModel.gettCustomSqlStatement();
        TObjectNameList columnList = insert.getColumnList(); // columns

        List<String> insertColumn = new ArrayList<>();
        if (columnList != null && columnList.size() > 0) {
            for (TObjectName objectName : columnList) {
                String columnNameOnly = objectName.getColumnNameOnly();
                if (columnNameOnly != null) {
                    insertColumn.add(CommonUtil.replace(columnNameOnly));
                }
            }
        } else {
            insertColumn = tableColumns.stream().map(TableColumnModel::getColumnName).collect(Collectors.toList());
        }
        webSQLParserResult.setInsertSetColumns(insertColumn);
        List<String> insertColumnUpper = new ArrayList<>();
        for (String col : insertColumn) {
            insertColumnUpper.add(col.toUpperCase(Locale.ROOT));
        }

        List<String> primaryColumnUpper = new ArrayList<>();
        for (String col : columnPrimaryKey) {
            primaryColumnUpper.add(col.toUpperCase(Locale.ROOT));
        }

        boolean notRealPk = false;
        for (String pk : primaryColumnUpper) {
            if (!insertColumnUpper.contains(pk)) {
                notRealPk = true;
                break;
            }
        }

        TMultiTargetList values = insert.getValues(); // values
        if (values != null && values.size() > 0) {

            List<Map<String, String>> insertKeyValues = new ArrayList<>();

            for (TMultiTarget multiTarget : values) {

                List<String> insertValue = new ArrayList<>();
                TResultColumnList columnTempList = multiTarget.getColumnList();
                if (columnTempList != null) {
                    for (TResultColumn tResultColumn : columnTempList) {
                        TExpression expr = tResultColumn.getExpr();
                        if (expr != null) {
                            if (expr.getConstantOperand() != null) {
                                String value = expr.getConstantOperand().toString();
                                if (value != null) {
                                    if ( "null".equalsIgnoreCase(value)) {
                                        value = null;
                                    }
                                    insertValue.add(CommonUtil.replaceParserColumnValue(value, false));
                                }
                            } else if (expr.getObjectOperand() != null) {
                                String value = expr.getObjectOperand().toString();
                                if (value != null) {
                                    if ( "null".equalsIgnoreCase(value)) {
                                        value = null;
                                    }
                                    insertValue.add(CommonUtil.replaceParserColumnValue(value, false));
                                }
                            } else if (EExpressionType.unary_minus_t.equals(expr.getExpressionType()) && expr.getOperatorToken() != null && expr.getRightOperand() != null){
                                String minus = expr.getOperatorToken().toString();
                                String number = expr.getRightOperand().toString();
                                insertValue.add(minus + CommonUtil.replaceParserColumnValue(number, false));
                            } else if (EExpressionType.parenthesis_t.equals(expr.getExpressionType()) && expr.getLeftOperand() != null) {
                                String value = expr.getLeftOperand().getConstantOperand().toString();
                                if (value != null) {
                                    if ( "null".equalsIgnoreCase(value)) {
                                        value = null;
                                    }
                                    insertValue.add(CommonUtil.replaceParserColumnValue(value, false));
                                }
                            } else {
                                insertValue.add(null);
                            }
                        } else {
                            insertValue.add(null);
                        }
                    }
                }

                // 解析出的键值
                Map<String, String> insertKeyValue = new LinkedHashMap<>();
                if (sqlserverHasIdentity) {
                    sqlserverHasIdentity(insertColumn, identityColumns);
                }
                if (insertColumn.size() == insertValue.size()) {
                    int i = 0;
                    for (String col : insertColumn) {
                        insertKeyValue.put(col, insertValue.get(i++));
                    }
                }

                insertKeyValues.add(insertKeyValue);

            }

            webSQLParserResult.setInsertKeyValues(insertKeyValues);
            webSQLParserResult.setNotRealPk(notRealPk);

            if (insertKeyValues.size() > 0) {
                String sql = "select " + tableColumn + " from " + schemaName + "." + tableName + " ";
                String whereClause = CommonUtil.buildWhereClause(insertKeyValues, tableColumns, paramDTO.getDbType(), primaryColumnUpper);
                if (!whereClause.isEmpty()) {
                    sql += whereClause;
                    webSQLParserResult.setBackUpSql(sql);
                }
            }

        } else if (sqlParserModel.getAction().isInsertIntoSelect() && insert.getSubQuery() != null) {
            String subSql = insert.getSubQuery().toString();
            if (StringUtils.isNotBlank(topClause)) {
                subSql = subSql.replaceFirst("(?i)select", "select " + topClause);
            }
            webSQLParserResult.setBackUpSql(subSql);
            webSQLParserResult.setInsertIntoSelect(true);
        } else if (insert.getSetColumnValues() != null && insert.getSetColumnValues().size() > 0) {

            TResultColumnList setColumnValues = insert.getSetColumnValues();
            List<TExpression> exprs = setColumnValues.getSortedColumns().stream().map(TResultColumn::getExpr).filter(e -> {
                String field = e.getLeftOperand().toString();
                String value = e.getRightOperand().toString();
                for (String pk : primaryColumnUpper) {
                    if (pk.equalsIgnoreCase(field) && value.equalsIgnoreCase("null")) {
                        return false;
                    }
                }
                return true;
            }).collect(Collectors.toList());

            StringBuilder whereClause = new StringBuilder("where ");
            for (int i = 0; i < exprs.size() - 1; i++) {
                whereClause.append(exprs.get(i).toString()).append(" and ");
            }
            whereClause.append(exprs.get(exprs.size() - 1).toString());
            String sql = "select " + tableColumn + " from " + schemaName + "." + tableName + " " + whereClause;
            webSQLParserResult.setBackUpSql(sql);
        }
    }

    /**
     * 备份语句为 on duplicate key update 类型
     */
    private void duplicateKeyUpdate(WebSQLParserResult webSQLParserResult, ParserParamDto paramDTO, SqlParseModel sqlParserModel,
                                    String tableName, String schemaName, List<TableColumnModel> tableColumns,
                                    List<String> columnPrimaryKey, Boolean sqlserverHasIdentity, List<String> identityColumns,
                                    String tableColumn, String topClause) {

        TObjectNameList columnList = ((TInsertSqlStatement) sqlParserModel.gettCustomSqlStatement()).getColumnList();
        if (columnList == null) {
            log.info("on duplicate key update 类型的sql中查不到列值");
            return;
        }

        if (columnPrimaryKey == null || columnPrimaryKey.size() == 0) {
            log.info("on duplicate key update 类型sql的表中查不到主键列");
            return;
        }

        TResultColumnList columnValueList = null;
        try {
            columnValueList = ((TInsertSqlStatement) sqlParserModel.gettCustomSqlStatement()).getValues().getMultiTarget(0).getColumnList();
        } catch (Exception e) {
            log.error("transform to TInsertSqlStatement error!", e);
        }

        List<Integer> primaryIndexes = new ArrayList<>();
        for (int i = 0; i < columnList.size(); i++) {
            String column = columnList.getObjectName(i).toString();
            for (String primary : columnPrimaryKey) {
                if (column.equals(primary)) {
                    primaryIndexes.add(i);
                    break;
                }
            }
        }

        // 判断backUpOperation类型
        String backupOperation = "";
        String whereConditions = "";
        if (primaryIndexes.size() == 0) {
            backupOperation = SqlConstant.KEY_INSERT;
        } else {
            StringBuilder tempQuery = new StringBuilder("SELECT * FROM " + schemaName + "." + tableName);
            StringBuilder tempWhereConditions = new StringBuilder(" WHERE 1=1 AND ");
            String query;

            for (Integer i : primaryIndexes) {
                tempWhereConditions.append(columnList.getObjectName(i).toString()).append("=")
                        .append(columnValueList.getResultColumn(i).toString())
                        .append(" AND ");
            }

            whereConditions = tempWhereConditions.substring(0, tempWhereConditions.lastIndexOf("AND ")) + ";";
            query = tempQuery.append(whereConditions).toString();

            try {
                List<Map<String, Object>> list = ExecuteSqlUtil.executeSql(paramDTO.getExecutionContext(), query);
                if (list.size() != 0) {
                    backupOperation = SqlConstant.KEY_UPDATE;
                } else {
                    backupOperation = SqlConstant.KEY_INSERT;
                }
            } catch (Exception e) {
                log.error("get backup operation error!", e);
            }
        }

        webSQLParserResult.setBackupOperation(backupOperation);

        if (backupOperation.equals(SqlConstant.KEY_INSERT)) {
            insert(webSQLParserResult, paramDTO, sqlParserModel, tableName, schemaName, tableColumns, columnPrimaryKey, sqlserverHasIdentity, identityColumns, tableColumn, topClause);
        } else if (backupOperation.equals(SqlConstant.KEY_UPDATE)) {
            String updateSql = "UPDATE " + schemaName + "." + tableName + " SET "
                    + ((TInsertSqlStatement) sqlParserModel.gettCustomSqlStatement()).getOnDuplicateKeyUpdate().toString() + whereConditions;

            ParserParamDto paramClone = parserMapper.copyPreCheckParamDTO(paramDTO);
            paramClone.setSql(updateSql);

            List<SqlParseModel> sqlParserModelList = preParedService.sqlParser(paramClone);
            if (sqlParserModelList == null || sqlParserModelList.size() == 0) {
                log.info("UPDATE语句无法生成SqlParserModel解析模型");
                return;
            }

            SqlParseModel sqlParserModelNew = sqlParserModelList.get(0);
            webSQLParserResult.setBackUpSql(SqlTransUtil.getSelectSql4BK(paramDTO.getDbType(), sqlParserModelNew.getOperation(), sqlParserModelNew.gettCustomSqlStatement(), tableColumn));

            update(webSQLParserResult, sqlParserModelNew, tableColumns, columnPrimaryKey);
        }
    }

    private void sqlserverHasIdentity(List<String> insertColumn, List<String> identityColumns) {
        for (String str : identityColumns) {
            if (insertColumn.contains(str)) {
                insertColumn.remove(str);
            }
        }
    }

    private String transformSqlserverTableName(String oldTable, Integer dbType) {
        if (StringUtils.isBlank(oldTable) || !DatabaseType.SQL_SERVER.getValue().equals(dbType)) {
            return oldTable;
        }

        String left = "[";
        String right = "]";
        String[] split = oldTable.split("\\.");
        if (split.length == 1) {
            return left + oldTable + right;
        } else if (split.length == 2) {
            return left + split[0] + right + "." + left + split[1] + right;
        } else if (split.length == 3) {
            return left + split[0] + right + "." + left + split[1] + right + "." + left + split[2] + right;
        }
        return oldTable;
    }

    private boolean canBackUp(ParserParamDto paramDTO, SqlParseModel sqlParserModel, WebSQLParserResult webSQLParserResult) {

        if (!Arrays.asList(SqlConstant.KEY_UPDATE, SqlConstant.KEY_DELETE, SqlConstant.KEY_INSERT,
                SqlConstant.KEY_TRUNCATE).contains(sqlParserModel.getOperation())) {
            return false;
        }

        // gBase8a/clickhouse/hive/inceptor/spark 主键值可重复,当作无主键处理
        // impala(Impala 2.5.0-cdh5.7.0)/hetu 无主键
        if (Arrays.asList(DatabaseType.G_BASE_8A.getValue(), DatabaseType.CLICKHOUSE.getValue(), DatabaseType.HIVE.getValue(),
                DatabaseType.ADBMYSQL2.getValue(), DatabaseType.IMPALA.getValue(), DatabaseType.INCEPTOR.getValue(),
                DatabaseType.HETU.getValue(), DatabaseType.SPARK.getValue(), DatabaseType.H_BASE.getValue()).contains(paramDTO.getDbType())) {
            return false;
        }

        if (sqlParserModel.gettCustomSqlStatement() == null
                || sqlParserModel.gettCustomSqlStatement().getTables() == null
                || sqlParserModel.getSqlAuthModelList().isEmpty()) {
            return false;
        }

        // 标记 insert ignore 语句
        if (sqlParseModel.gettCustomSqlStatement() instanceof TInsertSqlStatement) {
            TInsertSqlStatement statement = (TInsertSqlStatement) sqlParseModel.gettCustomSqlStatement();
            if (statement.getIgnore() != null) {
                webSQLParserResult.setIgnore(true);
            } else if (statement.getInsertToken() != null && statement.getInsertToken().toString().equalsIgnoreCase("replace")){
                webSQLParserResult.setReplace(true);
                return false;
            }
        }

        boolean canBackup = true;

        // 是多表，且不包含可备份的表，且不是DuplicateKey Update语句，不可备份
        if (sqlParserModel.getSqlAuthModelList().size() != 1
                && !sqlParserModel.getAction().isContainsBackupTable()
                && !sqlParserModel.getAction().isDuplicateKeyUpdate()) {
            canBackup = false;
        } else if (sqlParserModel.gettCustomSqlStatement().getTables().size() != 1) {
            canBackup = false;
        }

        // 拿到真实需要备份的表
        SqlAuthModel sqlAuthModel = sqlParserModel.getSqlAuthModelList().get(0);
        if (sqlParserModel.getAction().isContainsBackupTable()) {
            for (SqlAuthModel sqlAuthModelTemp : sqlParserModel.getSqlAuthModelList()) {
                if (sqlAuthModelTemp.isCanBackup()) {
                    sqlAuthModel = sqlAuthModelTemp;
                    break;
                }
            }
        }

        // 判断是否配置了误操作恢复
        RecycleBin recycleBin = this.recycleBinMapper.getRecycleBinBySchemaId(sqlAuthModel.getSchemaUniqueKey());
        if (recycleBin == null) {
            recycleBin = this.recycleBinMapper.getRecycleBinByConnectionId(paramDTO.getConnectId());
        }

        if (recycleBin != null && StringUtils.isNotBlank(recycleBin.getOperations())) {
            List<String> recycleOperations = Arrays.asList(recycleBin.getOperations().split(","));
            recycleOperations = recycleOperations.stream().map(String::toUpperCase).collect(Collectors.toList());
            if (!recycleOperations.contains(sqlParserModel.getOperation())) {
                return false;
            }
        } else {
            return false;
        }

        // 动态SQL、任务管理、脚本变更、导入工单 根据安全规则中的开关，二次校验
        switch (OriginType.of(paramDTO.getOrigin())) {
            case DYNAMIC_SQL:
                if (DefaultSwitchParamType.defaultOpen(webSQLParserInfo.getSystemParamConfig().get(SecurityRuleType.FAST_CHANGE_BACKUP_CONFIG.getName())).isDisable()) {
                    return false;
                }
                break;
            case JOB_EXECUTE:
                if (DefaultSwitchParamType.defaultOpen(webSQLParserInfo.getSystemParamConfig().get(SecurityRuleType.SQL_EXECUTE_BACKUP_CONFIG.getName())).isDisable()) {
                    return false;
                }
                break;
            case SCRIPT:
                if (DefaultSwitchParamType.defaultOpen(webSQLParserInfo.getSystemParamConfig().get(SecurityRuleType.DATA_CHANGE_BACKUP_CONFIG.getName())).isDisable()) {
                    return false;
                }
                break;
            case IMPORT:
                if (DefaultSwitchParamType.defaultOpen(webSQLParserInfo.getSystemParamConfig().get(SecurityRuleType.DATA_IMPORT_BACKUP_CONFIG.getName())).isDisable()) {
                    return false;
                }
                break;
            default:
        }

        if (!canBackup) {
            sqlParserModel.getAction().setShowReason(true);
        }

        return canBackup;
    }

}
