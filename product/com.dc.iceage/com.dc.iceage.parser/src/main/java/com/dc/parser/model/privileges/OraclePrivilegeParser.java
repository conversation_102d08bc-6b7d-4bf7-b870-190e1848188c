package com.dc.parser.model.privileges;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.segment.dcl.OracleRoleOrPrivilegeSegment;
import com.dc.parser.model.segment.dcl.QuotasSegment;
import com.dc.parser.model.statement.dcl.AlterUserStatement;
import com.dc.parser.model.statement.dcl.GrantStatement;
import com.dc.parser.model.statement.dcl.RevokeStatement;
import com.dc.springboot.core.model.privilege.PrivilegeModel;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

import static com.dc.parser.model.util.SQLUtils.getActualObjectName;

public class OraclePrivilegeParser implements PrivilegeParser {

    @Override
    public void parseAlterUser(AlterUserStatement stmt, PrivilegeModel model, DatabaseType databaseType) {
        List<String> grantees = stmt.getUsers().stream().map(g -> getActualObjectName(databaseType, g)).collect(Collectors.toList());
        List<String> roles = stmt.getDefaultRoles().stream().map(p -> getActualObjectName(databaseType, p)).collect(Collectors.toList());
        List<String> expectRoles = new ArrayList<>();
        if (stmt.getExpectRoles() != null) {
            expectRoles = stmt.getExpectRoles().stream().map(e -> getActualObjectName(databaseType, e)).collect(Collectors.toList());
        }
        model.setOperateType(2);
        model.setRolePrivileges(roles);
        model.setGrantees(grantees);
        model.setExpectRoles(expectRoles);
        model.setAll(stmt.isALL());
        model.setNone(stmt.isNone());
        QuotasSegment quotas = stmt.getQuotasSegment();
        if (quotas != null) {
            model.setQuotas(true);
            model.setTablespace(getActualObjectName(databaseType, quotas.getTablespace()));
            if (quotas.getQuotasSize() != null) {
                BigDecimal size = new BigDecimal(quotas.getQuotasSize());
                BigDecimal unit = new BigDecimal("1024");
                if (quotas.getCapacityUnit() != null) {
                    switch (quotas.getCapacityUnit().toUpperCase(Locale.ROOT)) {
                        case "E":
                            size = size.multiply(unit);
                        case "P":
                            size = size.multiply(unit);
                        case "T":
                            size = size.multiply(unit);
                        case "G":
                            size = size.multiply(unit);
                        case "M":
                            size = size.multiply(unit);
                        case "K":
                            size = size.multiply(unit);
                            break;
                    }
                }
                model.setMaxBytes(size.toString());
            }
            model.setUnlimited(quotas.isUnlimited());
        }
    }

    @Override
    public void parseRevoke(RevokeStatement stmt, PrivilegeModel model, DatabaseType databaseType) {
        List<String> objPrivileges = new ArrayList<>();
        List<String> rolePrivileges = new ArrayList<>();
        List<String> systemPrivileges = new ArrayList<>();
        for (OracleRoleOrPrivilegeSegment rop : stmt.getOracleRoleOrPrivileges()) {
            if (rop.getPrivilege() != null) {
                if (rop.getPrivilege().getColumns().isEmpty()) {
                    objPrivileges.add(rop.getPrivilege().getPrivilegeType().toUpperCase(Locale.ROOT).replaceAll("\\s+", " "));
                }
            } else if (rop.getRole() != null) {
                rolePrivileges.add(getActualObjectName(databaseType, rop.getRole()));
            } else if (rop.getSystemPrivileges() != null) {
                systemPrivileges.add(rop.getSystemPrivileges().toUpperCase(Locale.ROOT).replaceAll("\\s+", " "));
            }
        }

        boolean isDir = stmt.getDirectoryNameSegment().isPresent();
        List<String> revokeUsers = stmt.getRevokeFromUsers().stream().map(g -> getActualObjectName(databaseType, g)).collect(Collectors.toList());
        String owner = "";
        String onObj = "";
        if (stmt.getTableSegment().isPresent()) {
            if (stmt.getTableSegment().get().getOwner().isPresent()) {
                owner = getActualObjectName(databaseType, stmt.getTableSegment().get().getOwner().get().getIdentifier());
            }
            onObj = getActualObjectName(databaseType, stmt.getTableSegment().get().getTableName().getIdentifier());
        } else if (isDir) {
            if (stmt.getDirectoryNameSegment().get().getOwner().isPresent()) {
                owner = getActualObjectName(databaseType, stmt.getDirectoryNameSegment().get().getOwner().get().getIdentifier());
            }
            onObj = getActualObjectName(databaseType, stmt.getDirectoryNameSegment().get().getName());
        }

        model.setDirectory(isDir);
        model.setOperateType(3);
        model.setObjPrivileges(objPrivileges);
        model.setRolePrivileges(rolePrivileges);
        model.setSystemPrivileges(systemPrivileges);
        model.setOwner(owner);
        model.setOnObject(onObj);
        model.setGrantees(revokeUsers);
    }

    @Override
    public void parseGrant(GrantStatement stmt, PrivilegeModel model, DatabaseType databaseType) {
        List<String> objPrivileges = new ArrayList<>();
        List<String> rolePrivileges = new ArrayList<>();
        List<String> systemPrivileges = new ArrayList<>();
        for (OracleRoleOrPrivilegeSegment rop : stmt.getOracleRoleOrPrivileges()) {
            if (rop.getPrivilege() != null) {
                if (rop.getPrivilege().getColumns().isEmpty()) {
                    objPrivileges.add(rop.getPrivilege().getPrivilegeType().toUpperCase(Locale.ROOT).replaceAll("\\s+", " "));
                }
            } else if (rop.getRole() != null) {
                String role = getActualObjectName(databaseType, rop.getRole());
                rolePrivileges.add(role);

            } else if (rop.getSystemPrivileges() != null) {
                systemPrivileges.add(rop.getSystemPrivileges().toUpperCase(Locale.ROOT).replaceAll("\\s+", " "));
            }
        }
        boolean isDir = stmt.getDirectoryNameSegment().isPresent();
        List<String> grantees = stmt.getGrantees().getGranteeList().stream().map(g -> getActualObjectName(databaseType, g)).collect(Collectors.toList());
        String onObj = "";
        String owner = "";
        if (stmt.getTableSegment().isPresent()) {
            if (stmt.getTableSegment().get().getOwner().isPresent()) {
                owner = getActualObjectName(databaseType, stmt.getTableSegment().get().getOwner().get().getIdentifier());
            }
            onObj = getActualObjectName(databaseType, stmt.getTableSegment().get().getTableName().getIdentifier());
        } else if (isDir) {
            if (stmt.getDirectoryNameSegment().get().getOwner().isPresent()) {
                owner = getActualObjectName(databaseType, stmt.getDirectoryNameSegment().get().getOwner().get().getIdentifier());
            }
            onObj = getActualObjectName(databaseType, stmt.getDirectoryNameSegment().get().getName());
        }

        model.setOperateType(1);
        model.setObjPrivileges(objPrivileges);
        model.setRolePrivileges(rolePrivileges);
        model.setSystemPrivileges(systemPrivileges);
        model.setDirectory(isDir);
        model.setOwner(owner);
        model.setOnObject(onObj);
        model.setGrantees(grantees);
        model.setWithAdminOption(stmt.getGrantees().isAdminOption());
        model.setWithGrantOption(stmt.getGrantees().isGrantOption());
        model.setWithHierarchyOption(stmt.getGrantees().isHierarchyOption());
    }
}
