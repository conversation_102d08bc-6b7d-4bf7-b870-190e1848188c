package com.dc.parser.model.chain.impl;

import com.dc.infra.spi.TypedSPILoader;
import com.dc.parser.clickhouse.ast.AlterTableClause;
import com.dc.parser.clickhouse.ast.AlterTableQuery;
import com.dc.parser.clickhouse.ast.DeleteAlterTableClause;
import com.dc.parser.clickhouse.ast.UpdateAlterTableClause;
import com.dc.parser.clickhouse.visitor.BaseSqlBuilder;
import com.dc.parser.component.ParserMapper;
import com.dc.parser.exec.check.SQLRuleCheckEngine;
import com.dc.parser.exec.context.SQLContextEngine;
import com.dc.parser.exec.sql.SQLStatementParserEngine;
import com.dc.parser.exec.sql.SQLStatementParserEngineFactory;
import com.dc.parser.model.chain.SqlCheckChain;
import com.dc.parser.model.chain.SqlCheckParserChain;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleContent;
import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.model.engine.CacheOption;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.metadata.ShardingSphereMetaData;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.rewrite.SQLRewriteEngine;
import com.dc.parser.rewrite.config.RuleConfiguration;
import com.dc.parser.rewrite.result.SQLRewriteResult;
import com.dc.parser.service.SqlAffectedRowsService;
import com.dc.parser.service.ThirdPartySqlAuditService;
import com.dc.parser.service.impl.SQLCheckExecutorImpl;
import com.dc.parser.service.sql.WebSQLParserInfo;
import com.dc.parser.type.ConnectionModeType;
import com.dc.parser.util.GetAuthUtil;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.parser.dto.AuthDto;
import com.dc.springboot.core.model.parser.dto.CheckResultDto;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.type.DefaultSwitchParamType;
import com.dc.sqlparser.stmt.TSelectSqlStatement;
import com.dc.sqlparser.stmt.vertica.TVerticaShow;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.summer.parser.sql.constants.OperationAuthConstant;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.utils.CommonUtil;
import com.dc.summer.parser.utils.SqlOperationUtil;
import com.dc.summer.parser.utils.model.SqlParseModel;
import com.dc.type.AuditOriginType;
import com.dc.type.DatabaseType;
import com.dc.type.SecurityRuleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

import static com.dc.infra.database.type.DatabaseType.Constant.MYSQL;
import static com.dc.infra.database.type.DatabaseType.Constant.ORACLE;

@Slf4j
public class SqlAuditChain extends SqlCheckParserChain {

    private final SqlAffectedRowsService sqlAffectedRowsService = Resource.getBeanRequireNonNull(SqlAffectedRowsService.class);
    private final ParserMapper parserMapper = ParserMapper.INSTANCE;

    private final ThirdPartySqlAuditService thirdPartySqlAuditService;

    public SqlAuditChain(ParserParamDto parserParamDto, SqlParseModel sqlParseModel, WebSQLParserInfo webSQLParserInfo) {
        super(parserParamDto, sqlParseModel, webSQLParserInfo);
        this.thirdPartySqlAuditService = Resource.getBean(ThirdPartySqlAuditService.class);
    }

    @Override
    public boolean proceed(WebSQLParserResult webSQLParserResult) {

        try {

            // 是否审核在安全规则中开启
            switch (AuditOriginType.of(parserParamDto.getAuditOrigin())) {
                case SQL_WINDOWS:
                    if (DefaultSwitchParamType.defaultOpen(webSQLParserInfo.getSystemParamConfig().get(SecurityRuleType.SQL_WINDOWS_SQL_AUDIT.getName())).isDisable()) {
                        return true;
                    }
                    break;
                case DATA_CHANGE:
                    if (DefaultSwitchParamType.defaultOpen(webSQLParserInfo.getSystemParamConfig().get(SecurityRuleType.DATA_CHANGE_SQL_AUDIT.getName())).isDisable()) {
                        return true;
                    }
                    break;
                default:
            }

            List<CheckRuleContent> sqlCheckRuleParamList = webSQLParserInfo.getCheckRuleContents();

            List<CheckResult> check = null;
            List<CheckResult> thirdPartyCheck = null;

            // 检查是否启用第三方SQL审核
            if (thirdPartySqlAuditService.isThirdPartyAuditEnabled(parserParamDto.getSqlAuditCheckSource())) {
                log.info("执行第三方SQL审核");
                thirdPartyCheck = thirdPartySqlAuditService.auditSql(parserParamDto.getSql(), webSQLParserInfo.getConnectionConfig().getConnectionConfiguration(), parserParamDto.getExecutionContext().getDataSource().getInfo());
            }

            // 检查是否启用本系统SQL审核
            if (thirdPartySqlAuditService.isSystemAuditEnabled(parserParamDto.getSqlAuditCheckSource())) {
                log.info("执行本系统SQL审核");
                check = executeSystemAudit(parserParamDto, sqlParseModel, sqlCheckRuleParamList);
            }

            // 合并审核结果
            List<CheckResult> allCheckResults = new ArrayList<>();
            if (check != null) {
                allCheckResults.addAll(check);
            }
            if (thirdPartyCheck != null) {
                allCheckResults.addAll(thirdPartyCheck);
            }

            // 处理合并后的审核结果
            if (!allCheckResults.isEmpty()) {
                webSQLParserResult.getCheckRuleList().addAll(parserMapper.toCheckResultDto(allCheckResults.stream().filter(e -> e.getStatus() == 1).collect(Collectors.toList())));
                webSQLParserResult.setAlert(allCheckResults.stream().anyMatch(e -> e.getIsAlert() == 1));
            }

        } catch (Exception e) {
            log.error("sql审核出错！", e);
        } finally {
            CheckResultDto workOrderSqlAuthVerify = checkWorkOrderSqlAuthVerify(parserParamDto, sqlParseModel, webSQLParserInfo);
            if (workOrderSqlAuthVerify != null) {
                webSQLParserResult.getCheckRuleList().add(workOrderSqlAuthVerify);
            }
        }

        return true;
    }

    /**
     * 执行本系统SQL审核
     */
    private List<CheckResult> executeSystemAudit(ParserParamDto parserParamDto, SqlParseModel sqlParseModel, List<CheckRuleContent> sqlCheckRuleParamList) {
        List<CheckResult> check = null;

        //TODO mysql审核
        if (DatabaseType.ORACLE.getValue().equals(parserParamDto.getDbType())) {
            try {

                DBCExecutionContext executionContext = parserParamDto.getExecutionContext();
                Connection connection = ((JDBCExecutionContext) executionContext).getConnection(new LoggingProgressMonitor());

                com.dc.infra.database.type.DatabaseType databaseType = TypedSPILoader.getService(com.dc.infra.database.type.DatabaseType.class, ORACLE);

                ShardingSphereMetaData metaData = new ShardingSphereMetaData(databaseType, connection, true, true);

                SQLStatementParserEngine sqlStatementParserEngine = SQLStatementParserEngineFactory.getSQLStatementParserEngine(databaseType, new CacheOption(2000, 65535L), new CacheOption(64, 1024L));

                SQLStatement sqlStatement = sqlStatementParserEngine.parse(sqlParseModel.getSql(), false);

                SQLContextEngine sqlContextEngine = new SQLContextEngine(metaData, parserParamDto.getSchemaName(), parserParamDto.getCatalogName());

                SQLStatementContext sqlStatementContext = sqlContextEngine.bind(sqlStatement, Collections.emptyList());

                RuleConfiguration config = RuleConfiguration.oracleRowIdInjectionOnly();

                SQLRewriteEngine engine = new SQLRewriteEngine(config);

                SQLRewriteResult result = engine.rewrite(sqlStatementContext, sqlParseModel.getSql(), metaData);

                check = new SQLRuleCheckEngine(
                        parserParamDto.getSql(),
                        ORACLE,
                        new SQLCheckExecutorImpl(parserParamDto.getExecutionContext()),
                        sqlCheckRuleParamList,
                        parserParamDto.getSchemaName(),
                        parserParamDto.isEditing())
                        .check();
            } catch (Exception e) {
                log.error("审核sql解析异常！", e);
                if (!Arrays.asList(SqlConstant.KEY_BEGIN, SqlConstant.KEY_DECLARE).contains(sqlParseModel.getOperation())) {
                    check = checkSyntaxError(sqlCheckRuleParamList, parserParamDto.getDbType());
                }
            }
        } else if (DatabaseType.MYSQL.getValue().equals(parserParamDto.getDbType())) {
            try {

                DBCExecutionContext executionContext = parserParamDto.getExecutionContext();
                Connection connection = ((JDBCExecutionContext) executionContext).getConnection(new LoggingProgressMonitor());

                com.dc.infra.database.type.DatabaseType databaseType = TypedSPILoader.getService(com.dc.infra.database.type.DatabaseType.class, MYSQL);

                ShardingSphereMetaData metaData = new ShardingSphereMetaData(databaseType, connection, true, true);

                SQLStatementParserEngine sqlStatementParserEngine = SQLStatementParserEngineFactory.getSQLStatementParserEngine(databaseType, new CacheOption(2000, 65535L), new CacheOption(64, 1024L));

                SQLStatement sqlStatement = sqlStatementParserEngine.parse(sqlParseModel.getSql(), false);

                SQLContextEngine sqlContextEngine = new SQLContextEngine(metaData, parserParamDto.getSchemaName(), parserParamDto.getCatalogName());

                SQLStatementContext sqlStatementContext = sqlContextEngine.bind(sqlStatement, Collections.emptyList());

                RuleConfiguration config = RuleConfiguration.dmlPrecomputationOnly();

                SQLRewriteEngine engine = new SQLRewriteEngine(config);

                SQLRewriteResult result = engine.rewrite(sqlStatementContext, sqlParseModel.getSql(), metaData);

                check = new SQLRuleCheckEngine(
                        parserParamDto.getSql(),
                        MYSQL,
                        new SQLCheckExecutorImpl(parserParamDto.getExecutionContext()),
                        sqlCheckRuleParamList,
                        parserParamDto.getSchemaName(),
                        parserParamDto.isEditing())
                        .check();
            } catch (Exception e) {
                log.error("审核sql解析异常！", e);
                if (!Arrays.asList(SqlConstant.KEY_BEGIN, SqlConstant.KEY_DECLARE).contains(sqlParseModel.getOperation())) {
                    check = checkSyntaxError(sqlCheckRuleParamList, parserParamDto.getDbType());
                }
            }
        } else {
            check = checkRule(parserParamDto, sqlParseModel, sqlCheckRuleParamList);
        }

        return check;
    }

    private static CheckResultDto checkWorkOrderSqlAuthVerify(ParserParamDto parserParamDto, SqlParseModel sqlParseModel, WebSQLParserInfo webSQLParserInfo) {

        // 工单越权加入审核提醒中，且是否提示由自身开关控制
        if (AuditOriginType.DATA_CHANGE.getValue().equals(parserParamDto.getAuditOrigin())
                && !"0".equals(webSQLParserInfo.getSystemParamConfig().get(SecurityRuleType.DATA_CHANGE_EXCEEDING_AUTH.getName()))) {

            final boolean isAccountDirectConnection = ConnectionModeType.ACCOUNT_DIRECT_CONNECTION.getValue().equals(parserParamDto.getCurrentPattern());

            SqlCheckChain sqlCheckChain = isAccountDirectConnection ?
                    new SqlAuthVerifyDirectChain(parserParamDto, sqlParseModel, webSQLParserInfo) :
                    new SqlAuthVerifySecurityChain(parserParamDto, sqlParseModel, webSQLParserInfo);

            if (sqlCheckChain.proceed(new WebSQLParserResult())) {
                return null;
            }

            CheckResultDto checkResultDto = new CheckResultDto();
            checkResultDto.setRuleName("用户当前无此对象的此操作权限");
            checkResultDto.setLevel(Integer.parseInt(webSQLParserInfo.getSystemParamConfig().get(SecurityRuleType.DATA_CHANGE_EXCEEDING_AUTH.getName())));
            return checkResultDto;

        }

        return null;
    }

    private List<CheckResult> checkRule(ParserParamDto paramDTO, SqlParseModel sqlParserModel, List<CheckRuleContent> sqlCheckRuleParamList) {

        List<CheckResult> checkResultList = new ArrayList<>();

        String authOperation;
        List<String> otherOperations;
        boolean hasWhereClause;
        boolean hasSelectWhere;
        if (DatabaseType.H_BASE.getValue().equals(paramDTO.getDbType())) {
            authOperation = paramDTO.getAuthDtoList().get(0).getOperations().get(0);
            otherOperations = new ArrayList<>();
            for (AuthDto authDto : paramDTO.getAuthDtoList()) {
                otherOperations.addAll(authDto.getOperations());
            }
            otherOperations.add(authOperation);
            hasWhereClause = StringUtils.isNotEmpty(paramDTO.getWhereClause());
            hasSelectWhere = hasWhereClause;
        } else {
            String objectType = Arrays.asList(SqlConstant.KEY_CREATE, SqlConstant.KEY_DROP, SqlConstant.KEY_ALTER, SqlConstant.KEY_EXECUTE).contains(sqlParserModel.getOperation())
                    ? CommonUtil.getObjectType(sqlParserModel.getSql(), paramDTO.getDbType(), sqlParserModel.getCustomSqlStatement(), sqlParserModel.gettCustomSqlStatement())
                    : "";
            authOperation = GetAuthUtil.getAuthOperation(CommonUtil.getObjectOperation(sqlParserModel.getOperation(), paramDTO.getDbType(), sqlParserModel.gettCustomSqlStatement()), objectType);
            otherOperations = SqlOperationUtil.getOtherOperations(sqlParserModel.gettCustomSqlStatement(), paramDTO.getDbType(), sqlParserModel.getCustomSqlStatement());
            otherOperations.add(authOperation);
            otherOperations.add(sqlParserModel.getOperation().toLowerCase(Locale.ROOT));

            hasWhereClause = CommonUtil.hasWhereClause(paramDTO.getDbType(), sqlParserModel.gettCustomSqlStatement(), sqlParserModel.getCustomSqlStatement())
                    || (SqlConstant.KEY_SHOW.equalsIgnoreCase(authOperation) && CommonUtil.getShowWhereClause(sqlParserModel.getSql()));

            if (!Arrays.asList(DatabaseType.MONGODB.getValue(), DatabaseType.ELASTIC_SEARCH.getValue()).contains(paramDTO.getDbType())
                    && SqlConstant.KEY_UPDATE.equalsIgnoreCase(sqlParserModel.getOperation())) {
                hasSelectWhere = CommonUtil.updateHasSelectWhere(sqlParserModel.gettCustomSqlStatement());
            } else {
                hasSelectWhere = hasWhereClause;
            }

            if (DatabaseType.CLICKHOUSE.getValue().equals(paramDTO.getDbType()) && List.of(SqlConstant.KEY_UPDATE, SqlConstant.KEY_DELETE).contains(sqlParserModel.getOperation())) {
                hasWhereClause = hasClickHouseWhereClause(sqlParserModel);
            }

        }

        String prefix = CheckRuleDatabasePrefix.of(paramDTO.getDbType()).getValue();

        Map<String, CheckRuleContent> sqlCheckRuleMap = sqlCheckRuleParamList
                .stream()
                .collect(Collectors.toMap(CheckRuleContent::getUniqueKey, each -> each, (value1, value2) -> value1));

        // 语法错误或者解析器不支持，请人工确认SQL正确性
        CheckRuleContent checkSyntaxError = sqlCheckRuleMap.get(prefix + CheckRuleUniqueKey.ALL_CHECK_SYNTAX_ERROR.getValue());
        if (checkSyntaxError != null && parserError(paramDTO.getDbType(), sqlParserModel)) {
            checkResultList.add(CheckResult.buildFailResult(checkSyntaxError));
            return checkResultList;
        }

        // select语句建议指定where条件
        CheckRuleContent checkSelectHasWhere = sqlCheckRuleMap.get(prefix + CheckRuleUniqueKey.DML_CHECK_SELECT_HAS_WHERE.getValue());
        if (otherOperations.contains(OperationAuthConstant.select) && checkSelectHasWhere != null && !hasSelectWhere) {
            checkResultList.add(CheckResult.buildFailResult(checkSelectHasWhere));
        }

        // update/delete语句建议指定where条件
        CheckRuleContent checkUpdateOrDeleteHasWhere = sqlCheckRuleMap.get(prefix + CheckRuleUniqueKey.DML_CHECK_UPDATE_OR_DELETE_HAS_WHERE.getValue());
        if (Arrays.asList(OperationAuthConstant.delete, OperationAuthConstant.update).contains(authOperation) && checkUpdateOrDeleteHasWhere != null && !hasWhereClause) {
            checkResultList.add(CheckResult.buildFailResult(checkUpdateOrDeleteHasWhere));
        }

        // 自定义规则
        CheckRuleContent checkOperationHighRisk = sqlCheckRuleMap.get(prefix + CheckRuleUniqueKey.ALL_CHECK_OPERATION_HIGH_RISK.getValue());
        if (checkOperationHighRisk != null
                && hasIntersection(Arrays.stream(checkOperationHighRisk.getValue().split(","))
                .collect(Collectors.toList()), otherOperations)) {
            checkResultList.add(CheckResult.buildFailResult(checkOperationHighRisk));
        }

        // UPDATE和DELETE语句评估影响行数过大
        CheckRuleContent checkAffectedRows = sqlCheckRuleMap.get(prefix + CheckRuleUniqueKey.DML_CHECK_AFFECTED_ROWS.getValue());
        if (Arrays.asList(OperationAuthConstant.delete, OperationAuthConstant.update).contains(authOperation) && checkAffectedRows != null) {
            int rows = paramDTO.isEditing() ? 1 : sqlAffectedRowsService.getRows(paramDTO, sqlParserModel);
            if (rows >= Integer.parseInt(checkAffectedRows.getValue())) {
                checkAffectedRows.appendAffectedRows(rows);
                checkResultList.add(CheckResult.buildFailResult(checkAffectedRows));
            }
        }

        // 单条INSERT语句，建议批量插入不超过阈值
        CheckRuleContent checkInsertRows = sqlCheckRuleMap.get(prefix + CheckRuleUniqueKey.DML_CHECK_BATCH_INSERT_LISTS_MAX.getValue());
        if (Objects.equals(OperationAuthConstant.insert, authOperation) && checkInsertRows != null) {
            int rows = paramDTO.isEditing() ? 1 : sqlAffectedRowsService.getRows(paramDTO, sqlParserModel);
            if (rows >= Integer.parseInt(checkInsertRows.getValue())) {
                checkInsertRows.appendAffectedRows(rows);
                checkResultList.add(CheckResult.buildFailResult(checkInsertRows));
            }
        }

        return checkResultList;
    }

    private static List<CheckResult> checkSyntaxError(List<CheckRuleContent> sqlCheckRuleParamList, Integer dbType) {
        for (CheckRuleContent checkRuleContent : sqlCheckRuleParamList) {
            if (checkRuleContent.getUniqueKey().equals(CheckRuleDatabasePrefix.of(dbType).getValue() + CheckRuleUniqueKey.ALL_CHECK_SYNTAX_ERROR.getValue())) {
                return List.of(CheckResult.buildFailResult(checkRuleContent));
            }
        }
        return null;
    }

    private static boolean hasClickHouseWhereClause(SqlParseModel sqlParserModel) {
        if (sqlParserModel.getAst() instanceof AlterTableQuery) {
            AlterTableQuery stmt = (AlterTableQuery) sqlParserModel.getAst();
            if (stmt.getClauses() != null) {
                List<AlterTableClause> clauses = stmt.getClauses();
                BaseSqlBuilder baseSqlBuilder = new BaseSqlBuilder();
                for (AlterTableClause alterTableClause : clauses) {
                    if (alterTableClause instanceof DeleteAlterTableClause) {
                        DeleteAlterTableClause deleteAlterTableClause = (DeleteAlterTableClause) alterTableClause;
                        String deleteWhere = baseSqlBuilder.visitFunctionColumnExpr(deleteAlterTableClause.getExpr());
                        return deleteWhere != null && !deleteWhere.isEmpty();
                    } else if (alterTableClause instanceof UpdateAlterTableClause) {
                        UpdateAlterTableClause updateAlterTableClause = (UpdateAlterTableClause) alterTableClause;
                        String updateWhere = baseSqlBuilder.visitWhereClause(updateAlterTableClause.getWhere());
                        return updateWhere != null && !updateWhere.isEmpty();
                    }
                }
            }
        }
        return false;
    }

    private static boolean parserError(Integer dbType, SqlParseModel sqlParserModel) {
        String objectType = "";
        if (Arrays.asList(SqlConstant.KEY_CREATE, SqlConstant.KEY_DROP, SqlConstant.KEY_ALTER, SqlConstant.KEY_EXECUTE).contains(sqlParserModel.getOperation())) {
            objectType = CommonUtil.getObjectType(sqlParserModel.getSql(), dbType, sqlParserModel.getCustomSqlStatement(), sqlParserModel.gettCustomSqlStatement());
        }

        if (!Arrays.asList(DatabaseType.CLICKHOUSE.getValue(), DatabaseType.H_BASE.getValue()).contains(dbType) && sqlParserModel.gettCustomSqlStatement() != null
                && sqlParserModel.gettCustomSqlStatement().getErrorCount() > 0 && objectType.isEmpty()) {
            boolean hasNoError =
                    (DatabaseType.getIdentCode(DatabaseType.notReturnErrorMessage()).contains(dbType) && sqlParserModel.gettCustomSqlStatement() instanceof TSelectSqlStatement)
                            || (Arrays.asList(DatabaseType.OCEAN_BASE_ORACLE.getValue(), DatabaseType.DM.getValue()).contains(dbType) && Arrays.asList(SqlConstant.KEY_DELETE, SqlConstant.KEY_UPDATE, SqlConstant.KEY_INSERT).contains(sqlParserModel.getOperation()))
                            || (sqlParserModel.gettCustomSqlStatement() instanceof TVerticaShow)
                            || (DatabaseType.KING_BASE.getValue().equals(dbType) && SqlConstant.KEY_DECLARE.equalsIgnoreCase(sqlParserModel.getOperation()))
                            || (Arrays.asList(DatabaseType.ADBMYSQL3.getValue(), DatabaseType.HETU.getValue()).contains(dbType) && SqlConstant.KEY_INSERT.equalsIgnoreCase(sqlParserModel.getOperation()))
                            || (CommonUtil.useColonSplit(dbType) && Arrays.asList(SqlConstant.KEY_COMMENT, SqlConstant.KEY_EXECUTE).contains(sqlParserModel.getOperation()))
                            || (Arrays.asList(DatabaseType.OSCAR.getValue(), DatabaseType.OSCAR_CLUSTER.getValue()).contains(dbType) && Arrays.asList(SqlConstant.KEY_COMMENT, SqlConstant.KEY_INSERT, SqlConstant.KEY_EXECUTE).contains(sqlParserModel.getOperation()))
                            || (DatabaseType.HETU.getValue().equals(dbType) && Arrays.asList(SqlConstant.KEY_TRUNCATE, SqlConstant.KEY_SHOW).contains(sqlParserModel.getOperation()))
                            || (Arrays.asList(DatabaseType.MYSQL.getValue(), DatabaseType.SEQUOIA_MYSQL.getValue(), DatabaseType.DORIS.getValue()).contains(dbType) && SqlConstant.KEY_SET.equals(sqlParserModel.getOperation()));
            return !hasNoError;
        } else if (DatabaseType.getIdentCode(DatabaseType.useDcSqlParser()).contains(dbType) && sqlParserModel.getCustomSqlStatement() != null) {
            return !sqlParserModel.getCustomSqlStatement().isSupport() && !DatabaseType.ELASTIC_SEARCH.getValue().equals(dbType);
        } else {
            return false;
        }
    }

    private static boolean hasIntersection(List<String> main, List<String> sub) {
        Set<String> intersection = new HashSet<>(main);
        intersection.retainAll(sub); // 保留 main 和 sub 的交集
        return !intersection.isEmpty(); // 如果交集不为空，则返回 true
    }

}
