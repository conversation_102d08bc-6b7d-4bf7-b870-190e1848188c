package com.dc.parser.util;

import com.dc.parser.sensitive.model.GSQLParserColumn;
import com.dc.parser.sensitive.model.GSQlParserNode;
import com.dc.parser.sensitive.model.SubQueryFromSourceTab;
import com.dc.springboot.core.model.sensitive.MaskNodeModel;
import com.dc.parser.type.ColAsciiType;
import com.dc.summer.parser.utils.CommonUtil;
import com.dc.type.DatabaseType;
import com.dc.sqlparser.types.EExpressionType;
import com.dc.sqlparser.types.ESqlClause;
import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.DSourceToken;
import com.dc.sqlparser.nodes.*;
import com.dc.sqlparser.stmt.TInsertSqlStatement;
import com.dc.sqlparser.stmt.TSelectSqlStatement;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class GetMaskNodeUtil {

    private final String TOKEN_IN_CHAIN = ".";

    private Integer dbType;
    private String schemaName; // database
    private String framework;//  dbo
    private DCustomSqlStatement tCustomSqlStatement;

    private List<MaskNodeModel> nodes = new ArrayList<>();

    private Map<TParseTreeNode, Set<GSQLParserColumn>> tParseTreeNodeSetHashMap = new HashMap<>();

    private Map<Integer, Boolean> compositeStmtCache = new HashMap<>();

    //SubQuery
    private List<SubQueryFromSourceTab> joins = new ArrayList<>();

    private Map<String, String> nameToAliasMap = new HashMap<>();

    public GetMaskNodeUtil(Integer dbType, String schemaName, String framework, DCustomSqlStatement tCustomSqlStatement) {
        this.dbType = dbType;
        this.schemaName = schemaName;
        this.framework = framework;
        this.tCustomSqlStatement = tCustomSqlStatement;
    }


    public List<MaskNodeModel> parser() {

        //select
        if (tCustomSqlStatement instanceof TSelectSqlStatement) {
            this.extractedStatement(new GSQlParserNode(tCustomSqlStatement));
        }

        // insert into select ....
        if (tCustomSqlStatement instanceof TInsertSqlStatement) {
            TInsertSqlStatement tInsertSqlStatement = (TInsertSqlStatement) tCustomSqlStatement;
            TSelectSqlStatement subQuery = null;
            if (null != tInsertSqlStatement) {
                subQuery = tInsertSqlStatement.getSubQuery();
            }
            if (tInsertSqlStatement.getSubQuery() != null) {
                this.extractedStatement(new GSQlParserNode(subQuery));
            }
        }

        return this.nodes;
    }


    private void extractedStatement(GSQlParserNode... nodes) {

        if (null != nodes) {

            for (GSQlParserNode node : nodes) {

                DCustomSqlStatement tCustomSqlStatement = (DCustomSqlStatement) node.getTreeNode();

                // 复合语句（可拆分为Left+Right）时避免深度拆分重复提取语句
                // 完整SQL = SQL1 + SQL2 + SQL3
                // leftStmt 部分拆解过程：：
                // (SQL1 + SQL2) + SQL3
                // SQL1 + SQL2
                // SQL1

                int code = tCustomSqlStatement.toString().hashCode();
                if (compositeStmtCache.containsKey(code)) {
                    return;
                }
                //最基础的语句例如 SQL1
                compositeStmtCache.put(code, true);


                if (null != tCustomSqlStatement && checkNodeNotRepeat(node)) {

                    //union || union all . 解析工具问题 这个一定放在getResultColumnList 上面，因为默认加载getLeftStmt
                    if (tCustomSqlStatement instanceof TSelectSqlStatement) {
                        TSelectSqlStatement selectSqlStatement = (TSelectSqlStatement) tCustomSqlStatement;
                        if (null != selectSqlStatement.getLeftStmt() && null != selectSqlStatement.getRightStmt()) {
                            Long startIndex = this.nodes.stream().filter(e -> false == e.isBizColumn()).count();
                            this.extractedStatement(new GSQlParserNode(selectSqlStatement.getLeftStmt()), new GSQlParserNode(selectSqlStatement.getRightStmt()));
                            Long endIndex = this.nodes.stream().filter(e -> false == e.isBizColumn()).count();
                            //能够被执行的union 百分之百返回列的数量一致
                            int columnSize = selectSqlStatement.getLeftStmt().getResultColumnList().size();
                            //修复union 合并列脱敏， select name from tab  union select id from tab ; name 和id 合并列了，需要同时处理. union 正常情况下 返回列数量必须是一致
                            this.mergeMaskNodes(startIndex.intValue(), endIndex.intValue(), columnSize);
                        }
                    }

                    //1-1 : tab- {columnName}
                    TResultColumnList tResultColumns = tCustomSqlStatement.getResultColumnList();
                    this.extractedResultColumnList(new GSQlParserNode(tResultColumns, node.getColumnExpr(), node.getColumnList()));

                    //2-1 : select name from tab,tab1;  引导 name同时属于 tab ,tab2:
                    TTableList tables = tCustomSqlStatement.getTables();
                    this.extractedTablesColumnList(new GSQlParserNode(tables, node.getColumnExpr(), node.getColumnList()));

                    //3-1  : subQuery- {columnName}
                    TJoinList joins = tCustomSqlStatement.getJoins();
                    this.extractedJoinsColumnList(new GSQlParserNode(joins, node.getColumnExpr()));
                }
            }
        }
    }

    private boolean checkNodeNotRepeat(GSQlParserNode node) {
        TParseTreeNode treeNode = node.getTreeNode();
        GSQLParserColumn columnExpr = node.getColumnExpr();
        tParseTreeNodeSetHashMap.computeIfAbsent(treeNode, tParseTreeNode -> new HashSet<>());
        return tParseTreeNodeSetHashMap.get(treeNode).add(columnExpr);
    }


    private void extractedTablesColumnList(GSQlParserNode node) {

        TObjectNameList columnList = node.getColumnList();

        TTableList tables = (TTableList) node.getTreeNode();

        GSQLParserColumn columnExpr = node.getColumnExpr();
        //识别NAME 》 select NAME from tab,tab1
        if (null != tables && tables.size() > 0) {

            TObjectNameList firstTObjectNameList = null;
            Set<TObjectName> noSourceTableColumns = new HashSet<>();

            for (TTable table : tables) {


                if (null != table) {

                    /*TAliasClause aliasClause = table.getAliasClause();*/
                    TObjectName tableName = table.getTableName();


                    if (null != tableName) {

                        MaskNodeModel tableToken = new NewDBOSchemaUtil(schemaName, framework, tableName).parser();//  this._3fSchemaSource(tableName);
                        String extractedTable = tableToken.getTableName();
                        if (DatabaseType.getIdentCode(DatabaseType.get3FDatabaseTypeList()).contains(this.dbType)) {
                            extractedTable = String.format("%s%s%s", tableToken.getFramework(), this.TOKEN_IN_CHAIN, tableToken.getTableName());
                        }


                        //----------- step 1--------------

                        TObjectNameList tObjectNameList = null;
                        if (null != table.getObjectNameReferences()) {
                            tObjectNameList = table.getObjectNameReferences();
                        }

                        //特殊处理击穿
                        //fix bug: select name , password from tab1 a,tab2 b  ,
                        if (null != tObjectNameList && tObjectNameList.size() <= 0 && null != firstTObjectNameList) {
                            // 因解析器 无法获取tab2 的列。故采用tab1的列集合
                            tObjectNameList = firstTObjectNameList;
                        }

                        // 没有指定来源的表，对每个表都赋值，把列填充进去。
                        // fix bug: select column from t1 join t2 on...  column 是 t2 导致无法脱敏问题
                        for (TObjectName tObjectName : noSourceTableColumns) {
                            if (tObjectNameList == null) {
                                tObjectNameList = new TObjectNameList();
                            }
                            tObjectNameList.addObjectName(tObjectName);
                        }

                        if (null != tObjectNameList && tObjectNameList.size() > 0) {

                            //缓存tab1列集合
                            firstTObjectNameList = tObjectNameList;

                            for (int i = 0; i < tObjectNameList.size(); i++) {
                                TObjectName tObj = tObjectNameList.getObjectName(i);

                                if (null != tObj && ESqlClause.selectList.equals(tObj.getLocation()) && null != tableToken && null != tableName && null != tableName.getObjectToken() && null != tObj.getDbObjectType() && "column".equals(tObj.getDbObjectType().name())) {

                                    if (tObj.getSourceTable() == null) {
                                        noSourceTableColumns.add(tObj);
                                    }

                                    //select (select password from tab a join tab2 on a.id= b.pid) from  ... ;
                                    String extractedColumn = tObj.getColumnNameOnly();
                                    boolean containsAlias = nameToAliasMap.containsKey(extractedColumn);
                                    String extractedAlias = containsAlias ? nameToAliasMap.get(extractedColumn) : tObj.getColumnNameOnly();
                                    if (null != columnExpr && null != columnExpr.getParentAliasClause()) {
                                        extractedAlias = columnExpr.getParentAliasClause().getAliasName().toString();
                                    }

                                    if (!containsAlias && null != extractedColumn && null != extractedAlias && !extractedColumn.equals(extractedAlias)) {
                                        continue;
                                    }

                                    //辅助subQuery  select name from (select name from tab union select name from tab2) a;  引导 name同时属于 tab ,tab2:
                                    joins.add(new SubQueryFromSourceTab(extractedColumn, extractedTable, extractedAlias));

                                    //logger.info("tabs link 1 :column=>{}, alias=>{}, tab=> {}, expr=>{}", extractedColumn, extractedAlias, extractedTable, tObj.toString());
                                    this.collectResultSet(new MaskNodeModel(tObj.toString(), extractedColumn, extractedAlias, extractedTable, true));
                                }
                            }
                        }


                        //----------- step 2--------------
                        TObjectNameList linkedColumns = null;
                        if (null != table.getLinkedColumns()) {
                            linkedColumns = table.getLinkedColumns();
                        }

                        //特殊处理击穿
                        //fix bug: select name , password from tab1 a,tab2 b  ,
                        if (null != linkedColumns && linkedColumns.size() <= 0 && null != firstTObjectNameList) {
                            // 因解析器 无法获取tab2 的列。故采用tab1的列集合
                            linkedColumns = firstTObjectNameList;
                        }


                        if (null != linkedColumns && linkedColumns.size() > 0) {
                            //缓存tab1列集合
                            firstTObjectNameList = linkedColumns;

                            for (TObjectName tObj : linkedColumns) {

                                if (null != tObj && ESqlClause.selectList.equals(tObj.getLocation()) && null != tableToken && null != tableName && null != tableName.getObjectToken()) {

                                    //select (select password from tab a join tab2 on a.id= b.pid) from  ... ;
                                    String extractedAlias = tObj.getColumnNameOnly();
                                    String extractedColumn = tObj.getColumnNameOnly();
                                    if (null != columnExpr && null != columnExpr.getParentAliasClause()) {
                                        extractedAlias = columnExpr.getParentAliasClause().getAliasName().toString();
                                    }

                                    if (null != extractedColumn && null != extractedAlias && !extractedColumn.equals(extractedAlias)) {
                                        continue;
                                    }

                                    //logger.info("tabs link 2 :column=>{}, alias=>{}, tab=> {}, expr=>{}", extractedColumn, extractedAlias, extractedTable, tObj.toString());
                                    this.collectResultSet(new MaskNodeModel(tObj.toString(), extractedColumn, extractedAlias, extractedTable, true));
                                }
                            }
                        }
                    }
                }
            }
        }

    }

    private void extractedJoinsColumnList(GSQlParserNode node) {

        TJoinList joins = (TJoinList) node.getTreeNode();

        if (null != joins && joins.size() > 0) {

            for (TJoin join : joins) {

                if (null != join) {

                    TTable table = join.getTable();

                    //----------- step 1--------------
                    if (null != table && null != table.getSubquery()) {
                        TSelectSqlStatement tSelectSqlStatement = table.getSubquery();
                        this.extractedStatement(new GSQlParserNode(tSelectSqlStatement, node.getColumnExpr()));
                    } else if (null != table && null != table.getCTE() && null != table.getCTE().getSubquery()) {
                        this.extractedStatement(new GSQlParserNode(table.getCTE().getSubquery(), node.getColumnExpr(), table.getCTE().getColumnList()));
                    }

                }
            }
        }
    }


    private void extractedResultColumnList(GSQlParserNode node) {

        TObjectNameList columnList = node.getColumnList();

        TResultColumnList tResultColumnList = (TResultColumnList) node.getTreeNode();
        if (tResultColumnList != null && tResultColumnList.size() > 0) {

            //
            GSQLParserColumn columnExpr = node.getColumnExpr();

            for (int i = 0; i < tResultColumnList.size(); i++) {
                TResultColumn tResultColumn = tResultColumnList.getResultColumn(i);

                if (null == tResultColumn || null == tResultColumn.getExpr() || null == tResultColumn.getExpr().getExpressionType()) {
                    continue;
                }

                //
                TExpression tExpr = tResultColumn.getExpr();

                if (null != tExpr) {

                    //def as {sql}
                    TAliasClause aliasClause = tResultColumn.getAliasClause();
                    if (null == aliasClause && !ColAsciiType.ASCII_2A.getValue().equals(tExpr.toString())) {
                        aliasClause = createAlias(tExpr);
                    }

                    //order => 1
                    String aliasName = null;
                    if (null != aliasClause) {
                        aliasName = aliasClause.getAliasName().toString();
                    }

                    //order => 2
                    // 识别 pwd 来源于 内部password 字段; sample 》  select pwd as p, n from (select password as pwd, name as n  from tab ) b
                    if ((null != columnExpr && null != aliasClause && null != columnExpr.getParentColumn() && columnExpr.getParentColumn().equals(aliasClause.getAliasName().toString()))) {
                        aliasClause = columnExpr.getParentAliasClause();
                    }

                    //order => 3
                    boolean isSelectALL = false;
                    //sample  》 select password as pwd from (select * from tab ) b
                    if (null != columnExpr && ColAsciiType.ASCII_2A.getValue().equals(tExpr.toString())) {
                        aliasClause = columnExpr.getParentAliasClause();
                        isSelectALL = true;
                    }

                    //order => 4
                    //select pwd from (select password as pwd, id  from ...)  ; 识别嵌套 pwd 属于 password;  外部SQL列名称  == 内部SQL列得别名
                    if (null != aliasName && null != columnExpr && null != columnExpr.getParentColumn() && !aliasName.equals(columnExpr.getParentColumn()) && !isSelectALL) {
                        continue;
                    }

//                    if (null != columnExpr) {
//                        columnExpr.setParentAliasClause(aliasClause);
//                    }

                    TObjectName tObjectName = null;
                    if (columnList != null && columnList.size() == tResultColumnList.size()) {
                        tObjectName = columnList.getObjectName(i);
                    }

                    //
                    this.extractedExpr(new GSQlParserNode(tExpr, null == columnExpr ? new GSQLParserColumn(aliasClause) : columnExpr), tObjectName);
                }
            }
        }
    }

    private void extractedExpr(GSQlParserNode node, TObjectName tObjectName) {

        TExpression tExpr = (TExpression) node.getTreeNode();

        if (null != tExpr) {

            String extractedTable = null;
            String extractedColumn = null;
            String extractedAlias = null;
            String extractedScript = tExpr.toString();

            //
            TTable sourceTable = null;
            TObjectName tableName = null;

            //--------- def Alias start --------
            TAliasClause aliasClause = createAlias(tExpr);
            //--------- def Alias end -----------


            //use parent AliasClause
            GSQLParserColumn columnExpr = node.getColumnExpr();
            if (null != columnExpr) {
                aliasClause = columnExpr.getParentAliasClause();
            }

            //
            if (null != aliasClause) {
                extractedAlias = aliasClause.getAliasName().toString();
            }

            TObjectName objectOperand = tExpr.getObjectOperand();
            if (null != objectOperand && null != objectOperand.getSourceTable()) {
                sourceTable = objectOperand.getSourceTable();
                extractedColumn = objectOperand.getColumnNameOnly();
                tableName = objectOperand.getSourceTable().getTableName();
            }

            MaskNodeModel tableToken = new NewDBOSchemaUtil(schemaName, this.framework, tableName).parser();//  this._3fSchemaSource(tableName);
            extractedTable = tableToken.getTableName();
            if (DatabaseType.getIdentCode(DatabaseType.get3FDatabaseTypeList()).contains(this.dbType)) {
                extractedTable = String.format("%s%s%s", tableToken.getFramework(), this.TOKEN_IN_CHAIN, tableToken.getTableName());
            }

            //fix 》 select NAME as N from (select * from (select *  from tab ) a) b
            if (null != columnExpr && null != columnExpr.getParentColumn() && ColAsciiType.ASCII_2A.getValue().equals(extractedColumn)) {
                extractedColumn = columnExpr.getParentColumn();
            }

            if (tObjectName != null) {
                extractedAlias = tObjectName.toString();
                nameToAliasMap.put(extractedColumn != null ? extractedColumn : extractedScript, extractedAlias);
            }

            //simple column
            if (tExpr.getExpressionType().equals(EExpressionType.simple_object_name_t)) {
                //logger.info("simple:column=>{}, alias=>{}, tab=> {}, expr=>{}", extractedColumn, extractedAlias, extractedTable, extractedScript);
                this.collectResultSet(new MaskNodeModel(extractedScript, extractedColumn, extractedAlias, extractedTable));
            }

            //simple constant
            if (tExpr.getExpressionType().equals(EExpressionType.simple_constant_t)) {
                //System.out.println(2);
            }

            //20220715 fix  > v1.0  语法之间存在各种嵌套由此采用EExpressionType类型识别局限性太小

            //subQuery  =>select ({subQuery}) from ...
            /*if (tExpr.getExpressionType().equals(EExpressionType.subquery_t)) {*/
            if (null != tExpr.getSubQuery() && null != tExpr.getSubQuery().getResultColumnList()) {
                //logger.info("sub query 1:column=>{}, alias=>{}, tab=> {}, expr=>{}", extractedColumn, extractedAlias, extractedTable, extractedScript);
                this.collectResultSet(new MaskNodeModel(extractedScript, extractedColumn, extractedAlias, extractedTable));
                this.extractedStatement(new GSQlParserNode(tExpr.getSubQuery(), new GSQLParserColumn(aliasClause, extractedColumn, extractedAlias)));
            }

            //subQuery  =>select ... from ({subQuery}) a
            if (null != sourceTable && null != sourceTable.getSubquery()) {
                //logger.info("sub query 2:column=>{}, alias=>{}, tab=> {}, expr=>{}", extractedColumn, extractedAlias, extractedTable, extractedScript);
                this.collectResultSet(new MaskNodeModel(extractedScript, extractedColumn, extractedAlias, extractedTable));
                this.extractedStatement(new GSQlParserNode(sourceTable.getSubquery(), new GSQLParserColumn(aliasClause, extractedColumn, extractedAlias)));
            }

            //subQuery  =>select ... from ( {unionSql} ) a
            if (null != joins && joins.size() > 0) {
                //处理 select name from (select name from tab union select name from tab2) a;  引导 name同时属于 tab ,tab2:
                for (SubQueryFromSourceTab temp : joins) {
                    if (null != extractedColumn && extractedColumn.equals(temp.getAlias())) {
                        this.collectResultSet(new MaskNodeModel(extractedScript, temp.getColumn(), extractedAlias, temp.getTable(), true));
                    }
                    //子查询存在 select * 则匹配所有
                    if (null != extractedColumn && temp.getAlias().equals(ColAsciiType.ASCII_2A.getValue())) {
                        this.collectResultSet(new MaskNodeModel(extractedScript, extractedColumn, extractedAlias, temp.getTable(), true));
                    }
                }
            }

            // Sample 》 oracle : select col1 || col2 || ...
            // Sample 》 select ({sql}) from ....
            /*if (Arrays.asList(EExpressionType.concatenate_t, EExpressionType.arithmetic_minus_t, EExpressionType.parenthesis_t, EExpressionType.arithmetic_plus_t).contains(tExpr.getExpressionType())) {*/
            if (null != tExpr.getLeftOperand() || null != tExpr.getRightOperand()) {
                //logger.info("select (sql):column=>{}, alias=>{}, tab=> {}, expr=>{}", extractedColumn, extractedAlias, extractedTable, extractedScript);
                this.collectResultSet(new MaskNodeModel(extractedScript, extractedColumn, extractedAlias, extractedTable));
                this.extractedParenthesis(
                        new GSQlParserNode(tExpr.getLeftOperand(), new GSQLParserColumn(aliasClause)),
                        new GSQlParserNode(tExpr.getRightOperand(), new GSQLParserColumn(aliasClause))
                );
            }


            //cass when
            /*if (tExpr.getExpressionType().equals(EExpressionType.case_t)) {*/
            if (null != tExpr.getCaseExpression()) {
                //logger.info("case when :column=>{}, alias=>{}, tab=> {}, expr=>{}", extractedColumn, extractedAlias, extractedTable, extractedScript);
                this.collectResultSet(new MaskNodeModel(extractedScript, extractedColumn, extractedAlias, extractedTable));
                this.extractedCaseWhenItem(new GSQlParserNode(tExpr.getCaseExpression(), new GSQLParserColumn(aliasClause)));
            }

            //function column
            /*if (tExpr.getExpressionType().equals(EExpressionType.function_t)) {*/
            if (null != tExpr.getFunctionCall()) {
                //logger.info("fun :column=>{}, alias=>{}, tab=> {}, expr=>{}", extractedColumn, extractedAlias, extractedTable, extractedScript);
                this.collectResultSet(new MaskNodeModel(extractedScript, extractedColumn, extractedAlias, extractedTable));
                this.extractedFunCall(new GSQlParserNode(tExpr.getFunctionCall(), new GSQLParserColumn(aliasClause)));
            }

        }

    }

    private void collectResultSet(MaskNodeModel node) {

        if (ObjectUtils.isEmpty(node)) {
            return;
        }

        if (StringUtil.isNullOrWhiteSpace(node.getTableName())) {
            return;
        }

        //过滤嵌套子查询的的列 原因是避免数据太多传输
        if ("subquery".equals(node.getTableName())) {
            return;
        }

        AtomicBoolean flag = new AtomicBoolean(true);
        Integer uniqueToken = node.getToken();
        if (null != nodes && nodes.size() > 0) {
            nodes.stream().forEach(e -> {
                if (e.getToken().equals(uniqueToken)) {
                    flag.set(false);
                    return;
                }
            });
        }
        if (flag.get()) {
            this.nodes.add(node);
        }
    }


    private void extractedCaseWhenItem(GSQlParserNode node) {

        if (null != node) {

            TCaseExpression caseExpression = (TCaseExpression) node.getTreeNode();

            if (null != caseExpression && null != caseExpression.getWhenClauseItemList()) {

                TWhenClauseItemList whenClauseItemList = caseExpression.getWhenClauseItemList();

                for (TWhenClauseItem item : whenClauseItemList) {

                    if (null == item && null != item.getReturn_expr() && null != item.getReturn_expr().getExpressionType()) {
                        continue;
                    }

                    //v2.0
                    if (null != item && null != item.getReturn_expr()) {
                        this.extractedExpr(new GSQlParserNode(item.getReturn_expr(), node.getColumnExpr()), null);
                    }

                }
            }
        }
    }

    private void extractedFunCall(GSQlParserNode node) {

        if (null != node) {

            TFunctionCall functionCall = (TFunctionCall) node.getTreeNode();

            if (null != functionCall && null != functionCall.getArgs()) {

                TExpressionList tExprArgsList = functionCall.getArgs();

                for (TExpression tExpr : tExprArgsList) {

                    //v2.0
                    if (null != tExpr) {
                        this.extractedExpr(new GSQlParserNode(tExpr, node.getColumnExpr()), null);
                    }
                }
            }

            //fix : select substring(name from 1 for 3) from student;
            if (null != functionCall && (null != functionCall.getExpr1() || null != functionCall.getExpr2() || null != functionCall.getExpr3())) {
                this.extractedParenthesis(node.getColumnExpr(), functionCall.getExpr1(), functionCall.getExpr2(), functionCall.getExpr3());
            }

            if (null != functionCall && null != functionCall.getGroupConcatParam() && null != functionCall.getGroupConcatParam().getExprList()) {

                //fix 》 SELECT GROUP_CONCAT(pwd,id)
                TExpressionList exprList = functionCall.getGroupConcatParam().getExprList();
                if (null != exprList && exprList.size() > 0) {

                    for (TExpression tExpr : exprList) {

                        //v2.0
                        if (null != tExpr) {
                            this.extractedExpr(new GSQlParserNode(tExpr, node.getColumnExpr()), null);
                        }

                    }
                }
            }
        }
    }


    private void extractedParenthesis(GSQLParserColumn columnExpr, TExpression... list) {
        if (null != list) {
            for (TExpression tExpr : list) {
                if (null != tExpr) {
                    this.extractedExpr(new GSQlParserNode(tExpr, columnExpr), null);
                }
            }
        }
    }


    private void extractedParenthesis(GSQlParserNode... nodes) {

        if (null != nodes) {

            for (GSQlParserNode node : nodes) {

                TExpression tExpr = (TExpression) node.getTreeNode();

                if (null != tExpr) {
                    this.extractedExpr(new GSQlParserNode(tExpr, node.getColumnExpr()), null);
                }

            }
        }
    }

    private void mergeMaskNodes(int start, int end, int order) {
        if (null != nodes) {
            try {

                // 案例：假设脱敏规则位于任意一条union sql 中，例如第二条SQL的password存在脱敏,
                // 因为：union采用首条SQL中的列作为返回列 例如：phone,
                // 且union语法百分之百列数量一致， 故可视为 phone,password,name 是同一个组，这里理解每条sql为一个数组,phone password，name 数组下标相同。
                // 基于以上原则,和脱敏匹配按列名准则,  将 phone字段作为 解析节点 password的别名 即可。

                //select t.phone from WHD_POC_TEST1 t
                //union all
                //select t.password from WHD_POC_TEST2 t   //  而外新增节点 ，将password 解析出一个表位WHD_POC_TEST2 列明为 password 别名为phone， 届时可以数据脱敏可以通过phone 找到 name 字段来源
                //union all
                //select t.name from WHD_POC_TEST3 t  //  而外新增节点 将name 解析出一个表位WHD_POC_TEST3 列明为 name 别名为 phone， 届时可以数据脱敏可以通过phone 找到 name 字段来源

                //必须过滤union 关联,每次只处理真实解析的
                List<MaskNodeModel> mergeMaskNodes = this.nodes.stream().filter(e -> false == e.isBizColumn()).collect(Collectors.toList()).subList(start, end);
                if (mergeMaskNodes.size() % order == 0) {
                    int limit = (mergeMaskNodes.size()) / order;
                    List<List<MaskNodeModel>> splitNodeList = Stream.iterate(0, n -> n + 1).limit(limit).parallel().map(a -> mergeMaskNodes.stream().skip(a * order).limit(order).parallel().collect(Collectors.toList())).collect(Collectors.toList()).stream().collect(Collectors.toList());
                    //union 首條作放回列名依据
                    List<MaskNodeModel> firstNodeList = splitNodeList.stream().findFirst().get();
                    int columnIndex = 0;
                    for (MaskNodeModel first : firstNodeList) {
                        for (int i = 1; i < limit; i++) {
                            //每条union 结果为一组
                            MaskNodeModel node = splitNodeList.get(i).get(columnIndex);
                            //因union合并结果集,故从第二条union sql起逐条将为首条SQL列明作为别名
                            //虚拟一个脱敏节点例如：select name from tab union select password tab2, 虚拟一个脱敏节点为 [tab=>tab2,column=>password, alias=>name], 即可通过别名脱敏
                            this.collectResultSet(new MaskNodeModel(node.getColumnScript(), node.getColumnName(), first.getColumnAlias(), node.getTableName(), true));
                        }
                        columnIndex++;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static TAliasClause createAlias(TExpression expr) {

        TAliasClause aliasClause = new TAliasClause();
        if (null != expr) {

            TObjectName objectOperand = null;
            if (null != expr) {
                objectOperand = expr.getObjectOperand();
            }

            //fix dboSchema  => columnNameOnly
            String columnNameOnly = null;
            if (null != objectOperand) {
                columnNameOnly = objectOperand.getColumnNameOnly();
            }

            //function ...
            if (null == columnNameOnly) {

                DSourceToken sourceToken = new DSourceToken();
                sourceToken.setString(expr.toString());

                TObjectName objectName = new TObjectName();
                objectName.setObjectToken(sourceToken);
                aliasClause.setAliasName(objectName);
                aliasClause.setAsToken(new DSourceToken("as"));
                aliasClause.setString(expr.toString());
            }

            if (null != columnNameOnly) {

                DSourceToken sourceToken = new DSourceToken();
                sourceToken.setString(columnNameOnly);

                TObjectName objectName = new TObjectName();
                objectName.setObjectToken(sourceToken);
                aliasClause.setAliasName(objectName);
                aliasClause.setAsToken(new DSourceToken("as"));
                aliasClause.setString(columnNameOnly);

            }
        }

        return aliasClause;
    }


}
