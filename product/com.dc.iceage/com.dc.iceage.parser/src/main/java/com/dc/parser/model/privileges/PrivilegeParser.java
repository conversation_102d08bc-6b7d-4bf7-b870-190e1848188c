package com.dc.parser.model.privileges;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.statement.dcl.AlterUserStatement;
import com.dc.parser.model.statement.dcl.GrantStatement;
import com.dc.parser.model.statement.dcl.RevokeStatement;
import com.dc.springboot.core.model.privilege.PrivilegeModel;

public interface PrivilegeParser {

    default void parseGrant(GrantStatement stmt, PrivilegeModel model, DatabaseType databaseType) {
    }

    default void parseRevoke(RevokeStatement stmt, PrivilegeModel model, DatabaseType databaseType) {
    }

    default void parseAlterUser(AlterUserStatement stmt, PrivilegeModel model, DatabaseType databaseType) {
    }

    static PrivilegeParser getParser(DatabaseType databaseType) {
        switch (databaseType.getType()) {
            case ORACLE:
                return new OraclePrivilegeParser();
            case MYSQL:
                return new MySqlPrivilegeParser();
        }

        throw new IllegalArgumentException("Unsupported database type: " + databaseType);
    }

}