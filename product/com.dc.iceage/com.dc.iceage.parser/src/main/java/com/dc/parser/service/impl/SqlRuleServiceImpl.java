package com.dc.parser.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.parser.constants.AuthConstant;
import com.dc.parser.constants.OwnerConstant;
import com.dc.parser.type.ConnectionModeType;
import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.repository.mysql.mapper.SchemaMapper;
import com.dc.repository.mysql.mapper.UserMapper;
import com.dc.repository.mysql.model.DatabaseConnection;
import com.dc.repository.mysql.model.PermissionRule;
import com.dc.repository.mysql.model.Schema;
import com.dc.repository.mysql.model.User;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.summer.parser.sql.constants.OperationAuthConstant;
import com.dc.repository.mysql.mapper.PermissionsDictionaryMapper;
import com.dc.parser.model.AuthRuleModel;
import com.dc.springboot.core.model.parser.AuthTraceModel;
import com.dc.springboot.core.model.parser.dto.PermissionRuleDto;
import com.dc.parser.service.SqlRuleService;
import com.dc.parser.type.DataNodeType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SqlRuleServiceImpl implements SqlRuleService {

    public static final String LEADER_ROLE_INSTANCE_AUTH = "leader_role_instance_auth";

    @Resource
    protected DatabaseConnectionMapper databaseConnectionMapper;
    @Resource
    protected SchemaMapper schemaMapper;
    @Resource
    protected UserMapper userMapper;
    @Resource
    protected PermissionsDictionaryMapper permissionsDictionary;

    @Override
    public List<AuthTraceModel> authSuperRule(ParserParamDto paramDTO) {
        List<AuthTraceModel> traceList = new ArrayList<>();
        AuthTraceModel traceModel = new AuthTraceModel();
        traceModel.setAuth_user("超级管理员"); // 超级/应用管理员会有授权人,除了admin这个系统生成的超级管理员
        traceModel.setAuth_time(null); // 授权时间和授权人一起获取
        traceModel.setRule_type("Instance");
        traceModel.setRule_object(paramDTO.getInstance().getInstance_name());
        List<String> condition = new ArrayList<>();
        condition.add("超级管理员"); // php传参数确定是超级/应用管理员
        traceModel.setRule_condition(condition);

        traceList.add(traceModel);
        return traceList;
    }

    @Override
    public AuthTraceModel buildSingleAuthTraceModel(PermissionRule permissionRuleModel, String type, String operation, String name) {
        AuthTraceModel traceModel = new AuthTraceModel();

        try {

            String origin = permissionRuleModel.getOrigin() != null ? permissionRuleModel.getOrigin().toString() : ""; // 权限来源
            Long authTimeStart = permissionRuleModel.getBegin_time(); // 生效时间
            Long authTimeEnd = permissionRuleModel.getEnd_time(); // 过期时间
            Long authTime = getTimeStamp(permissionRuleModel.getGmt_create(), ""); // 授权时间
            String authUser = getCreatorName(permissionRuleModel.getOperator()); // 授权人
            String groupName = permissionRuleModel.getGroup_name(); // 用户组
            String orderCode = permissionRuleModel.getOrder_code(); // 关联工单号
            String orderRelevance = permissionRuleModel.getOrder_relevance(); // 运维单号
            String userId = permissionRuleModel.getUser_id(); // 用户unique_key
            String organizationId = permissionRuleModel.getOrganization_id(); // 组织unique_key
            String userOperator = permissionRuleModel.getUser_operator(); // 操作人unique_key

            traceModel.setOrigin(origin);
            traceModel.setAuth_time(authTime);
            traceModel.setAuth_time_start(authTimeStart);
            traceModel.setAuth_time_end(authTimeEnd);
            traceModel.setAuth_user(authUser);
            traceModel.setGroup_name(groupName);
            traceModel.setOrder_code(orderCode);
            traceModel.setOrder_relevance(orderRelevance);
            traceModel.setRule_type(type);
            traceModel.setUser_id(userId);
            traceModel.setOrganization_id(organizationId);
            traceModel.setUser_operator(userOperator);

            List<String> condition = new ArrayList<>();
            condition.add(getAuthString(operation));
            traceModel.setRule_condition(condition);

            traceModel.setRule_object(permissionRuleModel.getObject_name());

        } catch (Exception e) {
            log.error("build single authTraceModel error!", e);
        }

        return traceModel;
    }

    @Override
    public List<AuthTraceModel> authRule(Map<String, List<AuthRuleModel>> authRuleKeys, ParserParamDto paramDTO) {
        List<AuthTraceModel> traceList = new ArrayList<>();
        try {

            if (paramDTO.getPeUserAuths() == null) {
                return traceList;
            }

            List<String> accountList = new ArrayList<>();

            for (PermissionRuleDto permissionRuleModel : paramDTO.getPeUserAuths()) {

                if (permissionRuleModel.getBase_type() != null) {
                    if ((ConnectionModeType.ACCOUNT_DIRECT_CONNECTION.getValue().equals(paramDTO.getCurrentPattern()) && permissionRuleModel.getBase_type() != 3)
                            || (ConnectionModeType.SECURITY_COOPERATE.getValue().equals(paramDTO.getCurrentPattern()) && permissionRuleModel.getBase_type() != 1)
                    ) {
                        continue;
                    }
                }

                String origin = permissionRuleModel.getOrigin() != null ? permissionRuleModel.getOrigin().toString() : ""; // 权限来源
                Long authTimeStart = permissionRuleModel.getBegin_time(); // 生效时间
                Long authTimeEnd = permissionRuleModel.getEnd_time(); // 过期时间
                Long authTime = getTimeStamp(permissionRuleModel.getGmt_create(), ""); // 授权时间
                String authUser = getCreatorName(permissionRuleModel.getOperator()); // 授权人
                String groupName = permissionRuleModel.getGroup_name(); // 用户组
                String orderCode = permissionRuleModel.getOrder_code(); // 关联工单号
                String orderRelevance = permissionRuleModel.getOrder_relevance();
                String userId = permissionRuleModel.getUser_id(); // 用户unique_key
                String organizationId = permissionRuleModel.getOrganization_id(); // 组织unique_key
                String userOperator = permissionRuleModel.getUser_operator(); // 操作人unique_key

                /*if (LEADER_ROLE_INSTANCE_AUTH.equalsIgnoreCase(permissionRuleModel.getAction_key())) {
                    authTimeStart = null; // 实例负责人不显示生效时间和过期时间
                    authTimeEnd = null;
                }*/

                if (permissionRuleModel.getAction_key() != null) {
                    String action = permissionRuleModel.getAction_key(); // 类型、名称、权限
                    if (authRuleKeys.get(action) != null) {
                        List<AuthRuleModel> authRuleModels = authRuleKeys.get(action);
                        authRuleModels = duplicateRemoval(authRuleModels); // 去重
                        for (AuthRuleModel authRuleModel : authRuleModels) {

                            if (needContinue(authRuleModel, permissionRuleModel, traceList)) {
                                continue;
                            } else if (ConnectionModeType.ACCOUNT_DIRECT_CONNECTION.getValue().equals(paramDTO.getCurrentPattern())) {
                                if (StringUtils.isNotBlank(paramDTO.getAccount()) && !paramDTO.getAccount().equals(permissionRuleModel.getObject_name())) {
                                    continue;
                                } else if (StringUtils.isNotBlank(paramDTO.getAccount()) && accountList.contains(paramDTO.getAccount())) {
                                    continue;
                                }
                            }

                            AuthTraceModel traceModel = new AuthTraceModel();
                            traceModel.setOrigin(origin);
                            traceModel.setAuth_time(authTime);
                            traceModel.setAuth_time_start(authTimeStart);
                            traceModel.setAuth_time_end(authTimeEnd);
                            traceModel.setAuth_user(authUser);
                            traceModel.setGroup_name(groupName);
                            traceModel.setOrder_code(orderCode);
                            traceModel.setOrder_relevance(orderRelevance);
                            traceModel.setRule_type(authRuleModel.getType());
                            traceModel.setUser_id(userId);
                            traceModel.setOrganization_id(organizationId);
                            traceModel.setUser_operator(userOperator);

                            List<String> condition = new ArrayList<>();
                            if (ConnectionModeType.ACCOUNT_DIRECT_CONNECTION.getValue().equals(paramDTO.getCurrentPattern())) {
                                condition.add(StringUtils.isNotBlank(paramDTO.getAccount()) ? paramDTO.getAccount() : authRuleModel.getOperation());
                                if (StringUtils.isNotBlank(paramDTO.getAccount())) {
                                    accountList.add(paramDTO.getAccount());
                                }
                            } else {
                                condition.add(AuthConstant.PRIVATE_TABLE_INSTANCE_AUTH.equals(action) ? OwnerConstant.private_table_manager : getAuthString(authRuleModel.getOperation()));
                            }
                            traceModel.setRule_condition(condition);

                            String objName = getObjectName(authRuleModel);
                            traceModel.setRule_object(objName);

                            traceList.add(traceModel);

                        }
                    }
                }

            }
        } catch (Exception e) {
            log.error("auth rule error!", e);
        }

        return traceList.stream().distinct().collect(Collectors.toList());
    }

    public String getObjectName(AuthRuleModel authRuleModel) {

        if ("Instance".equals(authRuleModel.getType())) {
            String instanceUniqueKey = authRuleModel.getName();
            DatabaseConnection connectionByUniqueKey = this.databaseConnectionMapper.getConnectionByUniqueKey(instanceUniqueKey);
            return connectionByUniqueKey.getInstance_name();
        } else if ("Schema".equals(authRuleModel.getType())) {
            String schemaUniqueKey = authRuleModel.getName();
            Schema schemaByUniqueKey = this.schemaMapper.selectOne(Wrappers.<Schema>lambdaQuery()
                    .eq(Schema::getUnique_key, schemaUniqueKey)
                    .eq(Schema::getIs_delete, 0)
            );
            return schemaByUniqueKey == null ? null
                    : StringUtils.isNotBlank(schemaByUniqueKey.getCatalog_name())
                    ? schemaByUniqueKey.getCatalog_name() + "." + schemaByUniqueKey.getSchema_name()
                    : schemaByUniqueKey.getSchema_name();
        } else if (Arrays.asList("Table", "Procedure", "Function", "View", "Template", "Package").contains(authRuleModel.getType())) {
            return authRuleModel.getName();
        }

        return "";
    }

    public String getAuthString(String operation) {
        List<String> permissionsDictionary = this.permissionsDictionary.findOperationGroupName(operation.toLowerCase(Locale.ROOT));
        if (permissionsDictionary == null || permissionsDictionary.isEmpty()) {
            if ("export".equalsIgnoreCase(operation)) {
                operation = "导出";
            }
            return operation;
        }
        String groupName = permissionsDictionary.get(0);
        if (OperationAuthConstant.statistics.equalsIgnoreCase(operation)) {
            operation = "数据统计";
        } else if (OperationAuthConstant.call.equalsIgnoreCase(operation)) {
            operation = "CALL/EXEC";
        } else if (OperationAuthConstant.begin_end.equalsIgnoreCase(operation)) {
            operation = "匿名块";
        }
        String returnString = operation.replaceAll("_", " ");
        return groupName + "(" + returnString.toUpperCase(Locale.ROOT) + ")";
    }

    public List<AuthRuleModel> duplicateRemoval(List<AuthRuleModel> authRuleModels) {
        List<AuthRuleModel> authRuleModels_new = new ArrayList<>();
        List<String> duplicateRemovalList = new ArrayList<>();
        for (AuthRuleModel authRuleModel : authRuleModels) {
            String str = authRuleModel.toString();
            if (!duplicateRemovalList.contains(str)) {
                duplicateRemovalList.add(str);
                authRuleModels_new.add(authRuleModel);
            }
        }

        return authRuleModels_new;
    }

    public String getCreatorName(String uniqueKey) {

        Optional<User> userOptional = userMapper.getUserByUniqueKey(uniqueKey);
        if (userOptional.isPresent()) {
            // 弃用name_key,改用username
            User user = userOptional.get();
            return String.format("%s(%s)", user.getUsername(), user.getRealName());
        }

        return "";
    }

    public boolean needContinue(AuthRuleModel authRuleModel, PermissionRuleDto permissionRuleModel, List<AuthTraceModel> traceList) {
        if ("export".equalsIgnoreCase(authRuleModel.getOperation())) {
            return true; // 权限追踪不再显示导出权限
        }
        if (permissionRuleModel.getResource_type() == 2 && "Schema".equals(authRuleModel.getType())
                && authRuleModel.getName() != null && !authRuleModel.getName().equals(permissionRuleModel.getSchema_id())) {
            return true;
        } else if (DataNodeType.getIdentCode(DataNodeType.getResourceTypes()).contains(permissionRuleModel.getResource_type())
                && Arrays.asList("Table", "Procedure", "Function", "View").contains(authRuleModel.getType())
                && authRuleModel.getOidName() != null && !authRuleModel.getOidName().equalsIgnoreCase(permissionRuleModel.getObject_name())) {
            return true;
        } else if (traceList.stream().anyMatch(authTraceModel -> authTraceModel.getRule_condition().contains(OwnerConstant.private_table_manager))) {
            return true;
        }
        return false;
    }

    public long getTimeStamp(String time, String format) {

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (!"".equals(format)) {
            simpleDateFormat = new SimpleDateFormat(format);
        }

        long timeStamp = 0;
        try {

            Date date = simpleDateFormat.parse(time);

            timeStamp = date.getTime() / 1000; // 转换成秒级时间戳

        } catch (Exception e) {
            log.error("get timeStamp error!", e);
        }
        return timeStamp;
    }

}
