package com.dc.parser.model.chain.impl;

import com.dc.infra.spi.TypedSPILoader;
import com.dc.parser.exec.sql.SQLStatementParserEngine;
import com.dc.parser.exec.sql.SQLStatementParserEngineFactory;
import com.dc.parser.model.chain.SqlCheckParserChain;
import com.dc.parser.model.engine.CacheOption;
import com.dc.parser.model.privileges.PrivilegeParser;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dcl.AlterUserStatement;
import com.dc.parser.model.statement.dcl.GrantStatement;
import com.dc.parser.model.statement.dcl.RevokeStatement;
import com.dc.parser.service.sql.WebSQLParserInfo;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.summer.parser.utils.model.SqlParseModel;
import com.dc.type.DatabaseType;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

@Slf4j
public class PrivilegesManagementChain extends SqlCheckParserChain {

    private final List<String> operations = new ArrayList<>(){{add("GRANT");add("ALTER");add("REVOKE");}};

    public PrivilegesManagementChain(ParserParamDto parserParamDto, SqlParseModel sqlParseModel, WebSQLParserInfo webSQLParserInfo) {
        super(parserParamDto, sqlParseModel, webSQLParserInfo);
    }

    @Override
    public boolean proceed(WebSQLParserResult webSQLParserResult) {

        if (!DatabaseType.getIdentCode(DatabaseType.supportPrivilegeManagement()).contains(parserParamDto.getDbType())) {
            return true;
        }

        if (!operations.contains(sqlParseModel.getOperation().toUpperCase(Locale.ROOT))) {
            return true;
        }

        try {
            PrivilegeModel privilegeModel = new PrivilegeModel();
            privilegeModel.setNeeded(true);

            Long origin = parserParamDto.getOrigin();
            OriginType type = OriginType.of(origin);
            if (type == OriginType.JOB_EXECUTE) {
                privilegeModel.setJob(true);
                privilegeModel.setUserId(parserParamDto.getUserId());
                privilegeModel.setUserName(parserParamDto.getUserName());
            }
            com.dc.infra.database.type.DatabaseType.Constant dbType = com.dc.infra.database.type.DatabaseType.Constant.ORACLE;
            switch (DatabaseType.of(parserParamDto.getDbType())) {
                case MYSQL:
                    dbType = com.dc.infra.database.type.DatabaseType.Constant.MYSQL;
                    break;
            }

            com.dc.infra.database.type.DatabaseType datebaseType = TypedSPILoader.getService(com.dc.infra.database.type.DatabaseType.class, dbType);
            SQLStatementParserEngine sqlStatementParserEngine = SQLStatementParserEngineFactory.getSQLStatementParserEngine(
                    datebaseType,
                    new CacheOption(2000, 65535L), new CacheOption(1, 1L));


            SQLStatement sqlStmt = sqlStatementParserEngine.parse(sqlParseModel.getSql(), false);
            PrivilegeParser privilegeParser = PrivilegeParser.getParser(datebaseType);

            if (sqlStmt instanceof GrantStatement) {
                privilegeParser.parseGrant((GrantStatement) sqlStmt, privilegeModel, datebaseType);

            } else if (sqlStmt instanceof AlterUserStatement) {
                privilegeParser.parseAlterUser((AlterUserStatement) sqlStmt, privilegeModel, datebaseType);

            } else if (sqlStmt instanceof RevokeStatement) {
                privilegeParser.parseRevoke((RevokeStatement) sqlStmt, privilegeModel, datebaseType);
            }

            webSQLParserResult.setPrivilegeModel(privilegeModel);

        } catch (Exception e) {
            log.error("权限纳管 - 解析失败 ",e);
        }

        return true;
    }

}
