package com.dc.parser.model.chain.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.parser.component.ParserMapper;
import com.dc.parser.constants.AuthConstant;
import com.dc.parser.model.chain.SqlCheckParserChain;
import com.dc.parser.service.MetaDataStoreService;
import com.dc.parser.service.UserRuleService;
import com.dc.parser.service.impl.SqlRuleServiceImpl;
import com.dc.parser.service.sql.WebSQLParserInfo;
import com.dc.parser.type.DataNodeType;
import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.*;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.parser.dto.*;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.sensitive.GradedClassifiedModel;
import com.dc.springboot.core.model.type.DefaultSwitchParamType;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.type.SecurityRuleType;
import com.dc.parser.util.GetAuthUtil;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.sensitive.MaskRule;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.parser.model.*;
import com.dc.parser.type.SensitiveDataType;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.dc.summer.parser.utils.model.SqlParseModel;
import com.dc.springboot.core.model.sensitive.DataMask;
import com.dc.summer.parser.utils.CommonUtil;
import com.dc.type.DatabaseType;
import com.dc.parser.type.ActionMaskType;
import com.dc.sqlparser.stmt.TSelectSqlStatement;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Nonnull;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class DataMaskParserChain extends SqlCheckParserChain {

    private final DBColumnMapper dbColumnMapper = Resource.getBeanRequireNonNull(DBColumnMapper.class);
    private final UserRuleService userRuleService = Resource.getBeanRequireNonNull(UserRuleService.class);
    private final SqlRuleServiceImpl sqlRuleService = Resource.getBeanRequireNonNull(SqlRuleServiceImpl.class);
    private final SchemaMapper schemaMapper = Resource.getBeanRequireNonNull(SchemaMapper.class);
    private final MetaDataStoreService metaDataStoreService = Resource.getBeanRequireNonNull(MetaDataStoreService.class);
    private final GroupUserMapper groupUserMapper = Resource.getBeanRequireNonNull(GroupUserMapper.class);
    private final JSON json = Resource.getBeanRequireNonNull(JSON.class);
    private final ParserMapper parserMapper = ParserMapper.INSTANCE;
    private final SensitiveLevelMapper sensitiveLevelMapper = Resource.getBeanRequireNonNull(SensitiveLevelMapper.class);

    public DataMaskParserChain(ParserParamDto parserParamDto, SqlParseModel sqlParseModel, WebSQLParserInfo webSQLParserInfo) {
        super(parserParamDto, sqlParseModel, webSQLParserInfo);
    }

    @Override
    public boolean proceed(WebSQLParserResult webSQLParserResult) {

        try {

            if (needDataMask(parserParamDto, sqlParseModel)) {

                Map<String, List<String>> schemaNameMap = this.getSchemaNameMap(sqlParseModel.getSqlAuthModelList());
                Map<String, List<String>> tableColumnMap = this.getTableColumnMap(sqlParseModel.getAction().getColumns(), parserParamDto.getDbType(), parserParamDto.getSchemaName(), parserParamDto.getFrameworkName());
                List<MaskTableModel> maskTableModels = this.buildMaskTableModel(schemaNameMap, tableColumnMap, parserParamDto.getConnectId(), sqlParseModel.getSqlAuthModelList(), parserParamDto.getDbType());

                if (webSQLParserInfo.isGradedClassified()) {

                    List<GradedClassifiedModel> gradedClassifiedModelList = this.getGradedClassifiedModel(maskTableModels, parserParamDto, sqlParseModel.getSqlAuthModelList());
                    GradedClassifiedModel gradedClassifiedModel = buildGradedClassifiedModel(gradedClassifiedModelList, parserParamDto);
                    if (gradedClassifiedModel.getSensitiveLevelToAuthLevel() != null && gradedClassifiedModel.getSensitiveLevelToAuthLevel().isEmpty()) {
                        gradedClassifiedModel.setSensitiveLevelToAuthLevel(null);
                    }
                    webSQLParserResult.setGradedClassifiedModel(gradedClassifiedModel);

                } else {
                    List<DataMask> dataMaskCfgList = this.getDataMaskConfig(maskTableModels, parserParamDto, sqlParseModel.getSqlAuthModelList());

                    if (!dataMaskCfgList.isEmpty()) {
                        // 获取所有字段
                        List<String> columns = this.buildColumns(sqlParseModel.getAction().getColumns(), parserParamDto.getDbType(), parserParamDto.getSchemaName(), parserParamDto.getFrameworkName());
                        // build alias
                        this.buildAlias(sqlParseModel, parserParamDto.getDbType(), parserParamDto.getSchemaName(), parserParamDto.getFrameworkName());

                        // 最终返回的脱敏字段配置(对应字段名和别名等)
                        List<DataMask> dataMaskList = duplicateRemoveUnionColumn(sqlParseModel, this.getDataMaskConfigs(columns, dataMaskCfgList, sqlParseModel.getAction(), parserParamDto.getDbType(), parserParamDto.getSchemaName(), parserParamDto.getFrameworkName()));
                        webSQLParserResult.setDataMask(dataMaskList);
                        if (CollectionUtils.isNotEmpty(dataMaskList)) {
                            webSQLParserResult.setSensitiveSelect(true);
                        }

                        // 脱敏权限追踪
                        webSQLParserResult.setMaskRules(buildMaskRule(parserParamDto, dataMaskList));
                    }
                }

            }

        } catch (Exception e) {
            log.error("数据脱敏，解析异常：", e);
            webSQLParserResult.setStatus(SqlExecuteStatus.FAIL.getValue());
            webSQLParserResult.setMessage("数据脱敏-解析异常! " + e.getMessage());
            return false;
        }

        return true;
    }

    private boolean needDataMask(ParserParamDto paramDTO, SqlParseModel sqlParserModel) {

        // mongoDB、redis、hBase不脱敏， 支持mongodb,redis 对接平安第三方脱敏
        if (Arrays.asList(/*DatabaseType.MONGODB.getValue(), DatabaseType.REDIS.getValue(),*/ DatabaseType.H_BASE.getValue()).contains(paramDTO.getDbType())) {
            return false;
        }

        // 实例配置了不脱敏，则跳过查询脱敏配置
        if (DefaultSwitchParamType.defaultOpen(webSQLParserInfo.getSystemParamConfig().get(SecurityRuleType.SENSITIVE_DATA_PROTECTION_DESENSITIZATION.getName())).isDisable()) {
            return false;
        }

        // 只有select操作需要查询脱敏
        if (!SqlConstant.KEY_SELECT.equalsIgnoreCase(sqlParserModel.getOperation())) {
            return false;
        }

        // explain、select function、select sequence不脱敏
        if (sqlParserModel.getAction().getExplainOperation() != null || sqlParserModel.getAction().isSelectFunction()
                || sqlParserModel.getAction().isSelectSequence() || sqlParserModel.getAction().isSelectValue()) {
            return false;
        }

        return true;
    }

    private Map<String, List<String>> getSchemaNameMap(List<SqlAuthModel> SqlAuthModelList) {
        Map<String, List<String>> schemaNameMap = new HashMap<>();
        for (SqlAuthModel sqlAuthModel : SqlAuthModelList) {
            if (sqlAuthModel.isFalseSchema()) {
                continue;
            }
            String schemaName = sqlAuthModel.getOldSchemaNameForDataMask();
            String name = sqlAuthModel.getOldNameForDataMask().toLowerCase(Locale.ROOT);

            schemaNameMap.computeIfAbsent(schemaName, k -> new ArrayList<>()).add(name);
        }
        return schemaNameMap;
    }

    private Map<String, List<String>> getTableColumnMap(Set<String> columns, Integer dbType, String defaultSchemaName, String defaultFrameworkName) {
        Map<String, List<String>> tabCol = new HashMap<>();
        if (columns == null || columns.isEmpty()) {
            return tabCol;
        }

        for (String column : columns) {
            String splitRegex = CommonUtil.useColonSplit(dbType) ? ":|\\." : "\\.";
            String[] split = column.toLowerCase(Locale.ROOT).split(splitRegex);

            String tableName;
            String columnName;
            if (DatabaseType.ELASTIC_SEARCH.getValue().equals(dbType)) {
                tableName = column;
                columnName = "*";
            } else {
                tableName = split[split.length - 2];
                columnName = split[split.length - 1];
            }

            if (DatabaseType.get3FDatabaseIntegerValueList().contains(dbType)) {
                if (split.length - 3 >= 0) {
                    buildTableColumnMap(tabCol, split[split.length - 3] + "." + tableName, columnName);
                } else {
                    if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType)) {
                        buildTableColumnMap(tabCol, defaultSchemaName + "." + tableName, columnName);
                    } else {
                        buildTableColumnMap(tabCol, defaultFrameworkName + "." + tableName, columnName);
                    }
                }
            } else {
                buildTableColumnMap(tabCol, tableName, columnName);
            }
        }
        return tabCol;
    }

    private void buildTableColumnMap(Map<String, List<String>> tabCol, String tableName, String columnName) {
        tabCol.computeIfAbsent(tableName, k -> new ArrayList<>()).add(columnName);
    }

    private List<MaskTableModel> buildMaskTableModel(Map<String, List<String>> schemaNameMap, Map<String, List<String>> tableNameMap,
                                                     String instanceUniqueKey, List<SqlAuthModel> sqlAuthModelList, Integer dbType) {

        List<MaskTableModel> maskTableModels = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : schemaNameMap.entrySet()) {
            String schemaName = entry.getKey();
            List<String> tables = entry.getValue();

            //在对接平安第三方脱敏平台, mongodb,redis 解析不到表情况下支持到按SCHEMA|INSTANCE脱敏
            if (ObjectUtils.isEmpty(tableNameMap) && List.of(DatabaseType.MONGODB.getValue(), DatabaseType.REDIS.getValue()).contains(dbType)) {
                MaskTableModel maskTableModel = new MaskTableModel();
                maskTableModel.setSchemaName(schemaName);
                String schemaUniqueKey = getSchemaUniqueKey(sqlAuthModelList, schemaName);
                if (schemaUniqueKey.isEmpty()) {
                    schemaUniqueKey = getSchemaUniqueKeyBySchemaName(instanceUniqueKey, schemaName, dbType);
                }
                maskTableModel.setSchemaUniqueKey(schemaUniqueKey);
                maskTableModel.setInstanceUniqueKey(instanceUniqueKey);
                maskTableModels.add(maskTableModel);
                continue;
            }


            for (String table : tables) {
                if (tableNameMap.containsKey(table)) {

                    String tableName = table;
                    String[] objectName = table.split("\\.");
                    String tempSchemaName = schemaName;
                    if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType)) {
                        tempSchemaName = schemaName + "." + objectName[0];
                        tableName = objectName[1];
                    }

                    MaskTableModel maskTableModel = new MaskTableModel();
                    maskTableModel.setSchemaName(tempSchemaName);
                    maskTableModel.setTableName(tableName);
                    String schemaUniqueKey = getSchemaUniqueKey(sqlAuthModelList, tempSchemaName);
                    if (schemaUniqueKey.isEmpty()) {
                        schemaUniqueKey = getSchemaUniqueKeyBySchemaName(instanceUniqueKey, tempSchemaName, dbType);
                    }
                    maskTableModel.setSchemaUniqueKey(schemaUniqueKey);
                    maskTableModel.setInstanceUniqueKey(instanceUniqueKey);
                    List<String> list = tableNameMap.get(table);
                    maskTableModel.setColumns(toLower(list));
                    for (String col : list) {
                        if (col.contains("*")) {
                            maskTableModel.setStar(true);
                            break;
                        }
                    }

                    maskTableModels.add(maskTableModel);
                }
            }
        }

        return maskTableModels;
    }

    private String getSchemaUniqueKey(List<SqlAuthModel> sqlAuthModelList, String schemaName) {
        if (schemaName != null) {
            for (SqlAuthModel sqlAuthModel : sqlAuthModelList) {
                if (schemaName.equalsIgnoreCase(sqlAuthModel.getFullSchemaName())) {
                    return sqlAuthModel.getSchemaUniqueKey();
                }
            }
        }
        return "";
    }

    private String getSchemaUniqueKeyBySchemaName(String instanceUniqueKey, String schemaName, Integer dbType) {
        if (schemaName != null) {
            String[] objectName = schemaName.split("\\.");
            String catalogName = "";
            if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType)) {
                catalogName = objectName[0];
                schemaName = objectName[1];
            }
            Optional<Schema> schema = this.metaDataStoreService.getRealSchema(instanceUniqueKey, schemaName, catalogName);
            return schema.isPresent() ? schema.get().getUnique_key() : "";
        }
        return "";
    }

    private List<String> toLower(List<String> oldList) {
        List<String> newList = new ArrayList<>();
        for (String str : oldList) {
            newList.add(str.toLowerCase(Locale.ROOT));
        }
        return newList;
    }

    private List<DataMask> getDataMaskConfig(List<MaskTableModel> maskTableModels, ParserParamDto paramDTO, List<SqlAuthModel> SqlAuthModelList) {

        this.getAuthMap(paramDTO, SqlAuthModelList); // 获取脱敏权限

        if (paramDTO.getOrderMaskLevel() != null) {
            paramDTO.setMaskAuth(new HashMap<>()); // 导出工单脱敏方式由页面指定,与用户脱敏权限无关
        }

        List<DataMask> dataMasks = new ArrayList<>();

        for (MaskTableModel maskTableModel : maskTableModels) {
            String tableName = maskTableModel.getTableName();
            String schemaUniqueKey = maskTableModel.getSchemaUniqueKey();

            List<DataMask> modelList = this.getMaskModelList(maskTableModel, schemaUniqueKey, tableName);

            for (DataMask model : modelList) {

                boolean hasRoleAuth = hasRoleAuth(paramDTO, 1);
                boolean hasHalfRoleAuth = hasRoleAuth(paramDTO, 2);

                String instanceMaskUniqueKey = paramDTO.getConnectId() + AuthConstant.connector + model.getSensitiveLevelId();
                boolean hasInstanceAuth = hasDataMaskAuth(AuthConstant.desensitization_instance, paramDTO.getMaskAuth(), instanceMaskUniqueKey);
                boolean hasHalfInstanceAuth = hasDataMaskAuth(AuthConstant.desensitization_half_instance, paramDTO.getMaskAuth(), instanceMaskUniqueKey);
                boolean hasInstanceCopyAuth = hasDataMaskAuth(AuthConstant.desensitization_instance_copy, paramDTO.getMaskAuth(), instanceMaskUniqueKey);

                String schemaMaskUniqueKey = paramDTO.getConnectId() + AuthConstant.connector + schemaUniqueKey
                        + AuthConstant.connector + model.getSensitiveLevelId();
                boolean hasSchemaAuth = hasDataMaskAuth(AuthConstant.desensitization_schema, paramDTO.getMaskAuth(), schemaMaskUniqueKey);
                boolean hasHalfSchemaAuth = hasDataMaskAuth(AuthConstant.desensitization_half_schema, paramDTO.getMaskAuth(), schemaMaskUniqueKey);
                boolean hasSchemaCopyAuth = hasDataMaskAuth(AuthConstant.desensitization_schema_copy, paramDTO.getMaskAuth(), schemaMaskUniqueKey);

                String tableMaskUniqueKey = paramDTO.getConnectId() + AuthConstant.connector + schemaUniqueKey
                        + AuthConstant.connector + DataNodeType.TABLE.getValue() + AuthConstant.connector + tableName
                        + AuthConstant.connector + model.getSensitiveLevelId();
                boolean hasTableAuth = hasDataMaskAuth(AuthConstant.desensitization_table, paramDTO.getMaskAuth(), tableMaskUniqueKey);
                boolean hasHalfTableAuth = hasDataMaskAuth(AuthConstant.desensitization_half_table, paramDTO.getMaskAuth(), tableMaskUniqueKey);
                boolean hasTableCopyAuth = hasDataMaskAuth(AuthConstant.desensitization_table_copy, paramDTO.getMaskAuth(), tableMaskUniqueKey);

                String columnMaskUniqueKey = paramDTO.getConnectId() + AuthConstant.connector + schemaUniqueKey + AuthConstant.connector + tableName + AuthConstant.connector + model.getColumnName();
                boolean hasColumnAuth = hasDataMaskAuth(AuthConstant.desensitization_column, paramDTO.getMaskAuth(), columnMaskUniqueKey);
                boolean hasHalfColumnAuth = hasDataMaskAuth(AuthConstant.desensitization_half_column, paramDTO.getMaskAuth(), columnMaskUniqueKey);
                boolean hasColumnCopyAuth = hasDataMaskAuth(AuthConstant.desensitization_column_copy, paramDTO.getMaskAuth(), columnMaskUniqueKey);

                buildDataMask(maskTableModel, model, hasRoleAuth || hasInstanceAuth || hasSchemaAuth || hasTableAuth || hasColumnAuth);

                if (paramDTO.getOrderMaskLevel() != null) {
                    model.setAuthLevel(paramDTO.getOrderMaskLevel()); // 导出工单脱敏方式由页面指定,与用户脱敏权限无关
                } else {
                    if (hasRoleAuth || hasInstanceAuth || hasSchemaAuth || hasTableAuth || hasColumnAuth) {
                        model.setAuthLevel(SensitiveDataType.CLEAR.getValue()); // 有实例、schema、表、字段级别的明文权限时
                    } else if (hasHalfRoleAuth || hasHalfInstanceAuth || hasHalfSchemaAuth || hasHalfTableAuth || hasHalfColumnAuth || paramDTO.getEnableDesensiteType() == 0) {
                        model.setAuthLevel(SensitiveDataType.HALF.getValue()); // 有实例、schema、表、字段级别的半脱权限，或者全脱敏开关关闭时
                    } else if (paramDTO.getEnableDesensiteType() == 1) {
                        model.setAuthLevel(SensitiveDataType.FULL.getValue()); // 用户没有敏感权限，且全脱开关打开时
                    }
                }

                if (hasInstanceCopyAuth || hasSchemaCopyAuth || hasTableCopyAuth || hasColumnCopyAuth) {
                    model.setEnableDesensitizationCopy(1);
                }

                dataMasks.add(model);
            }
        }

        return dataMasks;
    }

    private List<GradedClassifiedModel> getGradedClassifiedModel(List<MaskTableModel> maskTableModels, ParserParamDto paramDTO, List<SqlAuthModel> SqlAuthModelList) {
        List<GradedClassifiedModel> list = new ArrayList<>();

        this.getGradedClassifiedAuth(paramDTO, SqlAuthModelList); // 获取脱敏权限

        Map<String, List<PermissionRuleDto>> maskAuthObject = paramDTO.getMaskAuthObject();

        Map<String, Integer> sensitiveLevelMap = getSensitiveLevelMap();

        for (MaskTableModel maskTableModel : maskTableModels) {
            String schemaUniqueKey = maskTableModel.getSchemaUniqueKey();

            GradedClassifiedModel gradedClassifiedModel = new GradedClassifiedModel();
            gradedClassifiedModel.setDatabaseName(maskTableModel.getSchemaName());

            if (webSQLParserInfo.isClassification()) {

                Map<Integer, List<String>> level = buildLevelMap();


                List<PermissionRuleDto> desensitizationInstance = getSensitiveLevelDesensitizationInstance(maskAuthObject, AuthConstant.desensitization_instance);
                List<PermissionRuleDto> desensitizationHalfInstance = getSensitiveLevelDesensitizationInstance(maskAuthObject, AuthConstant.desensitization_half_instance);
                List<PermissionRuleDto> desensitizationNotInstance = getSensitiveLevelDesensitizationInstance(maskAuthObject, AuthConstant.desensitization_not_instance);

                List<PermissionRuleDto> desensitizationSchema = getSensitiveLevelDesensitizationSchema(maskAuthObject, AuthConstant.desensitization_schema, schemaUniqueKey);
                List<PermissionRuleDto> desensitizationHalfSchema = getSensitiveLevelDesensitizationSchema(maskAuthObject, AuthConstant.desensitization_half_schema, schemaUniqueKey);
                List<PermissionRuleDto> desensitizationNotSchema = getSensitiveLevelDesensitizationSchema(maskAuthObject, AuthConstant.desensitization_not_schema, schemaUniqueKey);

                List<String> clearInstance = getSensitiveLevel(desensitizationInstance);
                level.get(SensitiveDataType.CLEAR.getValue()).addAll(clearInstance);
                List<String> clearSchema = getSensitiveLevel(desensitizationSchema);
                level.get(SensitiveDataType.CLEAR.getValue()).addAll(clearSchema);
                // 添加复制权限
                Map<String, Integer> clearInstance2 = getSensitiveLevelAndCopy(desensitizationInstance);
                Map<String, Integer> clearInstance3 = getSensitiveLevelAndCopy(desensitizationSchema);
                Map<String, Integer> copy = mergeSensitiveLevelAndCopy(clearInstance2, clearInstance3);
                if (copy != null && !copy.isEmpty()) {
                    gradedClassifiedModel.setEnableDesensitizationCopy(copy);
                }

                List<String> halfInstance = getSensitiveLevel(desensitizationHalfInstance);
                level.get(SensitiveDataType.HALF.getValue()).addAll(halfInstance);
                List<String> halfSchema = getSensitiveLevel(desensitizationHalfSchema);
                level.get(SensitiveDataType.HALF.getValue()).addAll(halfSchema);

                List<String> notInstance = getSensitiveLevel(desensitizationNotInstance);
                level.get(SensitiveDataType.FULL.getValue()).addAll(notInstance);
                List<String> notSchema = getSensitiveLevel(desensitizationNotSchema);
                level.get(SensitiveDataType.FULL.getValue()).addAll(notSchema);

                Map<Integer, List<Integer>> integerListMap = buildLevelMap(level, sensitiveLevelMap);
                Map<Integer, Integer> sLevelMap = buildLevelMap(integerListMap);

                gradedClassifiedModel.setLevel(true);
                gradedClassifiedModel.setSensitiveLevelToAuthLevel(sLevelMap);
            } else {
                // 不是分级分类的
                // 实例角色
                boolean hasRoleAuth = hasRoleAuth(paramDTO, 1);
                // boolean hasHalfRoleAuth = hasRoleAuth(paramDTO, 2);

                // 实例级别
                List<PermissionRuleDto> desensitizationInstanceNormal = getDesensitizationInstance(maskAuthObject, AuthConstant.desensitization_instance);
                List<PermissionRuleDto> desensitizationHalfInstanceNormal = getDesensitizationInstance(maskAuthObject, AuthConstant.desensitization_half_instance);

                // schema级别
                List<PermissionRuleDto> desensitizationSchemaNormal = getDesensitizationSchema(maskAuthObject, AuthConstant.desensitization_schema, schemaUniqueKey);
                List<PermissionRuleDto> desensitizationHalfSchemaNormal = getDesensitizationSchema(maskAuthObject, AuthConstant.desensitization_half_schema, schemaUniqueKey);

                gradedClassifiedModel.setLevel(false);
                gradedClassifiedModel.setHasDefaultPlainAuth(hasRoleAuth || !desensitizationInstanceNormal.isEmpty() || !desensitizationSchemaNormal.isEmpty());

                if (!gradedClassifiedModel.getHasDefaultPlainAuth()) {
                    if (desensitizationHalfInstanceNormal.isEmpty() && desensitizationHalfSchemaNormal.isEmpty()) {
                        gradedClassifiedModel.setHasDefaultPlainAuth(null);
                    }
                }
                // 添加复制权限
                int enableCopy = haveEnableCopy(desensitizationInstanceNormal, desensitizationSchemaNormal);
                HashMap<String, Integer> map = new HashMap<>();
                map.put("null", enableCopy);
                gradedClassifiedModel.setEnableDesensitizationCopy(map);

            }

            list.add(gradedClassifiedModel);
        }

        return list;
    }

    private int haveEnableCopy(List<PermissionRuleDto> desensitizationInstanceNormal, List<PermissionRuleDto> desensitizationSchemaNormal) {
        if (desensitizationInstanceNormal != null) {
            for (PermissionRuleDto dto : desensitizationInstanceNormal) {
                if (dto.getEnable_desensitization_copy() != null && dto.getEnable_desensitization_copy() == 1) {
                    return 1;
                }
            }
        }
        if (desensitizationSchemaNormal != null) {
            for (PermissionRuleDto dto : desensitizationSchemaNormal) {
                if (dto.getEnable_desensitization_copy() != null && dto.getEnable_desensitization_copy() == 1) {
                    return 1;
                }
            }
        }

        return 0;
    }


    //复制权限, 合并取最大
    private Map<String, Integer> mergeSensitiveLevelAndCopy(Map<String, Integer> clearInstance2, Map<String, Integer> clearInstance3) {
        Map<String, Integer> result = new HashMap<>();
        for (Map.Entry<String, Integer> entry3 : clearInstance3.entrySet()) {
            Integer value2 = clearInstance2.get(entry3.getKey());
            if (value2 == null) {
                result.put(entry3.getKey(), entry3.getValue());
                continue;
            }
            if (entry3.getValue() == 1 || value2 == 1) {
                result.put(entry3.getKey(), 1);
            }
        }

        for (Map.Entry<String, Integer> entry2 : clearInstance2.entrySet()) {
            Integer value2 = clearInstance3.get(entry2.getKey());
            if (value2 != null) {
                continue;
            }
            result.put(entry2.getKey(), entry2.getValue());
        }

        return result;
    }

    private Map<String, Integer> getSensitiveLevelAndCopy(List<PermissionRuleDto> desensitizationInstance) {
        Map<String, Integer> hashMap = new HashMap<>();
        for (PermissionRuleDto dto : desensitizationInstance) {
            if (dto.getEnable_desensitization_copy() != null && dto.getEnable_desensitization_copy() == 1) {
                hashMap.put(dto.getSensitive_level(), 1);
            }
        }
        return hashMap;
    }

    private Map<String, Integer> getSensitiveLevelMap() {
        List<SensitiveLevel> sensitiveLevels = this.sensitiveLevelMapper.selectList(Wrappers.<SensitiveLevel>lambdaQuery()
                .eq(SensitiveLevel::getIsDelete, 0)
        );
        if (sensitiveLevels == null || sensitiveLevels.isEmpty()) {
            return new HashMap<>();
        }
        return sensitiveLevels.stream()
                .collect(Collectors.toMap(SensitiveLevel::getUniqueKey, SensitiveLevel::getLevel, (e1, e2) -> e1));
    }

    private List<PermissionRuleDto> getSensitiveLevelDesensitizationInstance(Map<String, List<PermissionRuleDto>> maskAuthObject, String key) {
        if (maskAuthObject.get(key) == null) {
            return new ArrayList<>();
        }
        return maskAuthObject.get(key).stream()
                .filter(e -> Integer.valueOf(2).equals(e.getGrant_type())).collect(Collectors.toList());
    }

    private List<PermissionRuleDto> getSensitiveLevelDesensitizationSchema(Map<String, List<PermissionRuleDto>> maskAuthObject, String key, String schemaUniqueKey) {
        if (maskAuthObject.get(key) == null) {
            return new ArrayList<>();
        }
        return maskAuthObject.get(key).stream()
                .filter(e -> Integer.valueOf(2).equals(e.getGrant_type()) && schemaUniqueKey.equals(e.getSchema_id())).collect(Collectors.toList());
    }

    private List<PermissionRuleDto> getDesensitizationInstance(Map<String, List<PermissionRuleDto>> maskAuthObject, String key) {
        if (maskAuthObject.get(key) == null) {
            return new ArrayList<>();
        }
        return maskAuthObject.get(key).stream()
                .filter(e -> !Integer.valueOf(2).equals(e.getGrant_type())).collect(Collectors.toList());
    }

    private List<PermissionRuleDto> getDesensitizationSchema(Map<String, List<PermissionRuleDto>> maskAuthObject, String key, String schemaUniqueKey) {
        if (maskAuthObject.get(key) == null) {
            return new ArrayList<>();
        }
        return maskAuthObject.get(key).stream()
                .filter(e -> !Integer.valueOf(2).equals(e.getGrant_type()) && schemaUniqueKey.equals(e.getSchema_id())).collect(Collectors.toList());
    }

    private List<String> getSensitiveLevel(List<PermissionRuleDto> permissionRuleDtoList) {
        return permissionRuleDtoList.stream().map(PermissionRuleDto::getSensitive_level).distinct().collect(Collectors.toList());
    }

    private Map<Integer, List<String>> buildLevelMap() {
        Map<Integer, List<String>> level = new HashMap<>();
        level.put(SensitiveDataType.FULL.getValue(), new ArrayList<>());
        level.put(SensitiveDataType.HALF.getValue(), new ArrayList<>());
        level.put(SensitiveDataType.CLEAR.getValue(), new ArrayList<>());
        return level;
    }

    private Map<Integer, List<Integer>> buildLevelMap(Map<Integer, List<String>> level, Map<String, Integer> sensitiveLevelMap) {
        if (sensitiveLevelMap.isEmpty()) {
            return new HashMap<>();
        }
        Map<Integer, List<Integer>> returnLevel = new HashMap<>();
        for (Map.Entry<Integer, List<String>> entry : level.entrySet()) {
            List<Integer> levelNumber = entry.getValue().stream().map(sensitiveLevelMap::get).collect(Collectors.toList());
            returnLevel.put(entry.getKey(), levelNumber);
        }
        return returnLevel;
    }

    private Map<Integer, Integer> buildLevelMap(Map<Integer, List<Integer>> integerListMap) {
        Map<Integer, Integer> map = new LinkedHashMap<>();
        if (integerListMap.get(SensitiveDataType.FULL.getValue()).contains(1)) {
            map.put(1, SensitiveDataType.FULL.getValue());
        }
        for (int i = 1; i <= 5; i++) {
            buildMap(map, integerListMap, i, SensitiveDataType.FULL.getValue());
            buildMap(map, integerListMap, i, SensitiveDataType.HALF.getValue());
            buildMap(map, integerListMap, i, SensitiveDataType.CLEAR.getValue());
        }
        return map;
    }

    private void buildMap(Map<Integer, Integer> map, Map<Integer, List<Integer>> integerListMap, int sLevel, int authLevel) {
        if (integerListMap.get(authLevel).contains(sLevel)) {
            map.put(sLevel, authLevel);
        }
    }

    private GradedClassifiedModel buildGradedClassifiedModel(List<GradedClassifiedModel> gradedClassifiedModelList, ParserParamDto paramDTO) {
        GradedClassifiedModel gradedClassifiedModel = new GradedClassifiedModel();

        if (!gradedClassifiedModelList.isEmpty()) {
            Map<String, Integer> subCopyTemp = gradedClassifiedModelList.get(0).getEnableDesensitizationCopy();
            for (GradedClassifiedModel classifiedModel : gradedClassifiedModelList) {
                subCopyTemp = mergeCopy(subCopyTemp, classifiedModel.getEnableDesensitizationCopy());
                if (subCopyTemp != null && !subCopyTemp.isEmpty()) {
                    gradedClassifiedModel.setEnableDesensitizationCopy(subCopyTemp);
                }
            }
        }
        if (webSQLParserInfo.isClassification()) {
            gradedClassifiedModel.setLevel(true);
            if (gradedClassifiedModelList.isEmpty()) {
                return gradedClassifiedModel;
            }
            Map<Integer, Integer> sensitiveLevelToAuthLevel = gradedClassifiedModelList.get(0).getSensitiveLevelToAuthLevel();
            for (GradedClassifiedModel classifiedModel : gradedClassifiedModelList) {
                Map<Integer, Integer> sub = classifiedModel.getSensitiveLevelToAuthLevel();
                merge(sensitiveLevelToAuthLevel, sub);
                gradedClassifiedModel.setDatabaseName(classifiedModel.getDatabaseName());
            }
            gradedClassifiedModel.setSensitiveLevelToAuthLevel(sensitiveLevelToAuthLevel);
        } else {
            gradedClassifiedModel.setLevel(false);
            for (GradedClassifiedModel classifiedModel : gradedClassifiedModelList) {
                if (classifiedModel.getHasDefaultPlainAuth() == null) {
                    gradedClassifiedModel.setHasDefaultPlainAuth(null);
                    gradedClassifiedModel.setDatabaseName(classifiedModel.getDatabaseName());
                    break;
                } else if (!classifiedModel.getHasDefaultPlainAuth()) {
                    gradedClassifiedModel.setHasDefaultPlainAuth(false);
                    gradedClassifiedModel.setDatabaseName(classifiedModel.getDatabaseName());
                    break;
                } else {
                    gradedClassifiedModel.setHasDefaultPlainAuth(true);
                    gradedClassifiedModel.setDatabaseName(classifiedModel.getDatabaseName());
                }
            }
        }

        Map<String, List<PermissionRuleDto>> maskAuthObject = paramDTO.getMaskAuthObject();
        if (maskAuthObject != null && !maskAuthObject.isEmpty()) {
            gradedClassifiedModel.setMaskAuthObject(maskAuthObject);
        }

        return gradedClassifiedModel;
    }

    // 取最小复制权限
    private Map<String, Integer> mergeCopy(Map<String, Integer> result, Map<String, Integer> sub) {
        if (result == null || sub == null) {
            return null;
        }
        for (Map.Entry<String, Integer> entry : sub.entrySet()) {
            Integer entryValue = entry.getValue();
            if (entryValue == null) {
                entryValue = 0;
            }
            if (result.get(entry.getKey()) == null) {
                result.put(entry.getKey(), entryValue);
                continue;
            }
            Integer resultValue = result.get(entry.getKey());
            // 不复制
            if (resultValue == null || resultValue == 0 || entryValue == 0) {
                result.put(entry.getKey(), 0);
            }
        }
        return result;
    }

    // 权限合并, 取最小
    private void merge(Map<Integer, Integer> sensitiveLevelToAuthLevel, Map<Integer, Integer> sub) {
        for (Map.Entry<Integer, Integer> entry : sub.entrySet()) {
            if (sensitiveLevelToAuthLevel.get(entry.getKey()) != null && sensitiveLevelToAuthLevel.get(entry.getKey()) > entry.getValue()) {
                sensitiveLevelToAuthLevel.put(entry.getKey(), entry.getValue());
            }
        }
    }

    private void getAuthMap(ParserParamDto paramDTO, List<SqlAuthModel> SqlAuthModelList) {
        if (paramDTO.getPeUserAuths() == null) {
            this.getAuth(paramDTO, SqlAuthModelList);
        }
    }

    private void getAuth(ParserParamDto paramDTO, List<SqlAuthModel> SqlAuthModelList) {
        List<InstanceRole> instanceMaskRole = getInstanceMaskRole();

        List<String> actionKeys = new ArrayList<>();
        GetAuthUtil.addMaskAuthList(actionKeys);
        actionKeys.addAll(instanceMaskRole.stream().map(InstanceRole::getUnique_key).collect(Collectors.toList()));

        Map<String, Object> map = new HashMap<>();
        map.put("now", System.currentTimeMillis() / 1000);
        map.put("user_id", paramDTO.getUserId());
        map.put("connect_id", paramDTO.getConnectId());
        map.put("action_keys", actionKeys);

        List<String> schemaIds = new ArrayList<>();
        List<String> objectNames = new ArrayList<>();
        for (SqlAuthModel sqlAuthModel : SqlAuthModelList) {
            schemaIds.add(sqlAuthModel.getSchemaUniqueKey());
            String fullName = sqlAuthModel.getFullName() != null ? sqlAuthModel.getFullName().toUpperCase(Locale.ROOT) : sqlAuthModel.getFullName();
            objectNames.add(fullName);
        }
        map.put("schema_ids", schemaIds);
        map.put("resource_types", DataNodeType.getIdentCode(DataNodeType.getResourceTypes()));
        map.put("object_names", objectNames);

        List<String> organizations = webSQLParserInfo.getOrganizations();
        map.put("organizations", organizations);

        List<PermissionRule> userAuths = userRuleService.getUserAuth(map);
        paramDTO.setPeUserAuths(parserMapper.toPermissionRuleDtoList(userAuths));

        Map<String, InstanceRole> instanceRoleMap = instanceMaskRole.stream()
                .collect(Collectors.toMap(InstanceRole::getUnique_key, instanceRole -> instanceRole));
        Map<String, List<String>> roleAuth = GetAuthUtil.buildRoleAuth(paramDTO.getPeUserAuths());
        Map<String, List<String>> maskAuth = GetAuthUtil.buildMaskAuth(paramDTO.getPeUserAuths());

        paramDTO.setInstanceRoleMap(parserMapper.toInstanceRoleDtoMap(instanceRoleMap));
        paramDTO.setRoleAuth(roleAuth);
        paramDTO.setMaskAuth(maskAuth);
    }

    private void getGradedClassifiedAuth(ParserParamDto paramDTO, List<SqlAuthModel> SqlAuthModelList) {
        List<InstanceRole> instanceMaskRole = getInstanceMaskRole();

        List<String> actionKeys = new ArrayList<>();
        GetAuthUtil.addMaskAuthList(actionKeys);
        actionKeys.add(AuthConstant.desensitization_not_instance);
        actionKeys.add(AuthConstant.desensitization_not_schema);
        actionKeys.addAll(instanceMaskRole.stream().map(InstanceRole::getUnique_key).collect(Collectors.toList()));

        Map<String, Object> map = new HashMap<>();
        map.put("now", System.currentTimeMillis() / 1000);
        map.put("user_id", paramDTO.getUserId());
        map.put("connect_id", paramDTO.getConnectId());
        map.put("action_keys", actionKeys);

        List<String> schemaIds = new ArrayList<>();
        List<String> objectNames = new ArrayList<>();
        for (SqlAuthModel sqlAuthModel : SqlAuthModelList) {
            schemaIds.add(sqlAuthModel.getSchemaUniqueKey());
            String fullName = sqlAuthModel.getFullName() != null ? sqlAuthModel.getFullName().toUpperCase(Locale.ROOT) : sqlAuthModel.getFullName();
            objectNames.add(fullName);
        }
        map.put("schema_ids", schemaIds);
        map.put("resource_types", DataNodeType.getIdentCode(DataNodeType.getResourceTypes()));
        map.put("object_names", objectNames);

        List<String> organizations = webSQLParserInfo.getOrganizations();
        map.put("organizations", organizations);

        List<PermissionRule> userAuths = userRuleService.getUserAuth(map);
        paramDTO.setPeUserAuths(parserMapper.toPermissionRuleDtoList(userAuths));

        Map<String, InstanceRole> instanceRoleMap = instanceMaskRole.stream()
                .collect(Collectors.toMap(InstanceRole::getUnique_key, instanceRole -> instanceRole));
        Map<String, List<String>> roleAuth = GetAuthUtil.buildRoleAuth(paramDTO.getPeUserAuths());

        Map<String, List<PermissionRuleDto>> maskAuth = GetAuthUtil.buildMaskAuthObject(paramDTO.getPeUserAuths());

        paramDTO.setInstanceRoleMap(parserMapper.toInstanceRoleDtoMap(instanceRoleMap));

        paramDTO.setRoleAuth(roleAuth);

        paramDTO.setMaskAuthObject(maskAuth);

    }

    private <k, v> void logMap(Map<k, v> map) {
        for (Map.Entry<k, v> entry : map.entrySet()) {
            log.info("key:" + entry.getKey());
            log.info("value:" + entry.getValue());
        }
    }

    private List<InstanceRole> getInstanceMaskRole() {
        return groupUserMapper.getInstanceRole()
                .stream()
                .filter(instanceRole -> Arrays.asList(1, 2).contains(instanceRole.getSensitive_rule()))
                .collect(Collectors.toList());
    }

    private boolean hasDataMaskAuth(String authToken, Map<String, List<String>> authMap, String dataMaskUniqueKey) {
        List<String> instanceItem = authMap.get(authToken);
        if (instanceItem != null) {
            for (String item : instanceItem) {
                if (item.equalsIgnoreCase(dataMaskUniqueKey)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean hasRoleAuth(ParserParamDto paramDTO, Integer level) {
        for (Map.Entry<String, List<String>> entry : paramDTO.getRoleAuth().entrySet()) {
            if (entry.getValue().contains(paramDTO.getConnectId())) {
                InstanceRoleDto instanceRoleDto = paramDTO.getInstanceRoleMap().get(entry.getKey());
                if (level.equals(instanceRoleDto.getSensitive_rule())) {
                    return true;
                }
            }
        }
        return false;
    }

    private List<DataMask> getMaskModelList(MaskTableModel maskTableModel, String schemaUniqueKey, String tableName) {
        return dbColumnMapper.getColumnMaskConfig(schemaUniqueKey, tableName, maskTableModel.isStar() ? null : maskTableModel.getColumns())
                .stream()
                .map(map -> json.getObjectMapper().convertValue(map, DataMask.class))
                .collect(Collectors.toList());
    }

    private List<DataMask> getMaskModelList(MaskTableModel maskTableModel, List<SqlFieldDataDto> sqlFieldDataList) {
        List<DataMask> list = new ArrayList<>();

        List<SqlFieldDataDto> collect = sqlFieldDataList.stream()
                .filter(e -> e.getSchemaName().equalsIgnoreCase(maskTableModel.getSchemaName())
                        && e.getTableName().equalsIgnoreCase(maskTableModel.getTableName()))
                .collect(Collectors.toList());

        boolean star = maskTableModel.isStar();

        if (star) {
            for (SqlFieldDataDto sqlFieldDataDto : collect) {
                DataMask dataMask = buildDataMask(sqlFieldDataDto.getSchemaName(), sqlFieldDataDto.getTableName(), sqlFieldDataDto.getFieldName());
                list.add(dataMask);
            }
        } else {
            List<String> columns = maskTableModel.getColumns();

            for (String col : columns) {
                SqlFieldDataDto sqlFieldDataDto = collect.stream().filter(e -> e.getFieldName().equalsIgnoreCase(col)).collect(Collectors.toList()).get(0);
                DataMask dataMask = buildDataMask(sqlFieldDataDto.getSchemaName(), sqlFieldDataDto.getTableName(), sqlFieldDataDto.getFieldName());
                list.add(dataMask);
            }
        }

        return list;
    }

    private DataMask buildDataMask(String schemaName, String tableName, String columnName) {
        DataMask dataMask = new DataMask();
        dataMask.setSchemaName(schemaName);
        dataMask.setTableName(tableName);
        dataMask.setColumnName(columnName);
        return dataMask;
    }

    private void buildDataMask(MaskTableModel maskTableModel, DataMask model, boolean hasClearAuth) {
        model.setActionMask(ActionMaskType.cfg.name());
        model.setSchemaName(maskTableModel.getSchemaName());
        if (!hasClearAuth) {
            model.setIsSensitive(1); // 查出来的都是要脱敏的字段
        }
        model.setIsDefault(model.getIsDefault() == null ? 0 : model.getIsDefault());
    }

    private List<String> buildColumns(Set<String> columnSet, Integer dbType, String defaultSchemaName, String defaultFrameworkName) {
        List<String> columns = new ArrayList<>();

        for (String column : columnSet) {
            String splitRegex = CommonUtil.useColonSplit(dbType) ? ":|\\." : "\\.";
            String[] split = column.toLowerCase(Locale.ROOT).split(splitRegex);

            String tableName;
            String columnName;
            if (DatabaseType.ELASTIC_SEARCH.getValue().equals(dbType)) {
                tableName = column;
                columnName = "*";
            } else {
                tableName = split[split.length - 2];
                columnName = split[split.length - 1];
            }

            String columnStr;
            if (DatabaseType.get3FDatabaseIntegerValueList().contains(dbType)) {
                if (split.length - 3 >= 0) {
                    columnStr = split[split.length - 3] + "." + tableName + "." + columnName;
                } else {
                    if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType)) {
                        columnStr = defaultSchemaName + "." + tableName + "." + columnName;
                    } else {
                        columnStr = defaultFrameworkName + "." + tableName + "." + columnName;
                    }
                }
            } else {
                columnStr = tableName + "." + columnName;
            }
            columns.add(columnStr.toUpperCase());
        }

        return columns;
    }

    private void buildAlias(SqlParseModel sqlParserModel, Integer dbType, String defaultSchemaName, String defaultFrameworkName) {
        Map<String, String> alias = sqlParserModel.getAction().getAlias();
        Map<String, List<String>> aliasBk = sqlParserModel.getAction().getAliasBk();

        if (alias != null) {
            if (sqlParserModel.gettCustomSqlStatement() instanceof TSelectSqlStatement) {
                TSelectSqlStatement tempSelect = (TSelectSqlStatement) sqlParserModel.gettCustomSqlStatement();
                if (tempSelect.getSelectDistinct() != null) {
                    sqlParserModel.getAction().setAlias(buildSelectDistinctAliasMap(alias));
                }
            }

            if (DatabaseType.get3FDatabaseIntegerValueList().contains(dbType)) {
                sqlParserModel.getAction().setAlias(build3FDatabaseAliasMap(alias, dbType, defaultSchemaName, defaultFrameworkName));
            } else {
                sqlParserModel.getAction().setAlias(build2FDatabaseAliasMap(alias));
            }
        }

        if (aliasBk != null) {
            if (DatabaseType.get3FDatabaseIntegerValueList().contains(dbType)) {
                sqlParserModel.getAction().setAliasBk(build3FDatabaseAliasMapBk(aliasBk, dbType, defaultSchemaName, defaultFrameworkName));
            } else {
                sqlParserModel.getAction().setAliasBk(build2FDatabaseAliasMap(aliasBk));
            }
        }
    }

    private Map<String, String> buildSelectDistinctAliasMap(Map<String, String> alias) {
        Map<String, String> newAliasMap = new HashMap<>();
        for (Map.Entry<String, String> entry : alias.entrySet()) {
            String oldEntryValue = entry.getValue();
            if (oldEntryValue.indexOf('(') == 0 && oldEntryValue.indexOf(')') == oldEntryValue.length() - 1) {
                oldEntryValue = oldEntryValue.substring(1, oldEntryValue.length() - 1);
            }
            newAliasMap.put(entry.getKey(), oldEntryValue.trim());
        }
        return newAliasMap;
    }

    private Map<String, String> build3FDatabaseAliasMap(Map<String, String> alias, Integer dbType, String defaultSchemaName, String defaultFrameworkName) {
        Map<String, String> newAliasMap = new HashMap<>();
        for (Map.Entry<String, String> entry : alias.entrySet()) {
            String oldEntryKey = entry.getKey();
            String[] split = oldEntryKey.split("\\.");
            String value = entry.getValue();
            if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType)) {
                if (value.startsWith("(") && value.endsWith(")") && value.contains(",")) {
                    value = "row";
                }
            }
            if (split.length == 2) {
                String middleName = DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType) ? defaultSchemaName : defaultFrameworkName;
                String newEntryKey = middleName.toUpperCase() + "." + split[0] + "." + split[1];
                newAliasMap.put(newEntryKey, value);
            } else {
                newAliasMap.put(oldEntryKey, value);
            }
        }
        return newAliasMap;
    }

    private Map<String, List<String>> build3FDatabaseAliasMapBk(Map<String, List<String>> aliasBk, Integer dbType, String defaultSchemaName, String defaultFrameworkName) {
        Map<String, List<String>> newAliasMap = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : aliasBk.entrySet()) {
            String oldEntryKey = entry.getKey();
            String[] split = oldEntryKey.split("\\.");
            if (split.length == 2) {
                String middleName = DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType) ? defaultSchemaName : defaultFrameworkName;
                String newEntryKey = middleName.toUpperCase() + "." + split[0] + "." + split[1];
                newAliasMap.put(newEntryKey, entry.getValue());
            } else {
                newAliasMap.put(oldEntryKey, entry.getValue());
            }
        }
        return newAliasMap;
    }

    private <T> Map<String, T> build2FDatabaseAliasMap(Map<String, T> alias) {
        Map<String, T> newAliasMap = new HashMap<>();
        for (Map.Entry<String, T> entry : alias.entrySet()) {
            String oldEntryKey = entry.getKey();
            String[] split = oldEntryKey.split("\\.");
            if (split.length == 3) {
                String newEntryKey = split[1] + "." + split[2];
                newAliasMap.put(newEntryKey, entry.getValue());
            } else {
                newAliasMap.put(oldEntryKey, entry.getValue());
            }
        }
        return newAliasMap;
    }

    private List<DataMask> getDataMaskConfigs(List<String> columns, List<DataMask> maskColumns, SqlActionModel action,
                                              Integer dbType, String defaultSchemaName, String defaultFrameworkName) {

        if (DatabaseType.ELASTIC_SEARCH.getValue().equals(dbType)) {
            buildESDataMaskConfigs(maskColumns);
            return maskColumns;
        }

        List<DataMask> dataMaskList = new ArrayList<>();

        for (String col : columns) {
            String[] split = col.split("\\.");
            boolean flag = "*".equals(split[split.length - 1]);

            for (DataMask column : maskColumns) {
                String columnTableName = column.getTableName();
                if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType)) {
                    String[] objectName = column.getSchemaName().split("\\.");
                    columnTableName = objectName[objectName.length - 1] + "." + columnTableName;
                }

                String key = columnTableName + "." + column.getColumnName();

                if (flag) {
                    if (split.length > 1) {
                        String tabName = split[split.length - 2];
                        if (DatabaseType.get3FDatabaseIntegerValueList().contains(dbType)) {
                            if (split.length > 2) {
                                tabName = split[split.length - 3] + "." + split[split.length - 2];
                            } else {
                                String middleName = DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType) ? defaultSchemaName : defaultFrameworkName;
                                tabName = middleName.toUpperCase() + "." + split[split.length - 2];
                            }
                        }
                        if (tabName.equalsIgnoreCase(columnTableName)) {
                            DataMask dataMaskConfig = this.buildDataMaskConfig(column, column.getColumnName(), columnTableName);
                            dataMaskList.add(dataMaskConfig);
                        }
                    }
                } else if (col.equalsIgnoreCase(key)) {
                    DataMask dataMaskConfig = this.buildDataMaskConfig(column, column.getColumnName(), columnTableName);

                    if (action.getAlias() != null && action.getAlias().get(key.toUpperCase()) != null) {
                        String regex = CommonUtil.useColonSplit(dbType) ? ":|\\." : "\\.";
                        String[] alias = action.getAlias().get(key.toUpperCase()).split(regex);
                        if ("*".equals(alias[alias.length - 1])) {
                            this.addDataMaskConfig(action.getAliasBk(), key, regex, column, columnTableName, dataMaskList);
                        } else {
                            String aliasName = replaceAliasName(alias[alias.length - 1]);
                            if (DatabaseType.ORACLE.getValue().equals(dbType) && alias.length == 2) {
                                // SELECT utl_raw.cast_to_varchar2(PADDR) FROM SCOTT."DATATYPE_TEST_RAW";
                                aliasName = this.getUtlRawAlias(aliasName, alias);
                            }
                            dataMaskConfig.setColumnAlias(aliasName);

                            this.addDataMaskConfig(action.getAliasBk(), key, regex, column, columnTableName, dataMaskList);
                        }
                    }

                    dataMaskList.add(dataMaskConfig);
                }
            }
        }

        return dataMaskList;
    }

    private List<DataMask> duplicateRemoveUnionColumn(SqlParseModel sqlParserModel, List<DataMask> dataMaskList) {
        if (sqlParserModel.gettCustomSqlStatement() instanceof TSelectSqlStatement) {
            TSelectSqlStatement selectSqlStatement = (TSelectSqlStatement) sqlParserModel.gettCustomSqlStatement();

            if (selectSqlStatement.getSetOperatorType() != null && "union".equals(selectSqlStatement.getSetOperatorType().toString())) {

                Map<String, DataMask> dataMaskMap = new HashMap<>();

                for (DataMask mask : dataMaskList) {
                    DataMask dataMask = dataMaskMap.get(mask.getColumnName());
                    if (dataMask == null) {
                        dataMaskMap.put(mask.getColumnName(), mask);
                    } else if (dataMask.getSensitiveLevel() < mask.getSensitiveLevel()) {
                        dataMaskMap.put(mask.getColumnName(), mask);
                    }
                }

                return new ArrayList<>(dataMaskMap.values());
            }
        }
        return dataMaskList;
    }

    private void buildESDataMaskConfigs(List<DataMask> maskColumns) {
        for (DataMask column : maskColumns) {
            column.setShowTableName(column.getTableName());
            column.setColumnAlias(column.getColumnName());
        }
    }

    private DataMask buildDataMaskConfig(DataMask maskColumnModel, String aliasName, String tableName) {
        DataMask dataMaskConfig = new DataMask();

        dataMaskConfig.setTableName(tableName);
        // tableName用来真正脱敏，兼容老版本脱敏方式，pg系列会带schema名；showTableName用来显示敏感审计表名，pg系列会不带schema名
        dataMaskConfig.setShowTableName(maskColumnModel.getTableName());
        dataMaskConfig.setColumnName(maskColumnModel.getColumnName());
        dataMaskConfig.setColumnAlias(aliasName);
        dataMaskConfig.setAlgorithmType(maskColumnModel.getAlgorithmType());
        dataMaskConfig.setAlgorithmParam(maskColumnModel.getAlgorithmParam());
        dataMaskConfig.setIsSensitive(maskColumnModel.getIsSensitive());
        dataMaskConfig.setIsDefault(maskColumnModel.getIsDefault());
        dataMaskConfig.setActionMask(maskColumnModel.getActionMask());
        dataMaskConfig.setSchemaName(maskColumnModel.getSchemaName());
        dataMaskConfig.setAuthLevel(maskColumnModel.getAuthLevel());
        dataMaskConfig.setDesensitizeRuleId(maskColumnModel.getDesensitizeRuleId());
        dataMaskConfig.setDesensitizeRuleName(maskColumnModel.getDesensitizeRuleName());
        dataMaskConfig.setDistinguishRuleId(maskColumnModel.getDistinguishRuleId());
        dataMaskConfig.setDistinguishRuleName(maskColumnModel.getDistinguishRuleName());
        dataMaskConfig.setSensitiveLevelColor(maskColumnModel.getSensitiveLevelColor());
        dataMaskConfig.setSensitiveLevelId(maskColumnModel.getSensitiveLevelId());
        dataMaskConfig.setSensitiveLevelName(maskColumnModel.getSensitiveLevelName());
        dataMaskConfig.setColumnId(maskColumnModel.getColumnId());
        dataMaskConfig.setConnectId(maskColumnModel.getConnectId());
        dataMaskConfig.setDbType(maskColumnModel.getDbType());
        dataMaskConfig.setInstanceName(maskColumnModel.getInstanceName());
        dataMaskConfig.setEnvironment(maskColumnModel.getEnvironment());
        dataMaskConfig.setConnectionDesc(maskColumnModel.getConnectionDesc());
        dataMaskConfig.setSchemaId(maskColumnModel.getSchemaId());
        dataMaskConfig.setEnableDesensitizationCopy(maskColumnModel.getEnableDesensitizationCopy());
        dataMaskConfig.setSensitiveLevel(maskColumnModel.getSensitiveLevel());
        return dataMaskConfig;
    }

    private String replaceAliasName(String aliasName) {
        if (aliasName != null && aliasName.contains(" ")) {
            aliasName = aliasName.replaceAll("\\s*\\,\\s*", "\\,")
                    .replaceAll("\\s*\\(\\s*", "\\(")
                    .replaceAll("\\s*\\)\\s*", "\\)");
        }
        return aliasName;
    }

    private void addDataMaskConfig(Map<String, List<String>> aliasBk, String key, String regex, DataMask column,
                                   String columnTableName, List<DataMask> dataMaskList) {
        if (aliasBk != null && aliasBk.get(key.toUpperCase()) != null) {
            String[] alias;
            for (String str : aliasBk.get(key.toUpperCase())) {
                alias = str.split(regex);
                String aliasName = replaceAliasName(alias[alias.length - 1]);
                dataMaskList.add(this.buildDataMaskConfig(column, aliasName, columnTableName));
            }
        }
    }

    private String getUtlRawAlias(String aliasName, String[] alias) {
        try {
            if ("utl_raw".equalsIgnoreCase(alias[0].trim()) && alias[1].toUpperCase(Locale.ROOT).contains("CAST_TO_VARCHAR2(") && alias[1].contains(")")) {
                return alias[0].trim() + "." + alias[1].replaceAll("\\s", "");
            }
        } catch (Exception e) {
            log.error("get utl raw alias error!", e);
        }
        return aliasName;
    }

    private List<MaskRule> buildMaskRule(ParserParamDto paramDTO, List<DataMask> dataMaskList) {

        List<PermissionRuleDto> authList = paramDTO.getPeUserAuths();

        if (authList.isEmpty()) {
            return null;
        }

        List<MaskRule> maskRules = new ArrayList<>();

        Map<String, InstanceRoleDto> instanceRoleMap = paramDTO.getInstanceRoleMap();

        Map<String, String> dataMaskMap = buildDataMaskMap(dataMaskList);

        for (PermissionRuleDto ruleDto : authList) {

            String actionKey = ruleDto.getAction_key();
            InstanceRoleDto instanceRoleDto = instanceRoleMap.get(actionKey);

            if (instanceRoleDto != null) {
                if (!Arrays.asList(1, 2).contains(instanceRoleDto.getSensitive_rule())) {
                    continue;
                }
            } else if (!Arrays.asList(AuthConstant.desensitization_instance, AuthConstant.desensitization_half_instance,
                    AuthConstant.desensitization_schema, AuthConstant.desensitization_half_schema,
                    AuthConstant.desensitization_table, AuthConstant.desensitization_half_table,
                    AuthConstant.desensitization_column, AuthConstant.desensitization_half_column).contains(actionKey)) {
                continue;
            }

            MaskRule maskRule = new MaskRule();

            if (Integer.valueOf(1).equals(paramDTO.getEnableDesensiteType())) {
                // 全脱开关打开，有半脱敏权限和明文权限
                if (instanceRoleDto != null) {
                    if (Integer.valueOf(1).equals(instanceRoleDto.getSensitive_rule())) {
                        maskRule.setDesensiteType("明文展示");
                    } else {
                        maskRule.setDesensiteType("半脱敏");
                    }
                } else if (Arrays.asList(AuthConstant.desensitization_half_instance, AuthConstant.desensitization_half_schema,
                        AuthConstant.desensitization_half_table, AuthConstant.desensitization_half_column).contains(actionKey)) {
                    if (Integer.valueOf(2).equals(ruleDto.getGrant_type())) {
                        if (dataMaskMap.get(ruleDto.getSensitive_level()) != null) {
                            maskRule.setDesensiteType(String.format(escapeUselessPercentageSign(dataMaskMap.get(ruleDto.getSensitive_level())), "半脱敏"));
                        } else {
                            continue;
                        }
                    } else {
                        maskRule.setDesensiteType("半脱敏");
                    }
                } else {
                    if (Integer.valueOf(2).equals(ruleDto.getGrant_type())) {
                        if (dataMaskMap.get(ruleDto.getSensitive_level()) != null) {
                            maskRule.setDesensiteType(String.format(escapeUselessPercentageSign(dataMaskMap.get(ruleDto.getSensitive_level())), "明文展示"));
                        } else {
                            continue;
                        }
                    } else {
                        maskRule.setDesensiteType("明文展示");
                    }
                }
            } else {
                // 全脱开关关闭，只有明文权限
                if (instanceRoleDto != null) {
                    if (Integer.valueOf(1).equals(instanceRoleDto.getSensitive_rule())) {
                        maskRule.setDesensiteType("明文展示");
                    } else {
                        continue;
                    }
                } else if (Arrays.asList(AuthConstant.desensitization_half_instance, AuthConstant.desensitization_half_schema,
                        AuthConstant.desensitization_half_table, AuthConstant.desensitization_half_column).contains(actionKey)) {
                    continue;
                } else {
                    if (Integer.valueOf(2).equals(ruleDto.getGrant_type())) {
                        if (dataMaskMap.get(ruleDto.getSensitive_level()) != null) {
                            maskRule.setDesensiteType(String.format(escapeUselessPercentageSign(dataMaskMap.get(ruleDto.getSensitive_level())), "明文展示"));
                        } else {
                            continue;
                        }
                    } else {
                        maskRule.setDesensiteType("明文展示");
                    }
                }
            }

            maskRule.setSchemaId(ruleDto.getSchema_id());
            maskRule.setTableName(ruleDto.getObject_name());
            maskRule.setAuthTime(ruleDto.getGmt_create());
            maskRule.setAuthTimeEnd(ruleDto.getEnd_time());
            maskRule.setAuthTimeStart(ruleDto.getBegin_time());
            maskRule.setOrigin(ruleDto.getOrigin());
            maskRule.setAuthUserOrganization(ruleDto.getGroup_name());
            maskRule.setOrderCode(ruleDto.getOrder_code());
            maskRule.setOrderRelevance(ruleDto.getOrder_relevance());
            maskRule.setConnectId(ruleDto.getConnect_id());
            maskRule.setGrantType(ruleDto.getGrant_type());
            maskRule.setSensitiveLevel(ruleDto.getSensitive_level());

            if (DataNodeType.CONNECT.getValue().equals(ruleDto.getResource_type())) {
                maskRule.setRuleType("实例");
            } else if (DataNodeType.SCHEMA.getValue().equals(ruleDto.getResource_type())) {
                maskRule.setRuleType("Schema");
            } else if (Arrays.asList(DataNodeType.TABLE.getValue(), DataNodeType.TABLE_SUB.getValue()).contains(ruleDto.getResource_type())) {
                maskRule.setRuleType("表");
                maskRule.setRuleObject(ruleDto.getObject_name());
            } else if (Arrays.asList(DataNodeType.TAB_COLUMN.getValue(), DataNodeType.TAB_COLUMN_SUB.getValue()).contains(ruleDto.getResource_type())) {
                maskRule.setRuleType("列");
                maskRule.setRuleObject(ruleDto.getColumn_name());
            }

            if ("sensitive_head".equals(ruleDto.getAction_key()) && instanceRoleDto != null) {
                maskRule.setSensitiveHead(instanceRoleDto.getRole_name());
            }

            maskRule.setAuthUser(sqlRuleService.getCreatorName(ruleDto.getOperator()));
            maskRule.setEnableDesensitizationCopy(ruleDto.getEnable_desensitization_copy() != null ? ruleDto.getEnable_desensitization_copy() : 0);

            maskRules.add(maskRule);
        }

        return maskRules;
    }

    private Map<String, String> buildDataMaskMap(List<DataMask> dataMaskList) {
        Map<String, String> map = new HashMap<>();
        for (DataMask dataMask : dataMaskList) {
            String value = "S" + dataMask.getSensitiveLevel() + "(" + dataMask.getSensitiveLevelName() + "):%s";
            map.put(dataMask.getSensitiveLevelId(), value);
        }
        return map;
    }

    /**
     * 转义普通的无用的%字符
     * @param original 转义前的字符串
     * @return 转义后的结果字符串
     * 举例 : S4(~@#%^&):%s  ->  S4(~@#%%^&):%s
     */
    private String escapeUselessPercentageSign(@Nonnull String original) {
        try {
            int lastColonIndex = original.lastIndexOf(":");
            if (lastColonIndex == -1) {
                return original;
            }
            String beforeColon = original.substring(0, lastColonIndex);
            String afterColon = original.substring(lastColonIndex);
            String replacedBefore = beforeColon.replace("%", "%%");
            return replacedBefore + afterColon;
        } catch (Exception e) {
            return original;
        }
    }

}
