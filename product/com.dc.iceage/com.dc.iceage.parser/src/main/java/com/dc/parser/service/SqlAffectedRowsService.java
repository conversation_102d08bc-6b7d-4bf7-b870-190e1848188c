package com.dc.parser.service;

import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.summer.parser.utils.model.SqlParseModel;

import java.util.Map;

public interface SqlAffectedRowsService {

    int getRows(ParserParamDto paramDTO, SqlParseModel sqlParserModel);

    long getRowsByExplain(ParserParamDto paramDTO, String query, String schemaName, String tableName);

    int getTruncateRows(ParserParamDto paramDTO, String schemaName, String tableName);

}
