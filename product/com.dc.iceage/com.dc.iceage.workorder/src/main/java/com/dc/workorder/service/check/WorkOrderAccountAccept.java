package com.dc.workorder.service.check;

import com.dc.repository.mysql.column.*;
import com.dc.repository.mysql.model.Order;
import com.dc.repository.mysql.model.OrderSqlParse;
import com.dc.repository.mysql.model.WorkOrderScript;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.ObjectTypeMessage;
import com.dc.springboot.core.model.database.ResourceObject;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.script.ParseScriptMessage;
import com.dc.springboot.core.model.script.WebSQLQueryInfo;
import com.dc.springboot.core.model.script.WebSQLScriptInfo;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.springboot.core.model.type.ParserExecuteType;
import com.dc.type.DatabaseType;
import com.dc.utils.CommonUtils;
import com.dc.workorder.utils.OrderSqlParseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class WorkOrderAccountAccept extends WorkOrderCheckSqlPrivilege {

    public WorkOrderAccountAccept(OriginType originType, List<ParserExecuteType> parserExecuteTypes, ConnectionConfig connectionConfig) {
        super(originType, parserExecuteTypes, connectionConfig);
    }

    @Override
    protected void generateOrderSqlParse(List<ResourceContent> resourceContents, BaseInfo baseInfo, AuthGrant authGrant, AuthRevoke authRevoke, Integer orderId, String scriptName, Integer dbType) {

        DatabaseType typ = DatabaseType.of(dbType);

        if (typ == DatabaseType.ORACLE) {
            genCreateUserForOracle(baseInfo, orderId, scriptName);
        } else if (typ == DatabaseType.MYSQL) {
            genCreateUserForMysql(baseInfo, orderId, scriptName);
        }


        super.generateOrderSqlParse(resourceContents, baseInfo, authGrant, authRevoke, orderId, scriptName, dbType);
    }

    private void genCreateUserForMysql(BaseInfo baseInfo, Integer orderId, String scriptName) {
        StringBuilder createSql = new StringBuilder(String.format("CREATE USER '%s'@'%s' IDENTIFIED BY '%s'", baseInfo.getUsername(), baseInfo.getHost(), baseInfo.getPassword()));

        StringBuilder withClause = new StringBuilder();
        if (baseInfo.getMax_queries_per_hour() != null && baseInfo.getMax_queries_per_hour() > 0) {
            withClause.append(String.format(" MAX_QUERIES_PER_HOUR %d", baseInfo.getMax_queries_per_hour()));
        }
        if (baseInfo.getMax_updates_per_hour() != null && baseInfo.getMax_updates_per_hour() > 0) {
            withClause.append(String.format(" MAX_UPDATES_PER_HOUR %d", baseInfo.getMax_updates_per_hour()));
        }
        if (baseInfo.getMax_connection_per_hour() != null && baseInfo.getMax_connection_per_hour() > 0) {
            withClause.append(String.format(" MAX_CONNECTIONS_PER_HOUR %d", baseInfo.getMax_connection_per_hour()));
        }
        if (baseInfo.getMax_user_connections() != null && baseInfo.getMax_user_connections() > 0) {
            withClause.append(String.format(" MAX_USER_CONNECTIONS %d", baseInfo.getMax_user_connections()));
        }
        if (withClause.length() > 0) {
            createSql.append(" WITH").append(withClause);
        }

        OrderSqlParse createOrderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, createSql.toString(), lineNumber++, null);
        orderSqlParses.add(createOrderSqlParse);
    }

    private void genCreateUserForOracle(BaseInfo baseInfo, Integer orderId, String scriptName) {
        String username = baseInfo.getUsername();
        String password = baseInfo.getPassword();

        StringBuilder createSql = new StringBuilder(String.format("CREATE USER %s IDENTIFIED BY \"%s\"", username, password));
        if (baseInfo.isExternally()) {
            createSql = new StringBuilder(String.format("CREATE USER %s IDENTIFIED EXTERNALLY", username));
        }

        String defaultTableSpace = baseInfo.getDefault_tablespace();
        if (StringUtils.isNotBlank(defaultTableSpace)) {
            createSql.append(String.format(" DEFAULT TABLESPACE %s", defaultTableSpace));
        }

        String temporaryTablespace = baseInfo.getTemporary_tablespace();
        if (StringUtils.isNotBlank(temporaryTablespace)) {
            createSql.append(String.format(" TEMPORARY TABLESPACE %s", temporaryTablespace));
        }

        if (StringUtils.isNotBlank(baseInfo.getProfile())) {
            if (Character.isDigit(baseInfo.getProfile().charAt(0))) {
                createSql.append(String.format(" PROFILE \"%s\"", baseInfo.getProfile()));
            } else {
                createSql.append(String.format(" PROFILE %s", baseInfo.getProfile()));
            }
        }

        if (baseInfo.isAccount_status()) {
            createSql.append(" ACCOUNT LOCK");
        }

        OrderSqlParse createOrderSqlParse = OrderSqlParseUtils.makeExecuteWait(orderId, scriptName, createSql.toString(), lineNumber++, null);
        orderSqlParses.add(createOrderSqlParse);
    }
}
