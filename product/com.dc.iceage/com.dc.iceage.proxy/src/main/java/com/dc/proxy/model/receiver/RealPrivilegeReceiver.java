package com.dc.proxy.model.receiver;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class RealPrivilegeReceiver {

    private String privilege;

    private String grantee;

    private String grantor;

    private String object_type;

    private String table_name;

    private String table_schema;

    private String granted_role;

    private String default_role;

    private Integer blocks;

    private Integer bytes;

    private Integer max_blocks;

    private Long max_bytes;

    private String tablespace_name;

    private String username;

    private String dropped;

    private String hierarchy;

    private String admin_option;

    private String grantable;

    private String host;

    public String getSchemaObject() {
        if (StringUtils.isBlank(table_schema)) {
            return "\"" + table_name + "\"";
        }
        return "\"" + table_schema + "\"" + "." + "\"" + table_name + "\"";
    }

    public String getGrantee() {
        return "\"" + grantee + "\"";
    }

    public String getGranteeForMysql() {
        return "'" + grantee + "'@'" + host + "'";
    }

    public String getTable_schemaForMysql() {
        return "`" + table_schema + "`";
    }

    public String getGranted_role() {
        return "\"" + granted_role + "\"";
    }

    public String getUsername() {
        return "\"" + username + "\"";
    }
}
