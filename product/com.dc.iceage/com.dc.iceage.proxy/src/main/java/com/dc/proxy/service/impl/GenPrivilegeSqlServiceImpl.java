package com.dc.proxy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.iceage.model.jdbc.ExecutionContainer;
import com.dc.proxy.component.ProxyMapper;
import com.dc.proxy.model.data.message.PrivilegeModelMessage;
import com.dc.proxy.model.data.message.RealPrivilegeMessage;
import com.dc.proxy.model.receiver.RealPrivilegeReceiver;
import com.dc.proxy.service.DatabaseService;
import com.dc.proxy.service.GenPrivilegeSqlService;
import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.*;
import com.dc.repository.mysql.type.GrantObjectType;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.ObjectTypeMessage;
import com.dc.springboot.core.model.database.ResourceObject;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GenPrivilegeSqlServiceImpl implements GenPrivilegeSqlService {

    @Resource
    private DcAccountObjPrivsMapper dcAccountObjPrivsMapper;

    @Resource
    private DcAccountRolePrivsMapper dcAccountRolePrivsMapper;

    @Resource
    private DcAccountSysPrivsMapper dcAccountSysPrivsMapper;

    @Resource
    private DcAccountTsQuotasMapper dcAccountTsQuotasMapper;

    @Resource
    private DcAccountOtherPrivsMapper dcAccountOtherPrivsMapper;

    @Resource
    private DcDbResourceAccountMapper dcDbResourceAccountMapper;

    @Resource
    private ProxyMapper proxyMapper;

    @Resource
    private DatabaseService databaseService;

    @Override
    public List<String> genSqlForReal(RealPrivilegeMessage message) {
        List<String> sqlList = new ArrayList<>();
        if (message.getDb_type() == 1) {
            genRealSqlForOracle(message, sqlList);
        } else if (message.getDb_type() == 2) {
            genRealSqlForMysql(message, sqlList);
        }
        return sqlList;
    }

    private void genRealSqlForMysql(RealPrivilegeMessage message, List<String> sqlList) {
        if (message.getType() == 5 && message.getAuth_data() != null) {
            for (RealPrivilegeReceiver objectAuth : message.getAuth_data()) {
                //GRANT SELECT ON "schema"."table" TO "user" WITH GRANT OPTION;
                StringBuilder sqlBuilder = new StringBuilder("GRANT ")
                        .append(objectAuth.getPrivilege())
                        .append(" ON ");

                sqlBuilder.append(objectAuth.getTable_schemaForMysql()).append(".*")
                        .append(" TO ")
                        .append(objectAuth.getGranteeForMysql());

                insertNoSorted(sqlList, sqlBuilder.toString());
            }
        } else if (message.getType() == 3 && message.getAuth_data() != null) {
            for (RealPrivilegeReceiver sysAuth : message.getAuth_data()) {
                String sql = String.format("GRANT %s ON *.* TO %s", sysAuth.getPrivilege(), sysAuth.getGranteeForMysql());
                insertNoSorted(sqlList, sql);
            }
        }
    }

    private void genRealSqlForOracle(RealPrivilegeMessage message, List<String> sqlList) {
        if (message.getType() == 1 && message.getAuth_data() != null) {     //对象权限
            for (RealPrivilegeReceiver objectAuth : message.getAuth_data()) {
                //GRANT SELECT ON "schema"."table" TO "user" WITH GRANT OPTION;
                StringBuilder sqlBuilder = new StringBuilder("GRANT ")
                        .append(objectAuth.getPrivilege())
                        .append(" ON ");

                if (objectAuth.getObject_type().equals("DIRECTORY")) {
                    sqlBuilder.append("DIRECTORY ");
                }

                sqlBuilder.append(objectAuth.getSchemaObject())
                        .append(" TO ")
                        .append(objectAuth.getGrantee());

                if (!objectAuth.getGrantable().equals("NO")) {
                    sqlBuilder.append(" WITH GRANT OPTION");
                }
                insertNoSorted(sqlList, sqlBuilder.toString());
            }

        } else if (message.getType() == 2 && message.getAuth_data() != null) {      //角色权限
            String username = "";
            for (RealPrivilegeReceiver roleAuth : message.getAuth_data()) {
                username = roleAuth.getGrantee();
                //GRANT "role" TO "user" WITH ADMIN OPTION;
                StringBuilder sqlBuilder = new StringBuilder("GRANT ")
                        .append(roleAuth.getGranted_role())
                        .append(" TO ")
                        .append(roleAuth.getGrantee());
                if (!roleAuth.getAdmin_option().equals("NO")) {
                    sqlBuilder.append(" WITH ADMIN OPTION");
                }
                insertNoSorted(sqlList, sqlBuilder.toString());
            }


        } else if (message.getType() == 3 && message.getAuth_data() != null) {      //系统权限
            for (RealPrivilegeReceiver sysAuth : message.getAuth_data()) {
                String sql = String.format("GRANT %s TO %s", sysAuth.getPrivilege(), sysAuth.getGrantee());
                if (!sysAuth.getAdmin_option().equals("NO")) {
                    sql += " WITH ADMIN OPTION";
                }
                insertNoSorted(sqlList, sql);
            }

        } else if (message.getType() == 4 && message.getAuth_data() != null) {      //限额
            for (RealPrivilegeReceiver tsQuota : message.getAuth_data()) {
                String maxBytes = tsQuota.getMax_bytes().toString();
                if (maxBytes.equals("-1")) {
                    maxBytes = "UNLIMITED";
                }
                String sql = String.format("ALTER USER %s QUOTA %s ON \"%s\"", tsQuota.getUsername(), maxBytes, tsQuota.getTablespace_name());
                insertNoSorted(sqlList, sql);
            }
        }
    }

    @Override
    public List<String> genSqlForModel(PrivilegeModelMessage message) {
        String accountId = message.getId();
        DcDbResourceAccount account = dcDbResourceAccountMapper.selectAccountWithResourceByUniqueKey(accountId);
        if (message.getDb_type() == 1) {
            return genModelSqlForOracle(message, account);
        } else if (message.getDb_type() == 2) {
            return genModelSqlForMysql(message, account);
        }
        return List.of();
    }

    private List<String> genModelSqlForMysql(PrivilegeModelMessage message, DcDbResourceAccount account) {
        List<String> sqlList = new ArrayList<>();

        if (message.getType() == 5 && account != null) {
            Set<String> sqlSet = new LinkedHashSet<>();

            LambdaQueryWrapper<DcAccountObjPrivs> qw = Wrappers.<DcAccountObjPrivs>lambdaQuery()
                    .eq(DcAccountObjPrivs::getAccountId, account.getId())
                    .eq(DcAccountObjPrivs::getObjectType, "SCHEMA")
                    .orderByAsc(DcAccountObjPrivs::getSchemaName, DcAccountObjPrivs::getObjectName, DcAccountObjPrivs::getPrivilege);
            List<DcAccountObjPrivs> objPrivs = dcAccountObjPrivsMapper.getPrivileges(qw);

            for (DcAccountObjPrivs priv : objPrivs) {
                String format = "GRANT %s ON %s* TO '%s'";
                String schema = priv.getSchemaName();
                if (schema == null) {
                    schema = "";
                } else if (!schema.isBlank()) {
                    schema = "`" + schema + "`" + ".";
                }

                String sql = String.format(format,
                        priv.getPrivilege(),
                        schema,
                        priv.getGrantee());

                if (!StringUtils.isBlank(account.getHost())) {
                    sql += "@'" + account.getHost() + "'";
                }
                sqlSet.add(sql);
            }

            return new ArrayList<>(sqlSet);
        } else if (message.getType() == 3 && account != null) {
            LambdaQueryWrapper<DcAccountSysPrivs> qw = Wrappers.<DcAccountSysPrivs>lambdaQuery()
                    .eq(DcAccountSysPrivs::getAccountId, account.getId())
                    .orderByAsc(DcAccountSysPrivs::getPrivilege);
            List<DcAccountSysPrivs> sysPrivs = dcAccountSysPrivsMapper.getPrivileges(qw);

            for (DcAccountSysPrivs sysAuth : sysPrivs) {
                String sql = String.format("GRANT %s ON *.* TO '%s'", sysAuth.getPrivilege(), sysAuth.getGrantee());
                if (!StringUtils.isBlank(account.getHost())) {
                    sql += "@'" + account.getHost() + "'";
                }
                insertNoSorted(sqlList, sql);
            }
        } else if (message.getType() == 6 && account != null) {
            LambdaQueryWrapper<DcAccountOtherPrivs> qw = Wrappers.<DcAccountOtherPrivs>lambdaQuery()
                    .eq(DcAccountOtherPrivs::getAccountId, account.getId());
            List<DcAccountOtherPrivs> otherPrivs = dcAccountOtherPrivsMapper.getPrivileges(qw);

            for (DcAccountOtherPrivs otherPriv : otherPrivs) {
                if (StringUtils.isBlank(otherPriv.getSql())) {
                    continue;
                }
                insertNoSorted(sqlList, otherPriv.getSql());
            }
        }

        return sqlList;

    }

    private List<String> genModelSqlForOracle(PrivilegeModelMessage message, DcDbResourceAccount account) {
        List<String> sqlList = new ArrayList<>();

        if (message.getType() == 1 && account != null) {    // 对象权限
            Set<String> sqlSet = new LinkedHashSet<>();

            LambdaQueryWrapper<DcAccountObjPrivs> qw = Wrappers.<DcAccountObjPrivs>lambdaQuery()
                    .eq(DcAccountObjPrivs::getAccountId, account.getId())
                    .orderByAsc(DcAccountObjPrivs::getSchemaName, DcAccountObjPrivs::getObjectName, DcAccountObjPrivs::getPrivilege);
            List<DcAccountObjPrivs> objPrivs = dcAccountObjPrivsMapper.getPrivileges(qw);

            //获取 字典类型的对象
            List<ResourceObject> objects = objPrivs.stream().filter(o -> !o.getObjectType().equals(GrantObjectType.SCHEMA.getName()))
                    .map(o -> new ResourceObject(o.getSchemaName(), o.getObjectName())).distinct().collect(Collectors.toList());
            ConnectionConfig connectionConfig = proxyMapper.toDatabaseConnectionDto(account.getResource()).buildConnectionConfig(null, null);

            ObjectTypeMessage objectTypeMessage = new ObjectTypeMessage();
            objectTypeMessage.setObjects(objects);
            objectTypeMessage.setConnectionConfig(connectionConfig);
            List<ResourceObject> directoryObjects = databaseService.getDirectoryObject(objectTypeMessage);

            try (ExecutionContainer executionContainer = new ExecutionContainer(connectionConfig.getConnectionConfiguration())) {
                DBCExecutionContext executionContext = executionContainer.getExecutionContext();
                for (DcAccountObjPrivs objPriv : objPrivs) {
                    if (objPriv.getObjectType().equals(GrantObjectType.SCHEMA.getName())) {
                        if (executionContext instanceof JDBCExecutionContext) {
                            Connection connection = ((JDBCExecutionContext) executionContext).getConnection(new VoidProgressMonitor());
                            DatabaseMetaData dbmd = connection.getMetaData();
                            ResultSet rs = dbmd.getTables(objPriv.getCatalogName(), objPriv.getSchemaName(), "%", new String[]{"TABLE"});

                            while (rs.next()) {
                                String trueTableName = rs.getString("TABLE_NAME");

                                StringBuilder sqlBuilder = new StringBuilder("GRANT ")
                                        .append(objPriv.getPrivilege())
                                        .append(" ON ");

                                sqlBuilder.append("\"").append(objPriv.getSchemaName()).append("\"")
                                        .append(".\"").append(trueTableName).append("\"")
                                        .append(" TO \"").append(objPriv.getGrantee()).append("\"");

                                if (objPriv.getGrantable() == 0b1) {
                                    sqlBuilder.append(" WITH GRANT OPTION");
                                }
                                sqlSet.add(sqlBuilder.toString());
                            }
                        }
                    } else {
                        StringBuilder sqlBuilder = new StringBuilder("GRANT ")
                                .append(objPriv.getPrivilege())
                                .append(" ON ");

                        int cur = 0;
                        while (cur < directoryObjects.size()) {
                            ResourceObject currentObj = directoryObjects.get(cur);
                            if (StringUtils.isNotBlank(currentObj.getOwner())) {
                                if (currentObj.getOwner().equals(objPriv.getSchemaName()) && currentObj.getName().equals(objPriv.getObjectName())) {
                                    break;
                                }
                            } else {
                                if (currentObj.getName().equals(objPriv.getObjectName())) {
                                    break;
                                }
                            }
                            cur++;
                        }

                        if (cur < directoryObjects.size()) {
                            sqlBuilder.append("DIRECTORY ");
                        }

                        sqlBuilder.append(objPriv.getSchemaObject())
                                .append(" TO \"")
                                .append(objPriv.getGrantee())
                                .append("\"");

                        if (objPriv.getGrantable() != null && objPriv.getGrantable() == 0b1) {
                            sqlBuilder.append(" WITH GRANT OPTION");
                        }
                        sqlSet.add(sqlBuilder.toString());
                    }
                }
            } catch (Exception e) {
                log.error("获取实例schema失败", e);
            }


            return new ArrayList<>(sqlSet);

        } else if (message.getType() == 2 && account != null) {     // 角色权限
            LambdaQueryWrapper<DcAccountRolePrivs> qw = Wrappers.<DcAccountRolePrivs>lambdaQuery()
                    .eq(DcAccountRolePrivs::getAccountId, account.getId())
                    .orderByAsc(DcAccountRolePrivs::getGrantedRole);
            List<DcAccountRolePrivs> rolePrivs = dcAccountRolePrivsMapper.getPrivileges(qw);

            for (DcAccountRolePrivs roleAuth : rolePrivs) {
                //GRANT "role" TO "user" WITH ADMIN OPTION;
                StringBuilder sqlBuilder = new StringBuilder("GRANT ")
                        .append("\"").append(roleAuth.getGrantedRole()).append("\"")
                        .append(" TO \"")
                        .append(roleAuth.getUsername())
                        .append("\"");
                if (roleAuth.getAdminOption() != null && roleAuth.getAdminOption() == 0b1) {
                    sqlBuilder.append(" WITH ADMIN OPTION");
                }
                insertNoSorted(sqlList, sqlBuilder.toString());
            }

        } else if (message.getType() == 3 && account != null) {     // 系统权限
            LambdaQueryWrapper<DcAccountSysPrivs> qw = Wrappers.<DcAccountSysPrivs>lambdaQuery()
                    .eq(DcAccountSysPrivs::getAccountId, account.getId())
                    .orderByAsc(DcAccountSysPrivs::getPrivilege);
            List<DcAccountSysPrivs> sysPrivs = dcAccountSysPrivsMapper.getPrivileges(qw);

            for (DcAccountSysPrivs sysAuth : sysPrivs) {
                String sql = String.format("GRANT %s TO \"%s\"", sysAuth.getPrivilege(), sysAuth.getGrantee());
                if (sysAuth.getAdminOption() != null && sysAuth.getAdminOption() == 0b1) {
                    sql += " WITH ADMIN OPTION";
                }
                insertNoSorted(sqlList, sql);
            }

        } else if (message.getType() == 4 && account != null) {     // 限额
            LambdaQueryWrapper<DcAccountTsQuotas> qw = Wrappers.<DcAccountTsQuotas>lambdaQuery()
                    .eq(DcAccountTsQuotas::getAccountId, account.getId())
                    .orderByAsc(DcAccountTsQuotas::getTablespaceName);
            List<DcAccountTsQuotas> tsQuotas = dcAccountTsQuotasMapper.getQuotas(qw);

            for (DcAccountTsQuotas tsQuota : tsQuotas) {
                String maxBytes = tsQuota.getMaxBytes().toString();
                if (maxBytes.equals("-1")) {
                    maxBytes = "UNLIMITED";
                }
                String sql = String.format("ALTER USER \"%s\" QUOTA %s ON \"%s\"", tsQuota.getUsername(), maxBytes, tsQuota.getTablespaceName());
                insertNoSorted(sqlList, sql);
            }

        } else if (message.getType() == 6 && account != null) {     // 其他脚本
            LambdaQueryWrapper<DcAccountOtherPrivs> qw = Wrappers.<DcAccountOtherPrivs>lambdaQuery()
                    .eq(DcAccountOtherPrivs::getAccountId, account.getId());
            List<DcAccountOtherPrivs> otherPrivs = dcAccountOtherPrivsMapper.getPrivileges(qw);

            for (DcAccountOtherPrivs otherPriv : otherPrivs) {
                if (StringUtils.isBlank(otherPriv.getSql())) {
                    continue;
                }
                insertNoSorted(sqlList, otherPriv.getSql());
            }
        }

        return sqlList;
    }

    void insertSorted(List<String> list, String sql) {
        int idx = Collections.binarySearch(list, sql);
        if (idx < 0) {
            idx = -idx - 1;
        }
        list.add(idx, sql);
    }

    void insertNoSorted(List<String> list, String sql) {
        list.add(sql);
    }


}
