spring:
  cloud:
    nacos:
      config:
        server-addr: 192.168.4.43:8848              # 指定 Nacos 配置中心的地址和端口号
        enabled: false                              # 是否启用从 Nacos 获取配置文件 (true 为开启，false 为关闭)
        prefix: dc-summer                           # 配置文件的前缀，一般用于区分不同的应用或模块
        file-extension: yaml                        # 配置文件的扩展名，支持 properties 和 yaml
        group: DEFAULT_GROUP                        # 配置文件所在的 Nacos 组，默认是 DEFAULT_GROUP
        extension-configs:                          # 额外的配置文件列表，可以指定多个
          - dataId: dc-iceage-dev.yaml              # 额外的配置文件的 Data ID
            group: DEFAULT_GROUP                    # 额外的配置文件所在的 Nacos 组
        refresh-enabled: false