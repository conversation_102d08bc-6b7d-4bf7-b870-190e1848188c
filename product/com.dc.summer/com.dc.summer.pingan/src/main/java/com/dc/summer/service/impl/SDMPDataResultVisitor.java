package com.dc.summer.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.repository.mysql.mapper.SensitiveLevelMapper;
import com.dc.repository.mysql.model.SensitiveLevel;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.springboot.core.model.log.SensitiveAuthDetail;
import com.dc.springboot.core.model.parser.dto.PermissionRuleDto;
import com.dc.springboot.core.model.sensitive.MaskRule;
import com.dc.springboot.core.model.type.SensitiveLevelType;
import com.dc.summer.component.PaMapper;
import com.dc.summer.data.transfer.DTUtils;
import com.dc.summer.exec.model.counter.HandlerCounter;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.model.NoSQLDataSource;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.data.DBDDocument;
import com.dc.summer.model.data.SimpleDocument;
import com.dc.summer.model.data.result.*;
import com.dc.summer.model.document.data.DBDataWrapper;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.model.type.WebSQLConstants;
import com.dc.type.DatabaseType;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.pingan.cdsf.driver.bridger.dto.*;
import com.pingan.cdsf.driver.bridger.service.DataDesensitizationService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Scope("prototype")
@Service
public class SDMPDataResultVisitor implements DataResultVisitor {

    @SuppressWarnings("all")
    @Autowired(required = false)
    private DataDesensitizationService dataResultService;
    @Resource
    private SensitiveLevelMapper sensitiveLevelMapper;
    @Resource
    PaDBConvertServiceImpl paDBConvertService;
    @Value("${enable-sdmp}")
    private boolean enableSDMP;
    private boolean notSupportDataBaseType;
    @Value("${pingan.sdmp.sensitive-permission-s1:-1}")
    private Integer defaultPermissionS1;
    @Value("${pingan.sdmp.sensitive-permission-s2:-1}")
    private Integer defaultPermissionS2;
    @Value("${pingan.sdmp.sensitive-permission-s3:-1}")
    private Integer defaultPermissionS3;
    @Value("${pingan.sdmp.sensitive-permission-s4:-1}")
    private Integer defaultPermissionS4;
    @Value("${pingan.sdmp.sensitive-permission-s5:-1}")
    private Integer defaultPermissionS5;
    @Value("${pingan.sdmp.sensitive-permission-unknow:-1}")
    private Integer defaultPermissionUnknow;
    @Value("${pingan.sdmp.sensitive-user-mark: }")
    private String exUserMark = "";

    private boolean isDesens;
    private String pre = "";

    private AuthenticationSdmpDatabaseDto authenticationConnectionDto = new AuthenticationSdmpDatabaseDto();

    private DataResultColumnDto[][] dataResultColumnDtos;

    private boolean endSensitive = false;

    @Resource
    private PaMapper dtoMapper;
    // 脱敏需要的用户名称,对应 平安的um 账号
    private final SdmpUserDesensDto userDesensDto = new SdmpUserDesensDto();

    private DBDAttributeBinding[] bindings;

    private boolean jsonFormat;
    //     @ApiModelProperty(value = "授权类型 1:敏感字段，2:敏感等级")
    private int grantType = 1;
    private boolean isExport;
    private boolean isAsyncExecute;
    private DesensitizationClassifyDto desensitizationClassifyDto = new DesensitizationClassifyDto();

    private String columnPermissionZH = "";
    private Map<String, List<PermissionRuleDto>> maskPermissionRule = new HashMap<>();
    private Map<String, Integer> enableDesensitizationCopy = new HashMap<>();
    private boolean desensSwitch = false;
    private RuleExportDesensitizationType ruleExportDesensitizationType;

    @Override
    public void visit(DataResultNodeInfo dataResultNodeInfo) {
        DBPConnectionConfiguration connectionConfiguration = dataResultNodeInfo.getConnectionConfiguration();
        isDesens = dataResultNodeInfo.isDesens();
        DatabaseType databaseType = connectionConfiguration.getDatabaseType();
        if (databaseType.getValue().equals(DatabaseType.ELASTIC_SEARCH.getValue())) {
            notSupportDataBaseType = true;
        }
        log.info("enableSDMP " + String.valueOf(enableSDMP) + "  isDesens " + String.valueOf(isDesens));
        if (!enableSDMP || !isDesens || notSupportDataBaseType) {
            return;
        }
        isExport = dataResultNodeInfo.isExport();
        isAsyncExecute = dataResultNodeInfo.isAsyncExecute();
        log.info("sdmp visit 脱敏 export     " + String.valueOf(isExport) + "  isAsyncExecute  " + String.valueOf(isAsyncExecute));

        jsonFormat = List.of(DatabaseType.MONGODB, DatabaseType.REDIS, DatabaseType.ELASTIC_SEARCH).contains(databaseType);

        // 确保脱敏拿到当前操作的登录用户
        String operator = HandlerCounter.getOperator();
        if (dataResultNodeInfo.getMaskAuthObject() != null) {
            maskPermissionRule = (Map<String, List<PermissionRuleDto>>) dataResultNodeInfo.getMaskAuthObject();
        }
        enableDesensitizationCopy = dataResultNodeInfo.getEnableDesensitizationCopy();
        userDesensDto.setUsername(operator.split("\\(")[0]);
        resetPermissionEXuser(desensitizationClassifyDto);

        userDesensDto.setDesensSwitch(dataResultNodeInfo.isBackstop());
        this.desensSwitch = dataResultNodeInfo.isBackstop();
        userDesensDto.setViewOption(dataResultNodeInfo.getConsoleType().getCode().toString());
        this.ruleExportDesensitizationType = dataResultNodeInfo.getRuleExportDesensitizationType();
        if (this.ruleExportDesensitizationType == null) {
            log.info("ruleExportDesensitizationType is null");
            this.ruleExportDesensitizationType = RuleExportDesensitizationType.DESENSITIZATION_EXPORT;
        }
        log.info("ruleExportDesensitizationType is " + ruleExportDesensitizationType);
        Map<Integer, Integer> levelMap = dataResultNodeInfo.getSensitiveLevelToAuthLevel();
        boolean level = dataResultNodeInfo.isLevel();
        Boolean defaultPlainAuth = dataResultNodeInfo.getHasDefaultPlainAuth();
        if (!level) {
            if (defaultPlainAuth == null) {
                defaultPlainAuth = false;
                pre = "(默认)";
            }
            if (isExport) {
                if (defaultPlainAuth && ruleExportDesensitizationType.equals(RuleExportDesensitizationType.DESENSITIZATION_EXPORT)) {
                    defaultPlainAuth = false;
                }
            }
            userDesensDto.setPlain(defaultPlainAuth);

        } else {
            if (levelMap != null) {
                levelMap.forEach((sensitiveLevel, authLevel1) -> {
                    SensitiveLevelType sensitiveLevelType = SensitiveLevelType.of(sensitiveLevel);
                    Integer authLevel = convertPaLevel(authLevel1);
                    if (authLevel != null) {

                        switch (sensitiveLevelType) {
                            case S1:
                                desensitizationClassifyDto.setS1(authLevel);
                                break;
                            case S2:
                                desensitizationClassifyDto.setS2(authLevel);
                                break;
                            case S3:
                                desensitizationClassifyDto.setS3(authLevel);
                                break;
                            case S4:
                                desensitizationClassifyDto.setS4(authLevel);
                                break;
                            case S5:
                                desensitizationClassifyDto.setS5(authLevel);
                                break;
                            case UNKNOWN:
                                desensitizationClassifyDto.setUnknow(authLevel);
                                break;
                            default:
                        }
                    }
                });
            } else {
                pre = "(默认)";
                // 默认等级可配置
//                resetPermissionEXuser(desensitizationClassifyDto);
            }
//            removeNull(desensitizationClassifyDto);
            // 导出, 不允许导出明文 //
            resetPermissionExport(desensitizationClassifyDto);

            grantType = 2;
            userDesensDto.setDesensitizationClassifyDto(desensitizationClassifyDto);
        }
        if (grantType == 2) {
            log.info("sdmp visit 脱敏模式 分级分类" + String.valueOf(grantType));
        } else {
            log.info("sdmp visit 脱敏模式 一般模式" + String.valueOf(grantType));
        }

        authenticationConnectionDto = dtoMapper.toAuthenticationConnectionDto(dataResultNodeInfo.getConnectionConfiguration());

        // 确保脱敏拿到mysql 数据库名称
        if (connectionConfiguration instanceof ConnectionConfiguration) {
            if (StringUtils.isBlank(authenticationConnectionDto.getDatabaseName())) {
                authenticationConnectionDto.setDatabaseName(((ConnectionConfiguration) connectionConfiguration).getSchemaName());
            }
            if (StringUtils.isBlank(authenticationConnectionDto.getDatabaseName())) {
                authenticationConnectionDto.setDatabaseName(((ConnectionConfiguration) connectionConfiguration).getCatalogName());
            }
        }


        if (databaseType.equals(DatabaseType.ORACLE)) {
            if (connectionConfiguration instanceof ConnectionConfiguration) {
                String dbName = ((ConnectionConfiguration) connectionConfiguration).getDbName();//脱敏从数据源里取
                log.info("sdmp receive query database name " + dbName);

                if (StringUtils.isNotBlank(dataResultNodeInfo.getDbName())) {
                    // 2025-5-15 添加导出的oracle , dbNmame 从env 里取的值的值 , 覆盖从数据源里取的值
                    dbName = dataResultNodeInfo.getDbName();
                    log.info("sdmp receive query database export name  " + String.valueOf(dbName));
                }
                if (StringUtils.isBlank(dbName)) {
                    log.error("dbName is null connectionId is " + ((ConnectionConfiguration) connectionConfiguration).getConnectionId());
                    log.error("dbName is null connection  is " + JSON.toJSONString(authenticationConnectionDto));
                    throw new RuntimeException("oracle dbName is null from sdmp visit ");
                }
                authenticationConnectionDto.setDatabaseName(dbName);
            }
        }
        log.info("sdmp visit 脱敏  dbName " + authenticationConnectionDto.getDatabaseName());
    }


    @Override
    public void visit(DataResultNodeCol dataResultNodeCol) {

        if (!enableSDMP || !isDesens || notSupportDataBaseType) {
            return;
        }

        bindings = dataResultNodeCol.getBindings();

        if (ArrayUtils.isEmpty(bindings)) {
            return;
        }

        List<SqlFieldData> sqlFieldDataList = dataResultNodeCol.getSqlFieldDataList();

        if (CollectionUtils.isEmpty(sqlFieldDataList)) {

            dataResultColumnDtos = new DataResultColumnDto[0][0];
            log.info("dataResultColumnDto is null ");
        } else if (bindings.length == sqlFieldDataList.size()) {

            dataResultColumnDtos = sqlFieldDataList.stream()
                    .map(sqlFieldData -> new DataResultColumnDto[]{dtoMapper.toDataResultColumnDto(sqlFieldData)})
                    .toArray(DataResultColumnDto[][]::new);

        } else {

            Map<String, List<SqlFieldData>> groupings = sqlFieldDataList
                    .stream()
                    .filter(sqlFieldData -> StringUtils.isNotBlank(sqlFieldData.getFieldAlias()))
                    .collect(Collectors.groupingBy(SqlFieldData::getFieldAlias));

            Map<String, List<SqlFieldData>> groupingsFieldName = sqlFieldDataList
                    .stream()
                    .filter(sqlFieldData -> StringUtils.isNotBlank(sqlFieldData.getFieldName()))
                    .collect(Collectors.groupingBy(SqlFieldData::getFieldName));

            Map<String, List<SqlFieldData>> groupingsFieldNameUpper = new HashMap<>();
            for (String s : groupingsFieldName.keySet()) {
                groupingsFieldNameUpper.put(s.toUpperCase(), groupingsFieldNameUpper.get(s));
            }
            dataResultColumnDtos = Arrays.stream(bindings)
                    .map(binding -> {
                        List<SqlFieldData> data = groupings.get(binding.getMetaAttribute().getLabel());
                        List<SqlFieldData> dataFieldName = groupingsFieldName.get(binding.getMetaAttribute().getLabel());
                        ArrayList<SqlFieldData> end = new ArrayList<>();
                        if (data != null) {
                            end.addAll(data);
                        }
                        if (dataFieldName != null) {
                            for (SqlFieldData sqlFieldData : dataFieldName) {
                                if (!end.contains(sqlFieldData)) {
                                    end.add(sqlFieldData);
                                }
                            }
                        }
                        if (CollectionUtils.isEmpty(end)) {
                            List<SqlFieldData> upperColumnName = groupingsFieldNameUpper.get(String.valueOf(binding.getMetaAttribute().getLabel()).toUpperCase());
                            if (upperColumnName != null) {
                                end.addAll(upperColumnName);
                            }
                        }
                        if (CollectionUtils.isEmpty(end)) {
                            return null;
                        }

                        return end.stream().map(sqlFieldData -> dtoMapper.toDataResultColumnDto(sqlFieldData))
                                .toArray(DataResultColumnDto[]::new);
                    })
                    .toArray(DataResultColumnDto[][]::new);
        }

        try {
            openFunctionSensitive();

            dataResultService.initSdk(authenticationConnectionDto, dataResultColumnDtos, userDesensDto);
        } catch (Exception e) {
            throw new ServiceException("访问脱敏列异常 dataResultNodeCol :", e);
        }

    }

    private void openFunctionSensitive() {
        boolean isFunc = false;
        try {
            if (dataResultColumnDtos != null) {
                for (DataResultColumnDto[] columnDto : dataResultColumnDtos) {
                    if (columnDto == null) {
                        log.info("sdmp dbInfo 表结构有空值");
                        log.info(" sdmp dbInfo 表结构 " + JSON.toJSONString(dataResultColumnDtos));
                        continue;
                    }

                    for (DataResultColumnDto dto : columnDto) {
                        if (dto == null) {
                            log.info("sdmp dbInfo 表结构有空值");
                            log.info(" sdmp dbInfo 表结构 " + JSON.toJSONString(dataResultColumnDtos));
                        }
                        if (dto != null && dto.isInFunction()) {
                            isFunc = true;
                            log.info("sdmp dbInfo   has func " + String.valueOf(dto.getColumnAlias()) + "  " + String.valueOf(dto.getColumnName()));
                        }
                    }
                }
            }
        } catch (Exception e) {
            isFunc = true;
            log.error(e.getMessage());
        }
        if (!isFunc) {
            return; // 非函数不设置
        }
        try { // 由于平安现场代码够不着修改, 函数脱敏 ,需要强制设置为 true 才能生效
            Class<? extends DataDesensitizationService> aClass = dataResultService.getClass();
            Field isFunction = aClass.getDeclaredField("isFunction");
            isFunction.setAccessible(true);
            isFunction.set(dataResultService, true);

        } catch (Exception e) {
            log.error("set function error" + e.getMessage());
        }
    }

    @Override
    public void visit(DataResultNodeRow dataResultNodeRow) {

        if (!enableSDMP || !isDesens || notSupportDataBaseType) {
            return;
        }

        if (ArrayUtils.isEmpty(bindings)) {
            return;
        }

        Object[] row = dataResultNodeRow.getRow();

        if (ArrayUtils.isEmpty(row)) {
            return;
        }

        RowResultDto dto_log = null;
        try {

            List<Object[]> list = new ArrayList<>();

            if (jsonFormat && !isExport) {
                // NoSQL SQL窗口
                DBDataWrapper dataWrapper = ((NoSQLDataSource<?>) bindings[0].getDataSource()).getDataWrapper();
                String json = dataWrapper.documentBuilder().toJson(row);
                list.add(new Object[]{json});
                List<RowResultDto[]> resultList = dataResultService.fetchDesensitizeRow(list);

                RowResultDto[] resultDtos = resultList.get(0);

                RowResultDto dto = resultDtos[0];
                convertRuleName(dto);

                row = dataWrapper.documentBuilder().fromJson(String.valueOf(dto.getData()), Object[].class);

                boolean isSensitive = dto.getSensType() != null || dto.getDesensitized();
                boolean isDesensitized = dto.getDesensitized();

                if (isSensitive || isDesensitized) {
                    for (DBDAttributeBinding binding : bindings) {
                        binding.setSensitive(dto.getSensType() != null || dto.getDesensitized());
                        binding.setDesensitized(dto.getDesensitized());
                        setSensiInfo(dto, binding, null);
                    }
                }


            } else if (jsonFormat && DTUtils.isJsonDocumentResults(bindings, row)) {
                // NoSQL 导出 SQL、TXT
                DBDDocument document = (DBDDocument) row[0];
                ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                document.serializeDocument(new VoidProgressMonitor(), buffer, StandardCharsets.UTF_8, DBDDocument.DBDDocumentType.BSON, false);
                String json = buffer.toString(StandardCharsets.UTF_8);
                list.add(new Object[]{json});
                List<RowResultDto[]> resultList = dataResultService.fetchDesensitizeRow(list);

                RowResultDto[] resultDtos = resultList.get(0);

                RowResultDto dto = resultDtos[0];
                convertRuleName(dto);

                row = new Object[]{new SimpleDocument(dto.getData())};

                boolean isSensitive = dto.getSensType() != null || dto.getDesensitized();
                boolean isDesensitized = dto.getDesensitized();

                if (isSensitive || isDesensitized) {
                    for (DBDAttributeBinding binding : bindings) {
                        binding.setSensitive(dto.getSensType() != null || dto.getDesensitized());
                        binding.setDesensitized(dto.getDesensitized());
                        setSensiInfo(dto, binding, null);
                    }
                }

            } else {
                // SQL
                list.add(row);
                Object[] copyRow = Arrays.copyOf(row, row.length);

                Map<Integer, String> bigDataIndexList = getBigDataIndexList(row);
                // pre -------------, 大字段类型, 不脱敏, 强制覆盖为 null
                for (Map.Entry<Integer, String> entry : bigDataIndexList.entrySet()) {
                    if (entry.getValue().equalsIgnoreCase("null")) {
                        row[entry.getKey()] = null;
                    } else {
                        String paramMap = new Gson().toJson(String.valueOf(row[entry.getKey()]));
                        row[entry.getKey()] = paramMap;
                    }
                }
                List<RowResultDto[]> resultList = dataResultService.fetchDesensitizeRow(list);
                RowResultDto[] resultDtos = resultList.get(0);
                if (resultDtos.length != row.length) {
                    log.info("sdmp visit 脱敏返回长度不合格" + row.length + "  return " + resultDtos.length + " 原:" + JSON.toJSONString(row) + "  返回:" + JSON.toJSONString(resultDtos));
                }
                for (int i = 0; i < resultDtos.length; i++) {
                    RowResultDto dto = resultDtos[i];
                    dto_log = resultDtos[i];
                    convertRuleName(dto);
                    row[i] = dto.getData();
                    DBDAttributeBinding binding = bindings[i];
                    if (!binding.isSensitive()) {
                        if (!String.valueOf(dto.getLevel()).equalsIgnoreCase("S1")) { // s1 不涉敏了, 不需要设置
                            binding.setSensitive(dto.getSensType() != null || dto.getDesensitized());
                        }
                    }
                    if (!binding.isDesensitized()) {
                        if (!String.valueOf(dto.getLevel()).equalsIgnoreCase("S1")) { // s1 不涉敏了, 不需要设置
                            binding.setDesensitized(dto.getDesensitized());
                        }
                    }
                    if (dataResultColumnDtos != null && dataResultColumnDtos.length > i) {
                        setSensiInfo(dto, binding, dataResultColumnDtos[i]);
                    }
                }
                // 大字段类型, 不脱敏,对其覆盖回原来的数据
                for (Map.Entry<Integer, String> entry : bigDataIndexList.entrySet()) {
                    if (entry.getValue().equalsIgnoreCase("null")) {
                        row[entry.getKey()] = copyRow[entry.getKey()];
                    } else {
                        Map paramMap = new Gson().fromJson(String.valueOf(row[entry.getKey()]), new TypeToken<Map<String, Object>>() {
                        }.getType());
                        row[entry.getKey()] = paramMap;
                    }
                }

            }
            dataResultNodeRow.setRow(row);
        } catch (Exception e) {
            log.error("访问脱敏行异常");
            if (dto_log != null) {
                log.error("dto_log" + JSON.toJSONString(dto_log));
            }

            StackTraceElement[] stackTrace = e.getStackTrace();
            log.error(e.getMessage());
            for (StackTraceElement element : stackTrace) {
                log.error(element.toString());
            }
            dataResultNodeRow.setRow(new Object[row.length]);
            throw new ServiceException("访问脱敏行异常:", e);
        }
    }

    @Override
    public boolean getSensitive() {
        return endSensitive;
    }

    private void convertRuleName(RowResultDto dto) {
        if (dto == null) {
            return;
        }
        boolean sensitive = (dto.getSensType() != null || dto.getDesensitized());
        boolean desensitized = (dto.getDesensitized());
        if ((sensitive || desensitized) && StringUtil.isBlank(dto.getSensType())) {
            dto.setSensType("默认脱敏规则");
            dto.setSensCode("默认脱敏规则");
//            log.info(" sdmp visit 脱敏或涉敏, 但是未返回涉敏规则 row , set 默认脱敏规则 " + String.valueOf(sensitive) + String.valueOf(desensitized) + JSON.toJSONString(dto.getLabelNames()));
        }

        if (!(dto.getDesensitized() && StringUtils.isBlank(dto.getSensCode()))) {
            return;
        }
        if (String.valueOf(dto.getData()).equals("######")) {
            dto.setSensCode("全遮");
            dto.setSensType("全遮");
        }
    }


    private void setSensiInfo(RowResultDto dto, DBDAttributeBinding binding, DataResultColumnDto[] dataResultColumnDto) {
//        log.info(" sdmp visit 脱敏 sdmp combine column " + String.valueOf(setOtherDescription(dataResultColumnDtos)));
//        log.info("check consumer setSensiInfo start " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date()));

//        if (this.isExport && !this.isAsyncExecute) {
//            return;// 导出不审计敏感
//        }
        if (binding != null && binding.isSensitive()){
            endSensitive = true;
        }
        // 设置字段的敏感等级
        setSensitiveLevel(dto, binding);
        setCopyEnable(binding);

        if (StringUtil.isBlank(dto.getSensType())) {
            return;// 不涉敏
        }

        if (!this.desensSwitch) {
            return;// 脱敏开关关掉不审计敏感
        }

        if (String.valueOf(dto.getLevel()).equalsIgnoreCase("S1")) {
            return;// s1字段不审计敏感日志, s1 在平安已经不涉敏了
        }

        if (dataResultColumnDto != null && dataResultColumnDto.length > 0) {
            String columnName = dataResultColumnDto[0].getColumnName();
            if (authenticationConnectionDto.getDatabaseType().equals(DatabaseType.ORACLE.getValue()) && String.valueOf(columnName).equalsIgnoreCase("rowid")) {
                return;// oracle 的 rowid 不计审计
            }
        }
//        log.info("check consumer setSensiInfo ing " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date()));

        try {
            setSensiInfo2(dto, binding, dataResultColumnDto);
        } catch (Exception e) {
            StackTraceElement[] stackTrace = e.getStackTrace();
            log.error(e.getMessage());
            for (StackTraceElement element : stackTrace) {
                log.error(element.toString());
            }
        }
//        log.info("check consumer setSensiInfo end " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date()));

    }

    private void setSensiInfo2(RowResultDto dto, DBDAttributeBinding binding, DataResultColumnDto[] dataResultColumnDto) {
        if (userDesensDto.getPlain() != null && userDesensDto.getPlain()) {
            columnPermissionZH = "明文展示";
        } else {
            columnPermissionZH = "脱敏文";
        }
        if (dto.getLevel() != null) {
            switch (dto.getLevel().toUpperCase()) {
                case "S1":
                    columnPermissionZH = (convertPaLevelZH(desensitizationClassifyDto.getS1()));
                case "S2":
                    columnPermissionZH = (convertPaLevelZH(desensitizationClassifyDto.getS2()));
                case "S3":
                    columnPermissionZH = (convertPaLevelZH(desensitizationClassifyDto.getS3()));
                case "S4":
                    columnPermissionZH = (convertPaLevelZH(desensitizationClassifyDto.getS4()));
                case "S5":
                    columnPermissionZH = (convertPaLevelZH(desensitizationClassifyDto.getS5()));
//                case "UNKNOWN":
//                    columnPermissionZH = (convertPaLevelZH(desensitizationClassifyDto.getUnknow()));
//                default:
//                    columnPermissionZH = (convertPaLevelZH(desensitizationClassifyDto.getUnknow()));
            }
        }
        if (dto.getLevel() == null && grantType == 2) {
            columnPermissionZH = (convertPaLevelZH(desensitizationClassifyDto.getUnknow()));
        }
        String allPermission = columnPermissionZH;
        if (grantType == 2) {
            allPermission = "S1:" + convertPaLevelZH(desensitizationClassifyDto.getS1()) +
                    ";S2:" + convertPaLevelZH(desensitizationClassifyDto.getS2()) +
                    ";S3:" + convertPaLevelZH(desensitizationClassifyDto.getS3()) +
                    ";S4:" + convertPaLevelZH(desensitizationClassifyDto.getS4());
//                    ";S5:" + convertPaLevelZH(desensitizationClassifyDto.getS5());
        }
        SensitiveLevel color = getColor(binding.getAuthLevel());

        SensitiveAuthDetail paSensiModel = new SensitiveAuthDetail();

        paSensiModel.setDesensitized(dto.getDesensitized());
        paSensiModel.setDesensBaseLine(dto.getDesensBaseLine());
        paSensiModel.setEncrypt(dto.getEncrypt());

        binding.setPaSensiModel(paSensiModel);
        paSensiModel.setAuthSensitive(dto.getLevel());

        paSensiModel.setDesensitizeRuleId(dto.getSensCode());
        paSensiModel.setDesensitizeRuleName(dto.getSensType());
        paSensiModel.setSensitiveLevelName(dto.getLevel());
        paSensiModel.setSensitiveLevelId(dto.getLevel());
        if (color != null) {
            paSensiModel.setSensitiveLevelId(color.getUniqueKey());
            paSensiModel.setSensitiveLevelName(color.getSensitiveLevelName());
            paSensiModel.setSensitiveLevelColor(color.getColor());
        }
        paSensiModel.setDistinguishRuleId(dto.getSensCode());
        paSensiModel.setDistinguishRuleName(dto.getSensType());

        paSensiModel.setInstanceName(authenticationConnectionDto.getInstanceName());
        paSensiModel.setIp(authenticationConnectionDto.getHostName());
        // 表结构
        if (dataResultColumnDto != null && dataResultColumnDto.length > 0) {
            for (DataResultColumnDto columnDto : dataResultColumnDto) {
                String schemaName = columnDto.getSchemaName();
                String tableName = columnDto.getTableName();
                if (Arrays.asList(DatabaseType.PG_SQL.getValue(), DatabaseType.RASE_SQL.getValue()).contains(authenticationConnectionDto.getDatabaseType())) {
                    schemaName = authenticationConnectionDto.getDatabaseName() + "." + schemaName;
                }
                if (Objects.equals(DatabaseType.ORACLE.getValue(), authenticationConnectionDto.getDatabaseType())) {
                    schemaName = authenticationConnectionDto.getDatabaseName() + "." + schemaName;
                }
                String columnName = columnDto.getColumnName();
                if (paSensiModel.getSchemaName() == null) {
                    paSensiModel.setSchemaName(schemaName);
                } else {
                    paSensiModel.setSchemaName(paSensiModel.getSchemaName() + "|" + schemaName);
                }
                if (paSensiModel.getTableName() == null) {
                    paSensiModel.setTableName(tableName);
                } else {
                    paSensiModel.setTableName(paSensiModel.getTableName() + "|" + tableName);
                }
                if (paSensiModel.getColumnName() == null) {
                    paSensiModel.setColumnName(columnName);
                } else {
                    paSensiModel.setColumnName(paSensiModel.getColumnName() + "|" + columnName);
                }
            }
        }
//        paSensiModel.setExtDescription(setOtherDescription(dataResultColumnDtos));
        ArrayList<MaskRule> ruleArrayList = new ArrayList<>();

        boolean hasResult = permissionNotNull(color, maskPermissionRule);
        if (maskPermissionRule != null && !maskPermissionRule.isEmpty() && hasResult) {
            for (Map.Entry<String, List<PermissionRuleDto>> listEntry : maskPermissionRule.entrySet()) {
                String permission = "";
                if (Arrays.asList("desensitization_instance_auth", "desensitization_schema_auth").contains(listEntry.getKey())) {
                    permission = "明文展示";
                } //AuthConstant.desensitization_instance ; // 明文
                if (Arrays.asList("desensitization_half_instance_auth", "desensitization_half_schema_auth").contains(listEntry.getKey())) {
                    permission = "脱敏文";

                } // AuthConstant.desensitization_half_instance ; // 脱敏文
                if (Arrays.asList("desensitization_not_instance_auth", "desensitization_not_schema_auth").contains(listEntry.getKey())) {
                    permission = "全遮";

                }   // AuthConstant.desensitization_not_instance ; // 全遮
                if (permission.equals("")) {
                    log.error("permission is null " + listEntry.getKey());
                    permission = "默认";
                }
                List<PermissionRuleDto> value = listEntry.getValue();
                for (PermissionRuleDto ruleDto : value) {
                    if (color != null && !color.getUniqueKey().equals(ruleDto.getSensitive_level())) {
                        continue;
                    }

                    MaskRule rule = new MaskRule();

                    rule.setAuthTimeStart(ruleDto.getBegin_time());
                    rule.setAuthTime(ruleDto.getGmt_create());
                    rule.setAuthTimeEnd(ruleDto.getEnd_time());
                    rule.setOrigin(ruleDto.getOrigin());
                    rule.setSchemaId(ruleDto.getSchema_id());
                    rule.setConnectId(ruleDto.getConnect_id());
                    rule.setOrderCode(ruleDto.getOrder_code());
                    rule.setEnableDesensitizationCopy(ruleDto.getEnable_desensitization_copy());
                    if (1 == ruleDto.getResource_type()) {
                        rule.setRuleType("实例");
                        rule.setRuleObject(authenticationConnectionDto.getInstanceName());
                        rule.setRuleObject(authenticationConnectionDto.getInstanceName());
                    } else {
                        rule.setRuleType("Schema");
                        rule.setRuleObject(paSensiModel.getSchemaName());
                    }
                    rule.setSensitiveLevel(ruleDto.getSensitive_level());
                    rule.setDesensiteType(permission);
                    if (color != null) {
                        rule.setDesensiteType(dto.getLevel() + "(" + color.getSensitiveLevelName() + ")" + permission);
                    }
                    rule.setGrantType(ruleDto.getGrant_type());
                    rule.setAuthUser(HandlerCounter.getOperator());
                    rule.setTableName(paSensiModel.getTableName());
                    rule.setAuthUserOrganization(ruleDto.getGroup_name());
                    rule.setEnableDesensitizationCopy(ruleDto.getEnable_desensitization_copy() != null ? ruleDto.getEnable_desensitization_copy() : 0);
                    if (StringUtils.isBlank(rule.getDesensiteType())) {
                        rule.setDesensiteType("默认");
                    }
                    ruleArrayList.add(rule);
                }
            }
        } else {
            MaskRule rule = new MaskRule();
            rule.setSensitiveLevel(dto.getLevel());

            Date date = new Date();
            rule.setOrigin(1);
            rule.setAuthTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date));
            rule.setAuthTimeStart(System.currentTimeMillis() / 1000);
            rule.setAuthTimeEnd(System.currentTimeMillis() / 1000);
            rule.setRuleType("实例");
            rule.setRuleObject(authenticationConnectionDto.getInstanceName());
            if (isExport) {
                rule.setOrigin(13);
            }
            rule.setDesensiteType(columnPermissionZH + pre);
            rule.setGrantType(grantType);
            rule.setAuthUser(HandlerCounter.getOperator());
            rule.setTableName(paSensiModel.getTableName());
            rule.setSensitiveHead(columnPermissionZH + pre);
            if (color != null) {
                rule.setSensitiveLevel(color.getUniqueKey());
                rule.setDesensiteType(dto.getLevel() + "(" + color.getSensitiveLevelName() + ")" + columnPermissionZH + pre);
                rule.setSensitiveHead(dto.getLevel() + "(" + color.getSensitiveLevelName() + ")" + columnPermissionZH + pre);
            }
            if (color == null && grantType == 2) {
                rule.setDesensiteType("未设置:" + columnPermissionZH + pre);
                rule.setSensitiveHead("未设置:" + columnPermissionZH + pre);
            }
            if (StringUtils.isBlank(rule.getDesensiteType())) {
                rule.setDesensiteType("默认");
            }
            ruleArrayList.add(rule);
        }


        paSensiModel.setMaskTemp(ruleArrayList);
        String identity = String.format("%s%s%s%s%s%s", authenticationConnectionDto.getDatabaseName(), paSensiModel.getSchemaName(), paSensiModel.getTableName(), paSensiModel.getColumnName(), dto.getSensType(), dto.getDesensitized());
        paSensiModel.setIdentity(String.valueOf(identity.hashCode()));
    }

    /**
     * 对平安组合列进行记录,以查证问题
     *
     * @param dataResultColumnDtos
     * @return
     */
    private String setOtherDescription(DataResultColumnDto[][] dataResultColumnDtos) {
        if (dataResultColumnDtos == null) {
            return null;
        }
        for (DataResultColumnDto[] dto : dataResultColumnDtos) {
            if (dto == null || dto.length == 1) {
                continue;
            }
            return JSON.toJSONString(dataResultColumnDtos);
        }
        return null;
    }

    private boolean permissionNotNull(SensitiveLevel color, Map<String, List<PermissionRuleDto>> values) {
        if (grantType == 1) {
            return true;
        }
        if (values == null || color == null) {
            return false;
        }
        for (List<PermissionRuleDto> ruleDtos : values.values()) {
            for (PermissionRuleDto dto : ruleDtos) {
                if (dto.getGrant_type() != 2) {
                    continue;
                }
                if (dto.getSensitive_level() != null && dto.getSensitive_level().equals(color.getUniqueKey())) {
                    return true;
                }
            }
        }
        return false;
    }

    private void setSensitiveLevel(RowResultDto dto, DBDAttributeBinding binding) {

        if (grantType == 1) {
            binding.setAuthLevel(0);
            binding.setSensitiveLevelName("not_set");
            return;
        }
        // 是等级模式, 但是没返回等级  //   返回空等级的时候, 不覆盖已经有的等级
        if (grantType == 2 && StringUtils.isBlank(dto.getLevel()) && binding.getAuthLevel() == 0) {
            binding.setAuthLevel(0);
            binding.setSensitiveLevelName("not_set");
            SensitiveLevel sensitiveLevel = getColor(0);

            if (sensitiveLevel != null) {
                binding.setSensitiveLevelName(sensitiveLevel.getSensitiveLevelName());
                binding.setSensitiveLevelColor(sensitiveLevel.getColor());
            }
            return;
        }
        if (dto.getLevel() != null && dto.getLevel().toUpperCase().equals("UNKNOWN")) {
            binding.setAuthLevel(0);
            binding.setSensitiveLevelName("not_set");
            SensitiveLevel sensitiveLevel = getColor(0);

            if (sensitiveLevel != null) {
                binding.setSensitiveLevelName(sensitiveLevel.getSensitiveLevelName());
                binding.setSensitiveLevelColor(sensitiveLevel.getColor());
            }
        } else if (dto.getLevel() != null) {
            // 这里是返回给前端的, 设置成 主线的等级格式
            char c1 = dto.getLevel().charAt(1);
            int level = Integer.parseInt(String.valueOf(c1));
            SensitiveLevel sensitiveLevel = getColor(level);
            binding.setAuthLevel(level);
            binding.setSensitiveLevelName(dto.getLevel());
            if (sensitiveLevel == null) {
                throw new RuntimeException("未知的敏感等级" + String.valueOf(dto.getLevel()) + " " + level);
            }

            binding.setAuthLevel(sensitiveLevel.getLevel());
            binding.setSensitiveLevelName(sensitiveLevel.getSensitiveLevelName());
            binding.setSensitiveLevelColor(sensitiveLevel.getColor());
        }
    }


    private void setCopyEnable(DBDAttributeBinding binding) {
        // 非分级分类设置明文复制权限
        if (grantType == 1 && enableDesensitizationCopy != null) {
            if (enableDesensitizationCopy.get("null") != null && enableDesensitizationCopy.get("null") == 1) {
                binding.setCopyPlaintext(true);
            }
        }
        if (grantType != 2) {
            return;
        }
        // grantType = 2 分级分类设置明文复制权限
        int level = binding.getAuthLevel();
        // S1 有明文权限即可复制
        if (level == 1 && String.valueOf(desensitizationClassifyDto.getS1()).equals("0")) {
            binding.setCopyPlaintext(true);
        }
        SensitiveLevel sensitiveLevel = getColor(level);
        if (sensitiveLevel == null || enableDesensitizationCopy == null) {
            return;
        }
        Integer copy = enableDesensitizationCopy.get(String.valueOf(sensitiveLevel.getUniqueKey()));
        if (copy != null && copy == 1) {
            binding.setCopyPlaintext(true);
        }
    }

    private SensitiveLevel getColor(int charAt) {
        Map<String, SensitiveLevel> map = getSensitiveLevelMap();
        for (SensitiveLevel model : map.values()) {
            if (model.getLevel().equals((int) charAt)) {
                return model;
            }
        }
        return null;
    }


    private PermissionRuleDto getMask(String level, List<PermissionRuleDto> maskPermissionRule) {
        if (StringUtils.isBlank(level)) {
            return null;
        }
        if (maskPermissionRule == null || maskPermissionRule.isEmpty()) {
            return null;
        }
        List<PermissionRuleDto> tempPermissionList = new ArrayList<>();
        for (PermissionRuleDto ruleDto : maskPermissionRule) {
            if (ruleDto.getResource_type() == null) {
                continue;
            }
            Integer settingLevel = getSensitiveLevelMap().get(ruleDto.getSensitive_level()).getLevel();
            //S1   -- 1
            //S2   -- 2
            if (level.contains(String.valueOf(settingLevel))) {
                tempPermissionList.add(ruleDto);
            }
        }
        // 取授权时间最长的
        PermissionRuleDto last = null;
        for (PermissionRuleDto dto : tempPermissionList) {
            if (last == null) {
                last = dto;
            }
            if (last.getEnd_time() < dto.getEnd_time()) {
                last = dto;
            }
        }
        if (last != null) {
            return last;
        }
        return maskPermissionRule.get(0);
    }

    private void removeNull(DesensitizationClassifyDto desensitizationClassifyDto) {
        desensitizationClassifyDto.setS1(isNotNull(desensitizationClassifyDto.getS1()));
        desensitizationClassifyDto.setS2(isNotNull(desensitizationClassifyDto.getS2()));
        desensitizationClassifyDto.setS3(isNotNull(desensitizationClassifyDto.getS3()));
        desensitizationClassifyDto.setS4(isNotNull(desensitizationClassifyDto.getS4()));
        desensitizationClassifyDto.setS5(isNotNull(desensitizationClassifyDto.getS5()));
        desensitizationClassifyDto.setUnknow(isNotNull(desensitizationClassifyDto.getUnknow()));
    }

    private Integer isNotNull(Integer s1) {
        if (s1 == null) {
            return 2;
        }
        return s1;
    }

    private Integer convertPaLevel(Integer authLevel) {
        if (authLevel == null) {
            return null;
        }
        // 全脱/(全遮)
        if (authLevel == 0) {
            return 2;
        }
        // 半托/()
        if (authLevel == 1) {
            return 1;
        }
        //明文/(明文)
        if (authLevel == 2) {
            return 0;
        }
        return null;
    }

    private String convertPaLevelZH(Integer authLevel) {
        if (authLevel == null) {
            return null;
        }
        // 全脱/(全遮)
        if (authLevel == 2) {
            return "全遮";
        }
        // 半托/()
        if (authLevel == 1) {
            return "脱敏文";
        }
        //明文/(明文)
        if (authLevel == 0) {
            return "明文展示";
        }
        return null;
    }

    private Map<Integer, String> getBigDataIndexList(Object[] row) {
        Map<Integer, String> indexList = new HashMap<>();
        for (int i = 0; i < row.length; i++) {
            if (row[i] == null) {
                continue;
            }
            if (!(row[i] instanceof Map)) {
                continue;
            }
            Map<String, Object> rowMap = (Map<String, Object>) row[i];

            Object columnType = rowMap.get(WebSQLConstants.ATTR_COLUMN_TYPE);
            if (columnType == null) {
                continue;
            }
            if ("clob".equalsIgnoreCase(columnType.toString()) || "other".equalsIgnoreCase(columnType.toString())) {
                indexList.put(i, "null");
                continue;

            }
            indexList.put(i, "map");
            log.warn("bigdata type is unknow " + JSON.toJSONString(row[i].getClass().getSimpleName()));
            log.warn("bigdata type is unknow " + JSON.toJSONString(row[i]));
        }
        return indexList;
    }

    private void resetPermissionExport(DesensitizationClassifyDto classifyDto) {
        if (!isExport) {
            return;
        }

        if (classifyDto != null) {
            classifyDto.setS1(getNotPlain(classifyDto.getS1()));
            classifyDto.setS2(getNotPlain(classifyDto.getS2()));
            classifyDto.setS3(getNotPlain(classifyDto.getS3()));
            classifyDto.setS4(getNotPlain(classifyDto.getS4()));
            classifyDto.setS5(getNotPlain(classifyDto.getS5()));
            classifyDto.setUnknow(getNotPlain(classifyDto.getUnknow()));
        }
    }

    private Integer getNotPlain(Integer s1) {
        if (s1 == 0 && ruleExportDesensitizationType.equals(RuleExportDesensitizationType.DESENSITIZATION_EXPORT)) {
            return 1; // 规则不允许明文的时候, 明文权限覆盖成脱敏文
        }
        return s1;
    }

    private void resetPermissionEXuser(DesensitizationClassifyDto classifyDto) {
        if (classifyDto == null) {
            return;
        }
        if (StringUtils.isNotBlank(exUserMark) && !userDesensDto.getUsername().toUpperCase().contains(exUserMark.toUpperCase())) {
            return;
        }
        if (defaultPermissionS1 != -1) {
            classifyDto.setS1(defaultPermissionS1);
        }
        if (defaultPermissionS2 != -1) {
            classifyDto.setS2(defaultPermissionS2);
        }
        if (defaultPermissionS3 != -1) {
            classifyDto.setS3(defaultPermissionS3);
        }
        if (defaultPermissionS4 != -1) {
            classifyDto.setS4(defaultPermissionS4);
        }
        if (defaultPermissionS5 != -1) {
            classifyDto.setS5(defaultPermissionS5);
        }
        if (defaultPermissionUnknow != -1) {
            classifyDto.setUnknow(defaultPermissionUnknow);
        }
    }

    private Map<String, SensitiveLevel> sensitiveLevels = new HashMap<>();

    Map<String, SensitiveLevel> getSensitiveLevelMap() {
        if (!sensitiveLevels.isEmpty()) {
            return sensitiveLevels;
        }
        List<SensitiveLevel> sensitiveLevels = this.sensitiveLevelMapper.selectList(Wrappers.<SensitiveLevel>lambdaQuery()
                .eq(SensitiveLevel::getIsDelete, 0)
        );
        if (sensitiveLevels == null || sensitiveLevels.isEmpty()) {
            return new HashMap<>();
        }
        for (SensitiveLevel level : sensitiveLevels) {
            this.sensitiveLevels.put(level.getUniqueKey(), level);
        }
        return this.sensitiveLevels;
    }
}