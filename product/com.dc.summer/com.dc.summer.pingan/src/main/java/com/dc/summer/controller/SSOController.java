package com.dc.summer.controller;

import com.dc.repository.mysql.model.User;
import com.dc.springboot.auth.AuthIgnored;
import com.dc.springboot.core.client.BackendClient;
import com.dc.springboot.core.config.PathConfig;
import com.dc.springboot.core.model.data.Client;
import com.dc.summer.config.SummerConfig;
import com.dc.summer.enumType.ActionName;
import com.dc.summer.enumType.ModuleName;
import com.dc.summer.service.UserService;
import com.dc.summer.util.IpUtil;
import com.dc.utils.StringUtils;
import com.pingan.cdsf.driver.bridger.dto.ResponseUserDto;
import com.pingan.cdsf.driver.bridger.dto.UserInfoDto;
import com.pingan.cdsf.driver.bridger.service.SsoAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Api(tags = "单点登录模块")
@Slf4j
@RequestMapping("/sso")
@RestController
@AuthIgnored
public class SSOController {
    // TODO
//    @Resource
//    private SsoAuthService ssoAuthService;

    @Resource
    private BackendClient backendClient;

    @Resource
    SummerConfig summerConfig;

    @Resource
    private UserService userService;

    @Value("${pingan.sso.paic-login:http://127.0.0.1}")
    private String pinganLoginUrl;


    /**
     * pa 平安科技
     */
    @ApiOperation("对接平安接口")
    @GetMapping("/pa")
    public void authentication(HttpServletRequest request,
                               HttpServletResponse response) throws Exception {
        Cookie[] cookies = request.getCookies();
        String token = null;
        if (cookies == null) {
            log.error("cookie获取失败！");
            response.sendRedirect(pinganLoginUrl);
            return;
        }
        for (Cookie cookie : cookies) {
            if (cookie.getName().equals("damsKey")) {
                token = cookie.getValue();
                break;
            }
        }
        if (StringUtils.isNullOrEmpty(token)) {
            log.error("cookie中获取token失败！");
            response.sendRedirect(pinganLoginUrl);
            return;
        }
        SsoAuthService ssoAuthService = com.dc.springboot.core.component.Resource.getBean(SsoAuthService.class);
        ResponseUserDto<UserInfoDto> userInfo = ssoAuthService.authSsoToken(token);
        String username = null;
        if (userInfo != null) {
            if ("A00000".equals(userInfo.getCode())) {
                username = userInfo.getData().getAccountName();
            } else {
                log.error("验证失败！" + userInfo.getMessage());
                response.sendRedirect(pinganLoginUrl);
                return;
            }
        }
        if (StringUtils.isNullOrEmpty(username)) {
            log.error("验证失败！");
            response.sendRedirect(pinganLoginUrl);
            return;
        }
        User sysUser = userService.findSysUser(username);
        if (sysUser == null) { //找不到用户则新建
            sysUser = userService.save(username);
        } else {
            if (sysUser.getIsDefPwd().equals(1L)) { // 平安需求，去掉首次修改密码逻辑
                userService.updatePwdField(sysUser.getId());
            }
        }
        String uniqueKey = sysUser.getUniqueKey();
        // 重定向到前端页面
        String frontend = PathConfig.getInstance().getDcFrontend();
        try {

            Map<String, Object> bodyParams = new HashMap<>();
            bodyParams.put("user_uuid", uniqueKey);
            bodyParams.put("module_name", ModuleName.USER_MANAGEMENT);
            bodyParams.put("action_name", ActionName.USER_LOGIN);
            bodyParams.put("log_content", "登录系统");
            bodyParams.put("access_ip", IpUtil.getIpAddr(request));
            backendClient.operationLog(Client.getClient(summerConfig.getPath().getDcBackend()), bodyParams);

            String redirectUrl = UriComponentsBuilder.fromHttpUrl(frontend)
                    .queryParam("token", uniqueKey)
                    .queryParam("__v_cache_buster", new Date().getTime())
                    .toUriString();

            response.sendRedirect(redirectUrl);
        } catch (IOException e) {
            response.sendRedirect(pinganLoginUrl);
            log.error("跳转失败重定向到login 页 " + e.getMessage());
        }
    }
}
