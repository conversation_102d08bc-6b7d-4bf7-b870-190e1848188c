package com.dc.summer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.*;
import com.dc.repository.mysql.service.DcDatabaseConnectionService;
import com.dc.repository.mysql.service.PaDbTableFieldService;
import com.dc.repository.mysql.service.SchemaService;
import com.dc.repository.redis.model.EnvConnection;
import com.dc.repository.redis.model.EnvSchema;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.springboot.core.model.parser.dto.ExtendedAttributesDto;
import com.dc.summer.config.SummerConfig;
import com.dc.summer.constants.ConnectAttributeConstants;
import com.dc.summer.enumType.PaHandleStatus;
import com.dc.summer.enumType.PaStepType;
import com.dc.summer.enumType.PaTaskStatus;
import com.dc.summer.model.*;
import com.dc.summer.model.thread.SummerThreadScheduler;
import com.dc.summer.model.type.ExecuteType;
import com.dc.summer.service.PaOrderBiz;
import com.dc.summer.service.PaOrderService;
import com.dc.summer.service.UserService;
import com.dc.summer.util.PaTaskUtil;
import com.dc.type.DatabaseType;
import com.dc.utils.DateUtil;
import com.dc.utils.bean.CloneUtils;
import com.dc.utils.http.HttpClientUtils;
import com.dc.springboot.core.model.type.OrderCurrentStatus;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.pingan.cdsf.driver.bridger.dto.*;
import com.pingan.cdsf.driver.bridger.service.DataDesensitizationService;
import com.pingan.cdsf.driver.bridger.service.EditSensTypeSdkService;
import com.pingan.cdsf.driver.bridger.service.EoaService;
import com.pingan.cdsf.driver.bridger.service.EsgService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.HttpURLConnection;
import java.rmi.ServerException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Slf4j
@Service
@Profile("env")
public class PaOrderServiceImpl implements PaOrderService {

    final Gson gson = new GsonBuilder().serializeNulls().create();

    @Resource
    private SummerThreadScheduler scheduler;

    @Resource
    private SummerConfig summerConfig;

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserService userService;

    @Resource
    private DcWorkOrderMapper workOrderMapper;

    @Resource
    public PaUserMapper paUserMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    DcBpmMapper bpmMapper;

    @Autowired(required = false)
    private DataDesensitizationService dataResultService;

    @Resource
    DcDatabaseConnectionService databaseConnectionService;

    @Resource
    PaDbTableFieldService paTableFieldService;

    @Resource
    SchemaService schemaService;

    @Resource
    public Map<String, PaOrderBiz> orderBizMap;

    @Autowired
    private PaDBConvertServiceImpl paDBConvertServiceImpl;


    @Override
    public void callbackTask(PaOrderCallBackMessage message) {
        log.info("异步处理回写签报ID:{}", message.getTaskId());
        scheduler.exec(ExecuteType.CALLBACK_TASK, () -> {
            //
            if (message.getTaskStatus() == PaTaskStatus.SUBMIT.getValue()) {
                //工单还在进行中不处理任何动作，商定在工单流最后一步才回调接口
                log.warn("平安回签状工单态不合法为，taskStatus:{}", message.getTaskStatus());
                return;
            }

            //
            Order order = workOrderMapper.selectOne(new QueryWrapper<Order>().lambda().eq(Order::getEoa_code, message.getTaskId()));
            if (ObjectUtils.isEmpty(order)) {
                //避免平安可将驳回的工单二次激活重新审批
                log.error("callback task not found, eoa:{}", message.getTaskId());
                return;
            }

            if (Arrays.asList(OrderCurrentStatus.SUCCESS.getValue(), OrderCurrentStatus.TERMINATION.getValue()).contains(order.getCurrent_status())) {
                //避免平安可将驳回的工单二次激活重新审批
                log.warn("平安二次审批回调拒绝，工单状态已完结。taskStatus:{}", message.getTaskStatus());
                return;
            }

            List<JsonObject> infos = new ArrayList<>();
            //detail
            List<PaOrderFlowResult> taskDetailList = message.getTaskDetail();
            for (PaOrderFlowResult flow : taskDetailList) {
                //flowId =0 的是审批人，节点, 我们的工单不需要
                if (flow.getFlowId() <= 0) {
                    continue;
                }
                JsonObject info = new JsonObject();
                info.addProperty("step", flow.getFlowId());
                info.addProperty("approval_time", flow.getFlowDoneDate());
                info.addProperty("real_approval_uuid", flow.getFlowOwnerMail());
                info.addProperty("approval_status", flow.getFlowResult());
                info.addProperty("approval_opinion", flow.getHandlePropose());
                infos.add(info);
            }

            //注意 ：TaskStatus 最终审批不通过 也是完成状态， 故不能通过此字段判断工作状态
            //注意 ：TaskStatus 最终审批不通过 也是完成状态， 故不能通过此字段判断工作状态
            //注意 ：TaskStatus 最终审批不通过 也是完成状态， 故不能通过此字段判断工作状态
            //获取最后审批流程中的状态， 不包含传阅
            List<PaOrderFlowResult> nodes = taskDetailList.
                    stream().filter(e -> PaStepType.CIRCULATION.getValue() != e.getStepType()).collect(Collectors.toList());
            Integer lastNodeFlowId = nodes.stream().map(PaOrderFlowResult::getFlowId).max(Integer::compareTo).get();
            List<PaOrderFlowResult> finalNodes = nodes.
                    stream().filter(e -> e.getFlowId() == lastNodeFlowId).collect(Collectors.toList());
            //是否存在并行审批(所有人均同意进入下一流程)
            boolean isParallel = finalNodes.stream().filter(e -> e.getStepType() == PaStepType.PARALLEL.getValue()).count() > 0 ? true : false;
            //最终流程中的所有处理状态。 使用Set代表去重
            Set<String> successful = finalNodes.stream().filter(e -> null != e.getHandleStatus()).map(PaOrderFlowResult::getHandleStatus).collect(Collectors.toSet());
            //默认失败
            int status = OrderCurrentStatus.TERMINATION.getValue();
            //并行审批(所有人均同意进入下一流程)
            List<String> reduce = successful.stream().filter(item -> !PaHandleStatus.successful().contains(item)).collect(toList());
            if (true == isParallel && reduce.size() == 0) {
                status = OrderCurrentStatus.SUCCESS.getValue(); //完成
            }
            //协同审批(任意一人同意进入下一审批流程)
            List<String> intersection = successful.stream().filter(item -> PaHandleStatus.successful().contains(item)).collect(toList());
            if (false == isParallel && intersection.size() > 0) {
                status = OrderCurrentStatus.SUCCESS.getValue(); //完成
            }

            //最后审批流审批用户，协同多个， 并行只有一个
            List<String> operatorUsers = finalNodes.stream().filter(e -> null != e.getHandleStatus()).map(PaOrderFlowResult::getFlowOwnerMail).collect(toList());
            //request param
            JsonObject param = new JsonObject();
            param.addProperty("task_id", message.getTaskId());
            param.addProperty("status", status);
            param.addProperty("audit_info", gson.toJson(infos));
            //获取审批人,目前只有协同， 故只有一个,
            param.addProperty("eoa_operator_display_name", String.join(",", operatorUsers.stream().findFirst().get()));
            String resp = HttpClientUtils.doPost(summerConfig.getPath().getDcBackend() + "/api/v1/order/eoa-order/callback-order", new StringEntity(gson.toJson(param), "UTF-8"));
            log.info("callback Order response: " + resp);


            String bpmKey = bpmMapper.getKeyById(order.getBpm_id());
            if (null != bpmKey && orderBizMap.containsKey("after_" + bpmKey)) {
                //Approved::
                WorkFlowCallbackMessage content = WorkFlowCallbackMessage.builder()
                        .status(status)
                        .orderId(order.getId())
                        .build();
                orderBizMap.get("after_" + bpmKey).workflowCallback(content);
            }
        });
    }

    @Override
    public void localCallbackTask(PaLocalOrderCallBackMessage message) {
        log.info("异步处理回写签报ID:{}", message.getCode());
        scheduler.exec(ExecuteType.CALLBACK_TASK, () -> {
            //
            if (message.getStatus() != OrderCurrentStatus.SUCCESS.getValue()) {
                //工单还在进行中不处理任何动作，商定在工单流最后一步才回调接口
                log.warn("平安本系统回签状工单态不合法为，orderStatus:{}", message.getStatus());
                return;
            }
            //
            Order order = workOrderMapper.selectOne(new QueryWrapper<Order>().lambda().eq(Order::getCode, message.getCode()));
            if (ObjectUtils.isEmpty(order)) {
                log.error("callback order not found, code:{}", message.getCode());
                return;
            }
            if (OrderCurrentStatus.SUCCESS.getValue() != order.getCurrent_status()) {
                //验证工单真实状态
                log.warn("回调拒绝，系统工单状态未通过。orderStatus:{}", order.getCurrent_status());
                return;
            }
            String bpmKey = bpmMapper.getKeyById(order.getBpm_id());
            if (null != bpmKey && orderBizMap.containsKey("after_" + bpmKey)) {
                //Approved::
                WorkFlowCallbackMessage content = WorkFlowCallbackMessage.builder()
                        .status(order.getCurrent_status())
                        .orderId(order.getId())
                        .build();
                orderBizMap.get("after_" + bpmKey).workflowCallback(content);
            }
        });
    }

    @Override
    public List<PaOrderResResult> asyncCall(BatchPaSubmitTask batchSubmitTask) throws Exception {
        /*拆单情况下一次性多个工单*/
        List<PaSubmitTaskMessage> messages = batchSubmitTask.getMessages();
        List<String> users = new ArrayList<>();
        for (PaSubmitTaskMessage message : messages) {
            List<PaApproveChainBean> approveChains = message.getApproveChain();
            if (approveChains != null && approveChains.size() > 0) {
                for (PaApproveChainBean approveChain : approveChains) {
                    String um = approveChain.getUm();
                    if (StringUtils.isNotEmpty(um)) {
                        String[] split = um.split(",");
                        for (String s : split) {
                            users.add(s.toUpperCase());
                        }
                    }
                }
            }
        }
        users = users.stream().distinct().collect(Collectors.toList());
        this.saveUser(users);
        for (PaSubmitTaskMessage message : messages) {
            try {
                EoaParam eoaParam = new EoaParam();
                eoaParam.setApproveChain(PaTaskUtil.approveChainToString(message.getApproveChain()));
                eoaParam.setTaskContent(PaTaskUtil.taskContentFormat(message.getTaskContent()));
                eoaParam.setTaskName(message.getTaskName());
                eoaParam.setUserId(PaTaskUtil.joinAccountWithDomain(message.getUserId()));
                eoaParam.setAttachments(PaTaskUtil.convert(message.getAttachments(), message.getUserId()));

                EoaService eoaService = com.dc.springboot.core.component.Resource.getBean(EoaService.class);
                EoaResponseDto result = eoaService.submitTask(eoaParam);
                Optional.ofNullable(result).ifPresent(re -> {
                    log.info("平安签报提交请返回结果：{}", gson.toJson(re));
                    if (String.valueOf(HttpURLConnection.HTTP_OK).equals(re.getResultCode())) {
                        message.setTaskId(re.getResultObject());
                    } else {
                        message.setReturnMessage(re.getResultMsg());
                    }
                });
            } catch (Exception e) {
                log.error("平安签报提交失败，工单ID{}：", message.getUuid());
                e.printStackTrace();
            }
        }

        return messages.stream()
                .map(message -> PaOrderResResult.builder()
                        .code(message.getUuid())
                        .message(message.getReturnMessage())
                        .taskId(message.getTaskId()).build())
                .collect(Collectors.toList());
    }


    @Override
    public List<User> creatUserFromAccount(List<String> umList) throws Exception {
        if (ObjectUtils.isEmpty(umList)) {
            throw new ServerException("参数不能为空");
        }
        //paUser转dcUser
        List<PaUser> paUserData = paUserMapper.selectList(new QueryWrapper<PaUser>()
                .select("um")
                .in("UPPER(um)", umList));  //保存paUser 已经控制都大写
        if (ObjectUtils.isEmpty(paUserData)) {
            throw new ServerException("UM账号不合法");
        }

        List<User> userExistsList = userMapper.selectList(new QueryWrapper<User>()
                .in("UPPER(username)", umList)
                .eq("is_delete", 0));
        Map<String, User> userDataMap = null;
        if (ObjectUtils.isNotEmpty(userExistsList)) {
            userDataMap = userExistsList.stream().collect(Collectors.toMap(u -> u.getUsername().toUpperCase(Locale.ROOT), u -> u, (k1, k2) -> k1));
        }

        //这种情况是 paUser 存在了,但是 dcUser 表中没有，及时创建一个
        List<User> returnUserList = new ArrayList<>();
        for (PaUser user : paUserData) {
            if (null != userDataMap && userDataMap.containsKey(user.getUm())) {
                returnUserList.add(userDataMap.get(user.getUm()));
                continue;
            }
            //pa账号需要大写
            User save = userService.save(user.getUm().toUpperCase(Locale.ROOT));
            returnUserList.add(save);
        }
        return returnUserList;
    }

    private void saveUser(List<String> usernames) {
        List<User> allUserList = userMapper.selectList(new QueryWrapper<User>().in("upper(username)", usernames).eq("is_delete", 0));
        List<PaUser> paUserList = paUserMapper.selectList(new QueryWrapper<PaUser>().in("um", usernames));

        for (String username : usernames) {
            String uuid = null;
            for (User user : allUserList) {
                if (username.equals(user.getUsername().toUpperCase())) {
                    uuid = user.getUniqueKey();
                    break;
                }
            }
            if (uuid == null) {
                boolean isSave = false;
                for (PaUser paUser : paUserList) {
                    if (username.equals(paUser.getUm())) {
                        isSave = true;
                        break;
                    }
                }
                if (!isSave) {
                    continue;
                }
                User sysUser = userService.save(username);
            }
        }
    }


    @Override
    public PaOrderResResult submitErrorClass(PaErrorClassMessage message) throws Exception {

        LocalDateTime time = LocalDateTime.now();
        String createTime = time.format(DateTimeFormatter.ofPattern(DateUtil.ymd_hms_str_1));
        List<PaErrorClassDto> errorList = new ArrayList<>();


        List<PaErrorClassModel> data = message.getErrorList();
        if (data.size() <= 0) {
            throw new ServiceException("errorList isEmpty");
        }


        DatabaseConnection paInstance = databaseConnectionService.get(message.getConnectionId());


        //paInstance.getSync() 如果是非同步的资产没有,通过后续手动修改扩展属性可以获得
        String instanceUUid = paInstance.getSync();

        //兼容手动添加资产后续补全从属性里缺失的实体uuid(同步映射)
        if (ObjectUtils.isEmpty(instanceUUid)) {
            String attributes = paInstance.getExtended_attributes();
            if (ObjectUtils.isEmpty(attributes)) {
                throw new ServiceException("extended_attributes isEmpty");
            }
            Map<String, String> extAttrMap = CloneUtils.transListToMap(
                    JSON.parseObject(attributes, new TypeReference<List<ExtendedAttributesDto>>() {
                    }),
                    ExtendedAttributesDto::getKey,
                    ExtendedAttributesDto::getValue);
            if (null != extAttrMap && extAttrMap.containsKey(ConnectAttributeConstants.INSTANCE_UUID)) {
                instanceUUid = extAttrMap.get(ConnectAttributeConstants.INSTANCE_UUID);
            }
        }

        if (ObjectUtils.isEmpty(instanceUUid)) {
            //throw new ServiceException("非UCMDB同步实例，缺实例UUID，请补全后再提交");
            //因为生产也有很多为空,保证接口调用不失败给一个默认值
            log.warn("instanceUUid not-exist.");
            instanceUUid = "not-exist";
        }

        Map<String, String> dbNameMap = new HashMap<>();

        Map<String, Schema> schemaMap = null;
        List<Schema> schemaList = schemaService.findSchemaList(data.stream().map(PaErrorClassModel::getSchemaId).collect(toList()));
        if (ObjectUtils.isNotEmpty(schemaList)) {
            schemaMap = schemaList.stream().collect(Collectors.toMap(Schema::getUnique_key, s -> s));
        }

        for (PaErrorClassModel model : data) {
            PaErrorClassDto dto = new PaErrorClassDto();
            dto.setRemark(message.getRemark());
            dto.setErrorType(message.getErrorType());
            dto.setCreatedTime(Optional.ofNullable(message.getCreatedTime()).orElse(createTime));
            dto.setCreatedBy(message.getUserId().toUpperCase(Locale.ROOT)); //提交人
            dto.setDatabaseTypeCd(message.getDatabaseTypeCd());
            dto.setInstanceName(paInstance.getEntity());  //平安 entity 比 message.getInstanceName() 可靠
            dto.setInstanceId(instanceUUid);

            //查询dbName,schemaName
            String dbName = null;
            Schema schema = null;
            if (null != schemaMap && schemaMap.containsKey(model.getSchemaId())) {
                schema = schemaMap.get(model.getSchemaId());
            }

            if (ObjectUtils.isEmpty(schema)) {
                log.error("schema not found.");
                continue;
            }

            String key = message.getConnectionId() + "-" + model.getSchemaId();
            if (dbNameMap.containsKey(key)) {
                dbName = dbNameMap.get(key);
            } else {
                dbName = this.getEnvDatabaseName(paInstance, schema);
                dbNameMap.put(key, dbName);
            }
            //
            dto.setDatabaseNameEn(dbName);
            dto.setSchemaName(schema.getSchema_name());
            dto.setObjectName(model.getObjectName());
            dto.setColumnName(model.getColumnName());
            errorList.add(dto);
        }

        EsgService service = com.dc.springboot.core.component.Resource.getBean(EsgService.class);
        ErrorClassParam param = new ErrorClassParam();
        param.setErrorList(errorList);
        EsgResponseDto responseDto = service.submitErrorClass(param);
        return PaOrderResResult.builder().code(message.getUuid()).message(responseDto.getMessage()).build();
    }

    @Override
    public List<ReturnSensTypeDto> fetchSensTypeList() throws Exception {
        final String key = "pa_sens_type_list";
        boolean keyExists = redisTemplate.hasKey(key);
        if (keyExists) {
            String jsonStr = redisTemplate.opsForValue().get(key).toString();
            return gson.fromJson(jsonStr, new TypeToken<List<ReturnSensTypeDto>>() {
            }.getType());
        }

        EditSensTypeSdkService service = com.dc.springboot.core.component.Resource.getBean(EditSensTypeSdkService.class);
        List<ReturnSensTypeDto> dictList = service.fetchSensTypeList();
        redisTemplate.opsForValue().set(key, gson.toJson(dictList), 15, TimeUnit.MINUTES);
        return dictList;
    }

    @Override
    public String getEnvDatabaseName(DatabaseConnection connection, Schema schema) {
        if (ObjectUtils.isEmpty(connection)) {
            log.info("connection isEmpty.");
            return null;
        }
        if (ObjectUtils.isEmpty(schema)) {
            log.info("schema isEmpty.");
            return null;
        }

        EnvSchema envSchema = new EnvSchema();
        envSchema.setSchemaName(schema.getSchema_name());
        envSchema.setDefDboName(schema.getDef_dbo_name());
        envSchema.setCatalogName(schema.getCatalog_name());

        //dbname
        EnvConnection envConnection = new EnvConnection();
        envConnection.setEnvironment(connection.getEnvironment());
        envConnection.setDbName(connection.getDb_name());
        envConnection.setConnectionId(connection.getUnique_key());
        envConnection.setInstanceName(connection.getInstance_name());
        envConnection.setEntityName(connection.getEntity());
        envConnection.setHostName(connection.getIp());
        envConnection.setHostPort(connection.getPort());
        envConnection.setConnectUser(connection.getUsername());
        envConnection.setServiceName(connection.getService_name());
        envConnection.setDatabaseType(connection.getDb_type());
        envConnection.setCatalogName(schema.getCatalog_name());
        //有缓存
        return paDBConvertServiceImpl.getDatabaseName(envConnection, envSchema);
    }

    @Override
    public List<ReturnTableColumnDto> fetchDBTableColumnList(PaTableColumnMessage message) throws Exception {
        //离线数据，当天有效， 定时任务会每天凌晨清理一遍数据
        PaDbTableField table = paTableFieldService.read(message.getConnectionId(), message.getSchemaId(), message.getTableName());
        if (null != table) {
            return gson.fromJson(table.getFields(), new TypeToken<List<ReturnTableColumnDto>>() {
            }.getType());
        }

        User user = userService.get(message.getUserId());
        DatabaseConnection connection = databaseConnectionService.get(message.getConnectionId());
        Schema schema = schemaService.get(message.getSchemaId());
        //dbInfo
        AuthenticationSdmpDatabaseDto authDto = new AuthenticationSdmpDatabaseDto();
        authDto.setHostName(connection.getIp());
        authDto.setDatabaseType(connection.getDb_type());
        authDto.setEntityName(List.of(DatabaseType.ORACLE.getValue()).contains(connection.getDb_type()) ? connection.getDb_name() : connection.getEntity());
        authDto.setHostPort(connection.getPort());
        //
        String dbName = this.getEnvDatabaseName(connection, schema);
        authDto.setDatabaseName(dbName);

        //columnList
        DataResultColumnDto columnDto = new DataResultColumnDto();
        columnDto.setSchemaName(schema.getSchema_name());
        columnDto.setColumnName("*");
        columnDto.setTableName(message.getTableName());
        columnDto.setInFunction(false);
        DataResultColumnDto[][] dataResultColumnDtoList = {
                {columnDto}
        };

        //operator user
        SdmpUserDesensDto sdmpUserDesensDto = new SdmpUserDesensDto();
        sdmpUserDesensDto.setUsername(user.getUsername().toUpperCase(Locale.ROOT));
        sdmpUserDesensDto.setPlain(true);

        log.info("SDK-Param: authDto:" + gson.toJson(authDto));
        log.info("SDK-Param: sdmpUserDesensDto:" + gson.toJson(sdmpUserDesensDto));
        log.info("SDK-Param: dataResultColumnDto:" + gson.toJson(dataResultColumnDtoList));

        //sdk param
        dataResultService.initSdk(authDto, dataResultColumnDtoList, sdmpUserDesensDto);
        List<ReturnTableColumnDto> returnTableColumnList = dataResultService.fetchTableColumnList();
        if (ObjectUtils.isEmpty(returnTableColumnList)) {
            return null;
        }
        //获取到数据记录离线，24h有效
        PaDbTableField dbTabField = new PaDbTableField();
        dbTabField.setConnectionId(message.getConnectionId());
        dbTabField.setSchemaId(message.getSchemaId());
        dbTabField.setTableName(message.getTableName());
        dbTabField.setFields(gson.toJson(returnTableColumnList));
        paTableFieldService.save(dbTabField);
        return returnTableColumnList;
    }

}
