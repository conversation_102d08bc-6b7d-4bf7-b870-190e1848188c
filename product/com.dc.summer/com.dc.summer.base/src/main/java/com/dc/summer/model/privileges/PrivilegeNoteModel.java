package com.dc.summer.model.privileges;

import com.dc.repository.mysql.model.DcDbResource;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.service.sql.WebSQLContextInfo;
import lombok.Data;

@Data
public class PrivilegeNoteModel {

    private PrivilegeNoteModel() {
    }

    private DcDbResource resource;

    private String schemaName;

    private String userName;

    private boolean isSuccess;

    private boolean isSyncJob;

    private PrivilegeModel privilegeModel;

    private WebSQLContextInfo webSQLContextInfo;

    private String privilegeExpire;

    private WebSQLQueryResult webSQLQueryResult;


    public static PrivilegeNoteModel build(WebSQLContextInfo webSQLContextInfo,
                                           PrivilegeModel privilegeModel,
                                           WebSQLQueryResult webSQLQueryResult,
                                           String privilegeExpire,
                                           boolean isSuccess,
                                           boolean isSyncJob
    ) {
        ConnectionConfiguration configuration = webSQLContextInfo.getConfiguration();

        PrivilegeNoteModel privilegeNoteModel = new PrivilegeNoteModel();
        privilegeNoteModel.webSQLContextInfo = webSQLContextInfo;
        privilegeNoteModel.isSuccess = isSuccess;
        privilegeNoteModel.isSyncJob = isSyncJob;
        privilegeNoteModel.userName = configuration.getUserName();
        privilegeNoteModel.schemaName = configuration.getSchemaName();
        privilegeNoteModel.privilegeExpire = privilegeExpire;
        privilegeNoteModel.privilegeModel = privilegeModel;
        privilegeNoteModel.webSQLQueryResult = webSQLQueryResult;

        return privilegeNoteModel;
    }

}
