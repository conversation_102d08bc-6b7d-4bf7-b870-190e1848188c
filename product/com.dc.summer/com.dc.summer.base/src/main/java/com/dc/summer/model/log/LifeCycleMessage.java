package com.dc.summer.model.log;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
@ApiModel("生命周期")
public class LifeCycleMessage {

    @ApiModelProperty(value = "窗口id")
    private String windowId;

    @ApiModelProperty(value = "操作类型 - 1.打开连接操作、2.关闭连接操作、3.切换事务模式、4.首选项变更", example = "1")
    private Integer operationType;

    @ApiModelProperty(value = "操作内容", example = "打开连接")
    private String operationContent;

    @ApiModelProperty(value = "消息", example = "成功", hidden = true)
    private String message;

    @ApiModelProperty(value = "自动提交", example = "true")
    private Boolean autoCommit;

    @ApiModelProperty(value = "进程ID", hidden = true)
    private String sessionId;

    @ApiModelProperty(value = "首选项", example = "auto_select_sql:0,use_native_format:0,date_format:yyyy-MM+dd,date_time_format: yyyy-MM-dd HH:mm:ss.SSS,time_format: HH:mm:ss")
    private Map<String, Object> preferences;

    @ApiModelProperty(value = "创建时间", hidden = true)
    private Long gmtCreate;

    @ApiModelProperty(value = "修改时间", hidden = true)
    private Long gmtModified;

    public LifeCycleMessage() {
        Date date = new Date();
        this.gmtCreate = date.getTime();
        this.gmtModified = date.getTime();
    }

    @JsonIgnore
    public Boolean getAutoConnect() {
        return preferences != null ? (Boolean) preferences.get("auto_connect") : null;
    }

    @JsonIgnore
    public void setAutoConnect(Boolean autoConnect) {
        if (autoConnect != null && preferences != null) {
            preferences.put("auto_connect", autoConnect);
        }
    }

    @JsonIgnore
    public void setShortConnect(Boolean shortConnect) {
        if (shortConnect != null && preferences != null) {
            preferences.put("short_connect", shortConnect);
        }
    }

}
