package com.dc.summer.model.privileges;

import com.dc.repository.mysql.mapper.DcDbResourceAccountInfoMapper;
import com.dc.repository.mysql.mapper.DcDbResourceAccountMapper;
import com.dc.repository.mysql.model.DcDbResource;
import com.dc.repository.mysql.model.DcDbResourceAccount;
import com.dc.repository.mysql.model.DcDbResourceAccountInfo;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.summer.DBException;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.type.DatabaseType;
import com.dc.utils.CipherUtils;
import com.dc.utils.PasswordStrengthUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

public abstract class AbstractPrivilegeNote implements PrivilegeNote {

    private final DcDbResourceAccountInfoMapper dcDbResourceAccountInfoMapper;

    private final DcDbResourceAccountMapper dcDbResourceAccountMapper;

    protected AbstractPrivilegeNote(DcDbResourceAccountInfoMapper dcDbResourceAccountInfoMapper, DcDbResourceAccountMapper dcDbResourceAccountMapper) {
        this.dcDbResourceAccountInfoMapper = dcDbResourceAccountInfoMapper;
        this.dcDbResourceAccountMapper = dcDbResourceAccountMapper;
    }

    @Override
    public List<String> grant(PrivilegeNoteModel privilegeNoteModel) throws DBException {
        return List.of();
    }

    @Override
    public List<String> revoke(PrivilegeNoteModel privilegeNoteModel) {
        return List.of();
    }

    @Override
    public List<String> alterUser(PrivilegeNoteModel privilegeNoteModel) {
        return List.of();
    }

    @Override
    public void createUser(PrivilegeNoteModel privilegeNoteModel) {
        PrivilegeModel privilegeModel = privilegeNoteModel.getPrivilegeModel();
        DcDbResource resource = privilegeNoteModel.getResource();
        boolean isSuccess = privilegeNoteModel.isSuccess();
        WebSQLContextInfo webSQLContextInfo = privilegeNoteModel.getWebSQLContextInfo();

        if (!isSuccess) {
            return;
        }

        ConnectionConfiguration configuration = webSQLContextInfo.getConfiguration();
        WebSQLQueryResult webSQLQueryResult = privilegeNoteModel.getWebSQLQueryResult();

        DcDbResourceAccount dcDbResourceAccount = new DcDbResourceAccount();
        if (webSQLContextInfo.getConfiguration().getDatabaseType() == DatabaseType.ORACLE) {
            privilegeModel.setUserName(privilegeNoteModel.getUserName().toUpperCase(Locale.ROOT));
        }
        dcDbResourceAccount.setUsername(privilegeModel.getUserName());
        dcDbResourceAccount.setResourceId(resource.getUnique_key());
        dcDbResourceAccount.setVerified((short) 1);
        dcDbResourceAccount.setConnection(configuration.getConnectionDesc());
        dcDbResourceAccount.setPeriod(0);
        dcDbResourceAccount.setUserType((short) 2);
        dcDbResourceAccount.setConnectId(webSQLContextInfo.getConfiguration().getConnectionId());
        dcDbResourceAccount.setIsDelete(0);

        String uniqueKey = UUID.randomUUID().toString().replace("-", "");
        dcDbResourceAccount.setUniqueKey(uniqueKey);
        // 密码
        String pwd = privilegeModel.getPassword();
        dcDbResourceAccount.setPassword(CipherUtils.sm4encrypt(pwd));
        // 密码强度
        dcDbResourceAccount.setPasswordStrength((short) PasswordStrengthUtil.getPasswordStrength(pwd));

        dcDbResourceAccount.setUserSourceType((short) 2);
        // user id
        dcDbResourceAccount.setCreatorId(webSQLQueryResult.getSqlHistory().getUserId());

        dcDbResourceAccount.setGmtCreate(LocalDateTime.now());
        dcDbResourceAccount.setGmtModified(LocalDateTime.now());
        dcDbResourceAccount.setDbRole("Normal");
        dcDbResourceAccount.setPrivilegeSyncState(2);
        dcDbResourceAccount.setHost(privilegeModel.getHost());

        dcDbResourceAccount.setDriverId(resource.getDriver_id());

        dcDbResourceAccountMapper.insert(dcDbResourceAccount);


        DcDbResourceAccountInfo dcDbResourceAccountInfo = new DcDbResourceAccountInfo();
        dcDbResourceAccountInfo.setGmtCreate(LocalDateTime.now());
        dcDbResourceAccountInfo.setGmtModified(LocalDateTime.now());
        dcDbResourceAccountInfo.setIsDelete(0);
        dcDbResourceAccountInfo.setUserSource(1);
        dcDbResourceAccountInfo.setUserId(webSQLQueryResult.getSqlHistory().getUserId());
        dcDbResourceAccountInfo.setAccountId(uniqueKey);

        dcDbResourceAccountInfoMapper.insert(dcDbResourceAccountInfo);
    }
}
