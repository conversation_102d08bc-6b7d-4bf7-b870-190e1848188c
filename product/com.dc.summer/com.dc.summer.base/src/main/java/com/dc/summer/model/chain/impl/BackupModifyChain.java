package com.dc.summer.model.chain.impl;

import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.*;
import com.dc.repository.mysql.component.MysqlMapper;
import com.dc.summer.DBException;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.chain.PreCheckBackupChain;
import com.dc.summer.model.counter.SqlSessionCounter;
import com.dc.springboot.core.model.recovery.BackupModel;
import com.dc.springboot.core.model.recovery.SqlData;
import com.dc.springboot.core.model.recovery.UpdateSetClauseModel;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.model.type.BackupSqlStateType;
import com.dc.summer.model.type.BackupWarningType;
import com.dc.summer.model.utils.SqlCheckUtils;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.summer.model.type.UpdateSetValueType;
import com.dc.summer.model.utils.BackupUtils;
import com.dc.summer.model.impl.struct.SimpleContainer;
import com.dc.summer.service.receiver.ResultObjectDataReceiver;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLQueryType;
import com.dc.utils.bean.CloneUtils;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.jetbrains.annotations.NotNull;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.*;

@Slf4j
public class BackupModifyChain extends PreCheckBackupChain {


    private final Gson gson = new GsonBuilder().serializeNulls().create();

    private String relationTableName;

    private String dataTableName;

    private final static List<SQLQueryType> BACKUP_TYPES = Arrays.asList(
            SQLQueryType.UPDATE,
            SQLQueryType.DELETE,
            SQLQueryType.TRUNCATE);

    public BackupModifyChain(DBRProgressMonitor monitor,
                             WebSQLContextInfo contextInfo,
                             String operation,
                             BackupModel backupModel,
                             String userId,
                             SqlSessionTemplate sqlSessionTemplate,
                             String originSql) {
        super(monitor, contextInfo, operation, backupModel, userId, sqlSessionTemplate, originSql);
    }

    @Override
    public List<SQLQueryType> getBackupTypes() {
        return BACKUP_TYPES;
    }

    @Override
    public boolean proceed(WebSQLQueryResult queryResult) {

        final boolean result = true;

        if (backupModel.isShowReason()) {
            queryResult.setBackupWarning("解析异常，无法获取备份数据，未备份!");
        }

        if (!allowBackup) {
            log.debug("当前语句不需要更新备份！sql = " + originSql);
            return result;
        }
        int limitNum = backupModel.getBackupDataRowLimit();
        if (contextInfo.getParamsConfig() != null && StringUtils.isNotBlank(contextInfo.getParamsConfig().getBackupDataRowLimit())) {
            try {
                int dataLimit = Integer.parseInt(contextInfo.getParamsConfig().getBackupDataRowLimit());
                if (dataLimit > 0) {
                    limitNum = dataLimit;
                }
            } catch (Exception e) {
                limitNum = backupModel.getBackupDataRowLimit();
                log.error("转成数字失败");
            }
        }

        boolean hasData = false;
        List<Map<String, Object>> objects = null;
        SimpleContainer simpleContainer = new SimpleContainer(dataSource);
        if (!backupModel.isExceedsRowLimit()) {
            objects = new ArrayList<>();
            try (ResultObjectDataReceiver resultObjectDataReceiver = new ResultObjectDataReceiver(simpleContainer)) {
                DBExecUtils.tryExecuteRecover(executionContext, dataSource, param -> {
                    try {
                        DBExecUtils.executeQuery(monitor, executionContext, "Backup Select", backupModel.getSql(), resultObjectDataReceiver, true);
                    } catch (DBException e) {
                        throw new InvocationTargetException(e);
                    }
                }, contextInfo.recoverBefore(), contextInfo.recoverAfter());
                hasData = resultObjectDataReceiver.isHasRow();
                objects.addAll(resultObjectDataReceiver.getObjects());
            } catch (DBException e) {
                log.error("执行备份SQL失败！", e);
            }
            if (!hasData) {
                log.debug("当前备份语句未查到需要备份的数据！ sql = " + backupModel.getSql());
                queryResult.setBackupWarning(BackupWarningType.NODATA.getValue());
                return result;
            }
        } else {
            queryResult.setBackupWarning(String.format(BackupWarningType.ROW_LIMIT.getValue(), limitNum));
        }

        List<SqlFieldData> fields = new ArrayList<>();
        try {
            List<SqlFieldData> sqlFieldDataList = SqlCheckUtils.getTableColumnsOrderBy(
                    dataSource.getContainer().getConnectionConfiguration().getDatabaseType().getValue(),
                    backupModel.getSchemaName(),
                    backupModel.getBackupTableName(),
                    contextInfo,
                    monitor,
                    simpleContainer);
            fields.addAll(sqlFieldDataList);
        } catch (Exception e) {
            log.error("查询备份表结构失败！", e);
            queryResult.setBackupWarning(BackupWarningType.NODATA.getValue());
            return result;
        }

        //查询结果是字段
        if (CollectionUtils.isEmpty(fields)) {
            log.debug("未查询备份表结构！");
            queryResult.setBackupWarning(BackupWarningType.NODATA.getValue());
            return result;
        }

        for (SqlFieldData sqlFieldData : fields) {
            if (bigDataType.contains(sqlFieldData.getFieldType().toUpperCase(Locale.ROOT))) {
                log.debug("大字段表不支持误操作恢复");
                queryResult.setBackupWarning(BackupWarningType.NODATA.getValue());
                return result;
            }
        }

        try {

            SqlSession sqlSession = sqlSessionTemplate.getSqlSessionFactory().openSession(false);
            SqlSessionCounter.setSqlSession(sqlSession);
            RelationTableMapper relationTableMapper = sqlSession.getMapper(RelationTableMapper.class);
            DataTableMapper dataTableMapper = sqlSession.getMapper(DataTableMapper.class);

            sqlSession.getConnection().setAutoCommit(false);

            //对主键列名进行排序
            Collections.sort(primaryKeyColumns);

            RcTable rcTable = createRcTable(fields, sqlSession);

            RcSql rcSql = createRcSql(sqlSession, rcTable, transactionIndex, originSql, backupModel.isExceedsRowLimit(), limitNum);

            if (backupModel.isExceedsRowLimit()) {
                log.debug("超过行限制");
                queryResult.setBackupWarning(String.format(BackupWarningType.ROW_LIMIT.getValue(), limitNum));
                return result;
            }

            // TODO execute
            Connection connection = sqlSession.getConnection();
            String insertDataSql = BackupUtils.generateInsertDataSql(dataTableName, fields, operation, bigDataType);
            PreparedStatement statement = connection.prepareStatement(insertDataSql);
            for (Map<String, Object> object : objects) {

                List<String> pkValueList = new ArrayList<>();
                //判断是否发生主键变更
                List<String> pkValueUpdateList = new ArrayList<>();
                List<UpdateSetClauseModel> updateSetClauseModelList = backupModel.getPrimaryKeyUpdateValue();

                HashMap<String, UpdateSetClauseModel> set = new HashMap<>(); // 记录set 子句中的要更新主键
                if (updateSetClauseModelList != null) {
                    for (UpdateSetClauseModel model : updateSetClauseModelList) {
                        set.put(model.getColumnName().toUpperCase(), model);
                    }
                }
                int cnt = 0;    // 要更新为null的主键的个数
                int i = 0;      // 主键的个数（联合主键）
                for (; i < primaryKeyColumns.size(); i++) {
                    pkValueList.add(object.get(primaryKeyColumns.get(i)).toString());
                    if (set.containsKey(primaryKeyColumns.get(i).toUpperCase())) {
                        UpdateSetClauseModel model = set.get(primaryKeyColumns.get(i).toUpperCase());
                        String value = set.get(primaryKeyColumns.get(i).toUpperCase()).getColumnValue();
                        if (UpdateSetValueType.object_name.getValue().equals(model.getColumnType())) {
                            value = getValueIgnoreCase(object, model.getColumnValue().toUpperCase());
                        }
                        pkValueUpdateList.add(value);
                        if (value.equalsIgnoreCase("null")) {
                            cnt++;
                        }
                    } else {
                        pkValueUpdateList.add(object.get(primaryKeyColumns.get(i)).toString());
                    }
                }
                if (i != 0 && cnt == i) {     // 如果所有主键都更新为null
                    log.debug("主键变更为 null，不做备份");
                    queryResult.setBackupWarning(BackupWarningType.NODATA.getValue());
                    return result;
                }

                RelationTable rootRelation = createRelationTable(relationTableMapper, transactionIndex, rcSql, pkValueList, pkValueUpdateList, updateSetClauseModelList);

                //查询data表中是否存在root数据，如果存在则不处理，不存在则新增一条数据
                DataTable dataTable = new DataTable();
                dataTable.setTableName(dataTableName);
                dataTable.setTransaction_index(transactionIndex);
                dataTable.setRow_id(rootRelation.getRow_id());
                Map<String, Object> map = dataTableMapper.getDataMap(dataTable);
                if (map == null || map.isEmpty()) {
                    List<SqlData> form_data = new ArrayList<>();
                    for (SqlFieldData sqlFieldData : fields) {
                        SqlData sqlData = new SqlData();
                        sqlData.setKey(sqlFieldData.getFieldName());
                        sqlData.setData_type(sqlFieldData.getFieldType());
                        Object value = object.get(sqlFieldData.getFieldName());
                        sqlData.setValue(value);
                        form_data.add(sqlData);
                    }

                    // TODO execute
                    if (form_data.size() > 0) {
                        try {
                            int parameterIndex;
                            for (parameterIndex = 0; parameterIndex < form_data.size(); parameterIndex++) {
                                if (form_data.get(parameterIndex).getValue() != null) {
                                    statement.setString(parameterIndex + 1, String.valueOf(form_data.get(parameterIndex).getValue()));
                                } else {
                                    statement.setObject(parameterIndex + 1, null);
                                }
                            }
                            statement.setObject(++parameterIndex, transactionIndex);
                            statement.setObject(++parameterIndex, rootRelation.getRow_id());
                        } catch (Exception e) {
                            log.error("BackupChain proceed error : ", e);
                        }

                        statement.addBatch();
                    }

                }
            }

            if (statement != null) {
                statement.executeBatch();
            }

        } catch (Exception e) {
            log.error("持久化备份失败！", e);
        }

        return result;
    }
    private static String getValueIgnoreCase(Map<String, Object> map, String targetKey) {
       if (map.get(targetKey) != null) {
           return map.get(targetKey).toString();
       }
       for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getKey().equalsIgnoreCase(targetKey)) {
                return entry.getValue().toString();
            }
       }
       return null;
    }

    @NotNull
    private RelationTable createRelationTable(RelationTableMapper relationTableMapper, Integer transactionIndex, RcSql rcSql, List<String> pkValueList, List<String> pkValueUpdateList, List<UpdateSetClauseModel> updateSetClauseModelList) {
        //插入relation关系表
        RelationTable relationTable = new RelationTable();
        relationTable.setRow_id(pkValueList.isEmpty() ? UUID.randomUUID().toString().replaceAll("-", "") : String.join(",", pkValueList));
        relationTable.setTableName(relationTableName);
        relationTable.setTransaction_index(transactionIndex);
        List<RelationTable> relationTableList = relationTableMapper.getRelationTableByRowId(relationTable);
        RelationTable rootRelation = new RelationTable();
        Long parentNodeId;
        relationTable.setIs_real_pk(CollectionUtils.isEmpty(primaryKeyColumns) ? 0 : 1);
        if (relationTableList == null || relationTableList.isEmpty()) {
            relationTable.setRc_sql_id(rcSql.getId());
            relationTableMapper.add(relationTable);
            MysqlMapper.INSTANCE.updateRelationTable(relationTable, rootRelation);
            parentNodeId = relationTable.getId();
        } else {
            //查询关系表根节点
            long root_id;
            RelationTable tempRelationTable = new RelationTable();
            //判断是根节点还是叶子节点
            if (relationTableList.get(0).getRoot_id() != null) {
                root_id = relationTableList.get(0).getRoot_id();
            } else {
                root_id = relationTableList.get(0).getId();
            }
            tempRelationTable.setId(root_id);
            tempRelationTable.setTableName(relationTableName);
            tempRelationTable.setTransaction_index(transactionIndex);
            rootRelation = relationTableMapper.getRelationTableById(tempRelationTable);
            //查询关系表当前row_id主节点
            RelationTable parentRelationTable = new RelationTable();
            parentRelationTable.setTableName(relationTableName);
            parentRelationTable.setRoot_id(rootRelation.getId());
            parentRelationTable.setTransaction_index(transactionIndex);
            List<RelationTable> parentRelationTableList = relationTableMapper.getRelationTableByRootId(parentRelationTable);
            parentNodeId = parentRelationTableList.get(0).getId();
        }
        //如果发生主键变更，则用新的row_id值
        if (updateSetClauseModelList != null && updateSetClauseModelList.size() > 0) {
            relationTable.setRow_id(String.join(",", pkValueUpdateList));
        }
        relationTable.setRc_sql_id(rcSql.getId());
        relationTable.setP_id(parentNodeId);
        relationTable.setRoot_id(rootRelation.getId());
        relationTableMapper.add(relationTable);
        return rootRelation;
    }

    @NotNull
    private RcSql createRcSql(SqlSession sqlSession, RcTable rcTable, Integer transactionIndex, String originSql, boolean exceedsRowLimit, int limitNum) throws DBException {

        RcSqlMapper rcSqlMapper = sqlSession.getMapper(RcSqlMapper.class);

        // SQL表
        RcSql rcSql = new RcSql();
        rcSql.setRc_table_id(rcTable.getId());
        rcSql.setSession_id(executionContext.getSessionId());
        rcSql.setOperation(operation);
        // TODO 修改结果集 SQL 需要拼接
        rcSql.setSql(originSql);
        rcSql.setOperator(userId);
        if (backupModel.getUpdateSetColumns() != null) {
            rcSql.setUpdate_columns(String.join(",", backupModel.getUpdateSetColumns()));
        }
        rcSql.setTransaction_index(transactionIndex);
        if (exceedsRowLimit) {
            rcSql.setState(BackupSqlStateType.OVERLIMIT.getCode());
            rcSql.setLog(String.format(BackupWarningType.ROW_LIMIT.getValue(), limitNum));
        } else {
            rcSql.setState(BackupSqlStateType.NORMAL.getCode());
        }
        rcSqlMapper.add(rcSql);
        return rcSql;
    }

    @NotNull
    private RcTable createRcTable(List<SqlFieldData> fields, SqlSession sqlSession) throws DBException {

        RcTableMapper rcTableMapper = sqlSession.getMapper(RcTableMapper.class);
        CreateTableMapper createTableMapper = sqlSession.getMapper(CreateTableMapper.class);

        Map<String, SqlFieldData> fieldDataMap = CloneUtils.transListToMap(fields, SqlFieldData::getFieldName, sqlFieldData -> sqlFieldData);

        //插入rc_table表
        RcTable rcTable = new RcTable();
        rcTable.setInstance_id(backupModel.getConnectId());
        rcTable.setInstance_name(backupModel.getInstanceName());
        rcTable.setSchema_id(backupModel.getSchemaId());
        rcTable.setSchema_name(backupModel.getSchemaName());
        rcTable.setTable_name(backupModel.getBackupTableName());
        List<RcTable> rcTableList = rcTableMapper.getRcTable(rcTable);
        String pkName = String.join(",", primaryKeyColumns);
        if (CollectionUtils.isEmpty(rcTableList)) {
            rcTable.setTable_structure(gson.toJson(fieldDataMap));
            rcTable.setPk_name(pkName);
            rcTableMapper.add(rcTable);
        } else {
            //判断是否表结构变更
            boolean isTableStructureChanged = true;
            for (int i = 0;i<rcTableList.size();i++) {
                rcTable = rcTableList.get(i);
                Map<String, SqlFieldData> mysqlFieldDataMap = gson.fromJson(rcTable.getTable_structure(), new TypeToken<Map<String, SqlFieldData>>() {
                }.getType());
                if (!SqlCheckUtils.isTableStructureChanged(mysqlFieldDataMap, fieldDataMap)) {
                    isTableStructureChanged = false;
                    break;
                }
            }
            if (isTableStructureChanged || !pkName.equals(rcTable.getPk_name())) {
                rcTable.setTable_structure(gson.toJson(fieldDataMap));
                rcTable.setPk_name(pkName);
                rcTableMapper.add(rcTable);
            }
        }

        //创建data表和关系表
        relationTableName = String.format("rc_rel_%s_%s", rcTable.getId(), executionContext.getSessionId());
        String createRelationTableSql = BackupUtils.createRelationTable(relationTableName);
        createTableMapper.createTable(createRelationTableSql);
        dataTableName = String.format("rc_%s_%s", rcTable.getId(), executionContext.getSessionId());
        String createDataTableSql = BackupUtils.createDataTableSql(dataTableName, fields, bigDataType);
        createTableMapper.createTable(createDataTableSql);
        return rcTable;
    }


}
