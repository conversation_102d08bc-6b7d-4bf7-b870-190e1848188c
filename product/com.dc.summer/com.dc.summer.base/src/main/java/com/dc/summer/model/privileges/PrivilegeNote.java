package com.dc.summer.model.privileges;

import com.dc.repository.mysql.mapper.DcDbResourceAccountInfoMapper;
import com.dc.repository.mysql.mapper.DcDbResourceAccountMapper;
import com.dc.repository.mysql.model.DcDbResource;
import com.dc.repository.mysql.model.DcDbResourceAccount;
import com.dc.repository.mysql.model.DcDbResourceAccountInfo;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.summer.DBException;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.type.DatabaseType;
import com.dc.utils.CipherUtils;
import com.dc.utils.PasswordStrengthUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

public interface PrivilegeNote {

    List<String> grant(PrivilegeNoteModel privilegeNoteModel) throws DBException;

    List<String> revoke(PrivilegeNoteModel privilegeNoteModel);

    List<String> alterUser(PrivilegeNoteModel privilegeNoteModel);

    void createUser(PrivilegeNoteModel privilegeNoteModel);


    static PrivilegeNote getPrivilegeNote(DatabaseType databaseType) {
        switch (databaseType) {
            case ORACLE:
                return Resource.getBean(OraclePrivilegeNote.class);
            case MYSQL:
                return Resource.getBean(MySqlPrivilegeNote.class);
        }

        throw new IllegalArgumentException("Unsupported database type: " + databaseType);
    }
}
