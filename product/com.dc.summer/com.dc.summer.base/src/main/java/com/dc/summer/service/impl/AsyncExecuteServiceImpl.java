package com.dc.summer.service.impl;

import com.dc.config.ApiConfig;
import com.dc.repository.mysql.mapper.DcAsyncQueryMapper;
import com.dc.repository.mysql.model.DcAsyncQuery;
import com.dc.repository.redis.model.EnvUser;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.ConnectionTokenMessage;
import com.dc.springboot.core.model.execution.ValidExecuteModel;
import com.dc.springboot.core.model.execution.VisitFrequencyDataModel;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.result.WebSQLExecuteInfo;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.springboot.core.model.sensitive.DataDesensitizeProcessor;
import com.dc.springboot.core.model.sensitive.ExportDesensitizeProcessor;
import com.dc.springboot.core.model.sensitive.GradedClassifiedModel;
import com.dc.springboot.core.model.sensitive.SqlDesensitization;
import com.dc.springboot.core.model.type.*;
import com.dc.summer.ModelPreferences;
import com.dc.summer.component.SummerMapper;
import com.dc.summer.config.SummerConfig;
import com.dc.summer.data.transfer.registry.DataTransferRegistry;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.chain.impl.StaticEnvChain;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.data.message.*;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.exec.DBCStatementType;
import com.dc.summer.model.impl.data.PrimaryKeyProcessor;
import com.dc.summer.model.impl.local.LocalSession;
import com.dc.summer.model.sql.SQLQuery;
import com.dc.summer.model.thread.SummerThreadScheduler;
import com.dc.summer.model.type.AsyncExecuteStatus;
import com.dc.summer.model.type.ExecuteType;
import com.dc.summer.model.type.LineDelimiterType;
import com.dc.summer.registry.center.Global;
import com.dc.summer.service.AsyncExecuteService;
import com.dc.summer.service.MessageService;
import com.dc.summer.service.result.WorkOrderTransferResult;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.summer.service.transfer.WebDataTransferHelper;
import com.dc.summer.service.transfer.WebDataTransferName;
import com.dc.type.DatabaseType;
import com.dc.utils.http.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Service
@Slf4j
public class AsyncExecuteServiceImpl implements AsyncExecuteService {

    @Resource
    private SummerConfig config;

    @Resource
    private DcAsyncQueryMapper dcAsyncQueryMapper;

    @Resource
    private MessageService messageService;

    @Resource
    private SummerThreadScheduler scheduler;


    private final SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd");


    @Override
    public WebAsyncTaskInfo asyncExecuteTask(AsyncExecuteMessage message) {
        ConnectionConfig connectionConfig = SummerMapper.INSTANCE.toConnectionTokenMessage(message.getConnection());
        ConnectionTokenMessage connectionTokenMessage = new ConnectionTokenMessage();
        connectionTokenMessage.setConnectionConfig(connectionConfig);
        connectionTokenMessage.setTokenConfig(message.getTokenConfig());
        connectionTokenMessage.setExecuteEvent(message.getExecuteEvent());
        connectionTokenMessage.setToken(message.getToken());
        WebSQLContextInfo contextInfo = WebSQLContextInfo.openExecuteContext(connectionTokenMessage);

        contextInfo.setConnection(message.getConnection());
        contextInfo.setUser(message.getUser());
        contextInfo.setSchema(message.getSchema());
        contextInfo.setParamsConfig(message.getParams());

        AtomicBoolean firstExecute = new AtomicBoolean(true);

        return contextInfo.createAsyncTask("async execute", (taskId, monitor) -> {
            DcAsyncQuery dcAsyncQuery = new DcAsyncQuery();
            dcAsyncQuery.setId(message.getId());
            dcAsyncQuery.setToken(message.getToken());
            dcAsyncQuery.setExecuteStatus(AsyncExecuteStatus.EXECUTING.getValue());

            if (contextInfo.isInterrupted()) {
                firstExecute.set(false);
                dcAsyncQuery.setExecuteStatus(AsyncExecuteStatus.FAILURE.getValue());
                dcAsyncQuery.setExecuteLog("用户中断执行");
                return;
            }

            if (dcAsyncQueryMapper.updateStatus(message.getId(), AsyncExecuteStatus.EXECUTING.getValue()) == 0) {
                log.debug("更新状态失败，id: {}", message.getId());
            }

            ValidExecuteModel executeModel = message.getBatchExecuteModels().get(0);
            executeModel.setOrigin(OriginType.ASYNC_EXECUTE.getValue());
            List<ExecuteEvent> executeEvents = new ArrayList<>();
            executeEvents.add(message.getExecuteEvent());
            StaticEnvChain.Builder builder = new StaticEnvChain.Builder(new VisitFrequencyDataModel(), executeEvents, contextInfo);
            builder.buildChain(executeModel, "").proceed(new WebSQLQueryResult());

            SqlExportMessage sqlExportMessage = message.getSqlExportMessage();
            sqlExportMessage.setExportType(ExportType.CSV);
            sqlExportMessage.setFileCharset("UTF-8");
            sqlExportMessage.setTextIdentifier(TextIdentifierType.DOUBLE_QUOTE);
            sqlExportMessage.setLineDelimiter(LineDelimiterType.CRLF);
            sqlExportMessage.setColumnDelimiter(ColumnDelimiterType.COMMA);

            SqlDesensitization sqlDesensitization = executeModel.getSqlDesensitization();
            GradedClassifiedModel gradedClassifiedModel = sqlDesensitization.getGradedClassifiedModel();
            gradedClassifiedModel.setDesens(message.getSqlExportMessage().isExportDesensitize());
            gradedClassifiedModel.setBackstop(message.getSqlExportMessage().isExportDesensitize());

            DataDesensitizeProcessor desensitizeData = new DataDesensitizeProcessor(
                    contextInfo.getDataSource(),
                    sqlDesensitization.getEnableDesensitizeType(),
                    sqlDesensitization.getDataMasks(),
                    sqlDesensitization.getSymbol(),
                    sqlDesensitization.getNodeModels(),
                    gradedClassifiedModel,
                    null,null);

            if (message.getTokenConfig().getRule_export_desensitization() == 1) {
                desensitizeData = new ExportDesensitizeProcessor(desensitizeData);
            }

            if (dcAsyncQueryMapper.updateRecord(dcAsyncQuery) == 0) {
                log.debug("更新状态失败，id: {}", message.getId());
            }


            WorkOrderTransferResult orderTransferResult = new WorkOrderTransferResult();
            WebSQLQueryResult queryResult = new WebSQLQueryResult();
            try {
                long total = executeModel.getRowsLimit();
                if (total == 0) {
                    total = executeModel.getPageSize();
                }

                if (executeModel.isQueryDataSizeControl()) {
                    contextInfo.getDataSource().getContainer().getPreferenceStore().setValue(ModelPreferences.RESULT_SET_MAX_ROWS_USE_SQL, true);
                    try (DBCStatement virtualStatement = DBUtils.makeStatement(null, new LocalSession(contextInfo.getDataSource()),
                            DBCStatementType.LOCAL, new SQLQuery(contextInfo.getDataSource(), executeModel.getSqlRecord().getSql()), executeModel.getOffset(), total)) {
                        executeModel.getSqlRecord().setSql(virtualStatement.getQueryString());
                    } catch (Exception ignored) {
                        // nothing to do here
                    }
                } else {
                    contextInfo.getDataSource().getContainer().getPreferenceStore().setValue(ModelPreferences.RESULT_SET_MAX_ROWS_USE_SQL, false);
                }

                if (!Objects.equals(connectionConfig.getDatabaseType(), DatabaseType.ORACLE.getValue())) {
                    int limit = 150;
                    if (limit > executeModel.getSqlRecord().getSql().length()) {
                        limit = executeModel.getSqlRecord().getSql().length();
                    }
                    dcAsyncQuery.setShowSql(executeModel.getSqlRecord().getSql().substring(0, limit));
                    dcAsyncQuery.setExecuteSql(executeModel.getSqlRecord().getSql());

                    if (dcAsyncQueryMapper.updateRecord(dcAsyncQuery) == 0) {
                        log.debug("更新状态失败，id: {}", message.getId());
                    }
                }

                orderTransferResult = contextInfo.getTransfer().exportDataBySql(
                        monitor,
                        DataTransferRegistry.getInstance().getProcessor(ExportType.CSV.getProcessorFullId()),
                        sqlExportMessage,
                        new WebDataTransferName.ZipName(),
                        new WebDataTransferName.SerialName(),
                        false,
                        true,
                        executeModel.getSql(),
                        desensitizeData,
                        new PrimaryKeyProcessor(),
                        total, message.getTokenConfig().getConsole(), message.getTokenConfig().getRule_export_desensitization(), message.getTokenConfig().getDbName(), true,executeModel.getSqlFieldDataList());

                if (orderTransferResult.getError() == null) {
                    String uploadPath = WebDataTransferHelper.toUpload(
                            config.getPath().getDcBackend(),
                            ApiConfig.UPLOAD.getPath(),
                            orderTransferResult.getFile(),
                            message.getUser().getUserId());
                    queryResult = orderTransferResult.getQueryResult();
                    dcAsyncQuery.setExecuteResult(uploadPath);
                    dcAsyncQuery.setExecuteMetadata(JSON.toJSONString(queryResult));
                    dcAsyncQuery.setExecuteStatus(AsyncExecuteStatus.SUCCESS.getValue());
                    dcAsyncQuery.setExecuteTime(new Date());
                }
            } catch (Exception e) {
                dcAsyncQuery.setExecuteStatus(AsyncExecuteStatus.FAILURE.getValue());
                dcAsyncQuery.setExecuteLog(e.getMessage());
            } finally {

                try {
                    if (orderTransferResult.getFile() != null) {
                        orderTransferResult.getFile().delete();
                    }
                } catch (Exception e) {
                    log.warn("删除临时文件失败 {}", e.getMessage());
                }

                executeModel.getSqlRecord().setRecordStatus(SqlExecuteStatus.SUCCESS.getValue());
                if (!Objects.equals(connectionConfig.getDatabaseType(), DatabaseType.ORACLE.getValue())) {
                    int limit = 150;
                    if (limit > executeModel.getSqlRecord().getSql().length()) {
                        limit = executeModel.getSqlRecord().getSql().length();
                    }
                    dcAsyncQuery.setShowSql(executeModel.getSqlRecord().getSql().substring(0, limit));
                    dcAsyncQuery.setExecuteSql(executeModel.getSqlRecord().getSql());
                }
                queryResult.setSqlHistory(executeModel.getSqlHistory());
                queryResult.setSqlRecord(executeModel.getSqlRecord());
                queryResult.setStatus(AsyncExecuteStatus.FAILURE.getValue());
                queryResult.getSqlRecord().setIsSuccess(true);
                queryResult.setSensitiveAuthDetailList(executeModel.getSensitiveAuthDetailList());
                if (orderTransferResult.getError() != null) {
                    queryResult.setMessage(orderTransferResult.getError().getMessage());
                    queryResult.getSqlRecord().setMessage(orderTransferResult.getError().getMessage());
                    queryResult.getSqlRecord().setRecordStatus(SqlExecuteStatus.EXEC_ERROR.getValue());
                    queryResult.setStatus(contextInfo.isInterrupted() ? SqlExecuteStatus.USER_ABORT.getValue() : SqlExecuteStatus.EXEC_ERROR.getValue());
                    dcAsyncQuery.setExecuteLog(orderTransferResult.getError().getMessage());
                    dcAsyncQuery.setExecuteStatus(AsyncExecuteStatus.FAILURE.getValue());
                } else if (!StringUtils.isBlank(queryResult.getMessage())) {
                    dcAsyncQuery.setExecuteLog(queryResult.getMessage());
                }
                if (dcAsyncQueryMapper.updateRecord(dcAsyncQuery) == 0) {
                    log.debug("更新状态失败，id: {}", message.getId());
                }
                // 审计
                try {
                    WebSQLExecuteInfo executeInfo = new WebSQLExecuteInfo().addQueryResults(List.of(queryResult));
                    if (firstExecute.get()) {
                        if (ConnectionPatternType.of(executeModel.getSqlHistory().getConnectionPattern()).existsSecurityCollaboration()) {
                            int securityRuleSetId = contextInfo.getConnection().getSecurityRuleSetId();
                            BatchAsyncExecuteMessage batchAsyncExecuteMessage = new BatchAsyncExecuteMessage();
                            batchAsyncExecuteMessage.setExecuteEvents(List.of(message.getExecuteEvent()));
                            batchAsyncExecuteMessage.setBatchExecuteModels(List.of(executeModel));
                            messageService.sendAlertMessageList(batchAsyncExecuteMessage, executeInfo, securityRuleSetId);
                        }
                        messageService.printLogMessage(contextInfo, executeInfo.getQueryResults(), message.getToken(), taskId);
                    }
                } catch (Exception e) {
                    log.warn("审计失败", e);
                } finally {
                    firstExecute.set(false);
                }

                // close session
                WebSQLContextInfo.recordClosed(message.getToken());
                WebSQLContextInfo.closeContext(message.getToken());
            }

        });
    }

    @Override
    public void asyncExecuteExport(AsyncExecuteExportMessage message) {
        scheduler.exec(ExecuteType.ASYNC_EXECUTE, () -> {
            DcAsyncQuery dcQuery = dcAsyncQueryMapper.getDcAsyncQueryById(message.getId());
            String url = config.getPath().getDcBackend() + ApiConfig.DOWNLOAD.getPath() + URLEncoder.encode(dcQuery.getExecuteResult(), Charset.defaultCharset());
            url = FileUtil.buildDownloadUrl(url, dcQuery.getExecuteResult());
            String dirPath = Global.getDOWNLOAD() + formatter.format(new Date()) + "/" + Objects.hashCode(System.currentTimeMillis() + dcQuery.getExecuteResult());
            String filePath = FileUtil.downloadFromUrl(url, dirPath, "");

            String excelPath = Global.getDOWNLOAD() + formatter.format(new Date()) + "/" + UUID.randomUUID().toString().replace("-", "") + ".xlsx";

            File csv = new File(filePath);
            File excel = new File(excelPath);

            try {
                convert(csv, excel, message.getLimit());
                String uploadPath = WebDataTransferHelper.toUpload(
                        config.getPath().getDcBackend(),
                        ApiConfig.UPLOAD.getPath(),
                        excel,
                        "");
                DcAsyncQuery dcAsyncQuery = new DcAsyncQuery();
                dcAsyncQuery.setId(dcQuery.getId());
                dcAsyncQuery.setDownloadResult(uploadPath);
                dcAsyncQueryMapper.updateRecord(dcAsyncQuery);
            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                try {
                    Files.delete(Paths.get(filePath));
                } catch (Exception e) {
                    log.error("删除临时文件失败！", e);
                }

                try {
                    Files.delete(Paths.get(excelPath));
                } catch (Exception e) {
                    log.error("删除临时文件失败！", e);
                }
            }
        });
    }


    public void convert(File csvFile, File excelFile, int limit) throws IOException {
        try (
                Reader reader = new BufferedReader(removeUTF8BOM(new FileInputStream(csvFile)));
                CSVParser parser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader().withQuote('"'));
                SXSSFWorkbook workbook = new SXSSFWorkbook(100); // 保留100行在内存中
                FileOutputStream out = new FileOutputStream(excelFile)
        ) {
            Sheet sheet = workbook.createSheet("Sheet1");

            int rowIndex = 0;

            // 写入表头
            Row headerRow = sheet.createRow(rowIndex++);
            int headerColIndex = 0;
            for (String header : parser.getHeaderNames()) {
                Cell cell = headerRow.createCell(headerColIndex++);
                cell.setCellValue(stripBom(header));
            }

            int cnt = 0;
            // 写入数据行
            for (CSVRecord record : parser) {
                Row row = sheet.createRow(rowIndex++);
                for (int i = 0; i < record.size(); i++) {
                    Cell cell = row.createCell(i);
                    cell.setCellValue(record.get(i));
                }

                cnt++;

                if (limit > 0 && cnt == limit) {
                    break;
                }
            }

            workbook.write(out);
            workbook.dispose(); // 清理临时文件
        }
    }

    // 用于剥离 UTF-8 BOM（EF BB BF）
    private Reader removeUTF8BOM(InputStream in) throws IOException {
        PushbackInputStream pushbackInputStream = new PushbackInputStream(in, 3);
        byte[] bom = new byte[3];
        int read = pushbackInputStream.read(bom, 0, 3);

        if (read == 3) {
            if (!(bom[0] == (byte) 0xEF && bom[1] == (byte) 0xBB && bom[2] == (byte) 0xBF)) {
                pushbackInputStream.unread(bom, 0, 3);
            }
        } else if (read > 0) {
            pushbackInputStream.unread(bom, 0, read);
        }

        return new InputStreamReader(pushbackInputStream, StandardCharsets.UTF_8);
    }

    // 保险处理（防止 BOM 存在于字段中）
    private String stripBom(String value) {
        if (value != null && value.startsWith("\uFEFF")) {
            return value.substring(1);
        }
        return value;
    }
}
