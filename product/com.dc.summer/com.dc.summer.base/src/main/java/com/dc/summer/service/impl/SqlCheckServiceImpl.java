package com.dc.summer.service.impl;

import com.dc.repository.mysql.mapper.*;
import com.dc.repository.redis.service.RedisService;
import com.dc.springboot.core.client.ParserSqlClient;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.springboot.core.model.message.MessageConstants;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.recovery.RecoveryDataSql;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.sensitive.DataDesensitizeProcessor;
import com.dc.springboot.core.model.sensitive.DataMask;
import com.dc.springboot.core.model.type.ParserExecuteType;
import com.dc.summer.config.SummerConfig;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.SqlCheckParam;
import com.dc.springboot.core.model.recovery.SqlData;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.model.data.model.CheckModel;
import com.dc.summer.model.type.BackupSqlStateType;
import com.dc.summer.model.type.SqlCheckStateType;
import com.dc.summer.model.utils.SqlCheckUtils;
import com.dc.summer.parser.sql.model.TableColumnModel;
import com.dc.summer.parser.utils.CommonUtil;
import com.dc.summer.service.SqlCheckService;
import com.dc.summer.model.impl.struct.SimpleContainer;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.repository.mysql.model.DataTable;
import com.dc.repository.mysql.model.RcSql;
import com.dc.repository.mysql.model.RcTable;
import com.dc.repository.mysql.model.RelationTable;
import com.dc.type.DatabaseType;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class SqlCheckServiceImpl implements SqlCheckService {

    private static final Integer EXPIRE_TIME = 60; // 单位秒

    private final Gson gson = new GsonBuilder().serializeNulls().create();

    @Resource
    private RcSqlMapper rcSqlMapper;

    @Resource
    private RcTableMapper rcTableMapper;

    @Resource
    private RcBatchMapper rcBatchMapper;

    @Resource
    private RelationTableMapper relationTableMapper;

    @Resource
    private DataTableMapper dataTableMapper;

    @Resource
    private RedisService redisService;

    @Resource
    private SummerConfig summerConfig;

    @Resource
    private ParserSqlClient parserSqlClient;

    @Override
    public void checkTableRecords(SqlCheckParam sqlCheckParam) {

        String redisKey = sqlCheckParam.getRedis_key();
        List<Long> sqlList = sqlCheckParam.getSqlList();
        Long batchId = sqlCheckParam.getBatchId();
        WebSQLContextInfo contextInfo = sqlCheckParam.getContextInfo();
        DBRProgressMonitor monitor = sqlCheckParam.getMonitor();
        List<String> wrongSchemaIdList = sqlCheckParam.getWrongSchemaIdList() == null ? new ArrayList<>() : sqlCheckParam.getWrongSchemaIdList();

        try {

            SimpleContainer simpleContainer = new SimpleContainer(contextInfo.getDataSource());

            // 获取数据库类型
            Integer dbType = rcBatchMapper.getRcBatchById(batchId).getDb_type();

            // 获取每条sql记录
            List<RcSql> rcSqlList = this.getRCSqlList(sqlList, batchId);

            // 每个表对应的sql记录
            Map<Long, List<RcSql>> rcTableIdRcSqlMap = this.buildRcTableIdRcSqlMap(rcSqlList);

            // 获取sessionId和transactionIdx
            String sessionId = this.getSessionId(rcSqlList);
            Integer transactionIdx = this.getTransactionIdx(rcSqlList);

            // 获取需要check的表信息
            List<RcTable> rcTableListByIds = this.getRCTableListByIds(rcSqlList, redisKey);

            // 检查表记录变化
            int ctn = 0;
            boolean isEnd = false;
            for (RcTable rcTable : rcTableListByIds) {
                if (contextInfo.isClosed() || contextInfo.isInterrupted()) {
                    break;
                }
                ctn++;
                String schemaName = SqlCheckUtils.replace(rcTable.getSchema_name());
                String tableName = SqlCheckUtils.replace(rcTable.getTable_name());
                String name = schemaName + "." + tableName;
                String checkMessage = "检查" + name + "表数据";
                this.saveCheckModel(name, checkMessage, SqlCheckStateType.running.getValue(), redisKey, ctn - 1); // 更新表状态

                if (ctn == rcTableListByIds.size()) {
                    isEnd = true;
                }

                // 验证schema是否变更
                if (wrongSchemaIdList.contains(rcTable.getSchema_id())) {
                    String resultMessage = "schema变更，不支持数据恢复";
                    this.saveCheckModel(name, isEnd, checkMessage, resultMessage, SqlCheckStateType.end.getValue(), null, redisKey, ctn - 1, null);
                    continue;
                }

                // 验证表结构变更
                Map<String, SqlFieldData> dbTableStructureMap = SqlCheckUtils.getTableStructureMap(dbType, schemaName, tableName, contextInfo, monitor, simpleContainer);
                boolean tableNotExists = dbTableStructureMap.isEmpty();
                String tableStructure = rcTable.getTable_structure();
                Map<String, SqlFieldData> rcTableStructureMap = gson.fromJson(tableStructure, new TypeToken<Map<String, SqlFieldData>>() {
                }.getType());
                boolean tableStructureChanged = SqlCheckUtils.isTableStructureChanged(rcTableStructureMap, dbTableStructureMap);

                // 获取表字段
                List<SqlFieldData> tableColumnsOrderBy = SqlCheckUtils.getTableColumnsOrderBy(dbType, schemaName, tableName, contextInfo, monitor, simpleContainer);
                List<String> tableColumns = SqlCheckUtils.getTableColumns(dbTableStructureMap, contextInfo, tableColumnsOrderBy);

                // 验证有无主键
                String pkName = rcTable.getPk_name();

                List<Long> sqlListTemp = new ArrayList<>();
                StringBuilder noBackupMessage = new StringBuilder();
                List<RcSql> tableRcSqlList = rcTableIdRcSqlMap.get(rcTable.getId());
                for (RcSql rcSql : tableRcSqlList) {
                    if (BackupSqlStateType.OVERLIMIT.getCode().equals(rcSql.getState())) {
                        noBackupMessage.append(rcSql.getLog());
                        break;
                    } else {
                        sqlListTemp.add(rcSql.getId());
                    }
                }

                if (tableNotExists) {
                    String resultMessage = "恢复对象不存在，无法进行数据恢复";
                    this.saveCheckModel(name, isEnd, checkMessage, resultMessage, SqlCheckStateType.end.getValue(), null, redisKey, ctn - 1, null);
                } else if (tableStructureChanged) {
                    String resultMessage = "表结构变更，不支持数据恢复";
                    this.saveCheckModel(name, isEnd, checkMessage, resultMessage, SqlCheckStateType.end.getValue(), null, redisKey, ctn - 1, null);
                } else if (pkName == null || pkName.isEmpty()) {
                    String resultMessage = "无主键表的操作暂不支持数据恢复";
                    this.saveCheckModel(name, isEnd, checkMessage, resultMessage, SqlCheckStateType.end.getValue(), null, redisKey, ctn - 1, null);
                } else if (noBackupMessage.length() != 0) {
                    this.saveCheckModel(name, isEnd, checkMessage, noBackupMessage.toString(), SqlCheckStateType.end.getValue(), null, redisKey, ctn - 1, null);
                } else {
                    String resultMessage = null;
                    List<Map<String, Object>> tableRecords = this.getTableRecords(rcTable, sessionId, transactionIdx, contextInfo, sqlListTemp, dbType, monitor, simpleContainer, tableColumns);
                    printTableRecords(tableRecords);
                    if (tableRecords.size() == 1 && tableRecords.get(0).get("errorMessage") != null) {
                        if (contextInfo.isClosed()) {
                            resultMessage = MessageConstants.CLOSED.getMessage();
                        } else if (contextInfo.isInterrupted()) {
                            throw new ServiceException(MessageConstants.INTERRUPTED.getMessage());
                        } else if (tableRecords.get(0).get("serviceExceptionMessage") != null) {
                            resultMessage = tableRecords.get(0).get("serviceExceptionMessage").toString();
                        } else {
                            resultMessage = "请检查是否产生锁，解锁后重新执行";
                        }
                        tableRecords.clear();
                    } else {
                        tableRecords = this.recordMask(tableRecords, dbType, sqlCheckParam, rcTable); // 数据脱敏
                    }
                    this.saveCheckModel(name, isEnd, checkMessage, resultMessage, SqlCheckStateType.end.getValue(), tableRecords, redisKey, ctn - 1, tableColumns);
                }

            }

        } catch (Exception e) {
            log.error("check table records error!", e);
        } finally {
            redisService.expire(redisKey, EXPIRE_TIME * 5); // redis过期时间
        }

    }

    public List<Map<String, Object>> getTableRecords(RcTable rcTable, String session_id, Integer transaction_idx, WebSQLContextInfo contextInfo,
                                                     List<Long> sqlList, Integer dbType, DBRProgressMonitor monitor, SimpleContainer simpleContainer,
                                                     List<String> tableColumns) {
        List<Map<String, Object>> tableRecords = new ArrayList<>();

        try {

            // 获取rel表名和data表名
            String rcRelName = "rc_rel_" + rcTable.getId() + "_" + session_id;
            String rcDataName = "rc_" + rcTable.getId() + "_" + session_id;

            // 获取主键
            String pkName = SqlCheckUtils.replace(rcTable.getPk_name());
            String[] pkSplit = pkName.split("\\,");
            List<String> pkList = Arrays.asList(pkSplit);
            String whereClause = SqlCheckUtils.buildWhereClause(dbType, pkName);

            // 获取表名
            String schemaName = SqlCheckUtils.replace(rcTable.getSchema_name());
            String tableName = SqlCheckUtils.replace(rcTable.getTable_name());
            schemaName = SqlCheckUtils.buildSchemaName(contextInfo.getDataSource(),schemaName);
            tableName = SqlCheckUtils.buildTableName(contextInfo.getDataSource(),tableName);
            String fullName = SqlCheckUtils.buildFullName(contextInfo.getDataSource(), schemaName, tableName);
            // 获取表结构
            String tableStructure = rcTable.getTable_structure();
            Map<String, SqlFieldData> rcTableStructureMap = gson.fromJson(tableStructure, new TypeToken<Map<String, SqlFieldData>>() {
            }.getType());

            // 构建恢复sql
            String whereClauseBuild = whereClause;
            StringBuilder updateBuild = new StringBuilder("UPDATE " + fullName + " SET {setClause} ");
            StringBuilder deleteBuild = new StringBuilder("DELETE FROM " + fullName);
            StringBuilder insertBuild = new StringBuilder("INSERT INTO " + fullName + "({columnName}) VALUES ({columnValue})");

            // 获取rel表数据
            List<RelationTable> relationTables = this.getRelationTables(rcRelName, sqlList, transaction_idx);

            // 分出rel表中root记录和非root记录
            List<RelationTable> relationTablesRoot = new ArrayList<>();
            List<RelationTable> relationTablesNotRoot = new ArrayList<>();
            for (RelationTable relationTable : relationTables) {
                if (relationTable.getRoot_id() == null) {
                    relationTablesRoot.add(relationTable);
                } else {
                    relationTablesNotRoot.add(relationTable);
                }
            }

            // 单条sql恢复,需手动查询root节点
            if (relationTables.size() == 1 && relationTables.get(0).getRoot_id() != null) {
                RelationTable rootRelTable = new RelationTable();
                rootRelTable.setTableName(rcRelName);
                rootRelTable.setTransaction_index(transaction_idx);
                rootRelTable.setId(relationTables.get(0).getRoot_id());
                RelationTable relationRootTable = relationTableMapper.getRelationTableById(rootRelTable);
                if (relationRootTable != null && relationTablesRoot.size() == 0) {
                    relationTablesRoot.add(relationRootTable);
                }
            }

            // 主键变更的relationTable取出,走另一个分支
            List<RelationTable> relationTablesUpdatePk = new ArrayList<>();
            List<RelationTable> relationTablesNotUpdatePk = new ArrayList<>();
            for (RelationTable relationTable : relationTablesNotRoot) {
                boolean isFind = false;

                for (RelationTable relationTableRoot : relationTablesRoot) {
                    if (relationTable.getRoot_id().equals(relationTableRoot.getId())
                            && !relationTable.getRow_id().equals(relationTableRoot.getRow_id())) {
                        relationTablesUpdatePk.add(relationTable);
                        isFind = true;
                        break;
                    }
                }

                if (!isFind) {
                    relationTablesNotUpdatePk.add(relationTable);
                }
            }

            // 非真实主键的relationTable取出,走另一个分支
            List<RelationTable> relationTablesNotRealPk = new ArrayList<>();
            List<RelationTable> relationTablesNormal = new ArrayList<>();
            for (RelationTable relationTable : relationTablesNotUpdatePk) {
                Integer isRealPk = relationTable.getIs_real_pk();
                if (isRealPk != null && isRealPk.equals(0)) {
                    relationTablesNotRealPk.add(relationTable);
                } else {
                    relationTablesNormal.add(relationTable);
                }
            }

            // 主键变更
            this.buildUpdatePKTableRecords(relationTablesUpdatePk, rcDataName, transaction_idx, pkList, fullName, monitor, dbType,
                    simpleContainer, contextInfo, schemaName, tableName, deleteBuild, rcTableStructureMap,
                    tableRecords, pkName, whereClause, rcRelName, whereClauseBuild, insertBuild, updateBuild);

            // normal
            this.buildNormalTableRecords(relationTablesNormal, rcDataName, transaction_idx, pkList, fullName, monitor, dbType,
                    simpleContainer, contextInfo, schemaName, tableName, deleteBuild, rcTableStructureMap,
                    tableRecords, pkName, whereClause, whereClauseBuild, insertBuild, updateBuild, tableColumns);

            // 非真实主键
            this.buildNotRealPKTableRecords(relationTablesNotRealPk, rcDataName, transaction_idx, pkList, dbType, fullName,
                    monitor, simpleContainer, contextInfo, schemaName, tableName, deleteBuild, rcTableStructureMap, tableRecords);


        } catch (Exception e) {
            log.error("get table records error!", e);
            tableRecords = new ArrayList<>();
            Map<String, Object> returnMap = new HashMap<>();
            returnMap.put("errorMessage", e);
            if (MessageConstants.FIELD_MISMATCH.getMessage().equals(e.getMessage())) {
                returnMap.put("serviceExceptionMessage", MessageConstants.FIELD_MISMATCH.getMessage());
            }
            tableRecords.add(returnMap);
        }

        return tableRecords;
    }

    public List<RcSql> getRCSqlList(List<Long> sqlList, Long batchId) {

        // 获取每条sql记录
        List<RcSql> rcSqlList;

        if (sqlList.size() != 0) {
            Map<String, Object> rcSqlIdsMap = new LinkedHashMap<>();
            rcSqlIdsMap.put("ids", sqlList);
            rcSqlList = rcSqlMapper.getRcSqlListById(rcSqlIdsMap);
        } else {
            rcSqlList = rcSqlMapper.getRcSqlListByBatchId(batchId);
        }

        return rcSqlList;

    }

    public Map<Long, List<RcSql>> buildRcTableIdRcSqlMap(List<RcSql> rcSqlList) {
        Map<Long, List<RcSql>> rcTableIdRcSqlMap = new HashMap<>();
        for (RcSql rcSql : rcSqlList) {
            rcTableIdRcSqlMap.putIfAbsent(rcSql.getRc_table_id(), new ArrayList<>());
            rcTableIdRcSqlMap.get(rcSql.getRc_table_id()).add(rcSql);
        }
        return rcTableIdRcSqlMap;
    }

    public String getSessionId(List<RcSql> rcSqlList) {

        if (rcSqlList.size() > 0) {
            return rcSqlList.get(0).getSession_id();
        }

        return "";
    }

    public Integer getTransactionIdx(List<RcSql> rcSqlList) {

        if (rcSqlList.size() > 0) {
            return rcSqlList.get(0).getTransaction_index();
        }

        return 1;
    }

    public List<RcTable> getRCTableListByIds(List<RcSql> rcSqlList, String redisKey) {

        // 获取需要检查的table
        Set<Long> rcTableIdSet = new HashSet<>();
        for (RcSql rcSql : rcSqlList) {
            Long rcTableId = rcSql.getRc_table_id();
            rcTableIdSet.add(rcTableId);
        }

        List<RcTable> rcTableListByIds = new ArrayList<>();
        if (rcTableIdSet.size() > 0) {
            Map<String, Object> rcTableIdsMap = new LinkedHashMap<>();
            rcTableIdsMap.put("ids", rcTableIdSet);
            rcTableListByIds = rcTableMapper.getRcTableListByIds(rcTableIdsMap);
        }

        // 将table待检查信息保存到redis
        for (RcTable rcTableById : rcTableListByIds) {
            String schemaName = SqlCheckUtils.replace(rcTableById.getSchema_name());
            String tableName = SqlCheckUtils.replace(rcTableById.getTable_name());
            String name = schemaName + "." + tableName;
            String checkMessage = "检查" + name + "表数据";
            this.saveCheckModel(name, checkMessage, SqlCheckStateType.padding.getValue(), redisKey);
        }

        return rcTableListByIds;

    }

    public List<RelationTable> getRelationTables(String rcRelName, List<Long> sqlList, Integer transactionIdx) {

        // 获取rel表数据
        Map<String, Object> rcSqlIdsMap = new LinkedHashMap<>();

        rcSqlIdsMap.put("table_name", rcRelName);
        rcSqlIdsMap.put("rc_sql_ids", sqlList);
        rcSqlIdsMap.put("transaction_index", transactionIdx);

        List<RelationTable> relationTables = relationTableMapper.getRelationTables(rcSqlIdsMap);

        return relationTables;

    }

    public void saveCheckModel(String name, String checkMessage, Integer state, String redisKey) {

        CheckModel checkModel = new CheckModel(name, checkMessage, state);
        String checkModelContent = gson.toJson(checkModel);
        redisService.lSet(redisKey, checkModelContent);

    }

    public void saveCheckModel(String name, String checkMessage, Integer state, String redisKey, int idx) {

        CheckModel checkModel = new CheckModel(name, checkMessage, state);
        String checkModelContent = gson.toJson(checkModel);
        redisService.lUpdateIndex(redisKey, idx, checkModelContent);

    }

    public void saveCheckModel(String name, boolean isEnd, String checkMessage, String resultMessage, Integer state,
                               List<Map<String, Object>> data, String redisKey, int idx, List<String> tableColumns) {

        CheckModel checkModel = new CheckModel(name, isEnd, checkMessage, resultMessage, state, data);
        checkModel.setTableColumns(tableColumns);
        String checkModelContent = gson.toJson(checkModel);
        redisService.lUpdateIndex(redisKey, idx, checkModelContent);

    }

    public void buildUpdatePKTableRecords(List<RelationTable> relationTablesUpdatePk, String rcDataName, Integer transactionIdx,
                                          List<String> pkList, String fullName, DBRProgressMonitor monitor, Integer dbType,
                                          SimpleContainer simpleContainer, WebSQLContextInfo contextInfo, String schemaName,
                                          String tableName, StringBuilder deleteBuild, Map<String, SqlFieldData> rcTableStructureMap,
                                          List<Map<String, Object>> tableRecords, String pkName, String whereClause, String rcRelName,
                                          String whereClauseBuild, StringBuilder insertBuild, StringBuilder updateBuild) throws Exception {

        Set<String> recoveryDataSqlColumnsSet = rcTableStructureMap.keySet();
        List<String> columnNames = new ArrayList<>(recoveryDataSqlColumnsSet);
        columnNames.sort((a, b) -> a.compareTo(b));
        String recoveryDataSqlColumns = generateSqlColumns(columnNames, dbType);

        List<String> pkChangeRowIds = new ArrayList<>(); // 已经添加的主键变更后的主键值
        List<String> rowIds = new ArrayList<>(); // 已经添加的主键值

        Collections.reverse(relationTablesUpdatePk); // 若是相同行变更,以最后变更的值为主

        for (RelationTable relationTable : relationTablesUpdatePk) {
            String rowId = relationTable.getRow_id();
            if (pkChangeRowIds.contains(rowId)) {
                continue;
            } else {
                pkChangeRowIds.add(rowId);
            }

            if (contextInfo.isClosed()) {
                throw new ServiceException("实例连接关闭，当前任务中断");
            } else if (contextInfo.isInterrupted()) {
                throw new ServiceException("用户中断操作！");
            }

            // 构建恢复sql的where条件
            List<SqlData> whereSqlDataList = SqlCheckUtils.buildWhereClause(rcTableStructureMap, pkName, rowId);
            String whereClauseCp = buildRecoverWhereClause(whereSqlDataList, dbType);
            // 设置主键值
//            String[] split = rowId.split("\\,", -1);
//            String whereClauseCp = whereClause;
//            for (String pkValue : split) {
//                String replace = "'" + pkValue.replace("'", "''") + "'";
//                String replaceResult = replace.replaceAll("\\\\", "\\\\\\\\");
//                whereClauseCp = whereClauseCp.replaceFirst("\\?", replaceResult);
//            }

            RelationTable rootRelTable = new RelationTable();
            rootRelTable.setTableName(rcRelName);
            rootRelTable.setTransaction_index(transactionIdx);
            rootRelTable.setId(relationTable.getRoot_id());
            RelationTable relationTableById = relationTableMapper.getRelationTableById(rootRelTable);
            // 需合并相同行的数据
            if (rowIds.contains(relationTableById.getRow_id())) {
                continue;
            } else {
                rowIds.add(relationTableById.getRow_id());
            }
            // 获取data表数据
            DataTable dataTable = new DataTable();
            dataTable.setTableName(rcDataName);
            dataTable.setTransaction_index(transactionIdx);
            dataTable.setRow_id(relationTableById.getRow_id());
            Map<String, Object> rcdataMap = dataTableMapper.getDataMap(dataTable);

            // 获取DB数据
            String query = "select * from " + fullName + whereClauseCp;
            Map<String, Object> dbRecord = SqlCheckUtils.getDBRecord(monitor, simpleContainer, query, contextInfo, schemaName, tableName, pkList);

            if (!dbRecord.isEmpty() && !rcdataMap.isEmpty()) {
                RecoveryDataSql recoveryDataSqlDelete = SqlCheckUtils.buildDeleteRecoveryDataSql(deleteBuild, whereClauseBuild, whereSqlDataList, fullName, pkList, columnNames, recoveryDataSqlColumns);
                RecoveryDataSql recoveryDataSqlInsert = SqlCheckUtils.buildInsertRecoveryDataSql(rcdataMap, insertBuild, rcTableStructureMap, fullName, pkList, columnNames, recoveryDataSqlColumns, dbType);
                // 更新数据
                Map<String, Object> returnMap = SqlCheckUtils.compareRecord(dbRecord, rcdataMap);
                returnMap.put("rc_recoveryDataSql", recoveryDataSqlDelete);
                returnMap.put("rc_recoveryDataSql_other", recoveryDataSqlInsert);
                tableRecords.add(returnMap);
            } else if (dbRecord.isEmpty() && !rcdataMap.isEmpty()) {
                // 变更后的主键无值,查看下变更前的主键值
                String[] splitTemp = relationTableById.getRow_id().split("\\,", -1);
                String whereClauseCpTemp = whereClause;
                for (String pkValue : splitTemp) {
                    String replace = "'" + pkValue.replace("'", "''") + "'";
                    whereClauseCpTemp = whereClauseCpTemp.replaceFirst("\\?", replace);
                }
                // 获取DB数据
                String queryTemp = "select * from " + fullName + whereClauseCpTemp;
                dbRecord = SqlCheckUtils.getDBRecord(monitor, simpleContainer, queryTemp, contextInfo, schemaName, tableName, pkList);
                if (dbRecord.size() == 0) {
                    RecoveryDataSql recoveryDataSql = SqlCheckUtils.buildInsertRecoveryDataSql(rcdataMap, insertBuild, rcTableStructureMap, fullName, pkList, columnNames, recoveryDataSqlColumns, dbType);
                    // 插入数据
                    Map<String, Object> returnMap = SqlCheckUtils.buildInsertRecord(rcdataMap);
                    returnMap.put("rc_recoveryDataSql", recoveryDataSql);
                    tableRecords.add(returnMap);
                } else {
                    whereSqlDataList = SqlCheckUtils.buildWhereClause(rcTableStructureMap, pkName, relationTableById.getRow_id());
                    RecoveryDataSql recoveryDataSql = SqlCheckUtils.buildUpdateRecoveryDataSql(dbRecord, rcdataMap, updateBuild, whereClauseBuild, whereSqlDataList, rcTableStructureMap, fullName, pkList, columnNames, recoveryDataSqlColumns, dbType);
                    if (recoveryDataSql.getSql() == null) {
                        String showPrimaryKeyValue = SqlCheckUtils.getShowPrimaryKeyValue(splitTemp, pkName);
                        recoveryDataSql.setShowPrimaryKeyValue(showPrimaryKeyValue);
                    }
                    // 更新数据
                    Map<String, Object> returnMap = SqlCheckUtils.compareRecord(dbRecord, rcdataMap);
                    returnMap.put("rc_recoveryDataSql", recoveryDataSql);
                    tableRecords.add(returnMap);
                }

            }

        }

    }
    private static String buildRecoverWhereClause(List<SqlData> whereSqlDataList, Integer dbType) {
        List<StringBuilder> whereOrClause = Stream.generate(StringBuilder::new)
                .limit(whereSqlDataList.size())
                .collect(Collectors.toList());
        DatabaseType databaseType = DatabaseType.of(dbType);
        for (int i = 0; i < whereSqlDataList.size(); i++) {
            SqlData whereSqlData = whereSqlDataList.get(i);
                String columnName = whereSqlData.getKey();
                if (List.of(DatabaseType.MYSQL, DatabaseType.OCEAN_BASE_MYSQL, DatabaseType.TDMYSQL).contains(databaseType)) {
                    columnName = String.format("`%s`", columnName);
                    if ("JSON".equalsIgnoreCase(whereSqlData.getData_type())) {
                        columnName = String.format("JSON_UNQUOTE(%s)", columnName);
                    }
                } else if (List.of(DatabaseType.ORACLE, DatabaseType.OCEAN_BASE_ORACLE, DatabaseType.DM).contains(databaseType)) {
                    columnName = String.format("\"%s\"", columnName);
                } else if (Objects.equals(DatabaseType.SQL_SERVER, databaseType)) {
                    columnName = String.format("[%s]", columnName);
                }
                String columnValue = whereSqlData.getValue().toString();
                StringBuilder orClause = whereOrClause.get(i);
                if (orClause.length() != 0) {
                    orClause.append(" and ");
                }
                orClause.append(columnName);
                orClause.append(columnValue == null ? " is " : " = ");
                TableColumnModel columnModel = new TableColumnModel();
                columnModel.setColumnName(columnName);
                columnModel.setDataType(whereSqlData.getData_type());
                columnModel.setColumnType(whereSqlData.getData_type());
                columnModel.setIsPrimaryKey(1);
                boolean needQuote = columnValue != null &&
                        CommonUtil.requiresQuotingForDbSpecifics(databaseType, columnValue, columnModel) &&
                        !CommonUtil.isTimestampTypeWithValuePrefix(columnModel, columnValue);
                if (needQuote) {
                    orClause.append("'");
                }
                orClause.append(columnValue);
                if (needQuote) {
                    orClause.append("'");
                }

        }

        return " where " + whereOrClause.stream()
                .map(stringBuilder -> stringBuilder.insert(0, "(").append(")").toString())
                .reduce((s1, s2) -> s1 + " and " + s2)
                .orElse("1 = 1");
    }
    public void buildNormalTableRecords(List<RelationTable> relationTablesNormal, String rcDataName, Integer transactionIdx,
                                        List<String> pkList, String fullName, DBRProgressMonitor monitor, Integer dbType,
                                        SimpleContainer simpleContainer, WebSQLContextInfo contextInfo, String schemaName,
                                        String tableName, StringBuilder deleteBuild, Map<String, SqlFieldData> rcTableStructureMap,
                                        List<Map<String, Object>> tableRecords, String pkName, String whereClause,
                                        String whereClauseBuild, StringBuilder insertBuild, StringBuilder updateBuild,
                                        List<String> tableColumns) throws Exception {

        Set<String> recoveryDataSqlColumnsSet = rcTableStructureMap.keySet();
        List<String> columnNames = new ArrayList<>(recoveryDataSqlColumnsSet);
        columnNames.sort(String::compareTo);
        String recoveryDataSqlColumns = generateSqlColumns(columnNames, dbType);

        List<String> rowIds = new ArrayList<>(); // 已经添加的主键值

        for (RelationTable relationTable : relationTablesNormal) {
            String rowId = relationTable.getRow_id();
            if (rowIds.contains(rowId)) {
                continue;
            } else {
                rowIds.add(rowId);
            }
            if (contextInfo.isClosed()) {
                throw new ServiceException("实例连接关闭，当前任务中断");
            } else if (contextInfo.isInterrupted()) {
                throw new ServiceException("用户中断操作！");
            }
            // 构建恢复sql的where条件
            List<SqlData> whereSqlDataList = SqlCheckUtils.buildWhereClause(rcTableStructureMap, pkName, rowId);
            String whereClauseCp = buildRecoverWhereClause(whereSqlDataList, dbType);
            // 设置主键值
            String[] split = rowId.split("\\,");
//            String whereClauseCp = whereClause;
//            for (String pkValue : split) {
//                String replace = "'" + pkValue.replace("'", "''") + "'";
//                String replaceResult = replace.replaceAll("\\\\", "\\\\\\\\");
//                whereClauseCp = whereClauseCp.replaceFirst("\\?", replaceResult);
//            }

            // 获取data表数据
            DataTable dataTable = new DataTable();
            dataTable.setTableName(rcDataName);
            dataTable.setTransaction_index(transactionIdx);
            dataTable.setRow_id(rowId);
            Map<String, Object> rcdataMap = dataTableMapper.getDataMap(dataTable);

            if (rcdataMap == null) {
                throw new ServiceException(MessageConstants.FIELD_MISMATCH.getMessage());
            }

            // insert的数据都会被删除
            String rcType = String.valueOf(rcdataMap.get("rc_type"));
            if ("insert".equalsIgnoreCase(rcType)) {
                Map<String, Object> objectMap = SqlCheckUtils.transMapStringToObject(rcdataMap);
                for (String column : tableColumns) {
                    if (!objectMap.containsKey(column)) {
                        throw new ServiceException(MessageConstants.FIELD_MISMATCH.getMessage());
                    }
                }
                RecoveryDataSql recoveryDataSql = SqlCheckUtils.buildDeleteRecoveryDataSql(deleteBuild, whereClauseBuild, whereSqlDataList, fullName, pkList, columnNames, recoveryDataSqlColumns);
                objectMap.put("rc_recoveryDataSql", recoveryDataSql);
                // 删除数据
                objectMap.put("rc_delete", 1);
                tableRecords.add(objectMap);
                continue;
            }

            // 获取DB数据
            String columns = "*";
            if (DatabaseType.DM.getValue().equals(dbType) && !tableColumns.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                for (String column : tableColumns) {
                    sb.append(",").append(SqlCheckUtils.assembleColumnName(column, dbType));
                }
                columns = sb.substring(1);
            }
            String query = "select " + columns + " from " + fullName + whereClauseCp;
            Map<String, Object> dbRecord = SqlCheckUtils.getDBRecord(monitor, simpleContainer, query, contextInfo, schemaName, tableName, pkList);

            if (dbRecord.size() == 0) {
                RecoveryDataSql recoveryDataSql = SqlCheckUtils.buildInsertRecoveryDataSql(rcdataMap, insertBuild, rcTableStructureMap, fullName, pkList, columnNames, recoveryDataSqlColumns, dbType);
                // 插入数据
                Map<String, Object> returnMap = SqlCheckUtils.buildInsertRecord(rcdataMap);
                returnMap.put("rc_recoveryDataSql", recoveryDataSql);
                tableRecords.add(returnMap);
            } else {
                RecoveryDataSql recoveryDataSql = SqlCheckUtils.buildUpdateRecoveryDataSql(dbRecord, rcdataMap, updateBuild, whereClauseBuild, whereSqlDataList, rcTableStructureMap, fullName, pkList, columnNames, recoveryDataSqlColumns, dbType);
                if (recoveryDataSql.getSql() == null) {
                    String showPrimaryKeyValue = SqlCheckUtils.getShowPrimaryKeyValue(split, pkName);
                    recoveryDataSql.setShowPrimaryKeyValue(showPrimaryKeyValue);
                }
                // 更新数据
                Map<String, Object> returnMap = SqlCheckUtils.compareRecord(dbRecord, rcdataMap);
                returnMap.put("rc_recoveryDataSql", recoveryDataSql);
                tableRecords.add(returnMap);
            }

        }

    }

    public void buildNotRealPKTableRecords(List<RelationTable> relationTablesNotRealPk, String rcDataName, Integer transactionIdx,
                                           List<String> pkList, Integer dbType, String fullName, DBRProgressMonitor monitor,
                                           SimpleContainer simpleContainer, WebSQLContextInfo contextInfo, String schemaName,
                                           String tableName, StringBuilder deleteBuild, Map<String, SqlFieldData> rcTableStructureMap,
                                           List<Map<String, Object>> tableRecords) throws Exception {

        Set<String> recoveryDataSqlColumnsSet = rcTableStructureMap.keySet();
        List<String> columnNames = new ArrayList<>(recoveryDataSqlColumnsSet);
        columnNames.sort((a, b) -> a.compareTo(b));
        String recoveryDataSqlColumns = generateSqlColumns(columnNames, dbType);

        // 非真实主键
        for (RelationTable relationTable : relationTablesNotRealPk) {
            String rowId = relationTable.getRow_id();

            if (contextInfo.isClosed()) {
                throw new ServiceException("实例连接关闭，当前任务中断");
            } else if (contextInfo.isInterrupted()) {
                throw new ServiceException("用户中断操作！");
            }

            // 获取data表数据
            DataTable dataTable = new DataTable();
            dataTable.setTableName(rcDataName);
            dataTable.setTransaction_index(transactionIdx);
            dataTable.setRow_id(rowId);
            Map<String, Object> rcdataMap = dataTableMapper.getDataMap(dataTable);

            // 获取除主键外的where条件
            StringBuilder where = new StringBuilder(" where 1=1 ");
            for (Map.Entry<String, Object> rcdata : rcdataMap.entrySet()) {
                String key = rcdata.getKey();
                Object value = rcdata.getValue();
                if (key.startsWith("_")) {
                    String key_ = key.replaceFirst("_", "");
                    if (!pkList.contains(key_)) {
                        where.append(" and ").append(SqlCheckUtils.assembleColumnName(key_, dbType)).append("='").append(value).append("'");
                    }
                }
            }

            // 获取DB数据
            String query = "select * from " + fullName + where;
            List<Map<String, Object>> notRealPKDBRecords = SqlCheckUtils.getNotRealPKDBRecord(monitor, simpleContainer, query, contextInfo, schemaName, tableName, pkList);

            if (notRealPKDBRecords.size() > 0) {
                for (Map<String, Object> notRealPKBDRecord : notRealPKDBRecords) {
                    // 删除数据
                    notRealPKBDRecord.put("rc_delete", 1);
                    RecoveryDataSql recoveryDataSql = SqlCheckUtils.buildNotRealPKRecoveryDataSql(deleteBuild, notRealPKBDRecord, dbType, pkList, rcTableStructureMap, fullName, columnNames, recoveryDataSqlColumns);
                    notRealPKBDRecord.put("rc_recoveryDataSql", recoveryDataSql);
                }
                tableRecords.addAll(notRealPKDBRecords);
            }

        }

    }

    /**
     * 生成格式化的sql列信息
     *
     * @param recoveryDataSqlColumnsList
     * @return
     */
    private String generateSqlColumns(List<String> recoveryDataSqlColumnsList, Integer dbType) {
        boolean added = false;
        String recoveryDataSqlColumns = "";
        StringBuilder recoveryDataSqlColumnsTemp = new StringBuilder();
        for (String str : recoveryDataSqlColumnsList) {
            recoveryDataSqlColumnsTemp.append(SqlCheckUtils.assembleColumnName(str, dbType)).append(",");
            added = true;
        }
        if (!added) {
            log.warn("拼接RecoveryDataSql.query的查询语句时，列值为空");
        } else {
            int len = recoveryDataSqlColumnsTemp.length();
            recoveryDataSqlColumns = recoveryDataSqlColumnsTemp.substring(0, len - 1);
        }
        return recoveryDataSqlColumns;
    }

    public void printTableRecords(List<Map<String, Object>> tableRecords) {
        try {
            log.info("误操作恢复备份-检查tableRecords:" + JSON.toJSONString(tableRecords));
        } catch (Exception ignore) {
        }
    }

    public List<Map<String, Object>> recordMask(List<Map<String, Object>> tableRecords, Integer dbType,
                                                SqlCheckParam sqlCheckParam, RcTable rcTable) {

        try {
            String sql = "SELECT * FROM " + rcTable.getTable_name();

            ParserParamDto preCheckParamDTO = new ParserParamDto();
            preCheckParamDTO.setSql(sql);
            preCheckParamDTO.setDbType(dbType);
            preCheckParamDTO.setIsVerify(ParserExecuteType.makeIsVerify(ParserExecuteType.DATA_MASK_PARSER));
            preCheckParamDTO.setConnectId(rcTable.getInstance_id());
            preCheckParamDTO.setSchemaId(rcTable.getSchema_id());
            preCheckParamDTO.setOffset(0);
            preCheckParamDTO.setLimit(0);
            preCheckParamDTO.setUserId(sqlCheckParam.getUser_id());
            preCheckParamDTO.setEnableDesensiteType(sqlCheckParam.getEnable_desensite_type());

            WebSQLParserResult webSQLParserResult = parserSqlClient.executeSql(Client.getClient(summerConfig.getPath().getDcIceage()), preCheckParamDTO).get(0);

            this.dataMask(webSQLParserResult.getDataMask(), tableRecords, sqlCheckParam.getEnable_desensite_type());

        } catch (Exception e) {
            log.error("recordMask error : ", e);
        }

        return tableRecords;

    }

    public void dataMask(List<DataMask> list, List<Map<String, Object>> tableRecords, Integer enableDesensiteType) {

        DataDesensitizeProcessor dataDesensitizeProcessor = new DataDesensitizeProcessor(
                null,
                enableDesensiteType,
                list,
                "******",
                null,
                null,
                null,
                null);

        if (CollectionUtils.isNotEmpty(list)) {

            for (DataMask cfg : list) {
                // ORACLE 列存在大小写 统一大写比较
                cfg.setColumnAlias(cfg.getColumnAlias().toUpperCase());
            }

            Map<String, DataMask> maskConfigMap = list.stream().collect(Collectors.toMap(DataMask::getColumnAlias, a -> a, (k1, k2) -> k1));

            for (Map<String, Object> row : tableRecords) {

                for (String key : row.keySet()) {

                    DataMask dataMask = null;

                    String originName = key.replaceFirst("rc_new_", "").toUpperCase();
                    if (maskConfigMap.containsKey(originName)) {
                        dataMask = maskConfigMap.get(originName);
                    }

                    Object value = row.get(key);
                    if (dataMask != null && value != null) {
                        row.put(key, dataDesensitizeProcessor.obtainDesensitizationValue(value, dataMask, 0));
                    }

                }

            }

        }

    }


}
