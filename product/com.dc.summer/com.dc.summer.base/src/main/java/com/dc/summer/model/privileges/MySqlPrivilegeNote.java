package com.dc.summer.model.privileges;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.repository.mysql.mapper.DcAccountObjPrivsMapper;
import com.dc.repository.mysql.mapper.DcAccountSysPrivsMapper;
import com.dc.repository.mysql.mapper.DcDbResourceAccountInfoMapper;
import com.dc.repository.mysql.mapper.DcDbResourceAccountMapper;
import com.dc.repository.mysql.model.DcAccountObjPrivs;
import com.dc.repository.mysql.model.DcAccountSysPrivs;
import com.dc.repository.mysql.model.DcDbResource;
import com.dc.repository.mysql.model.DcDbResourceAccount;
import com.dc.repository.mysql.type.GrantObjectType;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.summer.DBException;
import com.dc.summer.component.SummerMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class MySqlPrivilegeNote extends AbstractPrivilegeNote {

    private final DcDbResourceAccountMapper dcDbResourceAccountMapper;

    private final DcAccountObjPrivsMapper dcAccountObjPrivsMapper;

    private final DcAccountSysPrivsMapper dcAccountSysPrivsMapper;

    private final SummerMapper summerMapper;

    private final DcDbResourceAccountInfoMapper dcDbResourceAccountInfoMapper;


    private static final List<String> globalPrivileges = List.of(
            "ALTER",
            "ALTER ROUTINE",
            "CREATE",
            "CREATE ROUTINE",
            "CREATE TABLESPACE",
            "CREATE TEMPORARY TABLES",
            "CREATE USER",
            "CREATE VIEW",
            "DELETE",
            "DROP",
            "EVENT",
            "EXECUTE",
            "FILE",
            "INDEX",
            "INSERT",
            "LOCK TABLES",
            "PROCESS",
            "REFERENCES",
            "RELOAD",
            "REPLICATION CLIENT",
            "REPLICATION SLAVE",
            "SELECT",
            "SHOW DATABASES",
            "SHOW VIEW",
            "SHUTDOWN",
            "SUPER",
            "TRIGGER",
            "UPDATE"
    );

    private static final List<String> schemaPrivileges = List.of(
            "SELECT",
            "INSERT",
            "UPDATE",
            "DELETE",
            "EXECUTE",
            "SHOW VIEW",
            "CREATE",
            "ALTER",
            "REFERENCES",
            "INDEX",
            "CREATE VIEW",
            "CREATE ROUTINE",
            "ALTER ROUTINE",
            "EVENT",
            "DROP",
            "TRIGGER",
            "CREATE TEMPORARY TABLES",
            "LOCK TABLES"
    );

    public MySqlPrivilegeNote(DcDbResourceAccountMapper dcDbResourceAccountMapper,
                              DcAccountObjPrivsMapper dcAccountObjPrivsMapper,
                              DcAccountSysPrivsMapper dcAccountSysPrivsMapper,
                              SummerMapper summerMapper, DcDbResourceAccountInfoMapper dcDbResourceAccountInfoMapper) {
        super(dcDbResourceAccountInfoMapper, dcDbResourceAccountMapper);
        this.dcDbResourceAccountMapper = dcDbResourceAccountMapper;
        this.dcAccountObjPrivsMapper = dcAccountObjPrivsMapper;
        this.dcAccountSysPrivsMapper = dcAccountSysPrivsMapper;
        this.summerMapper = summerMapper;
        this.dcDbResourceAccountInfoMapper = dcDbResourceAccountInfoMapper;
    }


    @Override
    public List<String> revoke(PrivilegeNoteModel privilegeNoteModel) {

        PrivilegeModel privilegeModel = privilegeNoteModel.getPrivilegeModel();
        DcDbResource resource = privilegeNoteModel.getResource();
        boolean isSyncJob = privilegeNoteModel.isSyncJob();
        List<String> hosts = privilegeModel.getHosts();

        List<String> grantees = privilegeModel.getGrantees();
        List<String> realGrantees = new ArrayList<>();
        List<String> objPrivileges = privilegeModel.getObjPrivileges();
        List<String> systemPrivileges = privilegeModel.getSystemPrivileges();
        String owner = privilegeModel.getOwner();

        int cur = 0;
        for (String grantee : grantees) {
            String host = hosts.get(cur);
            cur++;
            LambdaQueryWrapper<DcDbResourceAccount> qwa = Wrappers.<DcDbResourceAccount>lambdaQuery()
                    .eq(DcDbResourceAccount::getResourceId, resource.getUnique_key())
                    .eq(DcDbResourceAccount::getUsername, grantee);
            if (!StringUtils.isBlank(host)) {
                qwa.eq(DcDbResourceAccount::getHost, host);
            }
            List<DcDbResourceAccount> resourceAccounts = dcDbResourceAccountMapper.getResourceAccounts(qwa);

            if (resourceAccounts.isEmpty()) {
                continue;
            }
            if (!StringUtils.isBlank(host)) {
                realGrantees.add(grantee + "@" + host);
            } else {
                realGrantees.add(grantee);
            }

            for (DcDbResourceAccount dcDbResourceAccount : resourceAccounts) {
                if (objPrivileges != null && !isSyncJob) {
                    for (String privilege : objPrivileges) {
                        LambdaQueryWrapper<DcAccountObjPrivs> qwo = Wrappers.<DcAccountObjPrivs>lambdaQuery()
                                .eq(DcAccountObjPrivs::getResourceId, resource.getId())
                                .eq(DcAccountObjPrivs::getAccountId, dcDbResourceAccount.getId())
                                .eq(DcAccountObjPrivs::getObjectType, GrantObjectType.SCHEMA.getName())
                                .eq(DcAccountObjPrivs::getGrantee, grantee);

                        if (!StringUtils.isBlank(owner)) {
                            qwo.eq(DcAccountObjPrivs::getSchemaName, owner);
                        }

                        if (privilege.equalsIgnoreCase("ALL") || privilege.equalsIgnoreCase("ALL PRIVILEGES")) {
                            dcAccountObjPrivsMapper.removePrivilege(qwo.ne(DcAccountObjPrivs::getPrivilege, "GRANT OPTION"));
                        } else {
                            dcAccountObjPrivsMapper.removePrivilege(qwo.eq(DcAccountObjPrivs::getPrivilege, privilege));
                        }
                    }
                }

                if (systemPrivileges != null && !isSyncJob) {
                    for (String privilege : systemPrivileges) {
                        LambdaQueryWrapper<DcAccountSysPrivs> qws = Wrappers.<DcAccountSysPrivs>lambdaQuery()
                                .eq(DcAccountSysPrivs::getResourceId, resource.getId())
                                .eq(DcAccountSysPrivs::getAccountId, dcDbResourceAccount.getId())
                                .eq(DcAccountSysPrivs::getGrantee, grantee);
                        if (privilege.equalsIgnoreCase("ALL") || privilege.equalsIgnoreCase("ALL PRIVILEGES")) {
                            dcAccountSysPrivsMapper.removePrivilege(qws.ne(DcAccountSysPrivs::getPrivilege, "GRANT OPTION"));
                        } else {
                            dcAccountSysPrivsMapper.removePrivilege(qws.eq(DcAccountSysPrivs::getPrivilege, privilege));
                        }
                    }
                }

            }

        }
        return realGrantees;

    }

    @Override
    public List<String> grant(PrivilegeNoteModel privilegeNoteModel) throws DBException {

        PrivilegeModel privilegeModel = privilegeNoteModel.getPrivilegeModel();
        DcDbResource resource = privilegeNoteModel.getResource();
        String username = privilegeNoteModel.getUserName();
        boolean isSyncJob = privilegeNoteModel.isSyncJob();
        String privilegeExpire = privilegeNoteModel.getPrivilegeExpire();
        List<String> hosts = privilegeModel.getHosts();

        List<DcAccountObjPrivs> objPrivs = new ArrayList<>();
        List<DcAccountSysPrivs> sysPrivs = new ArrayList<>();
        List<String> realGrantees = new ArrayList<>();


        List<String> grantees = privilegeModel.getGrantees();
        List<String> objPrivileges = privilegeModel.getObjPrivileges();
        List<String> systemPrivileges = privilegeModel.getSystemPrivileges();

        int cur = 0;
        for (String grantee : grantees) {
            String host = hosts.get(cur);
            LambdaQueryWrapper<DcDbResourceAccount> qwa = Wrappers.<DcDbResourceAccount>lambdaQuery()
                    .eq(DcDbResourceAccount::getResourceId, resource.getUnique_key())
                    .eq(DcDbResourceAccount::getUsername, grantee)
                    .eq(DcDbResourceAccount::getHost, host);

            cur++;

            DcDbResourceAccount dcDbResourceAccount = dcDbResourceAccountMapper.getResourceAccount(qwa);
            if (dcDbResourceAccount == null) {
                log.info("非纳管资源账号，不记录权限 {}", grantee);
                continue;
            }

            realGrantees.add(grantee + "@" + host);

            if (objPrivileges != null && !isSyncJob) {
                for (String privilege : objPrivileges) {
                    DcAccountObjPrivs priv = new DcAccountObjPrivs();
                    priv.setResourceId(resource.getId());
                    priv.setAccountId(dcDbResourceAccount.getId());
                    priv.setIsExpire((byte) 0);
                    priv.setGmtCreate(LocalDateTime.now());
                    priv.setGmtModified(LocalDateTime.now());
                    priv.setGrantee(grantee);
                    priv.setPrivilege(privilege);
                    priv.setCatalogName("");
                    priv.setSchemaName(privilegeModel.getOwner());
                    priv.setObjectName("");
                    priv.setObjectType(GrantObjectType.SCHEMA.getName());
                    priv.setGrantor(username);
                    priv.setGrantable((byte) 0);
                    priv.setHierarchy((byte) 0);
                    priv.setIsDelete((byte) 0);
                    if (StringUtils.isNotBlank(privilegeExpire)) {
                        priv.setIsExpire((byte) 1);
                        priv.setPrivilegeExpire(LocalDateTime.parse(privilegeExpire, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }

                    if (privilege.equalsIgnoreCase("ALL") || privilege.equalsIgnoreCase("ALL PRIVILEGES")) {
                        for (String p : schemaPrivileges) {
                            DcAccountObjPrivs c = summerMapper.copy(priv);
                            c.setPrivilege(p);
                            objPrivs.add(c);
                        }
                    } else {
                        objPrivs.add(priv);
                    }

                    if (privilegeModel.isWithGrantOption()) {
                        DcAccountObjPrivs c = summerMapper.copy(priv);
                        c.setPrivilege("GRANT OPTION");
                        objPrivs.add(c);
                    }

                }
            }

            if (systemPrivileges != null && !isSyncJob) {
                for (String privilege : systemPrivileges) {
                    DcAccountSysPrivs priv = new DcAccountSysPrivs();
                    priv.setResourceId(resource.getId());
                    priv.setAccountId(dcDbResourceAccount.getId());
                    priv.setIsExpire((byte) 0);
                    priv.setGmtCreate(LocalDateTime.now());
                    priv.setGmtModified(LocalDateTime.now());
                    priv.setGrantee(grantee);
                    priv.setPrivilege(privilege);
                    priv.setIsDelete((byte) 0);
                    if (StringUtils.isNotBlank(privilegeExpire)) {
                        priv.setIsExpire((byte) 1);
                        priv.setPrivilegeExpire(LocalDateTime.parse(privilegeExpire, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                    if (privilege.equalsIgnoreCase("ALL") || privilege.equalsIgnoreCase("ALL PRIVILEGES")) {
                        for (String p : globalPrivileges) {
                            DcAccountSysPrivs c = summerMapper.copy(priv);
                            c.setPrivilege(p);
                            sysPrivs.add(c);
                        }
                    } else {
                        sysPrivs.add(priv);
                    }

                    if (privilegeModel.isWithGrantOption()) {
                        DcAccountSysPrivs c = summerMapper.copy(priv);
                        c.setPrivilege("GRANT OPTION");
                        sysPrivs.add(c);
                    }
                }
            }
        }

        if (!objPrivs.isEmpty()) {
            if (privilegeModel.isWithGrantOption()) {
                dcAccountObjPrivsMapper.replaceInto(objPrivs);
            } else {
                dcAccountObjPrivsMapper.replaceIntoNoGrantable(objPrivs);
            }
        }
        if (!sysPrivs.isEmpty()) {
            if (privilegeModel.isWithAdminOption()) {
                dcAccountSysPrivsMapper.replaceInto(sysPrivs);
            } else {
                dcAccountSysPrivsMapper.replaceIntoNoAdminOption(sysPrivs);
            }
        }

        return realGrantees;
    }

}
