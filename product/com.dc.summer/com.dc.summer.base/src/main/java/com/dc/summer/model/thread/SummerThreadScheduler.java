package com.dc.summer.model.thread;

import com.dc.springboot.core.model.thread.BaseThreadScheduler;
import com.dc.summer.exec.model.counter.HandlerCounter;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.data.message.TaskInfoMessage;
import com.dc.summer.model.type.ExecuteType;
import com.dc.summer.service.ResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.function.Consumer;

@Slf4j
@Component
public class SummerThreadScheduler extends BaseThreadScheduler<ExecuteType, SummerThreadScheduler.Result> {

    @Resource
    private ResultService resultService;

    public void exec(ExecuteType executeType, TaskInfoMessage message, WebAsyncTaskInfo mountTaskInfo) {

        boolean executeImmediately = true;

        if (message.getTaskId() != null) {
            try {
                WebAsyncTaskInfo asyncTaskInfo = resultService.getAsyncTaskInfo(message);
                asyncTaskInfo.setMountTaskInfo(mountTaskInfo);
                boolean cancelMountTask = mountTaskInfo.getStatus() == null && asyncTaskInfo.isOver();
                if (cancelMountTask && !asyncTaskInfo.isContinueExecution()) {
                    asyncTaskInfo.cancel();
                }
                executeImmediately = cancelMountTask && asyncTaskInfo.isContinueExecution();
            } catch (Exception e) {
                log.warn("挂载任务异常：" + e.getMessage());
            }
        }

        if (executeImmediately) {
            exec(executeType, mountTaskInfo);
        }
    }
    /**
     * 执行异步任务并在完成后执行回调
     */
    public void execWithCallBack(ExecuteType executeType, TaskInfoMessage message, WebAsyncTaskInfo mountTaskInfo, Consumer<String> callback) {

        boolean executeImmediately = true;

        if (message.getTaskId() != null) {
            try {
                WebAsyncTaskInfo asyncTaskInfo = resultService.getAsyncTaskInfo(message);
                asyncTaskInfo.setMountTaskInfo(mountTaskInfo);
                boolean cancelMountTask = mountTaskInfo.getStatus() == null && asyncTaskInfo.isOver();
                if (cancelMountTask && !asyncTaskInfo.isContinueExecution()) {
                    asyncTaskInfo.cancel();
                }
                executeImmediately = cancelMountTask && asyncTaskInfo.isContinueExecution();
            } catch (Exception e) {
                log.warn("挂载任务异常：" + e.getMessage());
            }
        }
        if (executeImmediately) {
            execWithCallBack(executeType, mountTaskInfo,callback);
        }
    }

    public void exec(ExecuteType executeType, WebAsyncTaskInfo webAsyncTaskInfo) {
        log.info("执行异步任务 taskId is [{}]", webAsyncTaskInfo.getTaskId());
        exec(executeType, webAsyncTaskInfo::exec);
    }

    /**
     * 执行异步任务并在完成后执行回调
     */
    public void execWithCallBack(ExecuteType executeType, WebAsyncTaskInfo webAsyncTaskInfo,
                    Consumer<String> callback) {
        log.info("执行异步任务（带完成回调） taskId is [{}]", webAsyncTaskInfo.getTaskId());
        exec(executeType, () -> webAsyncTaskInfo.execWithCallback(callback));
    }

    @Override
    protected void delResult(Result result) {
        HandlerCounter.release();
    }

    @Override
    protected void setResult(Result result) {
        HandlerCounter.setOperator(result.operator);
        HandlerCounter.setUserId(result.userId);
        HandlerCounter.setToken(result.token);
        HandlerCounter.setConnectionPattern(result.connectionPattern);
    }

    @Override
    protected Result getResult() {
        final String operator = HandlerCounter.getOperator();
        final String userId = HandlerCounter.getUserId();
        final String token = HandlerCounter.getToken();
        final Integer connectionPattern = HandlerCounter.getConnectionPattern();
        return new Result(operator, userId, token, connectionPattern);
    }


    protected static class Result {
        public final String operator;
        public final String userId;
        public final String token;
        public final Integer connectionPattern;

        public Result(String operator, String userId, String token, Integer connectionPattern) {
            this.operator = operator;
            this.userId = userId;
            this.token = token;
            this.connectionPattern = connectionPattern;
        }
    }

}
