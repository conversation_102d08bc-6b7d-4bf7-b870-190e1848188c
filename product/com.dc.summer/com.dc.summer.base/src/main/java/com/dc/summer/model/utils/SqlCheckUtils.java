package com.dc.summer.model.utils;

import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.springboot.core.model.message.MessageConstants;
import com.dc.springboot.core.model.recovery.RecoveryDataSql;
import com.dc.springboot.core.model.recovery.SqlData;
import com.dc.springboot.core.utils.SqlFieldDataUtils;
import com.dc.summer.DBException;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.model.impl.struct.SimpleContainer;
import com.dc.summer.service.receiver.ResultObjectDataReceiver;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.type.DatabaseType;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class SqlCheckUtils {

    public static boolean isTableStructureChanged(Map<String, SqlFieldData> rcTableStructureMap, Map<String, SqlFieldData> dbTableStructureMap) {

        try {

            // 若无表结构数据,默认变更
            if (rcTableStructureMap.size() == 0 || dbTableStructureMap.size() == 0) {
                return true;
            }

            // 字段个数不一致
            if (rcTableStructureMap.size() != dbTableStructureMap.size()) {
                return true;
            }

            for (Map.Entry<String, SqlFieldData> entry : rcTableStructureMap.entrySet()) {

                // 字段已被删除
                if (dbTableStructureMap.get(entry.getKey()) == null) {
                    return true;
                }

                // 字段类型改变
                if (!entry.getValue().getFieldType().equalsIgnoreCase(dbTableStructureMap.get(entry.getKey()).getFieldType())) {
                    return true;
                }

                boolean dataLengthIsNull = StringUtils.isEmpty(entry.getValue().getDataLength()) || StringUtils.isEmpty(dbTableStructureMap.get(entry.getKey()).getDataLength());
                // 字段长度改变
                if (!dataLengthIsNull && !entry.getValue().getDataLength().equalsIgnoreCase(dbTableStructureMap.get(entry.getKey()).getDataLength())) {
                    return true;
                }

                // 主键改变
                if (!entry.getValue().getIsPrimaryKey().equals(dbTableStructureMap.get(entry.getKey()).getIsPrimaryKey())) {
                    return true;
                }

            }

            return false;

        } catch (Exception e) {
            log.error("judge table structure changed error!", e);
        }

        // 出异常默认表结构变更
        return true;

    }

    public static String replace(String name) {
        if (null == name) {
            return null;
        }
        return name.replaceAll("\"|'|`|\\[|\\]", "");
    }

    public static Map<String, Object> copyMap(Map<String, Object> source) {

        Map<String, Object> target = new HashMap<>();

        for (Map.Entry<String, Object> entry : source.entrySet()) {
            target.put(entry.getKey(), entry.getValue());
        }

        return target;
    }

    public static Map<String, SqlFieldData> arrayToHashMap(List<SqlFieldData> fields) {

        Map<String, SqlFieldData> fieldDataMap = new HashMap<>();

        if (null != fields) {
            fieldDataMap = fields.stream().collect(Collectors.toMap(SqlFieldData::getFieldName, test -> test));
        }

        return fieldDataMap;

    }

    public static Map<String, Object> transMapStringToObject(Map<String, Object> rcdataMap) {

        Map<String, Object> objectMap = new HashMap<>();

        for (Map.Entry<String, Object> entry : rcdataMap.entrySet()) {
            // 若表的字段有id会影响之后字段不匹配的判断
            if (!"id".equals(entry.getKey())) {
                objectMap.put(entry.getKey().replaceFirst("_", ""), entry.getValue());
            }
        }

        return objectMap;

    }

    public static String buildWhereClause(Integer dbType, String pkName) {

        StringBuilder whereClause = new StringBuilder(" where 1=1 ");

        String[] pkSplit = pkName.split("\\,");

        for (String pkColumn : pkSplit) {
            whereClause.append(" and ").append(assembleColumnName(pkColumn, dbType)).append("=? ");
        }

        return whereClause.toString();
    }

    public static List<SqlData> buildWhereClause(Map<String, SqlFieldData> rcTableStructureMap, String pkName, String pkValue) {
        List<SqlData> sqlDataList = new ArrayList<>();
        String[] pkSplit = pkName.split("\\,", -1);
        String[] valueSplit = pkValue.split("\\,", -1);
        if (pkSplit.length != valueSplit.length) {
            throw new ServiceException(MessageConstants.FIELD_MISMATCH.getMessage());
        }
        int idx = 0;
        for (String pk : pkSplit) {
            SqlData sqlData = new SqlData();
            sqlData.setKey(pk);
            sqlData.setValue(valueSplit[idx++]);
            SqlFieldData sqlFieldData = rcTableStructureMap.get(pk);
            if (sqlFieldData != null) {
                sqlData.setData_type(sqlFieldData.getFieldType());
            }
            sqlDataList.add(sqlData);
        }
        return sqlDataList;
    }

    public static String getShowPrimaryKeyValue(String[] pkValue, String pkName) {

        StringBuilder sb = new StringBuilder();
        String[] pkNameSplit = pkName.split("\\,", -1);

        if (pkNameSplit.length != pkValue.length) {
            return "";
        } else {
            for (int i = 0; i < pkNameSplit.length; i++) {
                sb.append(",").append(pkNameSplit[i]).append("=").append(pkValue[i]);
            }
        }

        if (sb.length() > 1) {
            return sb.substring(1);
        } else {
            return "";
        }

    }

    public static Map<String, Object> compareRecord(Map<String, Object> dbRecord, Map<String, Object> rcdataMap) {
        Map<String, Object> objectMap = SqlCheckUtils.copyMap(dbRecord);
        for (Map.Entry<String, Object> entry : dbRecord.entrySet()) {
            String key = entry.getKey();
            if (entry.getValue() == null && rcdataMap.get("_" + key) == null) {
                continue;
            }
            Object cellValue = entry.getValue();
            if (cellValue instanceof java.sql.Timestamp) {
                objectMap.put(entry.getKey(), cellValue.toString());
            }
            String value = cellValue == null ? null : cellValue.toString();
            Object rcdata = rcdataMap.get("_" + key);
            if (value == null) {
                if (rcdata != null) {
                    objectMap.put("rc_new_" + key, rcdata);
                }
            } else {
                if (!value.equals(rcdata)) {
                    if (rcdata == null) {
                        rcdata = "Null";
                    }
                    objectMap.put("rc_new_" + key, rcdata);
                }
            }
        }

        return objectMap;
    }

    public static Map<String, Object> buildInsertRecord(Map<String, Object> rcdataMap) {
        Map<String, Object> insertRecord = new HashMap<>();
        for (Map.Entry<String, Object> entry : rcdataMap.entrySet()) {
            String key = String.valueOf(entry.getKey());
            String value;
            if (entry.getValue() == null) {
                value = null;
            } else {
                value = String.valueOf(entry.getValue());
            }
            if (key.startsWith("_")) {
                insertRecord.put(key.replaceFirst("_", "rc_new_"), value);
            }
        }
        return insertRecord;
    }

    public static List<SqlFieldData> toField(List<Map<String, Object>> object, Integer dbType) {

        List<SqlFieldData> fields = new ArrayList<>();

        if (null != object) {

            for (Map<String, Object> map : object) {

                SqlFieldData sqlFieldData = new SqlFieldData();

                for (String key : map.keySet()) {

                    Object value = map.get(key);
                    if (value == null) {
                        continue;
                    }

                    // 这里Debug的时候有一个空指针异常,以下代码采用相同方式处理
                    if ("COLUMN_NAME".equalsIgnoreCase(key)) {
                        sqlFieldData.setFieldName(value.toString());
                        continue;
                    }

                    if ("COL_NAME".equalsIgnoreCase(key)) {
                        sqlFieldData.setFieldName(value.toString());
                        continue;
                    }

                    if ("COLUMN_TYPE".equalsIgnoreCase(key)) {
                        if (DatabaseType.MYSQL.getValue().equals(dbType)) {
                            String pattern = ".*(\\d+)";
                            Pattern r = Pattern.compile(pattern);
                            String dataLength = value.toString();
                            Matcher m = r.matcher(dataLength);
                            if (m.find()) {
                                sqlFieldData.setDataLength(dataLength.substring(dataLength.indexOf("(") + 1, dataLength.indexOf(")")));
                            }
                        }
                        continue;
                    }

                    if ("DATA_TYPE".equalsIgnoreCase(key)) {
                        if (DatabaseType.HIVE.getValue().equals(dbType)) {
                            String pattern = ".*(\\d+)";
                            Pattern r = Pattern.compile(pattern);
                            String dataLength = value.toString();
                            Matcher m = r.matcher(dataLength);
                            if (m.find()) {
                                sqlFieldData.setDataLength(dataLength.substring(dataLength.indexOf("(") + 1, dataLength.indexOf(")")));
                                value = value.toString().substring(0, dataLength.indexOf("(") + 1);
                            }
                        }
                        sqlFieldData.setFieldType(value.toString());
                        continue;
                    }

                    if ("IS_PRIMARY_KEY".equalsIgnoreCase(key)) {
                        sqlFieldData.setIsPrimaryKey(Integer.parseInt(value.toString()));
                        continue;
                    }

                    if ("DATA_LENGTH".equalsIgnoreCase(key)) {
                        sqlFieldData.setDataLength(value.toString());
                    }
                }

                fields.add(sqlFieldData);
            }
        }

        return fields;
    }

    public static List<Map<String, Object>> getExecuteSqlResults(WebSQLContextInfo contextInfo, DBRProgressMonitor monitor,
                                                                 SimpleContainer simpleContainer, String sql) throws Exception {

        List<Map<String, Object>> objects = new ArrayList<>();

        if (contextInfo.isClosed()) {
            throw new ServiceException("实例连接关闭，当前任务中断");
        }

        try (ResultObjectDataReceiver resultObjectDataReceiver = new ResultObjectDataReceiver(simpleContainer)) {

            DBExecUtils.tryExecuteRecover(contextInfo.getExecutionContext(), contextInfo.getDataSource(), param -> {
                try {
                    DBExecUtils.executeQuery(monitor, contextInfo.getExecutionContext(),
                            "SqlCheck Select", sql, resultObjectDataReceiver, true);
                } catch (DBException e) {
                    throw new InvocationTargetException(e);
                }
            }, contextInfo.recoverBefore(), contextInfo.recoverAfter());

            objects.addAll(resultObjectDataReceiver.getObjects());

        }

        return objects;

    }

    public static Map<String, SqlFieldData> getTableStructureMap(Integer dbType, String schemaName, String tableName,
                                                                 WebSQLContextInfo contextInfo, DBRProgressMonitor monitor,
                                                                 SimpleContainer simpleContainer) throws Exception {
        // 因为执行sqlserver时候这里为空，下面一句报了一个空指针异常，所以更改了一下数据源获取方式
        String tabColumnSql = contextInfo.getDataSourceContainer().getDataSource().getInfo().getTableColumnSql(schemaName, tableName);

        List<Map<String, Object>> objects = SqlCheckUtils.getExecuteSqlResults(contextInfo, monitor, simpleContainer, tabColumnSql);

        List<SqlFieldData> fields = SqlCheckUtils.toField(objects, dbType);

        return SqlCheckUtils.arrayToHashMap(fields);
    }

    public static List<SqlFieldData> getTableColumnsOrderBy(Integer dbType, String schemaName, String tableName,
                                                            WebSQLContextInfo contextInfo, DBRProgressMonitor monitor,
                                                            SimpleContainer simpleContainer) throws Exception {

        String tabColumnSql = contextInfo.getDataSource().getInfo().getTableColumnSql(schemaName, tableName);
        List<Map<String, Object>> objects = SqlCheckUtils.getExecuteSqlResults(contextInfo, monitor, simpleContainer, tabColumnSql);

        return SqlCheckUtils.toField(objects, dbType);
    }

    public static List<String> getTableColumns(Map<String, SqlFieldData> dbTableStructureMap, WebSQLContextInfo contextInfo,
                                               List<SqlFieldData> tableColumnsOrderBy) {

        List<String> list = new ArrayList<>();
        List<String> listTemp = new ArrayList<>();
        List<String> bigDataType = new ArrayList<>();

        try {

            bigDataType = contextInfo.getDataSource().getInfo().getBigDataColumnType();

        } catch (Exception e) {
            log.error("get table columns error!", e);
        }

        for (Map.Entry<String, SqlFieldData> entry : dbTableStructureMap.entrySet()) {
            String key = entry.getKey();
            SqlFieldData value = entry.getValue();
            if (!bigDataType.contains(value.getFieldType().toUpperCase(Locale.ROOT))) {
                listTemp.add(key);
            }
        }

        if (tableColumnsOrderBy != null) {
            // 返回需要顺序
            for (SqlFieldData sqlFieldData : tableColumnsOrderBy) {
                if (listTemp.contains(sqlFieldData.getFieldName())) {
                    list.add(sqlFieldData.getFieldName());
                }
            }

        } else {
            list = listTemp;
        }

        return list;

    }

    public static void filterBigData(WebSQLContextInfo contextInfo, List<Map<String, Object>> objects, List<SqlFieldData> fields) {

        List<String> bigDataType = new ArrayList<>();
        try {
            bigDataType = contextInfo.getDataSource().getInfo().getBigDataColumnType();

        } catch (Exception e) {
            log.error("get big data type error!", e);
        }

        Map<String, SqlFieldData> fieldDataMap = new HashMap<>();
        List<String> columns = new ArrayList<>();
        if (null != fields && fields.size() > 0) {
            columns = fields.stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
            fieldDataMap = fields.stream().collect(Collectors.toMap(SqlFieldData::getFieldName, a -> a, (k1, k2) -> k1));
        }

        int index = 0;
        for (Map<String, Object> object : objects) {

            Map<String, Object> linkMap = new LinkedHashMap<>();

            for (String key : object.keySet()) {
                // 默认采用结果中的列名称。
                String columnName = key;
                int n = 0;
                // 特殊情况如类似MYSQL的SHOW DATABASES结果返回的列名,和fields真实列名不一致时,优先采用Fields得到的列名称.
                if (null != fields && fields.size() > 0 && !columns.contains(key)) {
                    columnName = columns.get(n);
                }
                // 默认VARCHAR
                SqlFieldData fieldData = new SqlFieldData();
                fieldData.setFieldName(columnName);
                fieldData.setFieldType("VARCHAR");

                Object value = object.get(key);

                if (fieldDataMap.containsKey(key)) {
                    fieldData = fieldDataMap.get(key);
                }
                // 过滤大数据库字段类型值
                if (!bigDataType.contains(fieldData.getFieldType().toUpperCase(Locale.ROOT))) {
                    linkMap.put(fieldData.getFieldName(), value);
                }
            }

            objects.set(index++, linkMap);

        }

    }

    public static Map<String, Object> getDBRecord(DBRProgressMonitor monitor, SimpleContainer simpleContainer,
                                                  String query, WebSQLContextInfo contextInfo, String schemaName,
                                                  String tableName, List<String> primaryKeyColumns) throws Exception {
        Map<String, Object> dbRecord = new HashMap<>();

        List<Map<String, Object>> objects = SqlCheckUtils.getExecuteSqlResults(contextInfo, monitor, simpleContainer, query);

        List<SqlFieldData> SqlFields = SqlFieldDataUtils.getExecuteSqlFieldData(contextInfo.getExecutionContext(), monitor, primaryKeyColumns, schemaName, tableName);

        SqlCheckUtils.filterBigData(contextInfo, objects, SqlFields); // 去除大数据类型

        if (objects.size() == 1) {
            dbRecord = objects.get(0);
        }

        return dbRecord;
    }

    public static List<Map<String, Object>> getNotRealPKDBRecord(DBRProgressMonitor monitor, SimpleContainer simpleContainer,
                                                                 String query, WebSQLContextInfo contextInfo, String schemaName,
                                                                 String tableName, List<String> primaryKeyColumns) throws Exception {
        List<Map<String, Object>> list = SqlCheckUtils.getExecuteSqlResults(contextInfo, monitor, simpleContainer, query);

        List<SqlFieldData> SqlFields = SqlFieldDataUtils.getExecuteSqlFieldData(contextInfo.getExecutionContext(), monitor, primaryKeyColumns, schemaName, tableName);

        SqlCheckUtils.filterBigData(contextInfo, list, SqlFields); // 去除大数据类型

        return list;
    }

    public static RecoveryDataSql buildDeleteRecoveryDataSql(StringBuilder build, String where, List<SqlData> whereSqlDataList,
                                                             String fullName, List<String> pkList, List<String> columnNames, String recoveryDataSqlColumns) {
        RecoveryDataSql recoveryDataSql = new RecoveryDataSql();

        String recoverySql = build + where;
        List<SqlData> sqlDataList = new ArrayList<>(whereSqlDataList);

        LinkedHashMap<String, Object> formatData = new LinkedHashMap<>();
        for (SqlData sd : sqlDataList) {
            formatData.put(sd.getKey(), sd.getValue());
        }
        for (SqlData list : whereSqlDataList) {
            formatData.put(list.getKey(), list.getValue());
        }

        recoveryDataSql.setSql(recoverySql);
        recoveryDataSql.setFormatData(formatData);
        recoveryDataSql.setColumnNames(columnNames);
        recoveryDataSql.setPrimaryKeyColumns(pkList);
        if (StringUtils.isNotBlank(fullName) && StringUtils.isNotBlank(recoveryDataSqlColumns)) {
            recoveryDataSql.setQuery("select " + recoveryDataSqlColumns + " from " + fullName);
        }

        return recoveryDataSql;
    }

    public static RecoveryDataSql buildUpdateRecoveryDataSql(Map<String, Object> bdRecord, Map<String, Object> rcdataMap,
                                                             StringBuilder build, String where, List<SqlData> whereSqlDataList,
                                                             Map<String, SqlFieldData> rcTableStructureMap,
                                                             String fullName, List<String> pkList, List<String> columnNames,
                                                             String recoveryDataSqlColumns, Integer dbType) {
        RecoveryDataSql recoveryDataSql = new RecoveryDataSql();
        List<SqlData> sqlDataList = new ArrayList<>();

        StringBuilder setClause = new StringBuilder();
        Map<String, Object> rcdataMapSorted = new TreeMap<>(bdRecord);
        for (Map.Entry<String, Object> entry : rcdataMapSorted.entrySet()) {
            String key = entry.getKey();
            if (entry.getValue() == null && rcdataMap.get("_" + key) == null) {
                continue;
            }
            String value = entry.getValue() == null ? null : entry.getValue().toString();
            Object rcdata = rcdataMap.get("_" + key);
            if (value == null) {
                if (rcdata != null) {
                    setClause.append(", ").append(assembleColumnName(key, dbType)).append("=? ");
                    SqlData sqlData = new SqlData();
                    sqlData.setKey(key);
                    sqlData.setValue(rcdata);
                    SqlFieldData sqlFieldData = rcTableStructureMap.get(key);
                    if (sqlFieldData != null) {
                        sqlData.setData_type(sqlFieldData.getFieldType());
                    }
                    sqlDataList.add(sqlData);
                }
            } else {
                if (!value.equals(rcdata)) {
                    setClause.append(", ").append(assembleColumnName(key, dbType)).append("=? ");
                    SqlData sqlData = new SqlData();
                    sqlData.setKey(key);
                    sqlData.setValue(rcdata);
                    SqlFieldData sqlFieldData = rcTableStructureMap.get(key);
                    if (sqlFieldData != null) {
                        sqlData.setData_type(sqlFieldData.getFieldType());
                    }
                    sqlDataList.add(sqlData);
                }
            }
        }
        sqlDataList.sort((a, b) -> a.getKey().compareTo(b.getKey()));
        LinkedHashMap<String, Object> formatData = new LinkedHashMap<>();
        for (SqlData sd : sqlDataList) {
            formatData.put(sd.getKey(), sd.getValue());
        }
        for (SqlData list : whereSqlDataList) {
            formatData.put(list.getKey(), list.getValue());
        }

        if (setClause.length() > 0) {
            String recoverySql = appendColumns("{setClause}", build.toString(), setClause.substring(1)) + where;
            sqlDataList.addAll(whereSqlDataList);

            recoveryDataSql.setSql(recoverySql);
            recoveryDataSql.setFormatData(formatData);
            recoveryDataSql.setColumnNames(columnNames);
            recoveryDataSql.setPrimaryKeyColumns(pkList);
            if (StringUtils.isNotBlank(fullName) && StringUtils.isNotBlank(recoveryDataSqlColumns)) {
                recoveryDataSql.setQuery("select " + recoveryDataSqlColumns + " from " + fullName);
            }
        }

        return recoveryDataSql;
    }

    public static RecoveryDataSql buildInsertRecoveryDataSql(Map<String, Object> rcdataMap, StringBuilder build,
                                                             Map<String, SqlFieldData> rcTableStructureMap,
                                                             String fullName, List<String> pkList, List<String> columnNames,
                                                             String recoveryDataSqlColumns, Integer dbType) {
        RecoveryDataSql recoveryDataSql = new RecoveryDataSql();
        List<SqlData> sqlDataList = new ArrayList<>();

        StringBuilder nameClause = new StringBuilder();
        StringBuilder valueClause = new StringBuilder();
        Map<String, Object> rcdataMapSorted = new TreeMap<>(rcdataMap);
        for (Map.Entry<String, Object> entry : rcdataMapSorted.entrySet()) {
            String key = String.valueOf(entry.getKey());
            String value;
            if (entry.getValue() == null) {
                value = null;
            } else {
                value = String.valueOf(entry.getValue());
            }
            if (key.startsWith("_")) {
                nameClause.append(", ").append(assembleColumnName(key.replaceFirst("_", ""), dbType));
                if (value == null) {
                    valueClause.append(", ").append("null");
                } else {
                    valueClause.append(", ").append("?");
                }
                SqlData sqlData = new SqlData();
                sqlData.setKey(key.replaceFirst("_", ""));
                sqlData.setValue(value);
                SqlFieldData sqlFieldData = rcTableStructureMap.get(key.replaceFirst("_", ""));
                if (sqlFieldData != null) {
                    sqlData.setData_type(sqlFieldData.getFieldType());
                }
                sqlDataList.add(sqlData);
            }
        }

        LinkedHashMap<String, Object> formatData = new LinkedHashMap<>();
        for (SqlData sd : sqlDataList) {
            formatData.put(sd.getKey(), sd.getValue());
        }

        if (nameClause.length() > 0) {
            String recoverySql = appendColumns("{columnName}", build.toString(), nameClause.substring(1))
                    .replaceFirst("\\{columnValue\\}", valueClause.substring(1));

            recoveryDataSql.setSql(recoverySql);
            recoveryDataSql.setFormatData(formatData);
            recoveryDataSql.setColumnNames(columnNames);
            recoveryDataSql.setPrimaryKeyColumns(pkList);
            if (StringUtils.isNotBlank(fullName) && StringUtils.isNotBlank(recoveryDataSqlColumns)) {
                recoveryDataSql.setQuery("select " + recoveryDataSqlColumns + " from " + fullName);
            }
        }

        return recoveryDataSql;
    }

    public static String appendColumns(String regex, String sql, String columns) {
        int index = sql.indexOf(regex);
        StringBuilder sb = new StringBuilder(sql);
        sql = sb.replace(index, index + regex.length(), columns).toString();
        return sql;
    }

    public static RecoveryDataSql buildNotRealPKRecoveryDataSql(StringBuilder build, Map<String, Object> dbRecord,
                                                                Integer dbType, List<String> pkList,
                                                                Map<String, SqlFieldData> rcTableStructureMap,
                                                                String fullName, List<String> columnNames, String recoveryDataSqlColumns) {

        RecoveryDataSql recoveryDataSql = new RecoveryDataSql();

        List<SqlData> sqlDataList = new ArrayList<>();
        StringBuilder whereSql = new StringBuilder(" WHERE 1=1 ");

        for (Map.Entry<String, Object> dbData : dbRecord.entrySet()) {
            String key = dbData.getKey();
            String value = dbData.getValue().toString();
            if (pkList.contains(key)) {
                whereSql.append(" and ").append(assembleColumnName(key, dbType)).append("=? ");
                SqlData sqlData = new SqlData();
                sqlData.setKey(key);
                sqlData.setValue(value);
                SqlFieldData sqlFieldData = rcTableStructureMap.get(key);
                if (sqlFieldData != null) {
                    sqlData.setData_type(sqlFieldData.getFieldType());
                }
                sqlDataList.add(sqlData);
            }
        }
        sqlDataList.sort((a, b) -> a.getKey().compareTo(b.getKey()));
        LinkedHashMap<String, Object> formatData = new LinkedHashMap<>();
        for (SqlData sd : sqlDataList) {
            formatData.put(sd.getKey(), sd.getValue());
        }

        String recoverySql = build.toString() + whereSql;
        recoveryDataSql.setSql(recoverySql);
        recoveryDataSql.setFormatData(formatData);
        recoveryDataSql.setColumnNames(columnNames);
        recoveryDataSql.setPrimaryKeyColumns(pkList);
        if (StringUtils.isNotBlank(fullName) && StringUtils.isNotBlank(recoveryDataSqlColumns)) {
            recoveryDataSql.setQuery("select " + recoveryDataSqlColumns + " from " + fullName);
        }

        return recoveryDataSql;
    }

    public static String assembleColumnName(String columnName, Integer dbType) {
        if (Arrays.asList(DatabaseType.ORACLE.getValue(), DatabaseType.DM.getValue()).contains(dbType)) {
            columnName = String.format("\"%s\"", columnName);
        } else if (DatabaseType.MYSQL.getValue().equals(dbType)) {
            columnName = String.format("`%s`", columnName);
        }
        return columnName;
    }

    public static String buildFullName(DBPDataSource dataSource, String schemaName, String tableName) {
        SQLDialect dialect = SQLUtils.getDialectFromDataSource(dataSource);
        String formatSchemaName = buildSchemaName(dataSource, schemaName);
        String formatTableName = buildTableName(dataSource, tableName);
        return formatSchemaName + dialect.getStructSeparator() + formatTableName;
    }

    public static String buildSchemaName(DBPDataSource dataSource, String schemaName) {
        return DBUtils.getQuotedIdentifier(dataSource,schemaName);
    }

    public static String buildTableName(DBPDataSource dataSource, String tableName) {
        SQLDialect dialect = SQLUtils.getDialectFromDataSource(dataSource);
        if (dialect.supportsSplitTable()) {
            String structSeparator = String.valueOf(dialect.getStructSeparator());
            String[] tables = tableName.split(Pattern.quote(structSeparator));
            return DBUtils.getFullyQualifiedName(dataSource, tables);
        } else {
            return DBUtils.getQuotedIdentifier(dataSource, tableName);
        }
    }
}
