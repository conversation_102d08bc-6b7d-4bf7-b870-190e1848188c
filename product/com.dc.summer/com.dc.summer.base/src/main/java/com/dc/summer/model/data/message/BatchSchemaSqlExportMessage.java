package com.dc.summer.model.data.message;

import com.dc.springboot.core.model.data.ResultFormat;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.type.*;
import com.dc.summer.model.type.LineDelimiterType;
import com.dc.summer.model.type.PageSelectedType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("多schema批量SQL导出信息")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
public class BatchSchemaSqlExportMessage {


    @NotNull
    @ApiModelProperty(value = "导出批次id", required = true, example = "batch_export_xx")
    private String batchId;

    @ApiModelProperty(value = "excel导出模式(非必填)", example = "MULTI_SHEET/MULTI_FILE")
    private ExportFileMode exportFileMode;

    private boolean multiSheet;


    @ApiModelProperty(value = "文件编码", example = "utf-8")
    private String fileCharset;

    @NotNull
    @ApiModelProperty(value = "导出类型", required = true, example = "XLSX")
    private ExportType exportType;

    @ApiModelProperty(value = "文本识别符", example = "DOUBLE_QUOTE")
    private TextIdentifierType textIdentifier;

    @ApiModelProperty(value = "行分隔符", example = "CRLF")
    private LineDelimiterType lineDelimiter;

    @ApiModelProperty(value = "列分隔符", example = "SEMICOLON")
    private ColumnDelimiterType columnDelimiter;

    @ApiModelProperty(value = "其他分隔符", example = "@")
    private String otherDelimiter;

    @ApiModelProperty(value = "水印内容", example = "SUMMER")
    private String watermarkContent;

    @ApiModelProperty(value = "水印角度", example = "45")
    private Integer watermarkAngle;

    @ApiModelProperty(value = "加密密码", example = "xxx")
    private String encryptPassword;

    @NotNull
    @ApiModelProperty(value = "已选择页面", required = true, example = "CURRENT_PAGE")
    private PageSelectedType pageSelected;

    @NotNull
    @ApiModelProperty(value = "用户ID", required = true, example = "wang")
    private String userId;

    @ApiModelProperty(value = "导出excel是否原格式", required = true, example = "1")
    private int excelUseOriginalFormat;

    @ApiModelProperty(value = "导出excel数字类型是否为数字格式", required = true, example = "1")
    private boolean excelUseNumberFormat;

    @ApiModelProperty(value = "导出excel时间日期格式", required = true, example = "yyyy-MM-dd_HH:mm:ss")
    private String excelDatetimeFormat;

    @Valid
    @ApiModelProperty(value = "告警消息")
    private ExecuteEvent executeEvent = new ExecuteEvent();

    @Valid
    @NotNull
    @ApiModelProperty(value = "多实例多schema导出信息", required = true)
    private List<SingleSchemaSqlExportMessage> singleExportMessages;

    @ApiModelProperty(value = "导出文件名")
    private String exportFileName;

    @ApiModelProperty(value = "拆分文件大小 - 单位MB")
    private Long splitFileSize;

    @ApiModelProperty(value = "结果集格式 - 用于避免重复查询时的格式信息")
    private ResultFormat resultFormat = new ResultFormat();

    @ApiModelProperty(value = "导出为INSERT或UPDATE",example = "INSERT/UPDATE")
    private String exportSqlType;

    // 是否合并为单个文件
    public boolean isMultiSheet() {
        return exportFileMode != null && exportFileMode.equals(ExportFileMode.MULTI_SHEET);
    }

}
