package com.dc.summer.model.privileges;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.*;
import com.dc.repository.mysql.type.GrantObjectType;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.summer.DBException;
import com.dc.summer.component.SummerMapper;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.summer.service.sql.WebSQLContextInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Component
public class OraclePrivilegeNote extends AbstractPrivilegeNote {

    private final DcDbResourceAccountMapper dcDbResourceAccountMapper;

    private final DcAccountObjPrivsMapper dcAccountObjPrivsMapper;

    private final DcAccountSysPrivsMapper dcAccountSysPrivsMapper;

    private final DcAccountRolePrivsMapper dcAccountRolePrivsMapper;

    private final DcAccountTsQuotasMapper dcAccountTsQuotasMapper;

    private final DcDbResourceAccountInfoMapper dcDbResourceAccountInfoMapper;


    private final SummerMapper summerMapper;

    private static final Map<String, List<String>> PRIVILEGE_MAPPING = Map.of(
            "TABLE", List.of("FLASHBACK", "DEBUG", "QUERY REWRITE", "ON COMMIT REFRESH", "REFERENCES",
                    "ALTER", "SELECT", "INSERT", "INDEX", "DELETE", "UPDATE"),
            "VIEW", List.of("MERGE VIEW", "FLASHBACK", "DEBUG", "QUERY REWRITE", "DELETE",
                    "REFERENCES", "UPDATE", "SELECT", "INSERT", "ON COMMIT REFRESH"),
            "SEQUENCE", List.of("ALTER", "SELECT"),
            "PROCEDURE", List.of("EXECUTE", "DEBUG"),
            "FUNCTION", List.of("EXECUTE", "DEBUG"),
            "PACKAGE", List.of("EXECUTE", "DEBUG"),
            "TYPE", List.of("EXECUTE", "DEBUG"),
            "LIBRARY", List.of("EXECUTE", "DEBUG"),
            "DIRECTORY", List.of("READ", "WRITE", "EXECUTE")
    );

    public OraclePrivilegeNote(DcDbResourceAccountMapper dcDbResourceAccountMapper, DcAccountObjPrivsMapper dcAccountObjPrivsMapper, DcAccountSysPrivsMapper dcAccountSysPrivsMapper, DcAccountRolePrivsMapper dcAccountRolePrivsMapper, DcAccountTsQuotasMapper dcAccountTsQuotasMapper, DcDbResourceAccountInfoMapper dcDbResourceAccountInfoMapper, SummerMapper summerMapper) {
        super(dcDbResourceAccountInfoMapper, dcDbResourceAccountMapper);
        this.dcDbResourceAccountMapper = dcDbResourceAccountMapper;
        this.dcAccountObjPrivsMapper = dcAccountObjPrivsMapper;
        this.dcAccountSysPrivsMapper = dcAccountSysPrivsMapper;
        this.dcAccountRolePrivsMapper = dcAccountRolePrivsMapper;
        this.dcAccountTsQuotasMapper = dcAccountTsQuotasMapper;
        this.dcDbResourceAccountInfoMapper = dcDbResourceAccountInfoMapper;
        this.summerMapper = summerMapper;
    }

    @Override
    public List<String> grant(PrivilegeNoteModel privilegeNoteModel) throws DBException {

        PrivilegeModel privilegeModel = privilegeNoteModel.getPrivilegeModel();
        DcDbResource resource = privilegeNoteModel.getResource();
        String schemaName = privilegeNoteModel.getSchemaName();
        String username = privilegeNoteModel.getUserName();
        boolean isSyncJob = privilegeNoteModel.isSyncJob();
        WebSQLContextInfo webSQLContextInfo = privilegeNoteModel.getWebSQLContextInfo();
        String privilegeExpire = privilegeNoteModel.getPrivilegeExpire();

        List<DcAccountObjPrivs> objPrivs = new ArrayList<>();
        List<DcAccountRolePrivs> rolePrivs = new ArrayList<>();
        List<DcAccountSysPrivs> sysPrivs = new ArrayList<>();
        List<String> realGrantees = new ArrayList<>();

        List<String> grantees = privilegeModel.getGrantees();
        List<String> objPrivileges = privilegeModel.getObjPrivileges();
        List<String> systemPrivileges = privilegeModel.getSystemPrivileges();
        List<String> rolePrivileges = privilegeModel.getRolePrivileges();
        String owner = privilegeModel.getOwner();
        if (StringUtils.isBlank(owner)) {
            owner = schemaName;
        }
        String onObject = privilegeModel.getOnObject();
        boolean grantable = privilegeModel.isWithGrantOption();
        boolean hierarchy = privilegeModel.isWithHierarchyOption();
        boolean adminOption = privilegeModel.isWithAdminOption();

        for (String grantee : grantees) {

            LambdaQueryWrapper<DcDbResourceAccount> qwa = Wrappers.<DcDbResourceAccount>lambdaQuery()
                    .eq(DcDbResourceAccount::getResourceId, resource.getUnique_key())
                    .eq(DcDbResourceAccount::getUsername, grantee);
            DcDbResourceAccount dcDbResourceAccount = dcDbResourceAccountMapper.getResourceAccount(qwa);
            if (dcDbResourceAccount == null) {
                log.info("非纳管资源账号，不记录权限 {}", grantee);
                continue;
            }
            realGrantees.add(grantee);

            if (objPrivileges != null && !isSyncJob) {
                for (String privilege : objPrivileges) {
                    DcAccountObjPrivs priv = new DcAccountObjPrivs();
                    priv.setResourceId(resource.getId());
                    priv.setAccountId(dcDbResourceAccount.getId());
                    priv.setIsExpire((byte) 0);
                    priv.setGmtCreate(LocalDateTime.now());
                    priv.setGmtModified(LocalDateTime.now());
                    priv.setGrantee(grantee);
                    priv.setPrivilege(privilege);
                    priv.setCatalogName("");
                    priv.setSchemaName(owner);
                    if (privilegeModel.isDirectory()) {
                        priv.setSchemaName("SYS");
                    }
                    priv.setObjectName(onObject);
                    priv.setObjectType(GrantObjectType.NONE.getName());
                    priv.setGrantor(username);
                    priv.setGrantable(grantable ? (byte) 1 : (byte) 0);
                    priv.setHierarchy(hierarchy ? (byte) 1 : (byte) 0);
                    priv.setIsDelete((byte) 0);
                    if (StringUtils.isNotBlank(privilegeExpire)) {
                        priv.setIsExpire((byte) 1);
                        priv.setPrivilegeExpire(LocalDateTime.parse(privilegeExpire, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                    if (privilege.equalsIgnoreCase("ALL") || privilege.equalsIgnoreCase("ALL PRIVILEGES")) {
                        DBPDataSourceInfo info = webSQLContextInfo.getExecutionContext().getDataSource().getInfo();
                        String objectType = info.getObjectType(new LoggingProgressMonitor(), webSQLContextInfo.getExecutionContext(), priv.getObjectName(), priv.getSchemaName());
                        List<String> privileges = PRIVILEGE_MAPPING.get(objectType);

                        for (String p : privileges) {
                            DcAccountObjPrivs c = summerMapper.copy(priv);
                            c.setPrivilege(p);
                            objPrivs.add(c);
                        }
                    } else {
                        objPrivs.add(priv);
                    }

                }
            }

            if (systemPrivileges != null && !isSyncJob) {
                for (String privilege : systemPrivileges) {
                    DcAccountSysPrivs priv = new DcAccountSysPrivs();
                    priv.setResourceId(resource.getId());
                    priv.setAccountId(dcDbResourceAccount.getId());
                    priv.setIsExpire((byte) 0);
                    priv.setGmtCreate(LocalDateTime.now());
                    priv.setGmtModified(LocalDateTime.now());
                    priv.setGrantee(grantee);
                    priv.setPrivilege(privilege);
                    priv.setAdminOption(adminOption ? (byte) 1 : (byte) 0);
                    priv.setIsDelete((byte) 0);
                    if (StringUtils.isNotBlank(privilegeExpire)) {
                        priv.setIsExpire((byte) 1);
                        priv.setPrivilegeExpire(LocalDateTime.parse(privilegeExpire, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                    if (privilege.equalsIgnoreCase("ALL") || privilege.equalsIgnoreCase("ALL PRIVILEGES")) {
                        String sql = webSQLContextInfo.getExecutionContext().getDataSource().getInfo().generateSystemPrivilegeSql();
                        List<Map<String, Object>> systemPrivilegeList = DBExecUtils.executeQuery(new LoggingProgressMonitor(), webSQLContextInfo.getExecutionContext(), "get system privilege list", sql);
                        List<String> privileges = systemPrivilegeList.stream().map(s -> (String) s.get("PRIVILEGE")).collect(Collectors.toList());
                        for (String p : privileges) {
                            DcAccountSysPrivs c = summerMapper.copy(priv);
                            c.setPrivilege(p);
                            sysPrivs.add(c);
                        }
                    } else {
                        sysPrivs.add(priv);
                    }
                }
            }

            if (rolePrivileges != null && !isSyncJob) {
                for (String privilege : rolePrivileges) {
                    DcAccountRolePrivs priv = new DcAccountRolePrivs();
                    priv.setResourceId(resource.getId());
                    priv.setAccountId(dcDbResourceAccount.getId());
                    priv.setIsExpire((byte) 0);
                    priv.setGmtCreate(LocalDateTime.now());
                    priv.setGmtModified(LocalDateTime.now());
                    priv.setUsername(grantee);
                    priv.setGrantedRole(privilege);
                    priv.setAdminOption(adminOption ? (byte) 1 : (byte) 0);
                    priv.setIsDelete((byte) 0);
                    priv.setDefaultRole((byte) 1);
                    if (StringUtils.isNotBlank(privilegeExpire)) {
                        priv.setIsExpire((byte) 1);
                        priv.setPrivilegeExpire(LocalDateTime.parse(privilegeExpire, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                    rolePrivs.add(priv);
                }
            }

        }

        if (!objPrivs.isEmpty()) {
            if (privilegeModel.isWithGrantOption()) {
                dcAccountObjPrivsMapper.replaceInto(objPrivs);
            } else {
                dcAccountObjPrivsMapper.replaceIntoNoGrantable(objPrivs);
            }
        }
        if (!rolePrivs.isEmpty()) {
            if (privilegeModel.isWithAdminOption()) {
                dcAccountRolePrivsMapper.replaceInto(rolePrivs);
            } else {
                dcAccountRolePrivsMapper.replaceIntoNoAdminOption(rolePrivs);
            }
        }
        if (!sysPrivs.isEmpty()) {
            if (privilegeModel.isWithAdminOption()) {
                dcAccountSysPrivsMapper.replaceInto(sysPrivs);
            } else {
                dcAccountSysPrivsMapper.replaceIntoNoAdminOption(sysPrivs);
            }
        }
        return realGrantees;

    }

    @Override
    public List<String> revoke(PrivilegeNoteModel privilegeNoteModel) {

        PrivilegeModel privilegeModel = privilegeNoteModel.getPrivilegeModel();
        DcDbResource resource = privilegeNoteModel.getResource();
        String schemaName = privilegeNoteModel.getSchemaName();
        boolean isSyncJob = privilegeNoteModel.isSyncJob();

        List<String> grantees = privilegeModel.getGrantees();
        List<String> realGrantees = new ArrayList<>();
        List<String> objPrivileges = privilegeModel.getObjPrivileges();
        List<String> systemPrivileges = privilegeModel.getSystemPrivileges();
        List<String> rolePrivileges = privilegeModel.getRolePrivileges();
        String owner = privilegeModel.getOwner();
        if (StringUtils.isBlank(owner)) {
            owner = schemaName;
        }
        if (privilegeModel.isDirectory()) {
            owner = "SYS";
        }
        String onObject = privilegeModel.getOnObject();
        for (String grantee : grantees) {

            LambdaQueryWrapper<DcDbResourceAccount> qwa = Wrappers.<DcDbResourceAccount>lambdaQuery()
                    .eq(DcDbResourceAccount::getResourceId, resource.getUnique_key())
                    .eq(DcDbResourceAccount::getUsername, grantee);
            DcDbResourceAccount dcDbResourceAccount = dcDbResourceAccountMapper.getResourceAccount(qwa);

            if (dcDbResourceAccount == null) {
                continue;
            }

            realGrantees.add(grantee);

            if (objPrivileges != null && !isSyncJob) {
                for (String privilege : objPrivileges) {
                    LambdaQueryWrapper<DcAccountObjPrivs> qwo = Wrappers.<DcAccountObjPrivs>lambdaQuery()
                            .eq(DcAccountObjPrivs::getResourceId, resource.getId())
                            .eq(DcAccountObjPrivs::getAccountId, dcDbResourceAccount.getId())
                            .eq(DcAccountObjPrivs::getObjectType, GrantObjectType.NONE.getName())
                            .eq(DcAccountObjPrivs::getGrantee, grantee)
                            .eq(DcAccountObjPrivs::getSchemaName, owner)
                            .eq(DcAccountObjPrivs::getObjectName, onObject);
                    if (privilege.equalsIgnoreCase("ALL")) {
                        dcAccountObjPrivsMapper.removePrivilege(qwo);
                    } else {
                        dcAccountObjPrivsMapper.removePrivilege(qwo.eq(DcAccountObjPrivs::getPrivilege, privilege));
                    }
                }
            }

            if (systemPrivileges != null && !isSyncJob) {
                for (String privilege : systemPrivileges) {
                    LambdaQueryWrapper<DcAccountSysPrivs> qws = Wrappers.<DcAccountSysPrivs>lambdaQuery()
                            .eq(DcAccountSysPrivs::getResourceId, resource.getId())
                            .eq(DcAccountSysPrivs::getAccountId, dcDbResourceAccount.getId())
                            .eq(DcAccountSysPrivs::getGrantee, grantee);
                    if (privilege.equalsIgnoreCase("ALL") || privilege.equalsIgnoreCase("ALL PRIVILEGES")) {
                        dcAccountSysPrivsMapper.removePrivilege(qws);
                    } else {
                        dcAccountSysPrivsMapper.removePrivilege(qws.eq(DcAccountSysPrivs::getPrivilege, privilege));
                    }
                }
            }

            if (rolePrivileges != null && !isSyncJob) {
                for (String privilege : rolePrivileges) {
                    LambdaQueryWrapper<DcAccountRolePrivs> qwr = Wrappers.<DcAccountRolePrivs>lambdaQuery()
                            .eq(DcAccountRolePrivs::getResourceId, resource.getId())
                            .eq(DcAccountRolePrivs::getAccountId, dcDbResourceAccount.getId())
                            .eq(DcAccountRolePrivs::getUsername, grantee)
                            .eq(DcAccountRolePrivs::getGrantedRole, privilege);
                    dcAccountRolePrivsMapper.removePrivilege(qwr);
                }
            }

        }
        return realGrantees;
    }

    @Override
    public List<String> alterUser(PrivilegeNoteModel privilegeNoteModel) {

        PrivilegeModel privilegeModel = privilegeNoteModel.getPrivilegeModel();
        DcDbResource resource = privilegeNoteModel.getResource();
        boolean isSyncJob = privilegeNoteModel.isSyncJob();
        String privilegeExpire = privilegeNoteModel.getPrivilegeExpire();

        List<String> realGrantees = new ArrayList<>();
        if (privilegeModel.isQuotas()) {
            LambdaQueryWrapper<DcDbResourceAccount> qwa = Wrappers.<DcDbResourceAccount>lambdaQuery()
                    .eq(DcDbResourceAccount::getResourceId, resource.getUnique_key())
                    .eq(DcDbResourceAccount::getUsername, privilegeModel.getGrantees().get(0));
            DcDbResourceAccount dcDbResourceAccount = dcDbResourceAccountMapper.getResourceAccount(qwa);
            if (dcDbResourceAccount != null) {
                if (!isSyncJob) {
                    DcAccountTsQuotas dcAccountTsQuotas = new DcAccountTsQuotas();
                    dcAccountTsQuotas.setResourceId(resource.getId());
                    dcAccountTsQuotas.setAccountId(dcDbResourceAccount.getId());
                    dcAccountTsQuotas.setIsExpire((byte) 0);
                    dcAccountTsQuotas.setGmtCreate(LocalDateTime.now());
                    dcAccountTsQuotas.setGmtCreate(LocalDateTime.now());
                    dcAccountTsQuotas.setIsDelete((byte) 0);
                    dcAccountTsQuotas.setTablespaceName(privilegeModel.getTablespace());
                    dcAccountTsQuotas.setUsername(privilegeModel.getGrantees().get(0));
                    if (privilegeModel.getMaxBytes() != null && privilegeModel.getMaxBytes().equals("0")) {
                        LambdaQueryWrapper<DcAccountTsQuotas> qwt = Wrappers.<DcAccountTsQuotas>lambdaQuery()
                                .eq(DcAccountTsQuotas::getResourceId, resource.getId())
                                .eq(DcAccountTsQuotas::getAccountId, dcDbResourceAccount.getId())
                                .eq(DcAccountTsQuotas::getTablespaceName, dcAccountTsQuotas.getTablespaceName())
                                .eq(DcAccountTsQuotas::getUsername, dcAccountTsQuotas.getUsername());
                        dcAccountTsQuotasMapper.removePrivilege(qwt);
                    } else {
                        if (StringUtils.isNotBlank(privilegeExpire)) {
                            dcAccountTsQuotas.setIsExpire((byte) 1);
                            dcAccountTsQuotas.setPrivilegeExpire(LocalDateTime.parse(privilegeExpire, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        }
                        if (privilegeModel.isUnlimited()) {
                            dcAccountTsQuotas.setMaxBytes(new BigDecimal("-1"));
                        } else if (privilegeModel.getMaxBytes() != null) {
                            dcAccountTsQuotas.setMaxBytes(new BigDecimal(privilegeModel.getMaxBytes()));
                        }
                        dcAccountTsQuotasMapper.replaceInto(dcAccountTsQuotas);
                    }
                }
                realGrantees.add(privilegeModel.getGrantees().get(0));
            }

        }
        return realGrantees;
    }

}
