package com.dc.summer.model.data.message;

import com.dc.springboot.core.model.data.Message;
import com.dc.springboot.core.model.execution.JobExportMessage;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.type.ColumnDelimiterType;
import com.dc.springboot.core.model.type.ExportType;
import com.dc.springboot.core.model.type.TextIdentifierType;
import com.dc.summer.model.data.model.SqlExportModel;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.data.ResultFormat;
import com.dc.summer.model.type.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("SQL导出信息")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SqlExportMessage extends Message {

    @Valid
    @ApiModelProperty(value = "令牌配置 - 修改里面配置的时候，重新打开会话就行，不会生成新的连接。")
    private TokenConfig tokenConfig;

    @Valid
    @NotNull
    @ApiModelProperty(value = "SQL选择模型列表", required = true)
    private List<SqlExportModel> sqlExportModels;

    @ApiModelProperty(value = "文件编码", example = "utf-8")
    private String fileCharset;

    @NotNull
    @ApiModelProperty(value = "导出类型", required = true, example = "XLSX")
    private ExportType exportType;

    @ApiModelProperty(value = "文本识别符", example = "DOUBLE_QUOTE")
    private TextIdentifierType textIdentifier;

    @ApiModelProperty(value = "行分隔符", example = "CRLF")
    private LineDelimiterType lineDelimiter;

    @ApiModelProperty(value = "列分隔符", example = "SEMICOLON")
    private ColumnDelimiterType columnDelimiter;

    @ApiModelProperty(value = "其他分隔符", example = "@")
    private String otherDelimiter;

    @ApiModelProperty(value = "水印内容", example = "SUMMER")
    private String watermarkContent;

    @ApiModelProperty(value = "水印角度", example = "45")
    private Integer watermarkAngle;

    @ApiModelProperty(value = "加密密码", example = "xxx")
    private String encryptPassword;

    @NotNull
    @ApiModelProperty(value = "已选择页面", required = true, example = "CURRENT_PAGE")
    private PageSelectedType pageSelected;

    @NotNull
    @ApiModelProperty(value = "用户ID", required = true, example = "wang")
    private String userId;

    @ApiModelProperty(value = "导出excel是否原格式", required = true, example = "1")
    private int excelUseOriginalFormat;

    @ApiModelProperty(value = "导出excel数字类型是否为数字格式", required = true, example = "1")
    private boolean excelUseNumberFormat;

    @ApiModelProperty(value = "导出excel时间日期格式", required = true, example = "yyyy-MM-dd_HH:mm:ss")
    private String excelDatetimeFormat;

    @Valid
    @ApiModelProperty(value = "告警消息")
    private ExecuteEvent executeEvent = new ExecuteEvent();

    @ApiModelProperty(value = "是否脱敏导出")
    private boolean exportDesensitize;

    @ApiModelProperty(value = "导出文件名")
    private String exportFileName;

    @ApiModelProperty(value = "拆分文件大小 - 单位MB")
    private Long splitFileSize;

    @ApiModelProperty(value = "结果集格式 - 用于避免重复查询时的格式信息")
    private ResultFormat resultFormat = new ResultFormat();

    @JsonIgnore
    @ApiModelProperty(value = "阶段 - 用于一条SQL生成多个结果集，导出需要传入stage，以区分是第几个结果集，从0开始，(参数自用，不需要php传递)")
    private int stage;

    @ApiModelProperty(value = "是否合并为多个sheet-批量导出用")
    private boolean multiSheet;

    @ApiModelProperty(value = "导出为INSERT或UPDATE",example = "INSERT/UPDATE")
    private String exportSqlType;


    public static SqlExportMessage buildInstance(JobExportMessage jobExportMessage) {
        SqlExportMessage sqlExportMessage = new SqlExportMessage();
        sqlExportMessage.setToken(jobExportMessage.getToken());
        sqlExportMessage.setExportType(jobExportMessage.getExportType());
        sqlExportMessage.setPageSelected(PageSelectedType.CURRENT_PAGE);
        sqlExportMessage.setFileCharset("UTF-8");
        sqlExportMessage.setTextIdentifier(TextIdentifierType.DOUBLE_QUOTE);
        sqlExportMessage.setLineDelimiter(LineDelimiterType.CRLF);
        sqlExportMessage.setUserId(jobExportMessage.getUserId());
        if (ExportType.CSV == jobExportMessage.getExportType()) {
            sqlExportMessage.setColumnDelimiter(ColumnDelimiterType.COMMA);
        } else {
            sqlExportMessage.setColumnDelimiter(ColumnDelimiterType.TAB);
        }
        sqlExportMessage.setExportDesensitize(jobExportMessage.isExportDesensitize());
        sqlExportMessage.setEncryptPassword(jobExportMessage.getEncryptPassword());
        sqlExportMessage.setExcelUseNumberFormat(true);
        sqlExportMessage.setExcelUseOriginalFormat(1);
        sqlExportMessage.setExportSqlType(jobExportMessage.getExportSqlType());
        return sqlExportMessage;
    }

    public static SqlExportMessage buildInstance(BatchSchemaSqlExportMessage batchMsg, SingleSchemaSqlExportMessage singleMsg) {
        SqlExportMessage sqlExportMessage = new SqlExportMessage();

        // 1. token/tokenConfig
        sqlExportMessage.setToken(singleMsg.getToken());
        sqlExportMessage.setTokenConfig(singleMsg.getTokenConfig());

        // 2. SQL导出模型
        sqlExportMessage.setSqlExportModels(singleMsg.getSqlExportModels());

        // 3. 文件编码
        sqlExportMessage.setFileCharset(batchMsg.getFileCharset());

        // 4. 导出类型
        sqlExportMessage.setExportType(batchMsg.getExportType());

        // 5. 文本识别符
        sqlExportMessage.setTextIdentifier(batchMsg.getTextIdentifier());

        // 6. 行分隔符
        sqlExportMessage.setLineDelimiter(batchMsg.getLineDelimiter());

        // 7. 列分隔符
        sqlExportMessage.setColumnDelimiter(batchMsg.getColumnDelimiter());
        sqlExportMessage.setOtherDelimiter(batchMsg.getOtherDelimiter());

        // 8. 水印
        sqlExportMessage.setWatermarkContent(batchMsg.getWatermarkContent());
        sqlExportMessage.setWatermarkAngle(batchMsg.getWatermarkAngle());

        // 9. 加密密码
        sqlExportMessage.setEncryptPassword(batchMsg.getEncryptPassword());

        // 10. 分页类型
        sqlExportMessage.setPageSelected(batchMsg.getPageSelected());

        // 11. 用户ID
        sqlExportMessage.setUserId(batchMsg.getUserId());

        // 12. excel相关
        sqlExportMessage.setExcelUseOriginalFormat(batchMsg.getExcelUseOriginalFormat());
        sqlExportMessage.setExcelUseNumberFormat(batchMsg.isExcelUseNumberFormat());
        sqlExportMessage.setExcelDatetimeFormat(batchMsg.getExcelDatetimeFormat());

        // 13. 导出脱敏
        sqlExportMessage.setExportDesensitize(singleMsg.isExportDesensitize());

        // 14. 导出文件名
        sqlExportMessage.setExportFileName(batchMsg.getExportFileName());

        // 15. 拆分文件大小
        sqlExportMessage.setSplitFileSize(batchMsg.getSplitFileSize());

        // 16. 结果集格式
        sqlExportMessage.setResultFormat(batchMsg.getResultFormat());

        // 17. 批次ID
        sqlExportMessage.setMultiSheet(batchMsg.isMultiSheet());

        sqlExportMessage.setExportSqlType(batchMsg.getExportSqlType());
        // 18. 阶段
//        sqlExportMessage.setStage(singleMsg.getStage());

        // 19. 告警事件
//        if (singleMsg.getTokenConfig() != null && singleMsg.getTokenConfig().getExecuteEvent() != null) {
//            sqlExportMessage.setExecuteEvent(singleMsg.getTokenConfig().getExecuteEvent());
//        } else {
//            sqlExportMessage.setExecuteEvent(batchMsg.getExecuteEvent());
//        }

        return sqlExportMessage;
    }
}
