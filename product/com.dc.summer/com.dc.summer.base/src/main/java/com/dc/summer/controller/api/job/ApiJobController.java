package com.dc.summer.controller.api.job;

import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.exec.model.observer.ContextSubject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 提供给job的接口
 */
@RequestMapping("/api")
@Slf4j
public class ApiJobController {

    @RequestMapping("/get-remote-password")
    public Result<String> getRemotePassword(@RequestBody DatabaseConnectionDto instance) {
        ConnectionConfig connectionConfig = instance.buildConnectionConfig(null, null);
        ConnectionConfiguration configuration = connectionConfig.getConnectionConfiguration();
        ContextSubject.trigger(contextObserver -> contextObserver.fillingConfigurationPassword(configuration));
        return Result.success(configuration.getUserPassword());
    }

}
