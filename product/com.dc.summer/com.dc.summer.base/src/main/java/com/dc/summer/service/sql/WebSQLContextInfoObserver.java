package com.dc.summer.service.sql;

import com.dc.springboot.core.component.Resource;
import com.dc.summer.exec.model.type.OperationType;
import com.dc.summer.exec.model.observer.ContextObserver;
import com.dc.summer.exec.model.observer.ContextSubject;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.log.LifeCycleMessage;
import com.dc.summer.service.MessageService;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
@Slf4j
public class WebSQLContextInfoObserver implements ContextObserver {

    private static volatile boolean init;

    private WebSQLContextInfoObserver() {
    }

    @Override
    public void closeSpecifiedContext(String token) {
        WebSQLContextInfo.delContext(token);
    }

    @Override
    public void printLogCloseLifeCycle(DBCExecutionContext context, String token, String scene, String errorMessage) {

        LifeCycleMessage message = new LifeCycleMessage();
        message.setWindowId(token);
        message.setOperationType(OperationType.CLOSE_CONNECTION.getValue());
        message.setOperationContent(String.format(OperationType.CLOSE_CONNECTION.getDesc(), scene));

        if (errorMessage != null) {
            message.setMessage(errorMessage);
        }

        Resource.getBeanRequireNonNull(MessageService.class).printLogLifeCycle(context, message);
    }

    @Override
    public void printLogOpenLifeCycle(DBCExecutionContext context, String token, String scene) {
        try {
            LifeCycleMessage message = new LifeCycleMessage();
            message.setWindowId(token);
            message.setOperationType(OperationType.OPEN_CONNECTION.getValue());
            message.setOperationContent(String.format(OperationType.OPEN_CONNECTION.getDesc(), scene));
            WebSQLContextInfo contextInfo = WebSQLContextInfo.getSimpleContext(token);
            Map<String, Object> preferences = contextInfo.getPreferences();
            message.setPreferences(preferences);
            message.setShortConnect(contextInfo.getShortConnect());
            Resource.getBeanRequireNonNull(MessageService.class).printLogLifeCycle(context, message);
        } catch (Exception e) {
            log.error("printLogOpenLifeCycle error,token:{}",token, e);
        }
    }

    public static void init() {
        if (!init) {
            init = true;
            ContextSubject.register(new WebSQLContextInfoObserver());
        }
    }

}
