package com.dc.summer.model.chain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.*;
import com.dc.repository.mysql.type.GrantObjectType;
import com.dc.repository.mysql.type.PrivilegeOperationType;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.chain.AbstractChain;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.summer.DBException;
import com.dc.summer.component.SummerMapper;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.log.PrivilegeMessage;
import com.dc.summer.model.privileges.PrivilegeNote;
import com.dc.summer.model.privileges.PrivilegeNoteModel;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.summer.service.MessageService;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.type.DatabaseType;
import com.dc.utils.CipherUtils;
import com.dc.utils.PasswordStrengthUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
public class PrivilegesNoteChain extends AbstractChain<WebSQLQueryResult> {

    private final WebSQLContextInfo webSQLContextInfo;

    private final MessageService messageService;

    private final PrivilegeModel privilegeModel;

    private final String privilegeExpire;

    private final DcDbResourceMapper dcDbResourceMapper;


    public PrivilegesNoteChain(WebSQLContextInfo contextInfo, PrivilegeModel privilegeModel, String privilegeExpire) {
        this.webSQLContextInfo = contextInfo;
        this.privilegeModel = privilegeModel;
        this.dcDbResourceMapper = Resource.getBean(DcDbResourceMapper.class);
        this.messageService = Resource.getBean(MessageService.class);
        this.privilegeExpire = privilegeExpire;
    }

    @Override
    public boolean proceed(WebSQLQueryResult webSQLQueryResult) {

        if (privilegeModel == null) {
            return true;
        }

        if (!privilegeModel.isNeeded()) {
            return true;
        }

        boolean isSuccess = webSQLQueryResult.getStatus() == SqlExecuteStatus.SUCCESS.getValue();
        boolean isSyncJob = privilegeModel.isSyncJob();
        boolean isJob = privilegeModel.isJob();
        try {
            ConnectionConfiguration configuration = webSQLContextInfo.getConfiguration();
            Integer dbType = configuration.getDatabaseType().getValue();
            String connectionDesc = configuration.getConnectionDesc();
            String userName = configuration.getUserName();
            List<String> resourceIds = privilegeModel.getResourceIds();
            List<DcDbResource> resources = null;
            if (!resourceIds.isEmpty()) {
                LambdaQueryWrapper<DcDbResource> qw = new LambdaQueryWrapper<DcDbResource>()
                        .in(DcDbResource::getUnique_key, resourceIds);

                resources = dcDbResourceMapper.selectList(qw);
            } else {
                resources = dcDbResourceMapper.getResourcesByConnectionDesc(dbType, connectionDesc);
            }
            if (CollectionUtils.isEmpty(resources)) {
                log.info("非纳管资源，不记录权限");
                return true;
            }

            Integer operationType = privilegeModel.getOperateType();
            PrivilegeOperationType otyp = PrivilegeOperationType.fromCode(operationType);


            PrivilegeNoteModel model = PrivilegeNoteModel.build(
                    webSQLContextInfo, privilegeModel,
                    webSQLQueryResult,
                    privilegeExpire,
                    isSuccess, isSyncJob
            );

            PrivilegeNote note = PrivilegeNote.getPrivilegeNote(DatabaseType.of(dbType));


            List<String> realGrantees = new ArrayList<>();
            for (DcDbResource resource : resources) {

                model.setResource(resource);
                if (otyp == PrivilegeOperationType.GRANT) {

                    realGrantees = note.grant(model);

                } else if (otyp == PrivilegeOperationType.REVOKE) {

                    realGrantees = note.revoke(model);

                } else if (otyp == PrivilegeOperationType.ALTER_USER) {

                    realGrantees = note.alterUser(model);

                } else if (otyp == PrivilegeOperationType.CREATE_USER && isSuccess) {

                    note.createUser(model);

                }

                if (realGrantees.isEmpty()) {
                    continue;
                }
                if (DatabaseType.of(dbType) == DatabaseType.MYSQL) {
                    final LoggingProgressMonitor monitor = new LoggingProgressMonitor();
                    List<Map<String, Object>> currentUser = DBExecUtils.executeQuery(monitor, webSQLContextInfo.getExecutionContext(), "Get current user", "SELECT CURRENT_USER() AS USER");
                    if (!CollectionUtils.isEmpty(currentUser)) {
                        userName = currentUser.get(0).get("USER").toString();
                    }
                }

                // 审计
                PrivilegeMessage privilegeMessage = new PrivilegeMessage();
                privilegeMessage.setResourceId(resource.getUnique_key());
                privilegeMessage.setResourceType(configuration.getDatabaseType().getValue());
                privilegeMessage.setResourceTypeZh(configuration.getDatabaseType().getName());
                privilegeMessage.setResource(resource.getResource_name());
                privilegeMessage.setAccount(String.join(",", realGrantees));
                privilegeMessage.setOperationContent(webSQLQueryResult.getSql());
                privilegeMessage.setSqlId(isSyncJob || isJob ? UUID.randomUUID().toString().replace("-", "") : webSQLQueryResult.getSqlHistory().getSqlId());
                privilegeMessage.setIsDelete(0);
                if (webSQLQueryResult.getMessage() == null) {
                    privilegeMessage.setExecuteResult("成功");
                } else {
                    privilegeMessage.setExecuteResult(webSQLQueryResult.getMessage());
                }
                if (isJob) {
                    privilegeMessage.setSourceZh("任务管理");
                    privilegeMessage.setSource(OriginType.JOB_EXECUTE.getValue());
                    privilegeMessage.setAuthorizerId(privilegeModel.getUserId());
                    privilegeMessage.setAuthorizer(privilegeModel.getUserName());
                } else {
                    privilegeMessage.setSourceZh(isSyncJob ? OriginType.JOB_SYNC_PRIVILEGE.getName() : webSQLQueryResult.getSqlHistory().getOriginZh());
                    privilegeMessage.setSource(isSyncJob ? OriginType.JOB_SYNC_PRIVILEGE.getValue() : webSQLQueryResult.getSqlHistory().getOrigin());
                    privilegeMessage.setAuthorizerId(isSyncJob ? "" : webSQLQueryResult.getSqlHistory().getUserId());
                    privilegeMessage.setAuthorizer(isSyncJob ? "" : webSQLQueryResult.getSqlHistory().getName());
                }
                privilegeMessage.setGrantor(userName);
                privilegeMessage.setGmtCreate(Instant.now().toEpochMilli());
                privilegeMessage.setGmtModified(Instant.now().toEpochMilli());
                messageService.printLogPrivilege(webSQLContextInfo.getExecutionContext(), privilegeMessage);

            }

            return true;
        } catch (Exception e) {
            log.error("权限录入失败！", e);
            return false;
        }
    }
}
