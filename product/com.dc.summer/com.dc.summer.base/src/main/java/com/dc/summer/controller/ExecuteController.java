package com.dc.summer.controller;

import com.dc.springboot.core.model.data.BatchMessage;
import com.dc.springboot.core.model.data.Message;
import com.dc.springboot.core.model.data.PreferencesMessage;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.ConnectionTokenMessage;
import com.dc.springboot.core.model.database.TestConnectionMessage;
import com.dc.springboot.core.model.exception.ConnectionException;
import com.dc.springboot.core.model.exception.NotFindTokenException;
import com.dc.springboot.core.model.exception.ResultException;
import com.dc.springboot.core.model.execution.*;
import com.dc.springboot.core.model.script.ParseScriptMessage;
import com.dc.summer.DBException;
import com.dc.summer.exec.model.observer.ContextSubject;
import com.dc.summer.model.data.DynamicSqlInfo;
import com.dc.summer.model.data.WebAsyncTaskReact;
import com.dc.summer.model.thread.SummerThreadScheduler;
import com.dc.springboot.core.model.database.TestData;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.data.message.*;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.type.ExecuteType;
import com.dc.summer.service.DataSourceService;
import com.dc.summer.service.DynamicSqlService;
import com.dc.summer.service.ExecuteService;
import com.dc.summer.service.ResultService;
import com.dc.summer.service.export.ExportFacadeService;
import com.dc.springboot.core.model.data.Result;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.springboot.core.model.result.WebSQLExecuteInfo;
import com.dc.summer.service.sql.WebSQLContextStatus;
import com.dc.springboot.core.model.script.WebSQLScriptInfo;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.*;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "执行控制器")
@Slf4j
@RequestMapping("/execute")
public class ExecuteController {

    @Resource
    private ExecuteService executeService;

    @Resource
    private DataSourceService dataSourceService;

    @Resource
    private ResultService resultService;

    @Resource
    private SummerThreadScheduler scheduler;

    @Resource
    private ExportFacadeService exportFacadeService;

    @Resource
    private DynamicSqlService dynamicSqlService;

    @ApiOperation("执行绑定SQL - 同步执行绑定SQL，根据 select 判断 dml 的绑定方式")
    @PostMapping(value = "/binding-execute-sql")
    public Result<WebSQLExecuteInfo> bindingExecuteSql(@RequestBody @Valid BindingExecuteMessage message) {
        WebSQLExecuteInfo executeInfo = executeService.executeBindingStatement(message);
        return Result.success(executeInfo);
    }

    @ApiOperation("单一执行SQL - 同步执行SQL，立刻返回结果集")
    @PostMapping(value = "/single-execute-sql")
    public Result<WebSQLExecuteInfo> singleExecuteSql(@RequestBody @Valid SingleSyncExecuteMessage message) {
        WebSQLExecuteInfo executeInfo = executeService.syncExecuteSingleQuery(message, !message.isKeepTask());
        return Result.success(executeInfo);
    }

    @ApiOperation("批量执行SQL - 同步执行脚本，立刻返回结果集")
    @PostMapping(value = "/batch-execute-sql")
    public Result<WebSQLExecuteInfo> batchExecuteSql(@RequestBody @Valid BatchSyncExecuteMessage message) {
        WebSQLExecuteInfo executeInfo = executeService.syncExecuteBatchQuery(message);
        return Result.success(executeInfo);
    }

    @ApiOperation("执行SQL - SQL窗口（无审核），执行完成后可以查询信息、查询结果集")
    @PostMapping(value = "/execute-sql")
    public Result<WebAsyncTaskInfo> executeSql(@RequestBody @Valid SingleAsyncExecuteMessage message) {
        WebAsyncTaskInfo webAsyncTaskInfo = executeService.asyncExecuteSingleQuery(message);
        scheduler.exec(ExecuteType.EXECUTE_SQL, webAsyncTaskInfo);
        return Result.success(webAsyncTaskInfo);
    }

    @ApiOperation("执行批量 - SQL窗口（有审核），异步执行SQL，快捷变更需要调用此接口。")
    @PostMapping(value = "/execute-batch")
    public Result<WebAsyncTaskInfo> executeBatch(@RequestBody @Valid BatchAsyncExecuteMessage message) {
        WebAsyncTaskInfo webAsyncTaskInfo = executeService.asyncExecuteBatchQuery(message);
        scheduler.execWithCallBack(ExecuteType.EXECUTE_BATCH, message.getTaskInfoMessage(), webAsyncTaskInfo, WebSQLContextInfo::closeShortConnection);
        return Result.success(webAsyncTaskInfo);
    }

    @ApiOperation("执行导出 - 进行结果集导出相关操作")
    @PostMapping(value = "/execute-export")
    public Result<WebAsyncTaskInfo> executeExport(@RequestBody @Valid SqlExportMessage message) {
        WebAsyncTaskInfo webAsyncTaskInfo = exportFacadeService.executeExport(message);
        scheduler.execWithCallBack(ExecuteType.EXECUTE_EXPORT, webAsyncTaskInfo, WebSQLContextInfo::closeShortConnection);
        return Result.success(webAsyncTaskInfo);
    }

    @ApiOperation("关闭连接 - 关闭当前窗口的 Session")
    @PostMapping(value = "/close-session")
    public Result<Object> closeSession(@RequestBody @Valid Message message) {
        String token = message.getToken();
        WebSQLContextInfo.recordClosed(token);
        scheduler.exec(ExecuteType.CLOSE_SESSION, () -> WebSQLContextInfo.closeContext(token));
        return Result.success();
    }

    @ApiOperation("批量关闭连接 - 这是一个实时的接口")
    @PostMapping(value = "/batch-close-session")
    public Result<List<Boolean>> batchCloseDatasource(@RequestBody @Valid BatchMessage message) {
        List<Boolean> closeSuccess = message.getTokens()
                .stream()
                .map(token -> {
                    try {
                        WebSQLContextInfo contextInfo = WebSQLContextInfo.getSimpleContext(token);
                        executeService.killConnection(contextInfo);
                        if (contextInfo.getAsyncTasks().stream().allMatch(WebAsyncTaskInfo::isOver)) {
                            ContextSubject.trigger(contextObserver -> {
                                try {
                                    contextObserver.printLogCloseLifeCycle(contextInfo.getExecutionContext(), token, "手动关闭", null);
                                } catch (DBException ignored) {
                                }
                            });
                            contextInfo.close();
                        } else {
                            contextInfo.setStatus(WebSQLContextStatus.CLOSED);
                        }
                    } catch (NotFindTokenException notFindTokenException) {
                        return false;
                    } catch (ConnectionException connectionException) {
                        WebSQLContextInfo.closeContext(token);
                    }
                    return true;
                })
                .collect(Collectors.toList());
        return Result.success(closeSuccess);
    }

    @ApiOperation("打开连接 - 通过参数直接连接")
    @PostMapping(value = "/open-session")
    public Result<Object> openSession(@RequestBody @Valid ConnectionTokenMessage message) {
        WebSQLContextInfo.openExecuteContext(message);
        return Result.success(null, "打开连接成功！");
    }

    @ApiOperation("构建连接 - 通过token获取cache后连接")
    @PostMapping(value = "/make-session")
    public Result<Object> makeSession(@RequestBody @Valid PreferencesMessage message) {
        WebSQLContextInfo.makeExecuteContext(message);
        return Result.success(null, "打开连接成功！");
    }

    @ApiOperation("终止执行 - 中止之前执行的 SQL 操作，通过进行杀死相关线程")
    @PostMapping(value = "/kill-execute")
    public Result<Object> killExecute(@RequestBody @Valid KillExecuteMessage message) {

        String token = message.getToken();

        scheduler.exec(ExecuteType.KILL_EXECUTE,
                () -> {
                    WebSQLContextInfo context = WebSQLContextInfo.getSimpleContext(token);
                    context.setSerialNumber(message.getSerialNumber());
                    context.setStatus(WebSQLContextStatus.INTERRUPTED);
                    executeService.killConnection(context);
                });

        return Result.success();
    }

    @ApiOperation("预览BLOB - 预览服务器 BLOB 文件")
    @PostMapping(value = "/preview-blob")
    public Result<WebAsyncTaskInfo> previewBlob(@RequestBody @Valid PreviewBlobMessage message) {
        WebAsyncTaskInfo webAsyncTaskInfo = executeService.asyncPreviewBlob(message);
        scheduler.execWithCallBack(ExecuteType.PREVIEW_BLOB, webAsyncTaskInfo, WebSQLContextInfo::closeShortConnection);
        return Result.success(webAsyncTaskInfo);
    }

    @ApiOperation("提交更新 - 结果集执行更新、添加、删除")
    @PostMapping(value = "/submit-update")
    public Result<WebAsyncTaskInfo> submitUpdate(@RequestBody @Valid SqlUpdateMessage message) {
        WebAsyncTaskInfo webAsyncTaskInfo = executeService.asyncExecuteUpdate(message);
        scheduler.execWithCallBack(ExecuteType.SUBMIT_UPDATE, webAsyncTaskInfo, WebSQLContextInfo::closeShortConnection);
        return Result.success(webAsyncTaskInfo);
    }

    @ApiOperation("检查回收 - 点击数据恢复，展示差异数据，结果存在redis中。")
    @PostMapping(value = "/check-recycle")
    public Result<WebAsyncTaskInfo> checkRecycle(@RequestBody @Valid CheckRecycleMessage message) {
        WebAsyncTaskInfo webAsyncTaskInfo = executeService.asyncCheckRecycle(message);
        scheduler.exec(ExecuteType.CHECK_RECYCLE, webAsyncTaskInfo);
        return Result.success(webAsyncTaskInfo);
    }

    @ApiOperation("任务导出 - 异步执行SQL，根据参数判断执行后，是否需要导出，定时任务需要调用接口。")
    @PostMapping(value = "/job-export")
    public Result<WebAsyncTaskInfo> jobExport(@RequestBody @Valid JobExportMessage message) {
        WebAsyncTaskInfo webAsyncTaskInfo = executeService.asyncJobExport(message);
        scheduler.exec(ExecuteType.JOB_EXPORT, webAsyncTaskInfo);
        return Result.success(webAsyncTaskInfo);
    }

    @ApiOperation("查询任务 - 对执行的SQL任务，进行异步查询")
    @PostMapping(value = "/task-info")
    public Result<WebAsyncTaskInfo> taskInfo(@RequestBody @Valid TaskInfoMessage taskMessage) throws DBCException {
        WebAsyncTaskInfo asyncTaskInfo = resultService.getAsyncTaskInfo(taskMessage);
        return Result.success(asyncTaskInfo);
    }

    @ApiOperation("查询结果 - 对执行的SQL结果集，进行异步查询")
    @PostMapping(value = "/task-result")
    public Result<WebSQLExecuteInfo> taskResult(@RequestBody @Valid TaskResultMessage taskMessage) throws ResultException {
        WebSQLExecuteInfo webSQLExecuteInfo = resultService.asyncSqlExecuteResults(taskMessage);
        return Result.success(webSQLExecuteInfo);
    }

    @ApiOperation("反应任务 - 阻塞接口一段时间，时间内返回任务状态或者任务结果")
    @PostMapping(value = "/task-react")
    public Result<WebAsyncTaskReact> taskReact(@RequestBody @Valid TaskInfoMessage taskMessage) throws DBCException, ResultException, InterruptedException {
        WebAsyncTaskReact asyncTaskReact = resultService.syncSqlExecuteResults(taskMessage);
        return Result.success(asyncTaskReact);
    }

    @ApiOperation("解析脚本 - SQL脚本进行拆分")
    @PostMapping(value = "/parse-script")
    public Result<WebSQLScriptInfo> parseScript(@RequestBody @Valid ParseScriptMessage message) {
        return Result.success(executeService.parseScript(message));
    }

    @ApiOperation("测试连接 - 调用此接口返回连接成功的驱动ID")
    @PostMapping(value = "/test-connection")
    public Result<TestData> testConnection(@RequestBody @Valid TestConnectionMessage message) {
        String driverId = WebSQLContextInfo.test(message);
        return Result.success(new TestData(driverId), "测试连接成功！");
    }

    @ApiOperation("准备连接 - 测试连接后，初始化数据源")
    @PostMapping(value = "/prepare-connection")
    public Result<TestData> prepareConnection(@RequestBody @Valid ConnectionConfig connectionConfig) {
        String driverId = WebSQLContextInfo.test(connectionConfig);
        connectionConfig.setDriverId(driverId);
        scheduler.exec(ExecuteType.PREPARE_CONNECTION, () -> WebSQLContextInfo.prepare(connectionConfig));
        return Result.success(new TestData(driverId), "准备连接成功！");
    }

    @ApiOperation("获取连接属性 - 调用此接口返回驱动连接属性")
    @PostMapping(value = "/connection-properties")
    public Result<Properties> connectionProperties(@RequestBody @Valid DriverPropertiesMessage message) {
        return Result.success(dataSourceService.syncGetDriverProperties(message));
    }

    @ApiOperation("获取连接属性 - 调用此接口返回驱动连接属性")
    @PostMapping(value = "/all-connection-properties")
    public Result<Map<String, Properties>> allConnectionProperties() {
        return Result.success(dataSourceService.syncGetDriverProperties());
    }

    @ApiOperation("SQL 生成")
    @PostMapping(value = "/sql-generate")
    public Result<String> sqlGenerate(@RequestBody @Valid SqlGenerateMessage message) {
        return Result.success(executeService.sqlGenerate(message));
    }

    @ApiOperation("动态SQL")
    @PostMapping(value = "dynamic-sql")
    public Result<DynamicSqlInfo> dynamicSql(@RequestBody @Valid DynamicSqlMessage message) {
        return Result.success(dynamicSqlService.generateSql(message));
    }

    @ApiOperation("赢得心跳 - 发送当前窗口的心跳，保持 Session 的连接")
    @PostMapping(value = "/win-heartbeat")
    public Result<List<String>> winHeartbeat(@RequestBody BatchMessage message) {
        return Result.success(WebSQLContextInfo.beating(message));
    }

}
