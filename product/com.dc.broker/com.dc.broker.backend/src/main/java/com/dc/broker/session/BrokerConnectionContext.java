package com.dc.broker.session;

import com.dc.broker.model.AccessInfo;
import com.dc.repository.redis.model.EnvConnection;
import com.dc.repository.redis.model.EnvSchema;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.summer.DBException;
import com.dc.summer.exec.config.SessionConfig;
import com.dc.summer.exec.handler.DataSourceConnectionHandler;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.exec.model.thread.BufferThread;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.summer.model.struct.rdb.DBSCatalog;
import io.netty.util.AttributeMap;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Connection session.
 */
@Slf4j
public final class BrokerConnectionContext {

    private static final DBRProgressMonitor MONITOR = new LoggingProgressMonitor();

    private static final Map<String, BrokerConnectionContext> contexts = new ConcurrentHashMap<>();

    private static final AtomicInteger SERIAL_NUMBER = new AtomicInteger(0);
    private final AttributeMap attributeMap;
    @Getter
    private final String serialNumber;

    @Getter
    @Setter
    private Boolean autoCommit;

    @Getter
    private ConnectionConfiguration configuration;

    private DataSourceConnectionHandler dataSourceContainer;

    private DBPDataSource dataSource;

    @Getter
    private BrokerStatementProcessor statementProcessor;

    @Getter
    private AccessInfo accessInfo;

    @Getter
    @Setter
    private EnvConnection envConnection;

    @Getter
    @Setter
    private EnvSchema envSchema;

    @Getter
    @Setter
    private String clientIp;

    public DBPDataSource getDataSource() {
        return dataSource;
    }

    public DataSourceConnectionHandler getDataSourceContainer() {
        return dataSourceContainer;
    }
    
    public BrokerConnectionContext(AttributeMap attributeMap) {
        this.attributeMap = attributeMap;
        this.serialNumber = "BROKER_CONNECTION_" + SERIAL_NUMBER.incrementAndGet();
        contexts.put(this.serialNumber, this);
    }

    public static void setDefaults(DBRProgressMonitor monitor, String catalogName, String schemaName, DBCExecutionContext context) throws DBException {

        DBCExecutionContextDefaults<?, ?> contextDefaults = context.getContextDefaults();

        String oldCatalogName = null;

        if (contextDefaults != null) {
            DBSCatalog catalog = contextDefaults.getDefaultCatalog();
            if (catalog != null) {
                oldCatalogName = catalog.getName();
            }
        }

        DBExecUtils.setExecutionContextDefaults(
                monitor,
                context.getDataSource(),
                context,
                catalogName,
                oldCatalogName,
                schemaName);
    }

    public boolean openSession(ConnectionConfig connectionConfig, AccessInfo accessInfo) throws DBException {
        ConnectionConfiguration connectionConfiguration = connectionConfig.getConnectionConfiguration();
        this.configuration = connectionConfiguration;
        this.dataSourceContainer = DataSourceConnectionHandler.handle(connectionConfiguration);
        this.dataSource = this.dataSourceContainer.getDataSource();
        this.accessInfo = accessInfo;
        this.accessInfo.setIp(this.clientIp);
        DBCExecutionContext executionContext = getExecutionContext();
        this.statementProcessor = new BrokerStatementProcessor(this, true);
        String schemaName = connectionConfig.getSchemaName();
        if (StringUtils.isNotBlank(schemaName)) {
            setDefaults(MONITOR, connectionConfig.getCatalogName(), schemaName, executionContext);
        }
        log.info("Client: {} Open Session, ConnectionId is: {}", this.serialNumber, this.configuration.getConnectionId());
        return executionContext.isConnected();
    }

    public DBCSession openSession(DBRProgressMonitor monitor, DBCExecutionPurpose purpose, String title) throws DBException {
        DBCExecutionContext context = getExecutionContext();
        DBCSession session = context.openSession(monitor, purpose, title);
        if (session.getDataSource().getInfo().supportsSetNetworkTimeout()) {
            setNetworkTimeout(session);
        }
        return session;
    }

    private void setNetworkTimeout(DBCSession session) {
        Integer networkTimeout = SessionConfig.getInstance().getNetworkTimeout();
        if (session.getPurpose() != DBCExecutionPurpose.USER_SCRIPT && networkTimeout != null && networkTimeout > 0) {
            try {
                if (session instanceof Connection) {
                    ((Connection) session).setNetworkTimeout(BufferThread.getExecutor(), networkTimeout * 1000);
                }
            } catch (SQLException e) {
                log.error("设置网络超时失败！");
            }
        }
    }

    public DBCExecutionContext getExecutionContext() throws DBException {
        return this.dataSourceContainer.getExecutionContext(serialNumber, DBCExecutionPurpose.UTIL.getId(), autoCommit,true, false,"", configuration.getCharset(), configuration);
    }

    public void close() {
        if (this.dataSourceContainer != null) {
            this.dataSourceContainer.closeExecutionContext(serialNumber);
        }
        contexts.remove(this.serialNumber);
    }

}
