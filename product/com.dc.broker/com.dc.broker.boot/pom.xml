<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dc</groupId>
        <artifactId>com.dc.broker</artifactId>
        <version>1.0</version>
    </parent>


    <artifactId>dc-broker</artifactId>
    <version>1.0</version>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.broker.base</artifactId>
            <version>${parent.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.broker.frontend</artifactId>
            <version>${parent.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.broker.backend</artifactId>
            <version>${parent.version}</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>${artifactId}-pure</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.12.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.github.core-lib</groupId>
                <artifactId>xjar-maven-plugin</artifactId>
                <version>4.0.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>build</goal>
                        </goals>
                        <phase>package</phase>
                        <configuration>
                            <password>data-captain</password>
                            <includes>
                                <include>com/dc/**/*.class</include>
                            </includes>
                            <sourceJar>${artifactId}-pure.jar</sourceJar>
                            <targetJar>${artifactId}-temp.jar</targetJar>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.core-lib</groupId>
                <artifactId>slot-maven-plugin</artifactId>
                <version>4.0.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>transform</goal>
                        </goals>
                        <phase>package</phase>
                        <configuration>
                            <sourceJar>${artifactId}-temp.jar</sourceJar>
                            <targetJar>${artifactId}.jar</targetJar>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <finalName>${artifactId}</finalName>
                            <archive>
                                <manifest>
                                    <!-- 项目启动类 -->
                                    <mainClass>com.dc.BrokerApplication</mainClass>
                                </manifest>
                            </archive>
                            <descriptors>
                                <descriptor>src/main/assembly/assembly.xml</descriptor>
                            </descriptors>
                            <appendAssemblyId>false</appendAssemblyId>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>