package com.dc.executor.model;

import com.dc.springboot.core.model.type.FileType;
import com.fasterxml.jackson.annotation.JsonProperty;

public class SqlExecuteJobParam {

    private Integer job_id; // job id
    private String content; // sql
    private String contents; // 已拆分的sql
    private Integer db_type; // 数据库类型
    private String connect_id; // 实例id
    private String schema_id; // schema id
    private String request_id;  // redis key值
    private Boolean need_export = true; // 是否需要导出
    private Integer export_type = FileType.CSV.getValue(); // 导出类型

    private String daily_start_at; // 每天起始时间
    private String daily_end_at; // 每天结束时间
    private String start_at; // 起始日期
    private String end_at; // 结束日期

    private String uid; // 执行用户id
    private String charset;

    @JsonProperty("job_name")
    private String jobName;
    @JsonProperty("log_id")
    private Long logId; // log id
    @JsonProperty("dumpName")
    private String dumpName; // 文件名
    @JsonProperty("dumpPath")
    private String dumpPath; // 文件路径
    private Integer mode = 0; // 现场 0:dc; 1:mc; 2:银华; 3:公安部认证; 4:华泰;
    private boolean is_repeat; // 是否重复执行
    private int enable_desensitize_type; // 全脱敏开关 0:关; 1:开;
    @JsonProperty("repeat_type")
    private Integer repeatType; // 任务频率(每天1、每周2、每月3)
    @JsonProperty("daily_strategy")
    private String dailyStrategy; // 任务频率每天，策略的key(不限是unlimited)
    @JsonProperty("export_mode")
    private String exportMode; // 0->本地导出 1->邮箱导出  0,1->本地导出+邮箱导出
    @JsonProperty("export_email")
    private String exportEmail; // 邮箱地址
    @JsonProperty("email_title")
    private String emailTitle; // 邮箱地址
    @JsonProperty("export_tips") // 安全规则中 数据导出-文件加密-提示语
    private String exportTips;
    @JsonProperty("export_sql_type") // 导出SQL格式 INSERT/UPDATE
    private String exportSqlType;

    public void setEmailTitle(String emailTitle) {
        this.emailTitle = emailTitle;
    }

    public String getEmailTitle() {
        return emailTitle;
    }

    public Integer getJob_id() {
        return job_id;
    }

    public void setJob_id(Integer job_id) {
        this.job_id = job_id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContents() {
        return contents;
    }

    public void setContents(String contents) {
        this.contents = contents;
    }

    public Integer getDb_type() {
        return db_type;
    }

    public void setDb_type(Integer db_type) {
        this.db_type = db_type;
    }

    public String getConnect_id() {
        return connect_id;
    }

    public void setConnect_id(String connect_id) {
        this.connect_id = connect_id;
    }

    public String getSchema_id() {
        return schema_id;
    }

    public void setSchema_id(String schema_id) {
        this.schema_id = schema_id;
    }

    public String getRequest_id() {
        return request_id;
    }

    public void setRequest_id(String request_id) {
        this.request_id = request_id;
    }

    public Boolean getNeed_export() {
        return need_export;
    }

    public void setNeed_export(Boolean need_export) {
        this.need_export = need_export;
    }

    public Integer getExport_type() {
        return export_type;
    }

    public void setExport_type(Integer export_type) {
        this.export_type = export_type;
    }

    public String getDaily_start_at() {
        return daily_start_at;
    }

    public void setDaily_start_at(String daily_start_at) {
        this.daily_start_at = daily_start_at;
    }

    public String getDaily_end_at() {
        return daily_end_at;
    }

    public void setDaily_end_at(String daily_end_at) {
        this.daily_end_at = daily_end_at;
    }

    public String getStart_at() {
        return start_at;
    }

    public void setStart_at(String start_at) {
        this.start_at = start_at;
    }

    public String getEnd_at() {
        return end_at;
    }

    public void setEnd_at(String end_at) {
        this.end_at = end_at;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public String getDumpName() {
        return dumpName;
    }

    public void setDumpName(String dumpName) {
        this.dumpName = dumpName;
    }

    public String getDumpPath() {
        return dumpPath;
    }

    public void setDumpPath(String dumpPath) {
        this.dumpPath = dumpPath;
    }

    public Integer getMode() {
        return mode;
    }

    public void setMode(Integer mode) {
        this.mode = mode;
    }

    public boolean isIs_repeat() {
        return is_repeat;
    }

    public void setIs_repeat(boolean is_repeat) {
        this.is_repeat = is_repeat;
    }

    public int getEnable_desensitize_type() {
        return enable_desensitize_type;
    }

    public void setEnable_desensitize_type(int enable_desensitize_type) {
        this.enable_desensitize_type = enable_desensitize_type;
    }

    public Integer getRepeatType() {
        return repeatType;
    }

    public void setRepeatType(Integer repeatType) {
        this.repeatType = repeatType;
    }

    public String getDailyStrategy() {
        return dailyStrategy;
    }

    public void setDailyStrategy(String dailyStrategy) {
        this.dailyStrategy = dailyStrategy;
    }

    public String getExportMode() {
        return exportMode;
    }

    public void setExportMode(String exportMode) {
        this.exportMode = exportMode;
    }

    public String getExportEmail() {
        return exportEmail;
    }

    public void setExportEmail(String exportEmail) {
        this.exportEmail = exportEmail;
    }

    public String getExportTips() {
        return exportTips;
    }

    public void setExportTips(String exportTips) {
        this.exportTips = exportTips;
    }

    public String getExportSqlType() {
        return exportSqlType;
    }

    public void setExportSqlType(String exportSqlType) {
        this.exportSqlType = exportSqlType;
    }
}
