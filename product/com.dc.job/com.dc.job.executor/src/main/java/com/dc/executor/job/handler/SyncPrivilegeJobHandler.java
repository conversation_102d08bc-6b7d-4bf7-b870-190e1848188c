package com.dc.executor.job.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.executor.component.JobMapper;
import com.dc.executor.service.GenSqlService;
import com.dc.executor.util.ClientUtils;
import com.dc.executor.util.DCJobLogger;
import com.dc.executor.util.ParallelStreamUtil;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.*;
import com.dc.repository.mysql.type.GrantObjectType;
import com.dc.springboot.core.client.BackendClient;
import com.dc.springboot.core.client.ParserSqlClient;
import com.dc.springboot.core.client.ProxyInternalClient;
import com.dc.springboot.core.client.SummerExecuteClient;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.data.ExecutorData;
import com.dc.springboot.core.model.data.Message;
import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.database.*;
import com.dc.springboot.core.model.execution.BindingExecuteMessage;
import com.dc.springboot.core.model.execution.SingleExecuteModel;
import com.dc.springboot.core.model.execution.SingleSyncExecuteMessage;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.parser.ParserCacheMessage;
import com.dc.springboot.core.model.parser.ParserCheckMessage;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.type.ConnectionPatternType;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.type.DatabaseType;
import com.dc.utils.Pair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * oracle 权限同步
 */
@Slf4j
@Component
public class SyncPrivilegeJobHandler extends AbstractJobHandler {

    private static final String summerInterfaceAddressParseScript = "/dc-summer/execute/parse-script";

    @Resource
    private DcDbResourceMapper dcDbResourceMapper;

    @Resource
    private DcDbResourceAccountMapper dcDbResourceAccountMapper;

    @Resource
    private DcAccountObjPrivsMapper dcAccountObjPrivsMapper;

    @Resource
    private DcAccountRolePrivsMapper dcAccountRolePrivsMapper;

    @Resource
    private DcAccountSysPrivsMapper dcAccountSysPrivsMapper;

    @Resource
    private DcAccountOtherPrivsMapper dcAccountOtherPrivsMapper;

    @Resource
    private DcAccountTsQuotasMapper dcAccountTsQuotasMapper;

    @Resource
    private SummerExecuteClient summerExecuteClient;

    @Resource
    private ProxyInternalClient proxyInternalClient;

    @Resource
    private BackendClient backendClient;

    @Resource
    private ParserSqlClient parserSqlClient;

    @XxlJob("SyncPrivilegeJobHandler")
    public ReturnT<String> syncPrivilegeJobHandler(String param) throws Exception {

        DCJobLogger.log("sync privilege handler start");
        try {
            Map<Integer, ExecutorData> executorDataMap = backendClient.getExecutorDataMap(Client.getClient(jobConfig.getPath().getDcBackend()));
            // 遍历资源
            Long count = dcDbResourceMapper.selectCount(null);
            int limit = 100;

            // 分批拆分
            for (long offset = 0; offset < count; offset += 100) {
                List<DcDbResource> curBatchResources = dcDbResourceMapper.getResources(new QueryWrapper<DcDbResource>().last("limit " + limit + " offset " + offset));
                curBatchResources.forEach(resource -> {
                    if (DatabaseType.of(Integer.valueOf(resource.getDb_type())) == DatabaseType.MYSQL) {
                        return;
                    }
                    Client proxyClient = Client.getClient(executorDataMap.get(resource.getExecutor()).getDcIceage());
                    DatabaseConnectionDto databaseConnectionDto = JobMapper.INSTANCE.toDatabaseConnectionDto(resource);
                    ResourceConnectionMessage resourceConnectionMessage = new ResourceConnectionMessage();
                    resourceConnectionMessage.setConnectionConfig(databaseConnectionDto.buildConnectionConfig(null, null));
                    resourceConnectionMessage.setResourceId(resource.getId());
                    proxyInternalClient.spiltSchemaPrivileges(proxyClient, resourceConnectionMessage);
                });
            }

            // 分批同步
            for (long offsetR = 0; offsetR < count; offsetR += limit) {
                List<DcDbResource> curBatchResources = dcDbResourceMapper.getResources(new QueryWrapper<DcDbResource>().last("limit " + limit + " offset " + offsetR));
                ParallelStreamUtil.parallelForEach(curBatchResources, 3, action(executorDataMap), 1, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            log.error("call syncPrivilegeJobHandler error!", e);
            DCJobLogger.log(e);
        }
        DCJobLogger.log("sync privilege handler finish");
        return ReturnT.SUCCESS;

    }

    private Consumer<DcDbResource> action(Map<Integer, ExecutorData> executorDataMap) {
        // 同步对象权限
        // 同步角色权限
        // 同步系统权限
        // 同步表空间分配权限
        // 同步其他权限
        return resource -> {
            DatabaseType databaseType = DatabaseType.of(Integer.valueOf(resource.getDb_type()));
            try {
                GenSqlService genSqlService = GenSqlService.getInstance(databaseType);
                final Client summerClient = Client.getClient(executorDataMap.get(resource.getExecutor()).getDcSummer());
                String resourceUniqueKey = resource.getUnique_key();
                Integer resourceId = resource.getId();
                LambdaQueryWrapper<DcDbResourceAccount> qw = Wrappers.<DcDbResourceAccount>lambdaQuery()
                        .eq(DcDbResourceAccount::getResourceId, resourceUniqueKey);
                long countA = dcDbResourceAccountMapper.selectCount(qw);

                int limit = 100;
                for (long offsetA = 0; offsetA < countA; offsetA += limit) {
                    Client proxyClient = Client.getClient(executorDataMap.get(resource.getExecutor()).getDcIceage());
                    DatabaseConnectionDto databaseConnectionDto = jobMapper.toDatabaseConnectionDto(resource);
                    ConnectionConfig connectionConfig = databaseConnectionDto.buildConnectionConfig(null, null);
                    ConnectionMessage connectionMessage = new ConnectionMessage();
                    connectionMessage.setConnectionConfig(connectionConfig);
                    List<String> directories = new ArrayList<>();
                    if (databaseType == DatabaseType.ORACLE) {
                        directories = proxyInternalClient.getDirectories(proxyClient, connectionMessage);
                    }
                    List<DcDbResourceAccount> curBatchAccounts = dcDbResourceAccountMapper.getResourceAccounts(qw.last(" limit " + limit + " offset " + offsetA));

                    for (DcDbResourceAccount dcDbResourceAccount : curBatchAccounts) {
                        Integer id = dcDbResourceAccount.getId();

                        List<Pair<PrivilegeModel, String>> sql = genSqlService.genSql(resource, dcDbResourceAccount, directories);

                        List<String> otherSql = new ArrayList<>();
                        // 同步其他权限
                        LambdaQueryWrapper<DcAccountOtherPrivs> qot = Wrappers.<DcAccountOtherPrivs>lambdaQuery()
                                .eq(DcAccountOtherPrivs::getResourceId, resourceId)
                                .eq(DcAccountOtherPrivs::getAccountId, id);

                        dcAccountOtherPrivsMapper.getPrivileges(qot).stream().map(DcAccountOtherPrivs::getSql).forEach(otherSql::add);

                        for (String script : otherSql) {
                            List<String> summerSplit = ClientUtils.getSummerSplit(script, resource.getDb_type().intValue(), jobConfig.getPath().getDcSummer() + summerInterfaceAddressParseScript);
                            List<Pair<PrivilegeModel, String>> pairList = summerSplit.stream().map(this::parseSql).collect(Collectors.toList());
                            sql.addAll(pairList);
                        }

                        if (!sql.isEmpty()) {
                            executeSql(summerClient, resource, sql);
                        }
                    }

                }

            } catch (Exception e) {
                log.error("call syncPrivilegeJobHandler error!", e);
                throw new RuntimeException(e);
            }
        };
    }

    private Pair<PrivilegeModel, String> parseSql(String sql) {
        try {
            Pair<PrivilegeModel, String> pair = new Pair<>(null, sql);
            ParserCheckMessage message = new ParserCheckMessage();
            message.setSql(sql);
            message.setDbType(DatabaseType.ORACLE.getValue());
            message.setIsVerify("PrivilegesManagement");
            List<WebSQLParserResult> webSQLParserResults = parserSqlClient.preCheckSql(Client.getClient(jobConfig.getPath().getDcIceage()), message);
            if (webSQLParserResults != null && !webSQLParserResults.isEmpty()) {
                pair.setFirst(webSQLParserResults.get(0).getPrivilegeModel());
                return pair;
            }
            return pair;
        } catch (Exception e) {
            log.error("call parseSql error!", e);
            return null;
        }
    }


    private void executeSql(Client client, DcDbResource resource, List<Pair<PrivilegeModel, String>> sql) {


        String token = "Sync Privileges - " + UUID.randomUUID();

        BindingExecuteMessage bindingExecuteMessage = new BindingExecuteMessage();
        SingleSyncExecuteMessage singleSyncExecuteMessage = new SingleSyncExecuteMessage();
        Message message = new Message();
        ConnectionTokenMessage connectionTokenMessage = new ConnectionTokenMessage();

        DatabaseConnectionDto databaseConnectionDto = jobMapper.toDatabaseConnectionDto(resource);
        ConnectionConfig connectionConfig = databaseConnectionDto.buildConnectionConfig(null, null);
        singleSyncExecuteMessage.setConnectionConfig(connectionConfig);
        connectionTokenMessage.setConnectionConfig(connectionConfig);

        TokenConfig tokenConfig = new TokenConfig();
        tokenConfig.setAutoCommit(true);
        tokenConfig.setAutoConnect(true);
        tokenConfig.setPurpose(DBCExecutionPurpose.USER.getId());
        tokenConfig.setExpirationTime(0L);
        singleSyncExecuteMessage.setTokenConfig(tokenConfig);
        connectionTokenMessage.setTokenConfig(tokenConfig);

        bindingExecuteMessage.setToken(token);
        ExecuteEvent executeEvent = new ExecuteEvent();
        executeEvent.setUserId("1e28506bf50009cb3c4894866f647e94");
        executeEvent.setOperationUser("admin" + "(" + "超级管理员" + ")");
        executeEvent.setConnectionPattern(ConnectionPatternType.SECURITY_COLLABORATION.getValue());
        bindingExecuteMessage.setExecuteEvent(executeEvent);
        connectionTokenMessage.setExecuteEvent(executeEvent);
        singleSyncExecuteMessage.setToken(token);
        message.setToken(token);
        connectionTokenMessage.setToken(token);

        ResponseEntity<Result<Object>> resultResponseEntity = summerExecuteClient.openSession(client, connectionTokenMessage);
        String cookieValue = resultResponseEntity.getHeaders().getFirst(HttpHeaders.SET_COOKIE);
        for (Pair<PrivilegeModel, String> s : sql) {
            if (s == null) {
                continue;
            }
            singleSyncExecuteMessage.setSingleExecuteModel(getSingleExecuteModel(s, true));
            summerExecuteClient.singleExecuteSql(client, singleSyncExecuteMessage, cookieValue);
        }

        summerExecuteClient.closeSession(client, message, cookieValue);

    }

    private Pair<PrivilegeModel, String> genGrantSql(DcAccountObjPrivs privs, List<String> directories) {
        Pair<PrivilegeModel, String> pairs = new Pair<>(null, null);
        PrivilegeModel privilegeModel = new PrivilegeModel();
        String format = "GRANT %s ON %s\"%s\" TO \"%s\" ";
        String schema = privs.getSchemaName();
        if (schema == null) {
            schema = "";
        } else if (!schema.isBlank()) {
            privilegeModel.setOwner(schema);
            schema = "\"" + schema + "\"" + ".";
        }

        String onObject = privs.getObjectName().toUpperCase();
        if (directories.contains(onObject)) {
            format = "GRANT %s ON DIRECTORY %s\"%s\" TO \"%s\" ";
            privilegeModel.setDirectory(true);
        }

        String sql = String.format(format,
                privs.getPrivilege(),
                schema,
                privs.getObjectName(),
                privs.getGrantee());

        if (privs.getGrantable() != null && privs.getGrantable() == 0b1) {
            sql += "WITH GRANT OPTION ";
            privilegeModel.setWithGrantOption(true);
        }
        if (privs.getHierarchy() != null && privs.getHierarchy() == 0b1) {
            sql += "WITH HIERARCHY OPTION ";
            privilegeModel.setWithHierarchyOption(true);
        }


        privilegeModel.setOperateType(1);
        privilegeModel.setObjPrivileges(List.of(privs.getPrivilege()));
        privilegeModel.setOnObject(onObject);
        privilegeModel.setGrantees(List.of(privs.getGrantee()));

        pairs.setFirst(privilegeModel);
        pairs.setSecond(sql);

        return pairs;

    }

    private Pair<PrivilegeModel, String> genGrantSql(DcAccountRolePrivs privs) {
        Pair<PrivilegeModel, String> pairs = new Pair<>(null, null);
        String grantSql = String.format("GRANT \"%s\" TO \"%s\" ", privs.getGrantedRole(), privs.getUsername());
        PrivilegeModel privilegeModel = new PrivilegeModel();
        privilegeModel.setOperateType(1);
        privilegeModel.setRolePrivileges(List.of(privs.getGrantedRole()));
        privilegeModel.setGrantees(List.of(privs.getUsername()));
        if (privs.getAdminOption() != null && privs.getAdminOption() == 0b1) {
            privilegeModel.setWithAdminOption(true);
            grantSql += "WITH ADMIN OPTION ";
        }
        pairs.setFirst(privilegeModel);
        pairs.setSecond(grantSql);
        return pairs;
    }


    private Pair<PrivilegeModel, String> genGrantSql(DcAccountSysPrivs privs) {
        Pair<PrivilegeModel, String> pairs = new Pair<>(null, null);
        String grantSql = String.format("GRANT %s TO \"%s\" ", privs.getPrivilege(), privs.getGrantee());
        PrivilegeModel privilegeModel = new PrivilegeModel();
        privilegeModel.setOperateType(1);
        privilegeModel.setGrantees(List.of(privs.getGrantee()));
        if (privs.getAdminOption() != null && privs.getAdminOption() == 0b1) {
            grantSql += "WITH ADMIN OPTION ";
        }
        pairs.setFirst(privilegeModel);
        pairs.setSecond(grantSql);
        return pairs;
    }

    private Pair<PrivilegeModel, String> genGrantSql(DcAccountTsQuotas quota) {
        Pair<PrivilegeModel, String> pairs = new Pair<>(null, null);
        PrivilegeModel privilegeModel = new PrivilegeModel();
        privilegeModel.setOperateType(2);
        privilegeModel.setGrantees(List.of(quota.getUsername()));
        BigDecimal maxBytes = quota.getMaxBytes();
        if (maxBytes.compareTo(BigDecimal.ZERO) < 0) {
            String sql = String.format("ALTER USER \"%s\" QUOTA UNLIMITED ON \"%s\"", quota.getUsername(), quota.getTablespaceName());
            pairs.setFirst(privilegeModel);
            pairs.setSecond(sql);
            return pairs;
        }

        int count = 0;
        BigDecimal maxim = new BigDecimal(1024);
        while (maxBytes.compareTo(maxim) > 0 && count < 3) {
            maxBytes = maxBytes.divide(maxim, 0, RoundingMode.DOWN);
            count++;
        }

        String[] units = {"", "K", "M", "G"};
        String unit = units[count];

        String sql = String.format("ALTER USER \"%s\" QUOTA %s%s ON \"%s\"", quota.getUsername(), maxBytes, unit, quota.getTablespaceName());
        pairs.setFirst(privilegeModel);
        pairs.setSecond(sql);
        return pairs;
    }

    public SingleExecuteModel getSingleExecuteModel(Pair<PrivilegeModel, String> sql, boolean isLimit) {

        SingleExecuteModel singleExecuteModel = new SingleExecuteModel();
        PrivilegeModel privilegeModel = sql.getFirst();
        if (privilegeModel != null) {
            privilegeModel.setNeeded(true);
            privilegeModel.setSyncJob(true);
        }
        singleExecuteModel.setPrivilegeModel(privilegeModel);
        singleExecuteModel.setSql(sql.getSecond());
        if (isLimit) {
            singleExecuteModel.setLimit(1);
        }

        return singleExecuteModel;
    }

}
