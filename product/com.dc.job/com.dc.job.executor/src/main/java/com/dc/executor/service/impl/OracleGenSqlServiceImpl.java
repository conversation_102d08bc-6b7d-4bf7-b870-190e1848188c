package com.dc.executor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.executor.service.GenSqlService;
import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.*;
import com.dc.repository.mysql.type.GrantObjectType;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.utils.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class OracleGenSqlServiceImpl implements GenSqlService {


    @Resource
    private DcAccountObjPrivsMapper dcAccountObjPrivsMapper;

    @Resource
    private DcAccountRolePrivsMapper dcAccountRolePrivsMapper;

    @Resource
    private DcAccountSysPrivsMapper dcAccountSysPrivsMapper;

    @Resource
    private DcAccountTsQuotasMapper dcAccountTsQuotasMapper;


    @Override
    public List<Pair<PrivilegeModel, String>> genSql(DcDbResource resource, DcDbResourceAccount account, List<String> directories) {
        Integer resourceId = resource.getId();
        Integer accountId = account.getId();

        // 同步对象权限
        LambdaQueryWrapper<DcAccountObjPrivs> qwo = Wrappers.<DcAccountObjPrivs>lambdaQuery()
                .eq(DcAccountObjPrivs::getResourceId, resourceId)
                .eq(DcAccountObjPrivs::getAccountId, accountId)
                .eq(DcAccountObjPrivs::getObjectType, GrantObjectType.NONE.getName());

        List<Pair<PrivilegeModel, String>> sql = dcAccountObjPrivsMapper.getPrivileges(qwo).stream().map(o -> genGrantSql(o, directories)).collect(Collectors.toList());

        // 同步角色权限
        LambdaQueryWrapper<DcAccountRolePrivs> qwr = Wrappers.<DcAccountRolePrivs>lambdaQuery()
                .eq(DcAccountRolePrivs::getResourceId, resourceId)
                .eq(DcAccountRolePrivs::getAccountId, accountId);

        List<DcAccountRolePrivs> rolePrivs = dcAccountRolePrivsMapper.getPrivileges(qwr);
        rolePrivs.stream().map(this::genGrantSql).forEach(sql::add);

        // 同步系统权限
        LambdaQueryWrapper<DcAccountSysPrivs> qws = Wrappers.<DcAccountSysPrivs>lambdaQuery()
                .eq(DcAccountSysPrivs::getResourceId, resourceId)
                .eq(DcAccountSysPrivs::getAccountId, accountId);

        dcAccountSysPrivsMapper.getPrivileges(qws).stream().map(this::genGrantSql).forEach(sql::add);

        // 同步表空间分配权限
        LambdaQueryWrapper<DcAccountTsQuotas> qwt = Wrappers.<DcAccountTsQuotas>lambdaQuery()
                .eq(DcAccountTsQuotas::getResourceId, resourceId)
                .eq(DcAccountTsQuotas::getAccountId, accountId);


        dcAccountTsQuotasMapper.getQuotas(qwt).stream().map(this::genGrantSql).forEach(sql::add);

        return sql;
    }


    private Pair<PrivilegeModel, String> genGrantSql(DcAccountObjPrivs privs, List<String> directories) {
        Pair<PrivilegeModel, String> pairs = new Pair<>(null, null);
        PrivilegeModel privilegeModel = new PrivilegeModel();
        String format = "GRANT %s ON %s\"%s\" TO \"%s\" ";
        String schema = privs.getSchemaName();
        if (schema == null) {
            schema = "";
        } else if (!schema.isBlank()) {
            privilegeModel.setOwner(schema);
            schema = "\"" + schema + "\"" + ".";
        }

        String onObject = privs.getObjectName().toUpperCase();
        if (directories.contains(onObject)) {
            format = "GRANT %s ON DIRECTORY %s\"%s\" TO \"%s\" ";
            privilegeModel.setDirectory(true);
        }

        String sql = String.format(format,
                privs.getPrivilege(),
                schema,
                privs.getObjectName(),
                privs.getGrantee());

        if (privs.getGrantable() != null && privs.getGrantable() == 0b1) {
            sql += "WITH GRANT OPTION ";
            privilegeModel.setWithGrantOption(true);
        }
        if (privs.getHierarchy() != null && privs.getHierarchy() == 0b1) {
            sql += "WITH HIERARCHY OPTION ";
            privilegeModel.setWithHierarchyOption(true);
        }


        privilegeModel.setOperateType(1);
        privilegeModel.setObjPrivileges(List.of(privs.getPrivilege()));
        privilegeModel.setOnObject(onObject);
        privilegeModel.setGrantees(List.of(privs.getGrantee()));

        pairs.setFirst(privilegeModel);
        pairs.setSecond(sql);

        return pairs;

    }

    private Pair<PrivilegeModel, String> genGrantSql(DcAccountRolePrivs privs) {
        Pair<PrivilegeModel, String> pairs = new Pair<>(null, null);
        String grantSql = String.format("GRANT \"%s\" TO \"%s\" ", privs.getGrantedRole(), privs.getUsername());
        PrivilegeModel privilegeModel = new PrivilegeModel();
        privilegeModel.setOperateType(1);
        privilegeModel.setRolePrivileges(List.of(privs.getGrantedRole()));
        privilegeModel.setGrantees(List.of(privs.getUsername()));
        if (privs.getAdminOption() != null && privs.getAdminOption() == 0b1) {
            privilegeModel.setWithAdminOption(true);
            grantSql += "WITH ADMIN OPTION ";
        }
        pairs.setFirst(privilegeModel);
        pairs.setSecond(grantSql);
        return pairs;
    }


    private Pair<PrivilegeModel, String> genGrantSql(DcAccountSysPrivs privs) {
        Pair<PrivilegeModel, String> pairs = new Pair<>(null, null);
        String grantSql = String.format("GRANT %s TO \"%s\" ", privs.getPrivilege(), privs.getGrantee());
        PrivilegeModel privilegeModel = new PrivilegeModel();
        privilegeModel.setOperateType(1);
        privilegeModel.setGrantees(List.of(privs.getGrantee()));
        if (privs.getAdminOption() != null && privs.getAdminOption() == 0b1) {
            grantSql += "WITH ADMIN OPTION ";
        }
        pairs.setFirst(privilegeModel);
        pairs.setSecond(grantSql);
        return pairs;
    }

    private Pair<PrivilegeModel, String> genGrantSql(DcAccountTsQuotas quota) {
        Pair<PrivilegeModel, String> pairs = new Pair<>(null, null);
        PrivilegeModel privilegeModel = new PrivilegeModel();
        privilegeModel.setOperateType(2);
        privilegeModel.setGrantees(List.of(quota.getUsername()));
        BigDecimal maxBytes = quota.getMaxBytes();
        if (maxBytes.compareTo(BigDecimal.ZERO) < 0) {
            String sql = String.format("ALTER USER \"%s\" QUOTA UNLIMITED ON \"%s\"", quota.getUsername(), quota.getTablespaceName());
            pairs.setFirst(privilegeModel);
            pairs.setSecond(sql);
            return pairs;
        }

        int count = 0;
        BigDecimal maxim = new BigDecimal(1024);
        while (maxBytes.compareTo(maxim) > 0 && count < 3) {
            maxBytes = maxBytes.divide(maxim, 0, RoundingMode.DOWN);
            count++;
        }

        String[] units = {"", "K", "M", "G"};
        String unit = units[count];

        String sql = String.format("ALTER USER \"%s\" QUOTA %s%s ON \"%s\"", quota.getUsername(), maxBytes, unit, quota.getTablespaceName());
        pairs.setFirst(privilegeModel);
        pairs.setSecond(sql);
        return pairs;
    }


}
