package com.dc.executor.service.impl;

import com.dc.executor.config.JobConfig;
import com.dc.executor.service.InstanceService;
import com.dc.springboot.core.client.SummerExecuteClient;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class InstanceServiceImpl implements InstanceService {


    @Resource
    private JobConfig jobConfig;
    @Resource
    private SummerExecuteClient summerExecuteClient;


    @Override
    public String getRemotePassword(DatabaseConnectionDto instance) {
        String password = "";
        try {
            final Client client = Client.getClient(jobConfig.getPath().getDcSummer());
            password = summerExecuteClient.getRemotePassword(client, instance);
        } catch (Exception e) {
            throw new RuntimeException("获取密码失败！", e);
        }
        return password;
    }
}
