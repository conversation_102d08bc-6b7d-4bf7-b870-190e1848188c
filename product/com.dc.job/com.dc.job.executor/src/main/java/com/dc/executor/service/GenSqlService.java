package com.dc.executor.service;

import com.dc.executor.service.impl.MySqlGenSqlServiceImpl;
import com.dc.executor.service.impl.OracleGenSqlServiceImpl;
import com.dc.repository.mysql.model.DcDbResource;
import com.dc.repository.mysql.model.DcDbResourceAccount;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.type.DatabaseType;
import com.dc.utils.Pair;

import java.util.List;

public interface GenSqlService {

    List<Pair<PrivilegeModel, String>> genSql(DcDbResource resource, DcDbResourceAccount account, List<String> dirs);

    static GenSqlService getInstance(DatabaseType databaseType) {
        switch (databaseType) {
            case ORACLE:
                return Resource.getBean(OracleGenSqlServiceImpl.class);
            case MYSQL:
                return Resource.getBean(MySqlGenSqlServiceImpl.class);
        }

        throw new IllegalArgumentException("<UNK>");
    }

}
