package com.dc.executor.job.handler;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.executor.model.SqlExecuteJobParam;
import com.dc.executor.type.TaskInfoStateType;
import com.dc.executor.util.ClientUtils;
import com.dc.executor.util.DCJobLogger;
import com.dc.executor.util.JobCommonUtil;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.enums.SqlExecuteStatusType;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.repository.mysql.mapper.SchemaMapper;
import com.dc.repository.mysql.mapper.SecurityRuleDetailsMapper;
import com.dc.repository.mysql.mapper.UserMapper;
import com.dc.repository.mysql.model.*;
import com.dc.springboot.core.client.ParserSqlClient;
import com.dc.springboot.core.client.SummerExecuteClient;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.data.Client;
import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.ConnectionTokenMessage;
import com.dc.springboot.core.model.database.TokenConfig;
import com.dc.springboot.core.model.execution.BatchExecuteModel;
import com.dc.springboot.core.model.execution.JobExportMessage;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.message.MessageConstants;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.springboot.core.model.recovery.BackupModel;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.springboot.core.model.sensitive.SqlDesensitization;
import com.dc.springboot.core.model.type.*;
import com.dc.springboot.core.utils.AESUtil;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.type.DatabaseType;
import com.dc.type.ExportModeType;
import com.dc.type.SecurityRuleType;
import com.dc.utils.CipherUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class SqlExecuteJobHandler extends AbstractJobHandler {

    private static final String summerInterfaceAddressParseScript = "/dc-summer/execute/parse-script";
    private static final String summerInterfaceAddressOpenSession = "/dc-summer/execute/open-session";
    private static final String summerInterfaceAddressJobExport = "/dc-summer/execute/job-export";
    private static final String summerInterfaceAddressTaskInfo = "/dc-summer/execute/task-info";
    private static final String summerInterfaceAddressTaskResult = "/dc-summer/execute/task-result";
    private static final String summerInterfaceAddressKillExecute = "/dc-summer/execute/kill-execute";
    private static final String summerInterfaceAddressCloseSession = "/dc-summer/execute/close-session";
    private static final String backendInterfaceAddressGetConfig = "/api/v1/system/internal-interface/get-config";
    private static final long redisExpirationTime = 60 * 60; // 默认redis过期时间为1小时

    @Resource
    private SchemaMapper schemaMapper;
    @Resource
    private DatabaseConnectionMapper databaseConnectionMapper;
    @Resource
    private UserMapper userMapper;
    @Resource
    private SummerExecuteClient summerExecuteClient;
    @Resource
    private SecurityRuleDetailsMapper securityRuleDetailsMapper;

    @Resource
    private ParserSqlClient parserSqlClient;

    @XxlJob("SqlExecuteJobHandler")
    public ReturnT<String> sqlExecuteJobHandler(String param) throws Exception {

        try {

            SqlExecuteJobParam sqlExecuteJobParam = JSON.parseObject(param, SqlExecuteJobParam.class);


            if (JobCommonUtil.needSkipBasedOnStrategy(sqlExecuteJobParam.getRepeatType(), sqlExecuteJobParam.getDailyStrategy(), jobConfig.getPath().getDcBackend())) {
                return ReturnT.SUCCESS;
            }

            // 更新触发时间为真正得执行任务的时间
            this.updateTriggerTime(sqlExecuteJobParam.getLogId());

            // 获取调度记录
            JobLog load = jobLogMapper.load(sqlExecuteJobParam.getLogId());

            // 获取连接实例信息
            DatabaseConnectionDto connection = null;
            if (StringUtils.isNotBlank(sqlExecuteJobParam.getConnect_id())) {
                DatabaseConnection dc = databaseConnectionMapper.getConnectionByUniqueKey(sqlExecuteJobParam.getConnect_id());
                connection = dc == null ? null : jobMapper.toDatabaseConnectionDto(dc);
            }

            // 实例删除或关闭,标注执行失败
            if (connection == null || connection.getIs_delete() == 1 || connection.getIs_active() == 0) {
                String errorMessage = "当前实例已删除或关闭,无法进行sql执行!";
                this.errorReturn(sqlExecuteJobParam.getLogId(), errorMessage, load.getJobId(), sqlExecuteJobParam.isIs_repeat());
                return ReturnT.SUCCESS;
            }

            // 获取默认schema信息
            Schema schemaDefault = null;
            Optional<Schema> schemaOptional = this.schemaMapper.getSchemaByUniqueKey(sqlExecuteJobParam.getSchema_id());
            if (schemaOptional.isEmpty()) {
                String errorMessage = "获取默认schema信息失败,无法进行sql执行!";
                this.errorReturn(sqlExecuteJobParam.getLogId(), errorMessage, load.getJobId(), sqlExecuteJobParam.isIs_repeat());
                return ReturnT.SUCCESS;
            } else {
                schemaDefault = schemaOptional.get();
            }

            // 更新状态为执行中
            this.updateBeginTriggerStatus(load.getJobId(), sqlExecuteJobParam.getLogId());

            // 调用summer拆分sql
            /*int dbType = this.getKingBaseSplitType(connection.getDb_type(), connection.getKingbase_database_mode());*/
            List<String> summerSplit = ClientUtils.getSummerSplit(sqlExecuteJobParam.getContent(), connection.getDb_type(), jobConfig.getPath().getDcSummer() + summerInterfaceAddressParseScript);

            // 构建调用parser接口的参数(除了sql)
            ParserParamDto paramDTO = this.buildPreCheckParamDTO(connection.getDb_type(), sqlExecuteJobParam);

            // 连接缓存token
            String token = "job_execute_" + UUID.randomUUID(); // token与connection组成键值对缓存

            // 构建调用summer接口的参数
            JobExportMessage jobExportMessage = this.buildJobExportMessage(sqlExecuteJobParam, paramDTO, summerSplit, connection, schemaDefault, token, load.getJobId());

            Map<String, Boolean> securityRule = getSecurityRule(connection.getSecurity_rule_set_id());
            jobExportMessage.setExportDesensitize(securityRule.get(SecurityRuleType.DESENSITIZATION_EXPORT.getName()));
            if (securityRule.get(SecurityRuleType.EXPORT_ENCRYPTED.getName())) {
                jobExportMessage.setEncryptPassword(new AESUtil().Encrypt(String.valueOf(System.currentTimeMillis()), "123456"));
            }

            Client client = Client.getClient(jobConfig.getPath().getDcSummer());

            ConnectionTokenMessage connectionTokenMessage = new ConnectionTokenMessage();

            ConnectionConfig connectionConfig = connection.buildConnectionConfig(schemaDefault.getSchema_name(), schemaDefault.getCatalog_name());
            connectionTokenMessage.setConnectionConfig(connectionConfig); // 实例连接配置信息

            TokenConfig tokenConfig = new TokenConfig();
            tokenConfig.setAutoConnect(true);
            tokenConfig.setAutoCommit(true);
            tokenConfig.setExpirationTime(redisExpirationTime);
            tokenConfig.setPurpose(DBCExecutionPurpose.USER_SCRIPT.getId());
            connectionTokenMessage.setTokenConfig(tokenConfig);

            connectionTokenMessage.setToken(token);

            ExecuteEvent executeEvent = new ExecuteEvent();
            executeEvent.setUserId(sqlExecuteJobParam.getUid());
            executeEvent.setOperationUser(getOperationUser(sqlExecuteJobParam.getUid()));
            executeEvent.setConnectionPattern(ConnectionPatternType.SECURITY_COLLABORATION.getValue());
            connectionTokenMessage.setExecuteEvent(executeEvent);

            SqlExecuteStatusType sqlExecuteStatusType = SqlExecuteStatusType.success;
            String cookieValue = "";
            try {
                try {
                    // open session
                    ResponseEntity<Result<Object>> resultResponseEntity = summerExecuteClient.openSession(client, connectionTokenMessage);
                    HttpHeaders headers = resultResponseEntity.getHeaders();
                    cookieValue = headers.getFirst(HttpHeaders.SET_COOKIE);
                } catch (Exception e) {
                    sqlExecuteStatusType = SqlExecuteStatusType.fail;
                    DCJobLogger.log("调用summer:open-session接口出错：" + e.getMessage());
                    log.info("调用open-session接口:" + jobConfig.getPath().getDcSummer() + summerInterfaceAddressOpenSession + "出错!");
                    return ReturnT.SUCCESS;
                }

                log.info("Start post summer interface: " + jobConfig.getPath().getDcSummer() + summerInterfaceAddressJobExport);
                int taskId = JobCommonUtil.jobExport(jobConfig.getPath().getDcSummer() + summerInterfaceAddressJobExport, jobExportMessage, cookieValue);

                if (!TaskInfoStateType.error.getValue().equals(taskId)) {
                    try {
                        int start = 0;

                        // 执行前再去获取一遍调度的状态
                        load = jobLogMapper.load(sqlExecuteJobParam.getLogId());
                        if (load.getTriggerStatus() == SqlExecuteStatusType.termination.getValue()) {
                            this.interruptReturn(load.getJobId(), sqlExecuteJobParam.isIs_repeat());
                            ClientUtils.killConnection(token, jobConfig.getPath().getDcSummer() + summerInterfaceAddressKillExecute, cookieValue);
                            sqlExecuteStatusType = SqlExecuteStatusType.termination;
                            return ReturnT.SUCCESS;
                        }

                        // 轮询获取任务
                        while (true) {
                            Map<String, Integer> taskInfo = ClientUtils.taskInfo(token, taskId, jobConfig.getPath().getDcSummer() + summerInterfaceAddressTaskInfo, cookieValue);

                            if (TaskInfoStateType.error.getValue().equals(taskInfo.get("status"))) {
                                sqlExecuteStatusType = SqlExecuteStatusType.fail;
                                DCJobLogger.log("调用summer:task-info接口出错！");
                                log.info("调用task-info接口:" + jobConfig.getPath().getDcSummer() + summerInterfaceAddressTaskInfo + "出错!");
                                break;
                            } else if (TaskInfoStateType.closed.getValue().equals(taskInfo.get("status"))) {
                                sqlExecuteStatusType = SqlExecuteStatusType.fail;
                                DCJobLogger.log(MessageConstants.CLOSED.getMessage());
                                log.info("调用task-info接口:" + jobConfig.getPath().getDcSummer() + summerInterfaceAddressTaskInfo + "出错，连接被关闭！");
                                break;
                            } else if (!TaskInfoStateType.wait.getValue().equals(taskInfo.get("status"))) {
                                int end = taskInfo.get("stage");
                                if (end - start > 0) {
                                    for (int i = start + 1; i <= end; i++) {
                                        Map<String, Object> objectMap = ClientUtils.taskResult(token, taskId, i, jobConfig.getPath().getDcSummer() + summerInterfaceAddressTaskResult, cookieValue);
                                        if (objectMap.get("sql") != null) {
                                            DCJobLogger.log(objectMap.get("sql").toString());
                                        }
                                        if (objectMap.get("message") != null) {
                                            DCJobLogger.log(objectMap.get("message").toString());
                                        }
                                        if (objectMap.get("backup_warning") != null) {
                                            DCJobLogger.log(objectMap.get("backup_warning").toString());
                                        }
                                        if (objectMap.get("status") != null) {
                                            int statusInt = Integer.parseInt(objectMap.get("status").toString());
                                            if (statusInt != 1) {
                                                sqlExecuteStatusType = SqlExecuteStatusType.fail;
                                            }
                                        }
                                    }
                                    start = end;
                                }
                            }

                            if (TaskInfoStateType.finished.getValue().equals(taskInfo.get("status"))) {
                                break;
                            }

                            try {
                                Thread.sleep(200);
                            } catch (InterruptedException e) {
                                this.interruptReturn(load.getJobId(), sqlExecuteJobParam.isIs_repeat());
                                ClientUtils.killConnection(token, jobConfig.getPath().getDcSummer() + summerInterfaceAddressKillExecute, cookieValue);
                                sqlExecuteStatusType = SqlExecuteStatusType.termination;
                                return ReturnT.SUCCESS;
                            }
                        }
                    } catch (Exception e) {
                        log.error("轮询获取执行结果出错！", e);
                    }
                } else {
                    sqlExecuteStatusType = SqlExecuteStatusType.fail;
                    DCJobLogger.log("调用summer:job-execute接口出错！");
                    log.info("调用job-execute接口:" + jobConfig.getPath().getDcSummer() + summerInterfaceAddressJobExport + "出错!");
                }
            } finally {
                ClientUtils.killConnection(token, jobConfig.getPath().getDcSummer() + summerInterfaceAddressCloseSession, cookieValue);

                this.updateFinalStatus(sqlExecuteStatusType, sqlExecuteJobParam.getLogId(), sqlExecuteJobParam.isIs_repeat(), load.getJobId()); // 更新最终状态

                if (sqlExecuteStatusType == SqlExecuteStatusType.success) {
                    if ( sqlExecuteJobParam.getExportTips() != null) {
                        DCJobLogger.log(sqlExecuteJobParam.getExportTips());
                    }
                }

                if (sqlExecuteStatusType == SqlExecuteStatusType.success && jobExportMessage.getExportModes() != null && jobExportMessage.getExportModes().contains(ExportModeType.EXPORT_EMAIL.getValue())
                        && jobExportMessage.getExportEmail() != null) {
                    DCJobLogger.log("向" + jobExportMessage.getExportEmail() +"邮箱发送邮件");
                }
            }

        } catch (Exception e) {
            log.error("call sqlExecuteJobHandler error!", e);
        }

        return ReturnT.SUCCESS;
    }


    private void interruptReturn(int jobId, boolean isRepeat) {
        if (!isRepeat) {
            this.updateTriggerStatus(jobId, 0);
        }
    }

    private ParserParamDto buildPreCheckParamDTO(Integer dbType, SqlExecuteJobParam sqlExecuteJobParam) {
        ParserParamDto paramDTO = new ParserParamDto(); // 构建调用parser接口的参数

        paramDTO.setDbType(dbType);
        paramDTO.setOrigin((long) OriginType.JOB_EXECUTE.getValue());
        paramDTO.setConnectId(sqlExecuteJobParam.getConnect_id());
        paramDTO.setSchemaId(sqlExecuteJobParam.getSchema_id());
        paramDTO.setLimit(0);
        paramDTO.setOffset(0);
        paramDTO.setUserId(sqlExecuteJobParam.getUid());
        paramDTO.setUserName(getOperationUser(sqlExecuteJobParam.getUid()));
        paramDTO.setIsVerify(ParserExecuteType.makeIsVerify(ParserExecuteType.DATA_MASK_PARSER, ParserExecuteType.SQL_BACKUP, ParserExecuteType.PRIVILEGES_MANAGEMENT));

        try {
            Map<String, Object> userToken = ClientUtils.getUserToken(sqlExecuteJobParam.getUid(), jobConfig.getPath().getDcBackend() + backendInterfaceAddressGetConfig);
            paramDTO.setSymbol(userToken.get("symbol").toString());
            int enable_desensite_type = "true".equalsIgnoreCase(userToken.get("enable_desensite_type").toString()) ? 1 : 0;
            paramDTO.setEnableDesensiteType(enable_desensite_type);
        } catch (Exception e) {
            log.error("build preCheckParamDTO error!", e);
        }

        return paramDTO;
    }

    private List<BatchExecuteModel> buildBatchExecuteModels(ParserParamDto paramDTO, List<String> summerSplit, String instanceName) {
        List<BatchExecuteModel> batchExecuteModels = new ArrayList<>();

        paramDTO.setSqlList(gson.toJson(summerSplit));

        List<WebSQLParserResult> webSQLParserResults = parserSqlClient.executeSql(Client.getClient(jobConfig.getPath().getDcIceage()), paramDTO);

        for (int i = 0; i < summerSplit.size(); i++) {
            String sql = summerSplit.get(i);
            WebSQLParserResult webSQLParserResult = webSQLParserResults.get(i);

            BatchExecuteModel batchExecuteModel = new BatchExecuteModel();

            BackupModel backupModel = new BackupModel(paramDTO.getConnectId(), paramDTO.getDbType(), instanceName, webSQLParserResult);

            batchExecuteModel.setBackupModel(backupModel);
            batchExecuteModel.setPrivilegeModel(webSQLParserResult.getPrivilegeModel());

            batchExecuteModel.setOperation(webSQLParserResult.getOperation());
            batchExecuteModel.setPrimaryKeyColumns(webSQLParserResult.getPrimaryKeyColumns());
            batchExecuteModel.setSql(sql);

            if (CollectionUtils.isNotEmpty(webSQLParserResult.getDataMask())) {
                SqlDesensitization sqlDesensitization = new SqlDesensitization();
                sqlDesensitization.setDataMasks(webSQLParserResult.getDataMask());
                sqlDesensitization.setEnableDesensitizeType(paramDTO.getEnableDesensiteType());
                sqlDesensitization.setSymbol(paramDTO.getSymbol());
                sqlDesensitization.setNodeModels(webSQLParserResult.getNodeModel());
                batchExecuteModel.setSqlDesensitization(sqlDesensitization);
            }

            batchExecuteModels.add(batchExecuteModel);
        }

        return batchExecuteModels;
    }

    private JobExportMessage buildJobExportMessage(SqlExecuteJobParam sqlExecuteJobParam, ParserParamDto paramDTO,
                                                   List<String> summerSplit, DatabaseConnectionDto connection,
                                                   Schema schemaDefault, String token, int jobId) {

        JobExportMessage jobExportMessage = new JobExportMessage();

        // 多条sql的信息
        List<BatchExecuteModel> batchExecuteModels = this.buildBatchExecuteModels(paramDTO, summerSplit, connection.getInstance_name());
        jobExportMessage.setBatchExecuteModels(batchExecuteModels);

        jobExportMessage.setErrorContinue(true); // 默认为出错继续执行
        jobExportMessage.setExport(sqlExecuteJobParam.getNeed_export());
        jobExportMessage.setExportType(this.getExportType(sqlExecuteJobParam.getExport_type()));
        jobExportMessage.setLogId(sqlExecuteJobParam.getLogId());
        jobExportMessage.setJobName(sqlExecuteJobParam.getJobName());
        jobExportMessage.setToken(token);
        TokenConfig tokenConfig = new TokenConfig();
        tokenConfig.setAutoCommit(true);
        tokenConfig.setAutoConnect(true);
        tokenConfig.setExpirationTime(redisExpirationTime);
        tokenConfig.setPurpose(DBCExecutionPurpose.USER_SCRIPT.getId());
        jobExportMessage.setTokenConfig(tokenConfig);
        jobExportMessage.setUserId(sqlExecuteJobParam.getUid());

        jobExportMessage.setSchemaName(jobMapper.toSchemaDto(schemaDefault).getCatalogSchemaName());

        ExecuteEvent executeEvent = new ExecuteEvent();
        executeEvent.setUserId(sqlExecuteJobParam.getUid());
        executeEvent.setOperationUser(getOperationUser(sqlExecuteJobParam.getUid()));
        executeEvent.setConnectionPattern(ConnectionPatternType.SECURITY_COLLABORATION.getValue());
        jobExportMessage.setExecuteEvent(executeEvent);
        jobExportMessage.setExportEmail(sqlExecuteJobParam.getExportEmail());
        jobExportMessage.setUserId(sqlExecuteJobParam.getUid());
        jobExportMessage.setEmailTitle(sqlExecuteJobParam.getEmailTitle());
        jobExportMessage.setExportSqlType(sqlExecuteJobParam.getExportMode());
        Optional.ofNullable(dcJobMapper.select(jobId)).ifPresent(j -> {
            jobExportMessage.setJobName(j.getJobName());
        });

        if (StringUtils.isNotBlank(sqlExecuteJobParam.getExportMode())) {
            jobExportMessage.setExportModes(getExportModes(sqlExecuteJobParam.getExportMode()));
            if (jobExportMessage.getExportModes().contains(ExportModeType.EXPORT_EMAIL.getValue())) {
                jobExportMessage.setContent(sqlExecuteJobParam.getContent());
                DCJob select = dcJobMapper.select(jobId);
                if (select != null) {
                    jobExportMessage.setCode(select.getCode());
                }
                if (connection.getConnection_desc() != null && connection.getInstance_name() != null) {
                    jobExportMessage.setInstanceDesc(connection.getConnection_desc() + "【" + connection.getInstance_name() + "】");
                }
            }
        }

        return jobExportMessage;
    }

    private String transferExportType(int type) {

        String exportType = ExportType.CSV.toString();

        if (FileType.CSV.getValue().equals(type)) {
            exportType = ExportType.CSV.toString();
        } else if (FileType.EXCEL.getValue().equals(type)) {
            exportType = ExportType.XLSX.toString();
        } else if (FileType.TEXT.getValue().equals(type)) {
            exportType = ExportType.TXT.toString();
        }

        return exportType;
    }

    private ExportType getExportType(int type) {
        if (FileType.CSV.getValue().equals(type)) {
            return ExportType.CSV;
        } else if (FileType.EXCEL.getValue().equals(type)) {
            return ExportType.XLSX;
        } else if (FileType.TEXT.getValue().equals(type)) {
            return ExportType.TXT;
        }
        return ExportType.CSV;
    }

    private int getKingBaseSplitType(int dbType, String splitType) {
        try {
            if (DatabaseType.KING_BASE.getValue().equals(dbType) && "oracle".equalsIgnoreCase(splitType)) {
                return DatabaseType.ORACLE.getValue();
            }
        } catch (Exception e) {
            log.error("get kingBase splitType error!", e);
        }
        return dbType;
    }

    private String getOperationUser(String userId) {
        try {
            User userByUniqueKey = this.userMapper.selectOne(Wrappers.<User>lambdaQuery()
                    .eq(User::getUniqueKey, userId)
            );
            return userByUniqueKey.getUsername() + "(" + userByUniqueKey.getRealName() + ")";
        } catch (Exception e) {
            log.error("get operation user error!", e);
        }
        return "";
    }

    private User getUser(String userId) {
        try {
            return this.userMapper.selectOne(Wrappers.<User>lambdaQuery().eq(User::getUniqueKey, userId));
        } catch (Exception e) {
            log.error("get user error!", e);
            return null;
        }
    }


    private List<Integer> getExportModes(String exportMode) {
        List<Integer> list = new ArrayList<>();

        String[] objectName = exportMode.split("\\,");
        for (String str : objectName) {
            Integer integer = parseStringToInteger(str);
            if (integer != null) {
                list.add(integer);
            }
        }

        return list;
    }

    private Integer parseStringToInteger(String str) {
        try {
            return Integer.parseInt(str);
        } catch (Exception e) {
            log.error("Parse string to integer error!", e);
        }
        return null;
    }

    public Map<String, Boolean> getSecurityRule(int securityRuleSetId) {
        Map<String, Boolean> securityRule = new HashMap<>();

        try {
            List<SecurityRuleDetails> securityRuleDetailsList = this.securityRuleDetailsMapper.findByRuleSetIdAnDetailsTplKeys(securityRuleSetId,
                    Set.of(SecurityRuleType.DESENSITIZATION_EXPORT.getName(), SecurityRuleType.EXPORT_ENCRYPTED.getName()));
            for (SecurityRuleDetails securityRuleDetails : securityRuleDetailsList) {
                if (SecurityRuleType.DESENSITIZATION_EXPORT.getName().equals(securityRuleDetails.getDetailsTplKey())) {
                    String value = securityRuleDetails.getValue();
                    securityRule.put(SecurityRuleType.DESENSITIZATION_EXPORT.getName(), StringUtils.isNotBlank(value) && Arrays.asList(value.split(",")).contains("task_manage_sql_execute"));
                } else if (SecurityRuleType.EXPORT_ENCRYPTED.getName().equals(securityRuleDetails.getDetailsTplKey())) {
                    String value = securityRuleDetails.getValue();
                    if (StringUtils.isNotBlank(value)) {
                        try {
                            Map<String, Object> jsonMap = (Map<String, Object>) JSON.parseObject(value, Map.class);
                            securityRule.put(SecurityRuleType.EXPORT_ENCRYPTED.getName(), "1".equals(jsonMap.get("switch").toString()));
                        } catch (Exception e) {
                            log.error(SecurityRuleType.EXPORT_ENCRYPTED.getName() + "内容转化失败");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("get security rule error!");
        }

        securityRule.putIfAbsent(SecurityRuleType.DESENSITIZATION_EXPORT.getName(), false);
        securityRule.putIfAbsent(SecurityRuleType.EXPORT_ENCRYPTED.getName(), false);

        return securityRule;
    }

}
