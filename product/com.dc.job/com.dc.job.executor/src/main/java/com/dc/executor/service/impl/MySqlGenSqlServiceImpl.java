package com.dc.executor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dc.executor.service.GenSqlService;
import com.dc.repository.mysql.mapper.DcAccountObjPrivsMapper;
import com.dc.repository.mysql.mapper.DcAccountSysPrivsMapper;
import com.dc.repository.mysql.model.DcAccountObjPrivs;
import com.dc.repository.mysql.model.DcAccountSysPrivs;
import com.dc.repository.mysql.model.DcDbResource;
import com.dc.repository.mysql.model.DcDbResourceAccount;
import com.dc.repository.mysql.type.GrantObjectType;
import com.dc.springboot.core.model.privilege.PrivilegeModel;
import com.dc.utils.Pair;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MySqlGenSqlServiceImpl implements GenSqlService {

    @Resource
    private DcAccountObjPrivsMapper dcAccountObjPrivsMapper;

    @Resource
    private DcAccountSysPrivsMapper dcAccountSysPrivsMapper;

    @Override
    public List<Pair<PrivilegeModel, String>> genSql(DcDbResource resource, DcDbResourceAccount account, List<String> dirs) {
        Integer accountId = account.getId();

        Integer resourceId = resource.getId();
        LambdaQueryWrapper<DcAccountObjPrivs> qwo = Wrappers.<DcAccountObjPrivs>lambdaQuery()
                .eq(DcAccountObjPrivs::getResourceId, resourceId)
                .eq(DcAccountObjPrivs::getAccountId, accountId)
                .eq(DcAccountObjPrivs::getObjectType, GrantObjectType.SCHEMA.getName());

        List<Pair<PrivilegeModel, String>> sql = dcAccountObjPrivsMapper.getPrivileges(qwo)
                .stream()
                .map(p -> genGrantSql(p, account))
                .collect(Collectors.toList());


        LambdaQueryWrapper<DcAccountSysPrivs> qws = Wrappers.<DcAccountSysPrivs>lambdaQuery()
                .eq(DcAccountSysPrivs::getResourceId, resourceId)
                .eq(DcAccountSysPrivs::getAccountId, accountId);

        dcAccountSysPrivsMapper.getPrivileges(qws)
                .stream()
                .map(p -> genGrantSql(p, account)).forEach(sql::add);

        return sql;
    }

    private Pair<PrivilegeModel, String> genGrantSql(DcAccountObjPrivs privs, DcDbResourceAccount account) {
        Pair<PrivilegeModel, String> pairs = new Pair<>(null, null);
        PrivilegeModel privilegeModel = new PrivilegeModel();
        String format = "GRANT %s ON %s* TO '%s'";
        String schema = privs.getSchemaName();
        if (schema == null) {
            schema = "";
        } else if (!schema.isBlank()) {
            privilegeModel.setOwner(schema);
            schema = "`" + schema + "`" + ".";
        }

        String sql = String.format(format,
                privs.getPrivilege(),
                schema,
                privs.getGrantee());

        if (!StringUtils.isBlank(account.getHost())) {
            sql += "@'" + account.getHost() + "'";
        }


        privilegeModel.setOperateType(1);
        privilegeModel.setObjPrivileges(List.of(privs.getPrivilege()));
        privilegeModel.setOnObject("*");
        privilegeModel.setGrantees(List.of(privs.getGrantee()));

        pairs.setFirst(privilegeModel);
        pairs.setSecond(sql);

        return pairs;

    }


    private Pair<PrivilegeModel, String> genGrantSql(DcAccountSysPrivs privs, DcDbResourceAccount account) {
        Pair<PrivilegeModel, String> pairs = new Pair<>(null, null);
        String grantSql = String.format("GRANT %s ON *.* TO '%s'", privs.getPrivilege(), privs.getGrantee());
        if (!StringUtils.isBlank(account.getHost())) {
            grantSql += "@'" + account.getHost() + "'";
        }
        PrivilegeModel privilegeModel = new PrivilegeModel();
        privilegeModel.setOperateType(1);
        privilegeModel.setGrantees(List.of(privs.getGrantee()));
        pairs.setFirst(privilegeModel);
        pairs.setSecond(grantSql);
        return pairs;
    }
}
