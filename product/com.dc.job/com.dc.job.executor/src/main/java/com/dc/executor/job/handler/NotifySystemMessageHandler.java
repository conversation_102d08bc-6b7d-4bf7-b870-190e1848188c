package com.dc.executor.job.handler;

import com.dc.executor.config.JobConfig;
import com.dc.executor.util.DCJobLogger;
import com.dc.job.biz.model.ReturnT;
import com.dc.job.enums.DcJobStatusEnum;
import com.dc.job.handler.annotation.XxlJob;
import com.dc.repository.mysql.model.DCJob;
import com.dc.springboot.core.component.JSON;
import com.dc.utils.http.HttpClientUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.StringEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class NotifySystemMessageHandler extends AbstractJobHandler {

    @Resource
    private JobConfig jobConfig;

    @XxlJob("NotifySystemMessageHandler")
    public ReturnT<String> notifyUserActionHandler(String param) {
        try {
            String uri = jobConfig.getPath().getDcSpi() + "/dc-spi/notify/system/message";

            log.info("Start Notify System Message Action!");
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(param);
            int logId = jsonNode.get("log_id").asInt();
            int dc_job_id = jsonNode.get("dc_job_id").asInt();
            DCJob dcJob = dcJobMapper.selectById(dc_job_id);
            if (dcJob == null) {
                log.info("dc_job is not exist!");
                dcJobMapper.endUpdateById(dc_job_id, DcJobStatusEnum.fail.getValue(), "任务不存在");
                return ReturnT.FAIL;
            }

            dcJobMapper.beginUpdateById(dc_job_id, DcJobStatusEnum.running.getValue(), logId);
            String spiResullt = HttpClientUtils.doPost(uri, new StringEntity(param, "UTF-8"));
            if (StringUtils.isBlank(spiResullt)) {
                log.info("call interface:" + uri + ", but get nothing!");
                DCJobLogger.log("call interface:" + uri + ", but get nothing!");
                dcJobMapper.endUpdateById(dc_job_id, DcJobStatusEnum.fail.getValue(), spiResullt);
                return ReturnT.FAIL;
            }
            Map<String, Object> map = JSON.parseObject(spiResullt, Map.class);
            if (map != null) {
                if (!map.get("code").equals(200)) {
                    DCJobLogger.log("call interface:" + uri + ", but get error:" + map.get("msg"));
                    dcJobMapper.endUpdateById(dc_job_id, DcJobStatusEnum.fail.getValue(), spiResullt);
                    return ReturnT.FAIL;
                } else {
                    dcJobMapper.endUpdateById(dc_job_id, DcJobStatusEnum.success.getValue(), spiResullt);
                    DCJobLogger.log("call interface:" + uri + ", success:" + map.get("msg"));
                    log.info("call interface:" + uri + ", success!");
                }
            }
        } catch (Exception e) {
            log.error("call NotifySystemMessageHandler error!", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
