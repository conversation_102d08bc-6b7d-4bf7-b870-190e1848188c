spring:
  application:
    name: dc-job-admin
  profiles:
    active: dev
  main:
    allow-circular-references: true

logging:
  config: classpath:logback.xml
  sub-length: 0

# 度量指标监控与健康检查
management:
  health:
    db:
      enabled: false
  endpoint:
    shutdown:
      enabled: true
  endpoints:
    web:
      base-path: /monitor     # 访问端点根路径，默认为 /actuator
      exposure:
        include: '*'          # 需要开启的端点，值得注意的是即使配置了 '*'，shutdown 端点也不会开启还需要额外配置

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: false
    auto-mapping-behavior: full
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:/mybatis-mapper/*.xml
  global-config:
    # 逻辑删除配置
    db-config:
      # 删除前
      logic-not-delete-value: 0
      # 删除后
      logic-delete-value: 1

jasypt:
  encryptor:
    property:
      prefix: "DC("
      suffix: ")"